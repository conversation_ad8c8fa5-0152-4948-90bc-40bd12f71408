import{j as e,B as l,a as s,A as a,T as r,I as i,b as t,P as n,d as o,D as x,F as d,e as c,S as h,M as b,f as j,g as m,h as u,i as v}from"./mui-vendor-DsBXMegs.js";import{u as p}from"./react-vendor-Be-rfjCm.js";import{u as g,a as y,T as f,A as k,I as C,b as w}from"./index-Ck4sQVom.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const z=()=>{const z=p(),I=g(),S=y((e=>e.settings)),A=S.thinkingDisplayStyle||f.COMPACT,M=!1!==S.thoughtAutoCollapse,D=S.multiModelDisplayStyle||"horizontal",B=!1!==S.showToolDetails,L=!1!==S.showCitationDetails,W=S.toolbarDisplayStyle||"both",N=S.inputBoxStyle||"default",T=S.inputLayoutStyle||"default",E=!1!==S.showSystemPromptBubble,F=!1!==S.showUserAvatar,U=!1!==S.showUserName,P=!1!==S.showModelAvatar,R=!1!==S.showModelName,G=S.messageBubbleMinWidth||50,$=S.messageBubbleMaxWidth||99,H=S.userMessageMaxWidth||80;return e.jsxs(l,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?s(e.palette.primary.main,.02):s(e.palette.background.default,.9)},children:[e.jsx(a,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(r,{children:[e.jsx(i,{edge:"start",color:"inherit",onClick:()=>{z("/settings/appearance")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(k,{})}),e.jsx(t,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"聊天界面设置"})]})}),e.jsxs(l,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsxs(n,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(l,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"思考过程显示"}),e.jsx(o,{title:"配置AI思考过程的显示方式和行为",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(C,{fontSize:"small"})})})]}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"自定义AI思考过程的显示方式和自动折叠行为"})]}),e.jsx(x,{}),e.jsxs(l,{sx:{p:{xs:1.5,sm:2}},children:[e.jsxs(d,{fullWidth:!0,variant:"outlined",sx:{mb:2},children:[e.jsx(c,{children:"显示样式"}),e.jsxs(h,{value:A,onChange:e=>{I(w({thinkingDisplayStyle:e.target.value}))},label:"显示样式",children:[e.jsx(b,{value:f.COMPACT,children:"紧凑模式（可折叠）"}),e.jsx(b,{value:f.FULL,children:"完整模式（始终展开）"}),e.jsx(b,{value:f.MINIMAL,children:"极简模式（小图标）"}),e.jsx(b,{value:f.BUBBLE,children:"气泡模式（聊天气泡）"}),e.jsx(b,{value:f.TIMELINE,children:"时间线模式（左侧指示器）"}),e.jsx(b,{value:f.CARD,children:"卡片模式（突出显示）"}),e.jsx(b,{value:f.INLINE,children:"内联模式（嵌入消息）"}),e.jsx(b,{value:f.HIDDEN,children:"隐藏（不显示思考过程）"})]})]}),e.jsx(j,{children:e.jsx(m,{control:e.jsx(u,{size:"small",checked:M,onChange:e=>{I(w({thoughtAutoCollapse:e.target.checked}))}}),label:"思考完成后自动折叠"})}),e.jsxs(t,{variant:"body2",color:"text.secondary",sx:{mt:1,fontSize:{xs:"0.8rem",sm:"0.875rem"},lineHeight:1.5},children:["设置AI助手思考过程的显示方式：",e.jsx("br",{}),"• 紧凑模式：标准卡片样式，可折叠展开",e.jsx("br",{}),"• 完整模式：始终展开显示全部内容",e.jsx("br",{}),"• 极简模式：只显示小图标，悬停查看内容",e.jsx("br",{}),"• 气泡模式：类似聊天气泡的圆润设计",e.jsx("br",{}),"• 时间线模式：左侧带时间线指示器",e.jsx("br",{}),"• 卡片模式：突出的渐变卡片设计",e.jsx("br",{}),"• 内联模式：嵌入在消息中的紧凑显示",e.jsx("br",{}),"• 隐藏：完全不显示思考过程"]})]})]}),e.jsxs(n,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(t,{variant:"subtitle1",children:"多模型对比显示"}),e.jsx(o,{title:"配置多模型对比时的布局方式",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(C,{fontSize:"small"})})})]}),e.jsxs(d,{fullWidth:!0,variant:"outlined",sx:{mb:2},children:[e.jsx(c,{children:"布局方式"}),e.jsxs(h,{value:D,onChange:e=>{I(w({multiModelDisplayStyle:e.target.value}))},label:"布局方式",children:[e.jsx(b,{value:"horizontal",children:"水平布局（默认）"}),e.jsx(b,{value:"vertical",children:"垂直布局（并排显示）"}),e.jsx(b,{value:"single",children:"单独布局（堆叠显示）"})]})]}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"设置多模型对比时的布局方式。水平布局将模型响应并排显示，垂直布局将模型响应上下排列，单独布局将模型响应堆叠显示。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsx(t,{variant:"subtitle1",sx:{mb:2},children:"工具栏显示方式"}),e.jsxs(d,{fullWidth:!0,variant:"outlined",sx:{mb:2},children:[e.jsx(c,{children:"工具栏显示样式"}),e.jsxs(h,{value:W,onChange:e=>{I(w({toolbarDisplayStyle:e.target.value}))},label:"工具栏显示样式",children:[e.jsx(b,{value:"both",children:"图标+文字（默认）"}),e.jsx(b,{value:"icon",children:"仅图标"}),e.jsx(b,{value:"text",children:"仅文字"})]})]}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"设置聊天界面顶部工具栏的显示方式。可以选择同时显示图标和文字，或仅显示图标，或仅显示文字。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsx(t,{variant:"subtitle1",sx:{mb:2},children:"输入框风格"}),e.jsxs(d,{fullWidth:!0,variant:"outlined",sx:{mb:2},children:[e.jsx(c,{children:"输入框风格"}),e.jsxs(h,{value:N,onChange:e=>{I(w({inputBoxStyle:e.target.value}))},label:"输入框风格",children:[e.jsx(b,{value:"default",children:"默认风格"}),e.jsx(b,{value:"modern",children:"现代风格"}),e.jsx(b,{value:"minimal",children:"简约风格"})]})]}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"选择聊天输入框和工具栏的视觉风格。默认风格保持原有设计，现代风格采用更时尚的外观，简约风格则更加简洁。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsx(t,{variant:"subtitle1",sx:{mb:2},children:"输入框布局样式"}),e.jsxs(d,{fullWidth:!0,variant:"outlined",sx:{mb:2},children:[e.jsx(c,{children:"布局样式"}),e.jsxs(h,{value:T,onChange:e=>{I(w({inputLayoutStyle:e.target.value}))},label:"布局样式",children:[e.jsx(b,{value:"default",children:"默认样式（工具栏+输入框分离）"}),e.jsx(b,{value:"compact",children:"聚合样式（输入框+功能图标集成）"})]})]}),e.jsxs(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:["选择聊天输入区域的布局方式：",e.jsx("br",{}),"• 默认样式：工具栏和输入框分别显示，功能清晰分离",e.jsx("br",{}),"• 聚合样式：输入框上方，下方为功能图标行，点击+号可展开更多功能"]})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(t,{variant:"subtitle1",children:"工具调用设置"}),e.jsx(o,{title:"配置工具调用的显示详情",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(C,{fontSize:"small"})})})]}),e.jsx(j,{children:e.jsx(m,{control:e.jsx(u,{size:"small",checked:B,onChange:e=>{I(w({showToolDetails:e.target.checked}))}}),label:"显示工具调用详情"})}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"控制是否显示工具调用的详细信息，包括调用参数和返回结果。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(t,{variant:"subtitle1",children:"引用设置"}),e.jsx(o,{title:"配置引用的显示详情",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(C,{fontSize:"small"})})})]}),e.jsx(j,{children:e.jsx(m,{control:e.jsx(u,{size:"small",checked:L,onChange:e=>{I(w({showCitationDetails:e.target.checked}))}}),label:"显示引用详情"})}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"控制是否显示引用的详细信息，包括引用来源和相关内容。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(t,{variant:"subtitle1",children:"头像和名称显示"}),e.jsx(o,{title:"自定义聊天界面中用户和模型的头像及名称显示",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(C,{fontSize:"small"})})})]}),e.jsxs(j,{children:[e.jsx(m,{control:e.jsx(u,{size:"small",checked:F,onChange:e=>{I(w({showUserAvatar:e.target.checked}))}}),label:"显示用户头像"}),e.jsx(m,{control:e.jsx(u,{size:"small",checked:U,onChange:e=>{I(w({showUserName:e.target.checked}))}}),label:"显示用户名称"}),e.jsx(m,{control:e.jsx(u,{size:"small",checked:P,onChange:e=>{I(w({showModelAvatar:e.target.checked}))}}),label:"显示模型头像"}),e.jsx(m,{control:e.jsx(u,{size:"small",checked:R,onChange:e=>{I(w({showModelName:e.target.checked}))}}),label:"显示模型名称"})]}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"控制聊天界面中用户和AI模型的头像及名称显示。可以根据个人喜好选择性隐藏这些元素，获得更简洁的聊天体验。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsx(t,{variant:"subtitle1",sx:{mb:2},children:"系统提示词气泡设置"}),e.jsxs(d,{fullWidth:!0,variant:"outlined",sx:{mb:2},children:[e.jsx(c,{id:"prompt-bubble-style-label",children:"系统提示词气泡显示"}),e.jsxs(h,{labelId:"prompt-bubble-style-label",value:E?"show":"hide",onChange:e=>{I(w({showSystemPromptBubble:"show"===e.target.value}))},label:"系统提示词气泡显示",children:[e.jsx(b,{value:"show",children:"显示"}),e.jsx(b,{value:"hide",children:"隐藏"})]})]}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"控制是否在聊天界面顶部显示系统提示词气泡。系统提示词气泡可以帮助您查看和编辑当前会话的系统提示词。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(l,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(t,{variant:"subtitle1",children:"消息气泡宽度设置"}),e.jsx(o,{title:"自定义聊天界面中消息气泡的宽度范围，适配不同设备屏幕",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(C,{fontSize:"small"})})})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(t,{variant:"body2",gutterBottom:!0,children:["AI消息最大宽度: ",$,"%"]}),e.jsx(v,{value:$,onChange:(e,l)=>{I(w({messageBubbleMaxWidth:l}))},min:50,max:100,step:5,marks:[{value:50,label:"50%"},{value:75,label:"75%"},{value:100,label:"100%"}],valueLabelDisplay:"auto",valueLabelFormat:e=>`${e}%`})]}),e.jsxs(l,{sx:{mb:3},children:[e.jsxs(t,{variant:"body2",gutterBottom:!0,children:["用户消息最大宽度: ",H,"%"]}),e.jsx(v,{value:H,onChange:(e,l)=>{I(w({userMessageMaxWidth:l}))},min:50,max:100,step:5,marks:[{value:50,label:"50%"},{value:75,label:"75%"},{value:100,label:"100%"}],valueLabelDisplay:"auto",valueLabelFormat:e=>`${e}%`})]}),e.jsxs(l,{sx:{mb:2},children:[e.jsxs(t,{variant:"body2",gutterBottom:!0,children:["消息最小宽度: ",G,"%"]}),e.jsx(v,{value:G,onChange:(e,l)=>{I(w({messageBubbleMinWidth:l}))},min:10,max:90,step:5,marks:[{value:10,label:"10%"},{value:30,label:"30%"},{value:50,label:"50%"},{value:70,label:"70%"},{value:90,label:"90%"}],valueLabelDisplay:"auto",valueLabelFormat:e=>`${e}%`})]}),e.jsxs(t,{variant:"body2",color:"text.secondary",sx:{mt:1},children:["调整消息气泡的宽度范围以适配不同设备：",e.jsx("br",{}),"• AI消息最大宽度：控制AI回复的最大显示宽度",e.jsx("br",{}),"• 用户消息最大宽度：控制用户消息的最大显示宽度",e.jsx("br",{}),"• 消息最小宽度：所有消息的最小显示宽度，避免过窄影响阅读",e.jsx("br",{}),"• 较小的宽度适合手机等窄屏设备，较大的宽度适合平板和电脑"]})]}),e.jsx(l,{sx:{height:"20px"}})]})]})};export{z as default};
