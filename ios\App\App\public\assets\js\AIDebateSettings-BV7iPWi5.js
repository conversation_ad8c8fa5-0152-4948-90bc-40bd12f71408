import{c as e,j as n,B as s,a as r,A as o,T as t,I as a,b as i,P as l,D as d,g as c,h as x,t as m,m as p,n as h,J as b,o as f,p as g,q as j,F as u,e as v,S as y,M as C,r as k}from"./mui-vendor-DsBXMegs.js";import{u as S,r as I}from"./react-vendor-Be-rfjCm.js";import{o as w,d as z,A,aa as P,ab as W,e as D,ac as E}from"./index-Dnlt-eWK.js";import{D as R}from"./DropdownModelSelector-Cblp38oH.js";import{F as N}from"./Forum-CVhVMzbl.js";import{A as $}from"./Add-CgwaVY5w.js";import{E as J}from"./Edit-_cexwR-h.js";import{S as T}from"./Save-BEY1hRQb.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const O=e(n.jsx("path",{d:"M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2m0 12H4V8h16z"})),G=5,M=1e3,B=10,q=!0,F=!0,_=()=>{const e=S(),_=w(),H=z((e=>e.settings.providers||[])),L=z((e=>e.settings.showAIDebateButton??!0)),V=H.flatMap((e=>e.models.filter((e=>e.enabled)).map((n=>({...n,providerName:e.name}))))),[Y,K]=I.useState({enabled:!1,maxRounds:G,autoEndConditions:{consensusReached:!0,maxTokensPerRound:M,timeoutMinutes:B},roles:[],moderatorEnabled:q,summaryEnabled:F}),[Q,U]=I.useState([]),[X,Z]=I.useState(!1),[ee,ne]=I.useState(null),[se,re]=I.useState(""),[oe,te]=I.useState(""),[ae,ie]=I.useState(!1),[le,de]=I.useState(null),[ce,xe]=I.useState({name:"",description:"",systemPrompt:"",modelId:"",color:"#2196f3",stance:"pro"}),me=[{name:"正方辩手",description:"支持观点的辩论者",systemPrompt:"你是一位专业的正方辩论者，具有以下特点：\n\n🎯 **核心职责**\n- 坚定支持和论证正方观点\n- 提供有力的证据和逻辑论证\n- 反驳对方的质疑和攻击\n\n💡 **辩论风格**\n- 逻辑清晰，论证有力\n- 引用具体事实、数据和案例\n- 保持理性和专业的态度\n- 语言简洁明了，重点突出\n\n📋 **回应要求**\n- 每次发言控制在150-200字\n- 先明确表达立场，再提供论证\n- 适当反驳对方观点\n- 结尾要有力且令人信服\n\n请始终站在正方立场，为你的观点据理力争！",stance:"pro",color:"#4caf50"},{name:"反方辩手",description:"反对观点的辩论者",systemPrompt:"你是一位犀利的反方辩论者，具有以下特点：\n\n🎯 **核心职责**\n- 坚决反对正方观点\n- 揭示对方论证的漏洞和问题\n- 提出有力的反驳和质疑\n\n💡 **辩论风格**\n- 思维敏锐，善于发现问题\n- 用事实和逻辑拆解对方论证\n- 提出替代方案或反面证据\n- 保持批判性思维\n\n📋 **回应要求**\n- 每次发言控制在150-200字\n- 直接指出对方观点的问题\n- 提供反面证据或案例\n- 语气坚定但保持礼貌\n\n请始终站在反方立场，用理性和事实挑战对方观点！",stance:"con",color:"#f44336"},{name:"中立分析师",description:"客观理性的分析者",systemPrompt:"你是一位客观中立的分析师，具有以下特点：\n\n🎯 **核心职责**\n- 客观分析双方观点的优缺点\n- 指出论证中的逻辑问题或亮点\n- 提供平衡的视角和见解\n\n💡 **分析风格**\n- 保持绝对中立，不偏向任何一方\n- 用理性和逻辑评估论证质量\n- 指出可能被忽视的角度\n- 寻找双方的共同点\n\n📋 **回应要求**\n- 每次发言控制在150-200字\n- 平衡评价双方观点\n- 指出论证的强弱之处\n- 提出新的思考角度\n\n请保持中立立场，为辩论提供客观理性的分析！",stance:"neutral",color:"#ff9800"},{name:"辩论主持人",description:"控制节奏的主持人",systemPrompt:'你是一位专业的辩论主持人，具有以下职责：\n\n🎯 **核心职责**\n- 引导辩论方向和节奏\n- 总结各方要点和分歧\n- 判断讨论是否充分\n- 决定何时结束辩论\n\n💡 **主持风格**\n- 公正中立，不偏向任何一方\n- 善于总结和归纳要点\n- 能够发现讨论的关键问题\n- 控制辩论节奏和质量\n\n📋 **回应要求**\n- 每次发言控制在150-200字\n- 总结前面的主要观点\n- 指出需要进一步讨论的问题\n- 推动辩论深入进行\n\n⚠️ **重要：结束辩论的条件**\n只有在以下情况下才明确说"建议结束辩论"：\n1. 已经进行了至少3轮完整辩论\n2. 各方观点出现明显重复\n3. 讨论已经非常充分，没有新的观点\n4. 达成了某种程度的共识\n\n在前几轮中，请专注于推动讨论深入，而不是急于结束！',stance:"moderator",color:"#9c27b0"},{name:"法律专家",description:"从法律角度分析问题",systemPrompt:"你是一位资深法律专家，从法律角度参与辩论：\n\n🎯 **专业视角**\n- 从法律法规角度分析问题\n- 引用相关法条和判例\n- 分析法律风险和合规性\n- 考虑法律实施的可行性\n\n💡 **专业特长**\n- 熟悉各类法律法规\n- 了解司法实践和判例\n- 能够识别法律漏洞和风险\n- 具备严谨的法律思维\n\n📋 **发言要求**\n- 每次发言150-200字\n- 引用具体法条或判例\n- 分析法律层面的利弊\n- 保持专业和严谨\n\n请从法律专业角度为辩论提供有价值的见解！",stance:"neutral",color:"#795548"},{name:"经济学家",description:"从经济角度评估影响",systemPrompt:"你是一位经济学专家，从经济角度参与辩论：\n\n🎯 **专业视角**\n- 分析经济成本和收益\n- 评估市场影响和效率\n- 考虑宏观和微观经济效应\n- 预测长期经济后果\n\n💡 **专业特长**\n- 掌握经济学理论和模型\n- 了解市场运行机制\n- 能够量化分析影响\n- 具备数据分析能力\n\n📋 **发言要求**\n- 每次发言150-200字\n- 提供经济数据或理论支撑\n- 分析成本效益\n- 考虑经济可持续性\n\n请从经济学角度为辩论提供专业的分析和建议！",stance:"neutral",color:"#607d8b"},{name:"技术专家",description:"从技术可行性角度分析",systemPrompt:"你是一位技术专家，从技术角度参与辩论：\n\n🎯 **专业视角**\n- 分析技术可行性和难度\n- 评估技术风险和挑战\n- 考虑技术发展趋势\n- 预测技术实现的时间和成本\n\n💡 **专业特长**\n- 掌握前沿技术发展\n- 了解技术实现的复杂性\n- 能够评估技术方案\n- 具备工程思维\n\n📋 **发言要求**\n- 每次发言150-200字\n- 提供技术事实和数据\n- 分析实现的技术路径\n- 指出技术限制和可能性\n\n请从技术专业角度为辩论提供切实可行的分析！",stance:"neutral",color:"#3f51b5"},{name:"社会学者",description:"从社会影响角度思考",systemPrompt:"你是一位社会学专家，从社会角度参与辩论：\n\n🎯 **专业视角**\n- 分析社会影响和后果\n- 考虑不同群体的利益\n- 评估社会公平性\n- 关注文化和价值观影响\n\n💡 **专业特长**\n- 了解社会结构和动态\n- 关注弱势群体权益\n- 具备人文关怀\n- 能够预测社会反应\n\n📋 **发言要求**\n- 每次发言150-200字\n- 关注社会公平和正义\n- 考虑不同群体的感受\n- 分析社会接受度\n\n请从社会学角度为辩论提供人文关怀的视角！",stance:"neutral",color:"#e91e63"},{name:"总结分析师",description:"专门负责辩论总结分析",systemPrompt:"你是一位专业的辞论总结分析师，具有以下特点：\n\n🎯 **核心职责**\n- 客观分析整个辩论过程\n- 总结各方的核心观点和论据\n- 识别争议焦点和共识点\n- 提供平衡的结论和建议\n\n💡 **分析风格**\n- 保持绝对客观和中立\n- 深度分析论证逻辑和质量\n- 识别辩论中的亮点和不足\n- 提供建设性的思考和启发\n\n📋 **总结要求**\n- 结构化呈现分析结果\n- 平衡评价各方表现\n- 指出论证的强弱之处\n- 提供深度思考和建议\n- 避免偏向任何一方\n\n请为辩论提供专业、深入、平衡的总结分析！",stance:"summary",color:"#607d8b"},{name:"魔鬼代言人",description:"专门提出反对意见",systemPrompt:'你是"魔鬼代言人"，专门提出反对和质疑：\n\n🎯 **核心职责**\n- 对任何观点都提出质疑\n- 寻找论证中的薄弱环节\n- 提出极端或边缘情况\n- 挑战常规思维\n\n💡 **思维特点**\n- 批判性思维极强\n- 善于发现问题和漏洞\n- 不怕提出不受欢迎的观点\n- 推动深度思考\n\n📋 **发言要求**\n- 每次发言150-200字\n- 必须提出质疑或反对\n- 指出可能的风险和问题\n- 挑战主流观点\n\n请扮演好魔鬼代言人的角色，为辩论带来更深层的思考！',stance:"con",color:"#424242"},{name:"实用主义者",description:"关注实际操作和效果",systemPrompt:"你是一位实用主义者，关注实际可操作性：\n\n🎯 **核心关注**\n- 实际操作的可行性\n- 实施成本和效果\n- 现实条件和限制\n- 短期和长期的实用性\n\n💡 **思维特点**\n- 务实理性，不空谈理论\n- 关注具体实施细节\n- 重视成本效益分析\n- 追求实际效果\n\n📋 **发言要求**\n- 每次发言150-200字\n- 关注实际操作层面\n- 分析实施的难点和方法\n- 提供具体可行的建议\n\n请从实用主义角度为辩论提供务实的见解！",stance:"neutral",color:"#8bc34a"}];I.useEffect((()=>{(()=>{try{const e=localStorage.getItem("aiDebateConfig");if(e){const n=JSON.parse(e);K(n)}const n=localStorage.getItem("aiDebateConfigGroups");if(n){const e=JSON.parse(n);U(e)}}catch(e){console.error("加载AI辩论配置失败:",e)}})()}),[]);const pe=e=>{try{localStorage.setItem("aiDebateConfig",JSON.stringify(e)),K(e)}catch(n){console.error("保存AI辩论配置失败:",n)}},he=e=>{try{localStorage.setItem("aiDebateConfigGroups",JSON.stringify(e)),U(e)}catch(n){console.error("保存分组配置失败:",n)}},be=e=>{let n=[];const s=V.length>0?V[0].id:"";switch(e){case"basic":n=[me.find((e=>"正方辩手"===e.name)),me.find((e=>"反方辩手"===e.name)),me.find((e=>"辩论主持人"===e.name))];break;case"professional":n=[me.find((e=>"正方辩手"===e.name)),me.find((e=>"反方辩手"===e.name)),me.find((e=>"中立分析师"===e.name)),me.find((e=>"辩论主持人"===e.name))];break;case"expert":n=[me.find((e=>"法律专家"===e.name)),me.find((e=>"经济学家"===e.name)),me.find((e=>"技术专家"===e.name)),me.find((e=>"辩论主持人"===e.name))];break;case"comprehensive":n=[me.find((e=>"正方辩手"===e.name)),me.find((e=>"反方辩手"===e.name)),me.find((e=>"中立分析师"===e.name)),me.find((e=>"法律专家"===e.name)),me.find((e=>"经济学家"===e.name)),me.find((e=>"辩论主持人"===e.name))]}const r=n.map(((e,n)=>({id:`role_${Date.now()}_${n}`,name:e.name,description:e.description,systemPrompt:e.systemPrompt,modelId:s,color:e.color,stance:e.stance}))),o={...Y,enabled:!0,roles:r};pe(o);const t="basic"===e?"基础辩论":"professional"===e?"专业辩论":"expert"===e?"专家论坛":"全面分析",a=V.length>0?V[0].name:"无可用模型";alert(`✅ 已成功配置"${t}"场景！\n\n包含 ${r.length} 个角色：\n${r.map((e=>`• ${e.name}`)).join("\n")}\n\n🤖 已自动配置默认模型：${a}\n💡 您可以在角色管理中为每个角色单独指定不同的模型`)};return n.jsxs(s,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?r(e.palette.primary.main,.02):r(e.palette.background.default,.9)},children:[n.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:n.jsxs(t,{children:[n.jsx(a,{edge:"start",color:"inherit",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:n.jsx(A,{})}),n.jsx(N,{sx:{mr:1,color:"#e11d48"}}),n.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"AI辩论设置"})]})}),n.jsxs(s,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[n.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[n.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[n.jsxs(i,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"},display:"flex",alignItems:"center"},children:[n.jsx(P,{sx:{mr:1,color:"#06b6d4"}}),"基本设置"]}),n.jsx(i,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"配置AI辩论功能的基础参数和选项"})]}),n.jsx(d,{}),n.jsxs(s,{sx:{p:{xs:1.5,sm:2}},children:[n.jsx(c,{control:n.jsx(x,{checked:Y.enabled,onChange:e=>pe({...Y,enabled:e.target.checked})}),label:"启用AI辩论功能",sx:{mb:2}}),n.jsx(c,{control:n.jsx(x,{checked:L,onChange:e=>_(W(e.target.checked))}),label:"在输入框显示AI辩论按钮",sx:{mb:2}}),n.jsxs(s,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",md:"1fr 1fr"},gap:2},children:[n.jsx(m,{label:"最大辩论轮数",value:Y.maxRounds,onChange:e=>{const n=e.target.value;if(""===n)pe({...Y,maxRounds:0});else{const e=parseInt(n);isNaN(e)||pe({...Y,maxRounds:e})}},helperText:"输入数字，建议1-20轮"}),n.jsx(m,{label:"每轮最大Token数",value:Y.autoEndConditions.maxTokensPerRound,onChange:e=>{const n=e.target.value;if(""===n)pe({...Y,autoEndConditions:{...Y.autoEndConditions,maxTokensPerRound:0}});else{const e=parseInt(n);isNaN(e)||pe({...Y,autoEndConditions:{...Y.autoEndConditions,maxTokensPerRound:e}})}},helperText:"输入数字，建议100-4000"})]}),n.jsxs(s,{sx:{mt:2},children:[n.jsx(c,{control:n.jsx(x,{checked:Y.moderatorEnabled,onChange:e=>pe({...Y,moderatorEnabled:e.target.checked})}),label:"启用主持人角色"}),n.jsx(c,{control:n.jsx(x,{checked:Y.summaryEnabled,onChange:e=>pe({...Y,summaryEnabled:e.target.checked})}),label:"自动生成辩论总结",sx:{ml:2}})]})]})]}),n.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[n.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[n.jsxs(i,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"},display:"flex",alignItems:"center"},children:[n.jsx(P,{sx:{mr:1,color:"#8b5cf6"}}),"快速配置"]}),n.jsx(i,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"为新手用户提供一键配置，快速创建完整的辩论场景"})]}),n.jsx(d,{}),n.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:n.jsxs(s,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",md:"repeat(2, 1fr)"},gap:2},children:[n.jsxs(p,{variant:"outlined",onClick:()=>be("basic"),sx:{p:2,textAlign:"left",flexDirection:"column",alignItems:"flex-start"},children:[n.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,mb:.5},children:"🎯 基础辩论"}),n.jsx(i,{variant:"caption",color:"text.secondary",children:"正方 + 反方 + 主持人（3角色）"})]}),n.jsxs(p,{variant:"outlined",onClick:()=>be("professional"),sx:{p:2,textAlign:"left",flexDirection:"column",alignItems:"flex-start"},children:[n.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,mb:.5},children:"🏛️ 专业辩论"}),n.jsx(i,{variant:"caption",color:"text.secondary",children:"正方 + 反方 + 中立分析师 + 主持人（4角色）"})]}),n.jsxs(p,{variant:"outlined",onClick:()=>be("expert"),sx:{p:2,textAlign:"left",flexDirection:"column",alignItems:"flex-start"},children:[n.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,mb:.5},children:"🎓 专家论坛"}),n.jsx(i,{variant:"caption",color:"text.secondary",children:"法律专家 + 经济学家 + 技术专家 + 主持人（4角色）"})]}),n.jsxs(p,{variant:"outlined",onClick:()=>be("comprehensive"),sx:{p:2,textAlign:"left",flexDirection:"column",alignItems:"flex-start"},children:[n.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,mb:.5},children:"🌟 全面分析"}),n.jsx(i,{variant:"caption",color:"text.secondary",children:"6个不同角色的全方位辩论"})]})]})})]}),n.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[n.jsx(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:n.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsxs(s,{children:[n.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"辩论角色管理"}),n.jsx(i,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"创建和管理AI辩论中的各种角色"})]}),n.jsx(p,{variant:"contained",startIcon:n.jsx($,{}),onClick:()=>{de(null),xe({name:"",description:"",systemPrompt:"",modelId:"",color:"#2196f3",stance:"pro"}),ie(!0)},sx:{background:"linear-gradient(90deg, #9333EA, #754AB4)",fontWeight:600,"&:hover":{background:"linear-gradient(90deg, #8324DB, #6D3CAF)"}},children:"添加角色"})]})}),n.jsx(d,{}),n.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:0===Y.roles.length?n.jsx(h,{severity:"info",sx:{mb:2},children:'还没有配置任何辩论角色。点击"添加角色"开始配置。'}):n.jsx(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:Y.roles.map((e=>{var r;return n.jsxs(s,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:1.5,border:1,borderColor:"divider",borderLeft:`4px solid ${e.color||"#2196f3"}`,borderRadius:1,bgcolor:"background.paper",transition:"all 0.2s ease","&:hover":{bgcolor:"action.hover",borderColor:"primary.main"}},children:[n.jsxs(s,{sx:{display:"flex",alignItems:"center",flexGrow:1,minWidth:0},children:[n.jsx(P,{sx:{mr:1,color:e.color||"#2196f3",fontSize:"1rem"}}),n.jsxs(s,{sx:{minWidth:0,flexGrow:1},children:[n.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1,mb:.5},children:[n.jsx(i,{variant:"body2",sx:{fontWeight:600},children:e.name}),n.jsx(b,{label:"pro"===e.stance?"正方":"con"===e.stance?"反方":"neutral"===e.stance?"中立":"moderator"===e.stance?"主持人":"总结",size:"small",sx:{bgcolor:e.color||"#2196f3",color:"white",fontWeight:600,height:"20px",fontSize:"0.7rem"}})]}),n.jsxs(i,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:[e.description," • ",e.modelId?(null==(r=V.find((n=>n.id===e.modelId)))?void 0:r.name)||"未知模型":"默认模型"]})]})]}),n.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:.5,ml:2},children:[n.jsx(a,{size:"small",onClick:()=>(e=>{de(e),xe(e),ie(!0)})(e),title:"编辑角色",children:n.jsx(J,{fontSize:"small"})}),n.jsx(a,{size:"small",onClick:()=>(e=>{const n={...Y,roles:Y.roles.filter((n=>n.id!==e))};pe(n)})(e.id),color:"error",title:"删除角色",children:n.jsx(D,{fontSize:"small"})})]})]},e.id)}))})})]}),n.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[n.jsx(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:n.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsxs(s,{children:[n.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"配置分组管理"}),n.jsx(i,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"保存和管理不同用途的辩论配置"})]}),n.jsx(p,{variant:"contained",startIcon:n.jsx($,{}),onClick:()=>{ne(null),re(""),te(""),Z(!0)},sx:{background:"linear-gradient(90deg, #f59e0b, #d97706)",fontWeight:600,"&:hover":{background:"linear-gradient(90deg, #d97706, #b45309)"}},children:"新建分组"})]})}),n.jsx(d,{}),n.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:0===Q.length?n.jsx(h,{severity:"info",children:'还没有保存任何配置分组。点击"新建分组"开始创建。'}):n.jsx(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:Q.map((e=>n.jsxs(s,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:1.5,border:1,borderColor:"divider",borderRadius:1,bgcolor:"background.paper",transition:"all 0.2s ease","&:hover":{bgcolor:"action.hover",borderColor:"primary.main"}},children:[n.jsxs(s,{sx:{display:"flex",alignItems:"center",flexGrow:1,minWidth:0},children:[n.jsx(O,{sx:{mr:1,color:"text.secondary",fontSize:"1rem"}}),n.jsxs(s,{sx:{minWidth:0,flexGrow:1},children:[n.jsx(i,{variant:"body2",sx:{fontWeight:600,mb:.5},children:e.name}),n.jsxs(i,{variant:"caption",color:"text.secondary",sx:{display:"block"},children:[e.config.roles.length," 个角色 • ",new Date(e.updatedAt).toLocaleDateString()]})]})]}),n.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:.5,ml:2},children:[n.jsx(p,{size:"small",onClick:()=>(e=>{K(JSON.parse(JSON.stringify(e.config))),pe(e.config)})(e),variant:"outlined",sx:{minWidth:"auto",px:1},children:"加载"}),n.jsx(a,{size:"small",onClick:()=>(e=>{ne(e),re(e.name),te(e.description),Z(!0)})(e),title:"编辑",children:n.jsx(J,{fontSize:"small"})}),n.jsx(a,{size:"small",onClick:()=>(e=>{const n=Q.map((n=>n.id===e?{...n,config:JSON.parse(JSON.stringify(Y)),updatedAt:Date.now()}:n));he(n),alert("分组配置已更新！")})(e.id),title:"保存当前配置到此分组",color:"primary",children:n.jsx(T,{fontSize:"small"})}),n.jsx(a,{size:"small",onClick:()=>{ne(null),re(`${e.name} - 副本`),te(`基于 ${e.name} 创建的副本`),Z(!0)},title:"复制",children:n.jsx(E,{fontSize:"small"})}),n.jsx(a,{size:"small",onClick:()=>(e=>{if(window.confirm("确定要删除这个配置分组吗？此操作不可撤销。")){const n=Q.filter((n=>n.id!==e));he(n)}})(e.id),color:"error",title:"删除",children:n.jsx(D,{fontSize:"small"})})]})]},e.id)))})})]})]}),n.jsxs(f,{open:ae,onClose:()=>ie(!1),maxWidth:"md",fullWidth:!0,children:[n.jsx(g,{children:le?"编辑角色":"添加新角色"}),n.jsxs(j,{children:[!le&&n.jsxs(s,{sx:{mb:3},children:[n.jsx(i,{variant:"subtitle2",sx:{mb:1},children:"快速模板："}),n.jsx(s,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:me.map(((e,s)=>n.jsx(b,{label:e.name,onClick:()=>(e=>{xe({...ce,...e})})(e),sx:{bgcolor:e.color,color:"white"}},s)))}),n.jsx(d,{sx:{my:2}})]}),n.jsxs(s,{sx:{display:"grid",gap:2},children:[n.jsx(m,{label:"角色名称",value:ce.name||"",onChange:e=>xe({...ce,name:e.target.value}),required:!0}),n.jsx(m,{label:"角色描述",value:ce.description||"",onChange:e=>xe({...ce,description:e.target.value}),multiline:!0,rows:2}),n.jsxs(u,{sx:{mb:2},children:[n.jsx(v,{children:"角色立场"}),n.jsxs(y,{value:ce.stance||"pro",onChange:e=>xe({...ce,stance:e.target.value}),children:[n.jsx(C,{value:"pro",children:"正方"}),n.jsx(C,{value:"con",children:"反方"}),n.jsx(C,{value:"neutral",children:"中立"}),n.jsx(C,{value:"moderator",children:"主持人"}),n.jsx(C,{value:"summary",children:"总结"})]})]}),n.jsxs(s,{sx:{mb:2},children:[n.jsx(i,{variant:"subtitle2",sx:{mb:1},children:"指定模型（可选）"}),n.jsx(R,{selectedModel:V.find((e=>e.id===ce.modelId))||null,availableModels:V,handleModelSelect:e=>xe({...ce,modelId:(null==e?void 0:e.id)||""})}),n.jsx(i,{variant:"caption",color:"text.secondary",sx:{mt:.5,display:"block"},children:"留空则使用默认模型"})]}),n.jsx(m,{label:"系统提示词",value:ce.systemPrompt||"",onChange:e=>xe({...ce,systemPrompt:e.target.value}),multiline:!0,rows:6,required:!0,helperText:"定义这个AI角色的行为、立场和回应风格"}),n.jsxs(s,{children:[n.jsx(i,{variant:"subtitle2",sx:{mb:1},children:"角色颜色"}),n.jsx("input",{type:"color",value:ce.color||"#2196f3",onChange:e=>xe({...ce,color:e.target.value}),style:{width:"100%",height:"40px",border:"none",borderRadius:"4px"}})]})]})]}),n.jsxs(k,{children:[n.jsx(p,{onClick:()=>ie(!1),children:"取消"}),n.jsx(p,{onClick:()=>{if(!ce.name||!ce.systemPrompt)return;const e={id:(null==le?void 0:le.id)||`role_${Date.now()}`,name:ce.name,description:ce.description||"",systemPrompt:ce.systemPrompt,modelId:ce.modelId,color:ce.color||"#2196f3",stance:ce.stance||"pro"};let n;n=le?Y.roles.map((n=>n.id===le.id?e:n)):[...Y.roles,e];const s={...Y,roles:n};pe(s),ie(!1)},variant:"contained",disabled:!ce.name||!ce.systemPrompt,children:"保存"})]})]}),n.jsxs(f,{open:X,onClose:()=>Z(!1),maxWidth:"sm",fullWidth:!0,children:[n.jsx(g,{children:ee?"编辑配置分组":"新建配置分组"}),n.jsx(j,{children:n.jsxs(s,{sx:{display:"grid",gap:2,mt:1},children:[n.jsx(m,{label:"分组名称",value:se,onChange:e=>re(e.target.value),required:!0,placeholder:"例如：学术辩论、商业分析、技术讨论"}),n.jsx(m,{label:"分组描述",value:oe,onChange:e=>te(e.target.value),multiline:!0,rows:3,placeholder:"描述这个配置分组的用途和特点"}),!ee&&n.jsx(h,{severity:"info",children:"将保存当前的所有配置（包括角色设置、轮数限制等）到这个分组中。"})]})}),n.jsxs(k,{children:[n.jsx(p,{onClick:()=>Z(!1),children:"取消"}),n.jsx(p,{onClick:()=>{if(!se.trim())return;const e=Date.now();let n;if(ee)n=Q.map((n=>n.id===ee.id?{...n,name:se.trim(),description:oe.trim(),updatedAt:e}:n));else{const s={id:`group_${e}`,name:se.trim(),description:oe.trim(),config:JSON.parse(JSON.stringify(Y)),createdAt:e,updatedAt:e};n=[...Q,s]}he(n),Z(!1)},variant:"contained",disabled:!se.trim(),startIcon:n.jsx(T,{}),children:ee?"保存修改":"创建分组"})]})]})]})};export{_ as default};
