import{j as e,B as o,a as r,A as s,T as t,I as i,b as n,m as l,P as a,d as c,D as d,f as x,g as p,h,$ as m,a0 as g,k as b,w as j,a1 as u,x as y,J as f}from"./mui-vendor-DsBXMegs.js";import{u as v}from"./react-vendor-Be-rfjCm.js";import{c as w,u as C,a as k,b as z,a9 as B}from"./index-Ck4sQVom.js";import{D as T,C as S,P as I}from"./dnd.esm-33eFxuRR.js";import{A as N,B as D}from"./bot-D_6xUTu5.js";import{W}from"./wand-sparkles-aR_DKulP.js";import{I as M,S as P,M as R,A as H}from"./settings-U9ls_XoW.js";import{T as G}from"./trash-2-CLDVVUq8.js";import{P as O}from"./plus-5If-ii08.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const A=w("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),Y=()=>{const w=v(),Y=C(),E=k((e=>e.settings)).topToolbar||{showSettingsButton:!0,showModelSelector:!0,modelSelectorStyle:"dialog",showChatTitle:!0,showTopicName:!1,showNewTopicButton:!1,showClearButton:!1,showMenuButton:!0,leftComponents:["menuButton","chatTitle","topicName","newTopicButton","clearButton"],rightComponents:["modelSelector","settingsButton"]},F=(e,o)=>{Y(z({topToolbar:{...E,[e]:o}}))},J=(e,o)=>{const r=K[e];if(r)if(F(r.key,o),o){const s=["modelSelector","settingsButton"].includes(e)?"rightComponents":"leftComponents",t=E[s]||[];t.includes(e)?F(r.key,o):Y(z({topToolbar:{...E,[r.key]:!0,[s]:[...t,e]}}))}else{const o=(E.leftComponents||[]).filter((o=>o!==e)),s=(E.rightComponents||[]).filter((o=>o!==e));Y(z({topToolbar:{...E,[r.key]:!1,leftComponents:o,rightComponents:s}}))}},K={menuButton:{name:"菜单按钮",icon:e.jsx(H,{size:20}),key:"showMenuButton"},chatTitle:{name:"对话标题",icon:e.jsx(B,{size:20}),key:"showChatTitle"},topicName:{name:"话题名称",icon:e.jsx(R,{size:20}),key:"showTopicName"},newTopicButton:{name:"新建话题",icon:e.jsx(O,{size:20}),key:"showNewTopicButton"},clearButton:{name:"清空按钮",icon:e.jsx(G,{size:20}),key:"showClearButton"},modelSelector:{name:"模型选择器",icon:e.jsx(D,{size:20}),key:"showModelSelector"},settingsButton:{name:"设置按钮",icon:e.jsx(P,{size:20}),key:"showSettingsButton"}},L=o=>{const r=K[o];if(!r||!E[r.key])return null;switch(o){case"menuButton":return e.jsx(i,{edge:"start",color:"inherit",size:"small",sx:{mr:1},children:e.jsx(H,{size:20})},o);case"chatTitle":return e.jsx(n,{variant:"h6",noWrap:!0,component:"div",children:"对话"},o);case"topicName":return e.jsx(n,{variant:"body2",noWrap:!0,sx:{color:"text.secondary",ml:1},children:"示例话题名称"},o);case"newTopicButton":return e.jsx(i,{color:"inherit",size:"small",sx:{ml:1},children:e.jsx(O,{size:20})},o);case"clearButton":return e.jsx(i,{color:"inherit",size:"small",sx:{ml:1},children:e.jsx(G,{size:20})},o);case"modelSelector":return e.jsx(f,{label:"GPT-4",size:"small",variant:"outlined",sx:{borderColor:"divider",color:"text.primary","& .MuiChip-label":{fontSize:"0.75rem"}}},o);case"settingsButton":return e.jsx(i,{color:"inherit",size:"small",children:e.jsx(P,{size:20})},o);default:return null}},$=()=>e.jsxs(a,{elevation:2,sx:{mb:3,overflow:"hidden"},children:[e.jsx(n,{variant:"subtitle2",sx:{p:2,pb:1,fontWeight:600},children:"实时预览"}),e.jsx(s,{position:"static",elevation:0,sx:{bgcolor:"background.paper",color:"text.primary",borderTop:"1px solid",borderColor:"divider"},children:e.jsxs(t,{sx:{justifyContent:"space-between",minHeight:"56px !important"},children:[e.jsx(o,{sx:{display:"flex",alignItems:"center",gap:1},children:(E.leftComponents||[]).map(L)}),e.jsx(o,{sx:{display:"flex",alignItems:"center",gap:1},children:(E.rightComponents||[]).map(L)})]})}),e.jsx(n,{variant:"caption",sx:{p:2,pt:1,color:"text.secondary",display:"block"},children:"这是顶部工具栏的实时预览，修改设置后会立即更新。可以拖拽下方的组件来调整顺序。"})]});return e.jsxs(o,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?r(e.palette.primary.main,.02):r(e.palette.background.default,.9)},children:[e.jsx(s,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(t,{children:[e.jsx(i,{edge:"start",color:"inherit",onClick:()=>{w("/settings/appearance")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(N,{size:20})}),e.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,color:"primary.main"},children:"顶部工具栏设置"}),e.jsx(l,{variant:"outlined",onClick:()=>w("/settings/appearance/top-toolbar-test"),size:"small",sx:{mr:1,borderColor:"primary.main",color:"primary.main","&:hover":{borderColor:"primary.dark",color:"primary.dark"}},children:"测试页面"}),e.jsx(l,{variant:"contained",startIcon:e.jsx(W,{size:16}),onClick:()=>w("/settings/appearance/top-toolbar-diy"),size:"small",sx:{bgcolor:"primary.main","&:hover":{bgcolor:"primary.dark"}},children:"DIY 布局"})]})}),e.jsxs(o,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsx($,{}),e.jsxs(a,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(o,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(n,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"基础组件显示"}),e.jsx(c,{title:"控制顶部工具栏中基础组件的显示与隐藏",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(M,{size:16})})})]}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"自定义顶部工具栏中基础组件的显示状态"})]}),e.jsx(d,{}),e.jsxs(o,{sx:{p:{xs:1.5,sm:2}},children:[e.jsxs(x,{children:[e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showSettingsButton,onChange:e=>J("settingsButton",e.target.checked)}),label:"显示设置按钮"}),e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showModelSelector,onChange:e=>J("modelSelector",e.target.checked)}),label:"显示模型选择器"}),e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showChatTitle,onChange:e=>J("chatTitle",e.target.checked)}),label:'显示"对话"标题'}),e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showMenuButton,onChange:e=>J("menuButton",e.target.checked)}),label:"显示菜单按钮"})]}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mt:1,fontSize:{xs:"0.8rem",sm:"0.875rem"},lineHeight:1.5},children:"控制顶部工具栏中基础组件的显示。隐藏设置按钮后，可以通过侧边栏菜单访问设置。"})]})]}),E.showModelSelector&&e.jsxs(a,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(n,{variant:"subtitle1",children:"模型选择器样式"}),e.jsx(c,{title:"选择模型选择器的显示样式",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(M,{size:16})})})]}),e.jsxs(m,{value:E.modelSelectorStyle,onChange:e=>F("modelSelectorStyle",e.target.value),children:[e.jsx(p,{value:"dialog",control:e.jsx(g,{size:"small"}),label:"弹窗式选择器（点击按钮弹出对话框）"}),e.jsx(p,{value:"dropdown",control:e.jsx(g,{size:"small"}),label:"下拉式选择器（直接下拉选择）"})]}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"图标模式可以节省顶部空间，适合小屏设备使用。"})]}),e.jsxs(a,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(n,{variant:"subtitle1",children:"扩展功能组件"}),e.jsx(c,{title:"添加额外的功能按钮到顶部工具栏",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(M,{size:16})})})]}),e.jsxs(x,{children:[e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showTopicName,onChange:e=>J("topicName",e.target.checked)}),label:"显示当前话题名称"}),e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showNewTopicButton,onChange:e=>J("newTopicButton",e.target.checked)}),label:"显示新建话题按钮"}),e.jsx(p,{control:e.jsx(h,{size:"small",checked:E.showClearButton,onChange:e=>J("clearButton",e.target.checked)}),label:"显示清空对话按钮"})]}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"这些功能可以让你快速访问常用操作，提高使用效率。"})]}),e.jsxs(a,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(n,{variant:"subtitle1",children:"组件排序"}),e.jsx(c,{title:"长按拖拽组件来调整顶部工具栏的布局顺序",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(M,{size:16})})})]}),e.jsx(T,{onDragEnd:e=>{if(!e.destination)return;const{source:o,destination:r}=e;if(o.droppableId===r.droppableId){const e="left"===o.droppableId?[...E.leftComponents||[]]:[...E.rightComponents||[]],[s]=e.splice(o.index,1);e.splice(r.index,0,s);const t="left"===o.droppableId?"leftComponents":"rightComponents";F(t,e)}else{const e="left"===o.droppableId?[...E.leftComponents||[]]:[...E.rightComponents||[]],s="left"===r.droppableId?[...E.leftComponents||[]]:[...E.rightComponents||[]],[t]=e.splice(o.index,1);s.splice(r.index,0,t),Y(z({topToolbar:{...E,leftComponents:"left"===o.droppableId?e:s,rightComponents:"right"===o.droppableId?e:s}}))}},children:e.jsxs(o,{sx:{display:"flex",gap:2},children:[e.jsxs(o,{sx:{flex:1},children:[e.jsx(n,{variant:"body2",sx:{mb:1,fontWeight:500},children:"左侧组件"}),e.jsx(S,{droppableId:"left",children:(o,r)=>e.jsxs(b,{ref:o.innerRef,...o.droppableProps,sx:{minHeight:200,p:1,bgcolor:r.isDraggingOver?"action.hover":"background.default",border:"2px dashed",borderColor:r.isDraggingOver?"primary.main":"divider",transition:"all 0.2s ease"},children:[(E.leftComponents||[]).filter((e=>{const o=K[e];return o&&E[o.key]})).map(((o,r)=>{const s=K[o];return s?e.jsx(I,{draggableId:o,index:r,children:(o,r)=>e.jsxs(j,{ref:o.innerRef,...o.draggableProps,...o.dragHandleProps,sx:{mb:1,bgcolor:r.isDragging?"primary.light":"background.paper",borderRadius:1,border:"1px solid",borderColor:r.isDragging?"primary.main":"divider",cursor:"grab","&:active":{cursor:"grabbing"},transform:r.isDragging?"rotate(5deg)":"none",transition:"all 0.2s ease"},children:[e.jsx(u,{sx:{minWidth:36},children:e.jsx(A,{size:20,color:"action"})}),e.jsx(u,{sx:{minWidth:36},children:s.icon}),e.jsx(y,{primary:s.name,primaryTypographyProps:{variant:"body2"}})]})},o):null})),o.placeholder]})})]}),e.jsxs(o,{sx:{flex:1},children:[e.jsx(n,{variant:"body2",sx:{mb:1,fontWeight:500},children:"右侧组件"}),e.jsx(S,{droppableId:"right",children:(o,r)=>e.jsxs(b,{ref:o.innerRef,...o.droppableProps,sx:{minHeight:200,p:1,bgcolor:r.isDraggingOver?"action.hover":"background.default",border:"2px dashed",borderColor:r.isDraggingOver?"primary.main":"divider",transition:"all 0.2s ease"},children:[(E.rightComponents||[]).filter((e=>{const o=K[e];return o&&E[o.key]})).map(((o,r)=>{const s=K[o];return s?e.jsx(I,{draggableId:o,index:r,children:(o,r)=>e.jsxs(j,{ref:o.innerRef,...o.draggableProps,...o.dragHandleProps,sx:{mb:1,bgcolor:r.isDragging?"primary.light":"background.paper",borderRadius:1,border:"1px solid",borderColor:r.isDragging?"primary.main":"divider",cursor:"grab","&:active":{cursor:"grabbing"},transform:r.isDragging?"rotate(5deg)":"none",transition:"all 0.2s ease"},children:[e.jsx(u,{sx:{minWidth:36},children:e.jsx(A,{size:20,color:"action"})}),e.jsx(u,{sx:{minWidth:36},children:s.icon}),e.jsx(y,{primary:s.name,primaryTypographyProps:{variant:"body2"}})]})},o):null})),o.placeholder]})})]})]})}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mt:2},children:"拖拽组件来调整顺序。只显示已开启的组件，关闭的组件不会出现在排序列表中。可以在左右区域之间拖拽来调整组件位置。"})]}),e.jsxs(a,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(n,{variant:"subtitle1",children:"快速配置"}),e.jsx(c,{title:"一键应用预设的工具栏配置",children:e.jsx(i,{size:"small",sx:{ml:1},children:e.jsx(M,{size:16})})})]}),e.jsxs(o,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[e.jsxs(o,{sx:{p:1.5,border:"1px solid #ddd",borderRadius:1,cursor:"pointer","&:hover":{bgcolor:"action.hover"}},onClick:()=>{Y(z({topToolbar:{showSettingsButton:!0,showModelSelector:!0,modelSelectorStyle:"dialog",showChatTitle:!0,showTopicName:!1,showNewTopicButton:!1,showClearButton:!1,showMenuButton:!0,leftComponents:["menuButton","chatTitle","topicName","newTopicButton","clearButton"],rightComponents:["modelSelector","settingsButton"]}}))},children:[e.jsx(n,{variant:"body2",sx:{fontWeight:500},children:"默认配置"}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"标准的工具栏布局"})]}),e.jsxs(o,{sx:{p:1.5,border:"1px solid #ddd",borderRadius:1,cursor:"pointer","&:hover":{bgcolor:"action.hover"}},onClick:()=>{Y(z({topToolbar:{showSettingsButton:!1,showModelSelector:!0,modelSelectorStyle:"dialog",showChatTitle:!1,showTopicName:!0,showNewTopicButton:!0,showClearButton:!0,showMenuButton:!0,leftComponents:["menuButton","topicName","newTopicButton","clearButton"],rightComponents:["modelSelector"]}}))},children:[e.jsx(n,{variant:"body2",sx:{fontWeight:500},children:"简洁配置"}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"精简的工具栏，节省空间"})]})]})]})]})]})};export{Y as default};
