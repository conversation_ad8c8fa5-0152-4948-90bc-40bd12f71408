/**
 * 内置记忆系统配置
 * 复用现有项目配置，无需额外配置
 */

import type { InternalMemoryConfig } from '../types/internalMemory';

// 内置记忆系统默认配置
export const internalMemoryConfig: InternalMemoryConfig = {
  // 功能开关
  enabled: true,
  
  // 记忆提取配置
  extraction: {
    model: 'gpt-4o-mini',              // 使用项目现有的LLM模型
    minConversationLength: 3,          // 最少对话轮数才开始提取
    maxMemoriesPerExtraction: 5        // 每次提取最多记忆数
  },
  
  // 语义搜索配置
  search: {
    embeddingModel: 'text-embedding-3-small', // 使用项目现有的嵌入模型
    similarityThreshold: 0.7,          // 相似度阈值
    maxResults: 5                      // 最多返回结果数
  },
  
  // 存储配置
  storage: {
    maxMemoriesPerUser: 1000,          // 每用户最大记忆数
    retentionDays: 90                  // 记忆保留天数
  }
};

// 获取记忆配置
export function getMemoryConfig(): InternalMemoryConfig {
  // 可以从环境变量或用户设置中覆盖默认配置
  const config = { ...internalMemoryConfig };
  
  // 检查环境变量覆盖
  if (typeof window !== 'undefined') {
    const envEnabled = import.meta.env.VITE_MEMORY_ENABLED;
    if (envEnabled !== undefined) {
      config.enabled = envEnabled === 'true';
    }
    
    const envAutoExtraction = import.meta.env.VITE_MEMORY_AUTO_EXTRACTION;
    if (envAutoExtraction !== undefined) {
      // 可以在这里添加更多环境变量配置
    }
  }
  
  return config;
}

// 更新记忆配置
export function updateMemoryConfig(updates: Partial<InternalMemoryConfig>): InternalMemoryConfig {
  Object.assign(internalMemoryConfig, updates);
  return internalMemoryConfig;
}

// 记忆分类显示名称映射
export const MEMORY_CATEGORY_LABELS = {
  preference: '偏好设置',
  background: '背景信息', 
  skill: '技能专长',
  habit: '使用习惯',
  plan: '计划目标'
} as const;

// 记忆重要性级别描述
export const MEMORY_IMPORTANCE_LEVELS = {
  1: '很低',
  2: '较低',
  3: '低',
  4: '中下',
  5: '中等',
  6: '中上',
  7: '高',
  8: '较高',
  9: '很高',
  10: '极高'
} as const;
