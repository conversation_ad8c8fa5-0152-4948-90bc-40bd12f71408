{"appId": "com.llmhouse.app", "appName": "AetherLink", "webDir": "dist", "android": {"initialFocus": true, "captureInput": false, "webContentsDebuggingEnabled": true, "allowMixedContent": true}, "ios": {"scheme": "AetherLink", "webContentsDebuggingEnabled": true, "allowsLinkPreview": false, "handleApplicationNotifications": false}, "server": {"androidScheme": "https", "allowNavigation": [], "cleartext": true}, "plugins": {"CapacitorHttp": {"enabled": false}, "CorsBypass": {}, "WebView": {"scrollEnabled": true, "allowFileAccess": true}, "Keyboard": {"resizeOnFullScreen": true}, "StatusBar": {"style": "DEFAULT", "overlaysWebView": false}, "AndroidEdgeToEdgeSupport": {"enabled": true, "statusBarColor": "transparent", "navigationBarColor": "transparent", "enforceEdgeToEdge": true}, "SplashScreen": {"launchShowDuration": 0}}, "packageClassList": ["FileOpenerPlugin", "NativeAudio", "SafeAreaPlugin", "SpeechRecognition", "AppPlugin", "CAPBrowserPlugin", "CAPCameraPlugin", "ClipboardPlugin", "DevicePlugin", "FilesystemPlugin", "HapticsPlugin", "KeyboardPlugin", "PreferencesPlugin", "SharePlugin", "StatusBarPlugin", "ToastPlugin", "FilePickerPlugin", "CorsBypassPlugin"]}