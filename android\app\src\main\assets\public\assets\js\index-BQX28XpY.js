const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/web-DtQhtwaA.js","assets/js/index-BtK6VV6Z.js","assets/js/mui-vendor-hRDvsX89.js","assets/js/react-vendor-C9ilihHH.js","assets/js/utils-vendor-BDm_82Hk.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/css/index-C_2Yhxqf.css","assets/js/web-C0MwD7EZ.js","assets/js/vue-vendor-Ch32PRwJ.js","assets/js/index-CfgM3OPG.js"])))=>i.map(i=>d[i]);
import{j as e,B as t,A as r,T as a,I as n,b as o,P as s,D as i}from"./mui-vendor-hRDvsX89.js";import{r as l,u as c}from"./react-vendor-C9ilihHH.js";import{aw as u,bh as d,bi as p,A as f}from"./index-BtK6VV6Z.js";import{d as g,r as b,c as m,a as h,t as w,o as v,b as y,e as x,f as V,n as I}from"./vue-vendor-Ch32PRwJ.js";import{C,a as j,b as k,T as R}from"./index-CfgM3OPG.js";import{_ as W}from"./syntax-vendor-DfDNeb5M.js";const E=g({name:"VueExample",props:{title:{type:String,default:"Vue 组件示例"}},setup:()=>({count:b(0)})}),P=(e,t)=>{const r=e.__vccOpts||e;for(const[a,n]of t)r[a]=n;return r},_={class:"vue-component"};const A=P(E,[["render",function(e,t,r,a,n,o){return v(),m("div",_,[h("h3",null,w(e.title),1),h("button",{onClick:t[0]||(t[0]=t=>e.count++)},"点击了："+w(e.count)+" 次",1),t[1]||(t[1]=h("p",null,"这是一个在React应用中的Vue组件",-1))])}],["__scopeId","data-v-aff63ca3"]]),D=({title:t})=>{const r=l.useRef(null),a=l.useRef(null);return l.useEffect((()=>{if(!r.current)return;const e=y(A,{title:t||"Vue 组件示例"});return e.mount(r.current),a.current=e,()=>{a.current&&(a.current.unmount(),a.current=null)}}),[t]),e.jsx("div",{ref:r,className:"vue-component-container"})},T=u("Device",{web:()=>W((()=>import("./web-DtQhtwaA.js")),__vite__mapDeps([0,1,2,3,4,5,6])).then((e=>new e.DeviceWeb))});var M,U,L,H;(U=M||(M={})).Heavy="HEAVY",U.Medium="MEDIUM",U.Light="LIGHT",(H=L||(L={})).Success="SUCCESS",H.Warning="WARNING",H.Error="ERROR";const N=u("Haptics",{web:()=>W((()=>import("./web-C0MwD7EZ.js")),__vite__mapDeps([7,1,2,3,4,5,6,8,9])).then((e=>new e.HapticsWeb))}),S=u("ModernWebView");const G=new class{async takePicture(e="CAMERA"){try{const t=await C.getPhoto({quality:90,allowEditing:!0,resultType:k.Uri,source:"CAMERA"===e?j.Camera:j.Photos});return{webPath:t.webPath,format:t.format}}catch(t){throw console.error("Camera error:",t),t}}async showToast(e,t="short"){await R.show({text:e,duration:t})}async getDeviceInfo(){try{const e=await T.getInfo(),t=await T.getBatteryInfo();return{model:e.model,platform:e.platform,operatingSystem:e.operatingSystem,osVersion:e.osVersion,manufacturer:e.manufacturer,batteryLevel:t.batteryLevel,isCharging:t.isCharging}}catch(e){throw console.error("Device info error:",e),e}}async vibrate(e="MEDIUM"){let t;switch(e){case"HEAVY":t=M.Heavy;break;case"LIGHT":t=M.Light;break;default:t=M.Medium}await N.impact({style:t})}async exitApp(){await d.exitApp()}async testModernWebViewPlugin(){try{const e=await S.echo({value:"Hello from frontend!"});return e}catch(e){throw console.error("❌ 插件连接失败:",e),e}}async getWebViewInfo(){try{if(p.isNativePlatform()){const e=await S.getWebViewInfo();return e}return{version:"Web Platform",versionName:navigator.userAgent,packageName:"browser",userAgent:navigator.userAgent,isGoogleChrome:navigator.userAgent.includes("Chrome"),isUpdatable:!1,supportsModernFeatures:!0,qualityLevel:"优秀",needsUpgrade:!1,strategy:"WEB_BROWSER",strategyDescription:"使用浏览器原生WebView",upgradeRecommendation:"您正在使用浏览器版本，无需升级。"}}catch(e){throw console.error("❌ WebView info error:",e),e}}async checkWebViewUpgrade(){try{if(p.isNativePlatform()){const e=await S.checkUpgradeNeeded();return e}return{needsUpgrade:!1,currentVersion:"Web Platform",minRecommendedVersion:0,isUpdatable:!1,upgradeRecommendation:"您正在使用浏览器版本，无需升级。"}}catch(e){throw console.error("❌ WebView upgrade check error:",e),e}}},O=g({name:"CapacitorFeatures",props:{title:{type:String,default:"Capacitor 原生功能演示"}},setup(){const e=b(null),t=b(null),r=b(null);return{deviceInfo:e,photoPath:t,webViewInfo:r,getDeviceInfo:async()=>{try{e.value=await G.getDeviceInfo()}catch(t){console.error("获取设备信息失败:",t)}},testEcho:async()=>{try{const e=await G.testModernWebViewPlugin();await G.showToast(`插件连接成功: ${e.plugin} v${e.version}`,"long")}catch(e){console.error("❌ 插件测试失败:",e),await G.showToast(`插件连接失败: ${e.message||e}`,"long")}},getWebViewInfo:async()=>{try{r.value=await G.getWebViewInfo(),r.value,await G.showToast("WebView信息获取成功!","short")}catch(e){console.error("❌ 获取WebView信息失败:",e),await G.showToast(`获取失败: ${e.message||e}`,"long"),r.value={version:"ERROR",versionName:"Failed to get info",packageName:"error",userAgent:e.message||"Unknown error",isGoogleChrome:!1,isUpdatable:!1,supportsModernFeatures:!1,qualityLevel:"错误",needsUpgrade:!0,strategy:"ERROR",strategyDescription:"获取信息失败",upgradeRecommendation:`错误详情: ${e.message||e}`}}},showToast:async(e,t="short")=>{try{await G.showToast(e,t)}catch(r){console.error("显示Toast失败:",r)}},vibrateDevice:async e=>{try{await G.vibrate(e)}catch(t){console.error("振动失败:",t)}},takePicture:async e=>{try{const r=await G.takePicture(e);t.value=r.webPath}catch(r){console.error("拍照失败:",r)}},getQualityClass:e=>{switch(e){case"优秀":return"quality-excellent";case"良好":return"quality-good";case"一般":return"quality-fair";case"需要升级":return"quality-poor";default:return""}}}}}),q={class:"capacitor-features"},B={class:"feature-card"},F={key:0,class:"info-panel"},$={class:"feature-card"},z={class:"btn-group"},Y={class:"feature-card"},Q={class:"btn-group"},J={key:0,class:"info-panel"},K={class:"feature-card"},X={class:"btn-group"},Z={key:0,class:"photo-container"},ee=["src"];const te=P(O,[["render",function(e,t,r,a,n,o){return v(),m("div",q,[h("h3",null,w(e.title),1),h("div",B,[t[16]||(t[16]=h("h4",null,"设备信息",-1)),h("button",{onClick:t[0]||(t[0]=(...t)=>e.getDeviceInfo&&e.getDeviceInfo(...t)),class:"feature-btn"},"获取设备信息"),e.deviceInfo?(v(),m("div",F,[h("p",null,[t[10]||(t[10]=h("strong",null,"平台:",-1)),V(" "+w(e.deviceInfo.platform),1)]),h("p",null,[t[11]||(t[11]=h("strong",null,"操作系统:",-1)),V(" "+w(e.deviceInfo.operatingSystem)+" "+w(e.deviceInfo.osVersion),1)]),h("p",null,[t[12]||(t[12]=h("strong",null,"制造商:",-1)),V(" "+w(e.deviceInfo.manufacturer),1)]),h("p",null,[t[13]||(t[13]=h("strong",null,"型号:",-1)),V(" "+w(e.deviceInfo.model),1)]),h("p",null,[t[14]||(t[14]=h("strong",null,"电池电量:",-1)),V(" "+w(Math.round(100*e.deviceInfo.batteryLevel))+"%",1)]),h("p",null,[t[15]||(t[15]=h("strong",null,"充电状态:",-1)),V(" "+w(e.deviceInfo.isCharging?"充电中":"未充电"),1)])])):x("",!0)]),h("div",$,[t[17]||(t[17]=h("h4",null,"交互功能",-1)),h("div",z,[h("button",{onClick:t[1]||(t[1]=t=>e.showToast("短消息")),class:"feature-btn small"},"短消息提示"),h("button",{onClick:t[2]||(t[2]=t=>e.showToast("这是一个较长的消息提示","long")),class:"feature-btn small"},"长消息提示"),h("button",{onClick:t[3]||(t[3]=t=>e.vibrateDevice("LIGHT")),class:"feature-btn small"},"轻微振动"),h("button",{onClick:t[4]||(t[4]=t=>e.vibrateDevice("MEDIUM")),class:"feature-btn small"},"中等振动"),h("button",{onClick:t[5]||(t[5]=t=>e.vibrateDevice("HEAVY")),class:"feature-btn small"},"强烈振动")])]),h("div",Y,[t[25]||(t[25]=h("h4",null,"WebView版本检测",-1)),h("div",Q,[h("button",{onClick:t[6]||(t[6]=(...t)=>e.testEcho&&e.testEcho(...t)),class:"feature-btn small"},"测试插件连接"),h("button",{onClick:t[7]||(t[7]=(...t)=>e.getWebViewInfo&&e.getWebViewInfo(...t)),class:"feature-btn"},"检测WebView版本")]),e.webViewInfo?(v(),m("div",J,[h("p",null,[t[18]||(t[18]=h("strong",null,"WebView版本:",-1)),V(" Chrome "+w(e.webViewInfo.version),1)]),h("p",null,[t[19]||(t[19]=h("strong",null,"质量评级:",-1)),h("span",{class:I(e.getQualityClass(e.webViewInfo.qualityLevel))},w(e.webViewInfo.qualityLevel),3)]),h("p",null,[t[20]||(t[20]=h("strong",null,"包名:",-1)),V(" "+w(e.webViewInfo.packageName),1)]),h("p",null,[t[21]||(t[21]=h("strong",null,"是否为Google Chrome:",-1)),V(" "+w(e.webViewInfo.isGoogleChrome?"是":"否"),1)]),h("p",null,[t[22]||(t[22]=h("strong",null,"支持现代特性:",-1)),V(" "+w(e.webViewInfo.supportsModernFeatures?"是":"否"),1)]),h("p",null,[t[23]||(t[23]=h("strong",null,"需要升级:",-1)),h("span",{class:I(e.webViewInfo.needsUpgrade?"upgrade-needed":"upgrade-ok")},w(e.webViewInfo.needsUpgrade?"是":"否"),3)]),h("p",null,[t[24]||(t[24]=h("strong",null,"升级建议:",-1)),V(" "+w(e.webViewInfo.upgradeRecommendation),1)])])):x("",!0)]),h("div",K,[t[26]||(t[26]=h("h4",null,"相机功能",-1)),h("div",X,[h("button",{onClick:t[8]||(t[8]=t=>e.takePicture("CAMERA")),class:"feature-btn"},"拍照"),h("button",{onClick:t[9]||(t[9]=t=>e.takePicture("PHOTOS")),class:"feature-btn"},"选择照片")]),e.photoPath?(v(),m("div",Z,[h("img",{src:e.photoPath,alt:"已拍照片"},null,8,ee)])):x("",!0)])])}],["__scopeId","data-v-e718ddfb"]]),re=()=>{const t=l.useRef(null),r=l.useRef(null);return l.useEffect((()=>{if(!t.current)return;const e=y(te,{title:"Capacitor 原生功能 (Vue实现)"});return e.mount(t.current),r.current=e,()=>{r.current&&(r.current.unmount(),r.current=null)}}),[]),e.jsx("div",{ref:t,className:"vue-capacitor-container",style:{minHeight:"400px"}})},ae=Object.freeze(Object.defineProperty({__proto__:null,default:()=>{const l=c();return e.jsxs(t,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",overflow:"hidden"},children:[e.jsx(r,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(a,{children:[e.jsx(n,{edge:"start",onClick:()=>{l(-1)},sx:{color:e=>e.palette.primary.main},children:e.jsx(f,{})}),e.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,color:"#42b983"},children:"Vue 组件演示"})]})}),e.jsx(t,{sx:{flexGrow:1,overflow:"auto",px:2,py:2,mt:8},children:e.jsxs(t,{sx:{maxWidth:"800px",mx:"auto",pb:6},children:[e.jsxs(s,{sx:{p:3,mb:4},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"基础Vue组件"}),e.jsx(t,{sx:{my:2},children:e.jsx(D,{title:"来自React的Vue组件"})}),e.jsx(o,{variant:"body2",color:"text.secondary",children:"这个基础Vue组件被包装在React组件中，数据通过props传递。"})]}),e.jsxs(s,{sx:{p:3,mb:4},children:[e.jsx(o,{variant:"h6",gutterBottom:!0,children:"Capacitor功能演示 (Vue实现)"}),e.jsx(t,{sx:{my:2},children:e.jsx(re,{})}),e.jsx(o,{variant:"body2",color:"text.secondary",children:"这个Vue组件展示了如何在Vue组件中使用Capacitor原生功能。"})]}),e.jsx(i,{sx:{my:4}}),e.jsx(o,{variant:"body1",children:"无视这个页面用来测试vue兼容。"})]})})]})}},Symbol.toStringTag,{value:"Module"}));export{M as I,L as N,ae as i};
