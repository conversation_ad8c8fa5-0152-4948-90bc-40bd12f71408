import{c as e,j as t,o as s,p as r,q as n,B as i,b as a,i as o,m as l,r as c}from"./mui-vendor-DsBXMegs.js";import{r as d}from"./react-vendor-Be-rfjCm.js";import{C as h}from"./CloudUpload-vPcGu9ot.js";const x=e([t.jsx("circle",{cx:"12",cy:"12",r:"3.2"},"0"),t.jsx("path",{d:"M9 2 7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5"},"1")]),u=({open:e,onClose:u,onSave:m,currentAvatar:j,title:g="上传头像"})=>{const[f,p]=d.useState(null),[v,y]=d.useState(1),w=d.useRef(null),C=d.useRef(null),b=d.useRef(null);d.useEffect((()=>{e&&j&&!f&&p(j)}),[e,j]),d.useEffect((()=>{e||(p(null),y(1))}),[e]);const R=()=>{var e;null==(e=w.current)||e.click()};return t.jsxs(s,{open:e,onClose:u,maxWidth:"sm",fullWidth:!0,children:[t.jsx(r,{children:g}),t.jsx(n,{children:t.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:2},children:[f?t.jsxs(t.Fragment,{children:[t.jsx(i,{sx:{position:"relative",width:200,height:200,overflow:"hidden",borderRadius:"50%"},children:t.jsx("img",{ref:b,src:f,alt:"Avatar preview",style:{width:"100%",height:"100%",objectFit:"cover",transform:`scale(${v})`,transformOrigin:"center"}})}),t.jsxs(i,{sx:{width:"100%",mt:2},children:[t.jsx(a,{gutterBottom:!0,children:"缩放"}),t.jsx(o,{value:v,onChange:(e,t)=>{y(t)},min:1,max:3,step:.1,"aria-labelledby":"zoom-slider"})]}),t.jsx(l,{variant:"outlined",startIcon:t.jsx(h,{}),onClick:R,sx:{mt:1},children:"更换图片"})]}):t.jsx(i,{sx:{width:200,height:200,border:"2px dashed #ccc",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",backgroundColor:"rgba(0,0,0,0.03)"},onClick:R,children:t.jsxs(i,{sx:{textAlign:"center"},children:[t.jsx(x,{sx:{fontSize:48,color:"text.secondary"}}),t.jsx(a,{variant:"body2",color:"text.secondary",children:"点击上传图片"})]})}),t.jsx("input",{type:"file",ref:w,style:{display:"none"},accept:"image/*",onChange:e=>{var t;const s=null==(t=e.target.files)?void 0:t[0];if(s){const e=new FileReader;e.onload=e=>{var t;const s=null==(t=e.target)?void 0:t.result;p(s)},e.readAsDataURL(s)}}}),t.jsx("canvas",{ref:C,style:{display:"none"}})]})}),t.jsxs(c,{children:[t.jsx(l,{onClick:u,children:"取消"}),t.jsx(l,{onClick:()=>{if(!f||!C.current)return;const e=new Image;e.onload=()=>{const t=C.current,s=t.getContext("2d");t.width=200,t.height=200,s.clearRect(0,0,t.width,t.height);const r=Math.min(e.width,e.height),n=(e.width-r)/2,i=(e.height-r)/2;s.drawImage(e,n,i,r,r,0,0,t.width,t.height);const a=t.toDataURL("image/png");m(a),u()},e.src=f},color:"primary",variant:"contained",disabled:!f,children:"保存"})]})]})};export{u as A};
