/**
 * 记忆系统测试工具
 * 用于验证记忆系统的基本功能
 */

import { InternalMemoryService } from './InternalMemoryService';
import { MemoryStorageService } from './MemoryStorageService';
import { MemoryEmbeddingService } from './EmbeddingService';
import { SemanticSearchService } from './SemanticSearchService';
import { ContextBuilder } from './ContextBuilder';
import { MemoryEnhancedMessageService } from './MemoryEnhancedMessageService';
import { MemoryCategory } from '../../types/internalMemory';
import LoggerService from '../LoggerService';

/**
 * 记忆系统测试类
 */
export class MemorySystemTest {
  private memoryService: InternalMemoryService;
  private storageService: MemoryStorageService;
  private embeddingService: MemoryEmbeddingService;
  private searchService: SemanticSearchService;
  private contextBuilder: ContextBuilder;
  private enhancedMessageService: MemoryEnhancedMessageService;

  constructor() {
    this.memoryService = InternalMemoryService.getInstance();
    this.storageService = MemoryStorageService.getInstance();
    this.embeddingService = MemoryEmbeddingService.getInstance();
    this.searchService = SemanticSearchService.getInstance();
    this.contextBuilder = ContextBuilder.getInstance();
    this.enhancedMessageService = MemoryEnhancedMessageService.getInstance();
  }

  /**
   * 运行基础功能测试
   */
  public async runBasicTests(): Promise<{
    success: boolean;
    results: Record<string, boolean>;
    errors: string[];
  }> {
    const results: Record<string, boolean> = {};
    const errors: string[] = [];
    const testUserId = 'test-user-' + Date.now();

    try {
      LoggerService.log('INFO', 'Starting memory system basic tests', { testUserId });

      // 测试1：检查服务初始化
      results.serviceInitialization = this.testServiceInitialization();

      // 测试2：创建和保存记忆
      results.createAndSaveMemory = await this.testCreateAndSaveMemory(testUserId);

      // 测试3：检索记忆
      results.retrieveMemories = await this.testRetrieveMemories(testUserId);

      // 测试4：记忆统计
      results.memoryStats = await this.testMemoryStats(testUserId);

      // 测试5：文本搜索
      results.textSearch = await this.testTextSearch(testUserId);

      // 测试6：清理测试数据
      results.cleanup = await this.testCleanup(testUserId);

      const allPassed = Object.values(results).every(result => result === true);

      LoggerService.log('INFO', 'Memory system basic tests completed', {
        testUserId,
        allPassed,
        results
      });

      return {
        success: allPassed,
        results,
        errors
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);
      LoggerService.log('ERROR', 'Memory system tests failed', { error, testUserId });

      return {
        success: false,
        results,
        errors
      };
    }
  }

  /**
   * 测试服务初始化
   */
  private testServiceInitialization(): boolean {
    try {
      return !!(
        this.memoryService &&
        this.storageService &&
        this.embeddingService &&
        this.searchService &&
        this.contextBuilder &&
        this.enhancedMessageService
      );
    } catch (error) {
      LoggerService.log('ERROR', 'Service initialization test failed', { error });
      return false;
    }
  }

  /**
   * 测试创建和保存记忆
   */
  private async testCreateAndSaveMemory(userId: string): Promise<boolean> {
    try {
      const testMemory = this.memoryService.createMemoryRecord(
        userId,
        '用户喜欢喝咖啡，特别是拿铁',
        MemoryCategory.PREFERENCE,
        8,
        { testData: true }
      );

      await this.memoryService.saveMemory(testMemory);

      // 验证记忆是否保存成功
      const savedMemories = await this.memoryService.getUserMemories(userId);
      return savedMemories.length > 0 && savedMemories.some(m => m.id === testMemory.id);
    } catch (error) {
      LoggerService.log('ERROR', 'Create and save memory test failed', { error, userId });
      return false;
    }
  }

  /**
   * 测试检索记忆
   */
  private async testRetrieveMemories(userId: string): Promise<boolean> {
    try {
      // 添加更多测试记忆
      const testMemories = [
        {
          content: '用户是一名软件工程师',
          category: MemoryCategory.BACKGROUND,
          importance: 7
        },
        {
          content: '用户每天早上跑步',
          category: MemoryCategory.HABIT,
          importance: 6
        }
      ];

      for (const memData of testMemories) {
        const memory = this.memoryService.createMemoryRecord(
          userId,
          memData.content,
          memData.category,
          memData.importance,
          { testData: true }
        );
        await this.memoryService.saveMemory(memory);
      }

      // 测试按分类检索
      const preferenceMemories = await this.memoryService.getMemoriesByCategory(
        userId,
        MemoryCategory.PREFERENCE
      );

      const backgroundMemories = await this.memoryService.getMemoriesByCategory(
        userId,
        MemoryCategory.BACKGROUND
      );

      return preferenceMemories.length > 0 && backgroundMemories.length > 0;
    } catch (error) {
      LoggerService.log('ERROR', 'Retrieve memories test failed', { error, userId });
      return false;
    }
  }

  /**
   * 测试记忆统计
   */
  private async testMemoryStats(userId: string): Promise<boolean> {
    try {
      const stats = await this.memoryService.getMemoryStats(userId);
      
      return !!(
        stats &&
        typeof stats.totalMemories === 'number' &&
        stats.totalMemories > 0 &&
        stats.categoryCounts &&
        typeof stats.averageImportance === 'number'
      );
    } catch (error) {
      LoggerService.log('ERROR', 'Memory stats test failed', { error, userId });
      return false;
    }
  }

  /**
   * 测试文本搜索
   */
  private async testTextSearch(userId: string): Promise<boolean> {
    try {
      const searchResults = await this.storageService.searchMemories(
        userId,
        '咖啡',
        { limit: 5 }
      );

      return searchResults.length > 0;
    } catch (error) {
      LoggerService.log('ERROR', 'Text search test failed', { error, userId });
      return false;
    }
  }

  /**
   * 测试清理
   */
  private async testCleanup(userId: string): Promise<boolean> {
    try {
      const memories = await this.memoryService.getUserMemories(userId);
      
      for (const memory of memories) {
        await this.memoryService.deleteMemory(memory.id);
      }

      const remainingMemories = await this.memoryService.getUserMemories(userId);
      return remainingMemories.length === 0;
    } catch (error) {
      LoggerService.log('ERROR', 'Cleanup test failed', { error, userId });
      return false;
    }
  }

  /**
   * 运行性能测试
   */
  public async runPerformanceTests(): Promise<{
    success: boolean;
    metrics: Record<string, number>;
    errors: string[];
  }> {
    const metrics: Record<string, number> = {};
    const errors: string[] = [];
    const testUserId = 'perf-test-user-' + Date.now();

    try {
      LoggerService.log('INFO', 'Starting memory system performance tests', { testUserId });

      // 测试记忆保存性能
      const saveStartTime = Date.now();
      for (let i = 0; i < 10; i++) {
        const memory = this.memoryService.createMemoryRecord(
          testUserId,
          `测试记忆 ${i}：用户喜欢${i % 2 === 0 ? '咖啡' : '茶'}`,
          MemoryCategory.PREFERENCE,
          5 + (i % 5),
          { testData: true, index: i }
        );
        await this.memoryService.saveMemory(memory);
      }
      metrics.saveTime = Date.now() - saveStartTime;

      // 测试记忆检索性能
      const retrieveStartTime = Date.now();
      const memories = await this.memoryService.getUserMemories(testUserId);
      metrics.retrieveTime = Date.now() - retrieveStartTime;
      metrics.memoriesRetrieved = memories.length;

      // 测试搜索性能
      const searchStartTime = Date.now();
      const searchResults = await this.storageService.searchMemories(
        testUserId,
        '咖啡',
        { limit: 5 }
      );
      metrics.searchTime = Date.now() - searchStartTime;
      metrics.searchResults = searchResults.length;

      // 清理测试数据
      for (const memory of memories) {
        await this.memoryService.deleteMemory(memory.id);
      }

      LoggerService.log('INFO', 'Memory system performance tests completed', {
        testUserId,
        metrics
      });

      return {
        success: true,
        metrics,
        errors
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);
      LoggerService.log('ERROR', 'Memory system performance tests failed', { error, testUserId });

      return {
        success: false,
        metrics,
        errors
      };
    }
  }

  /**
   * 获取系统状态
   */
  public getSystemStatus(): {
    enabled: boolean;
    services: Record<string, boolean>;
    config: any;
  } {
    return {
      enabled: this.memoryService.isEnabled(),
      services: {
        memoryService: !!this.memoryService,
        storageService: !!this.storageService,
        embeddingService: !!this.embeddingService,
        searchService: !!this.searchService,
        contextBuilder: !!this.contextBuilder,
        enhancedMessageService: !!this.enhancedMessageService
      },
      config: this.enhancedMessageService.getMemoryStatus()
    };
  }
}
