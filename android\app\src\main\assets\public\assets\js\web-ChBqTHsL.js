import{W as e}from"./index-BtK6VV6Z.js";import"./mui-vendor-hRDvsX89.js";import"./react-vendor-C9ilihHH.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const{Client:t}=require("@modelcontextprotocol/sdk/client/index.js"),{SSEClientTransport:n}=require("@modelcontextprotocol/sdk/client/sse.js");class o extends e{constructor(){super(),this.sseConnections=new Map,this.wsConnections=new Map,this.mcpClients=new Map,this.mcpTransports=new Map,this.connectionCounter=0,this.proxyServerUrl=null,this.detectProxyServer()}async detectProxyServer(){const e=["http://localhost:3002","http://127.0.0.1:3002","http://localhost:3001","http://127.0.0.1:3001","http://localhost:8080","http://127.0.0.1:8080"];for(const n of e)try{const e=new AbortController,t=setTimeout((()=>e.abort()),1e3),o=await fetch(`${n}/health`,{method:"GET",signal:e.signal});if(clearTimeout(t),o.ok){this.proxyServerUrl=n;break}}catch(t){}this.proxyServerUrl||console.warn("⚠️ No CORS proxy server detected. Some requests may fail due to CORS.")}setProxyServer(e){this.proxyServerUrl=e}async request(e){const{url:t,method:n="GET",headers:o={},data:s,params:r,timeout:i=3e4,responseType:c="json",followRedirects:a=!0}=e;let l=t;if(r){const e=new URLSearchParams(r);l+=(t.includes("?")?"&":"?")+e.toString()}let d=l,h={method:n,headers:o,redirect:a?"follow":"manual"};this.proxyServerUrl&&this.isCrossOrigin(l)&&(d=`${this.proxyServerUrl}/proxy/${encodeURIComponent(l)}`);const u=new AbortController,p=setTimeout((()=>u.abort()),i);h.signal=u.signal;try{s&&["POST","PUT","PATCH"].includes(n)&&("string"==typeof s?h.body=s:(h.body=JSON.stringify(s),o["Content-Type"]||(o["Content-Type"]="application/json")));const e=await fetch(d,h);let t;switch(clearTimeout(p),c){case"text":t=await e.text();break;case"blob":t=await e.blob();break;case"arraybuffer":t=await e.arrayBuffer();break;default:try{t=await e.json()}catch{t=await e.text()}}const r={};return e.headers.forEach(((e,t)=>{r[t]=e})),{status:e.status,statusText:e.statusText,headers:r,data:t,url:e.url}}catch(C){if(clearTimeout(p),this.proxyServerUrl&&d.includes(this.proxyServerUrl))return console.warn(`⚠️ Proxy request failed, trying direct request: ${C}`),this.request({...e,url:l});throw C}}async get(e){return this.request({...e,method:"GET"})}async post(e){return this.request({...e,method:"POST"})}async put(e){return this.request({...e,method:"PUT"})}async patch(e){return this.request({...e,method:"PATCH"})}async delete(e){return this.request({...e,method:"DELETE"})}async startSSE(e){const t="sse_"+ ++this.connectionCounter,{url:n,headers:o={},withCredentials:s=!1,reconnectTimeout:r=3e3}=e;let i=n;this.proxyServerUrl&&this.isCrossOrigin(n)&&(i=`${this.proxyServerUrl}/sse-proxy/${encodeURIComponent(n)}`);const c=new EventSource(i);return this.sseConnections.set(t,c),c.onopen=()=>{this.notifyListeners("sseOpen",{connectionId:t,status:"connected"})},c.onmessage=e=>{this.notifyListeners("sseMessage",{connectionId:t,type:"message",data:e.data,id:e.lastEventId})},c.onerror=()=>{this.notifyListeners("sseError",{connectionId:t,error:"Connection error"})},{connectionId:t}}async stopSSE(e){const{connectionId:t}=e,n=this.sseConnections.get(t);n&&(n.close(),this.sseConnections.delete(t),this.notifyListeners("sseClose",{connectionId:t,status:"disconnected"}))}isCrossOrigin(e){try{const t=new URL(e),n=new URL(window.location.href);return t.origin!==n.origin}catch{return!1}}async createSSEConnection(e){const t="sse_"+ ++this.connectionCounter,{url:n,headers:o={},reconnect:s={}}=e,{enabled:r=!0,initialDelay:i=1e3,maxDelay:c=3e4,maxAttempts:a=10}=s;let l=0,d=i;const h=()=>{let e=n;this.proxyServerUrl&&this.isCrossOrigin(n)&&(e=`${this.proxyServerUrl}/sse-proxy/${encodeURIComponent(n)}`);const o=new EventSource(e);this.sseConnections.set(t,o),o.onopen=()=>{l=0,d=i,this.notifyListeners("sseConnectionChange",{connectionId:t,status:"connected"})},o.onmessage=e=>{this.notifyListeners("sseMessage",{connectionId:t,type:"message",data:e.data,id:e.lastEventId})},o.onerror=()=>{this.notifyListeners("sseConnectionChange",{connectionId:t,status:"error",error:"Connection error"}),r&&l<a?setTimeout((()=>{l++,d=Math.min(2*d,c),o.close(),h()}),d):this.sseConnections.delete(t)},o.addEventListener("error",(e=>{this.notifyListeners("sseMessage",{connectionId:t,type:"error",data:"Connection error"})}))};return this.notifyListeners("sseConnectionChange",{connectionId:t,status:"connecting"}),h(),{connectionId:t,status:"connecting"}}async closeSSEConnection(e){const{connectionId:t}=e,n=this.sseConnections.get(t);n&&(n.close(),this.sseConnections.delete(t),this.notifyListeners("sseConnectionChange",{connectionId:t,status:"disconnected"}))}async createWebSocketConnection(e){const t="ws_"+ ++this.connectionCounter,{url:n,protocols:o,headers:s,timeout:r=1e4}=e;return new Promise(((e,s)=>{const i=new WebSocket(n,o);this.wsConnections.set(t,i);const c=setTimeout((()=>{i.close(),this.wsConnections.delete(t),s(new Error("WebSocket connection timeout"))}),r);i.onopen=()=>{clearTimeout(c),this.notifyListeners("webSocketConnectionChange",{connectionId:t,status:"connected"}),e({connectionId:t,status:"connected"})},i.onmessage=e=>{this.notifyListeners("webSocketMessage",{connectionId:t,data:e.data,type:"string"==typeof e.data?"text":"binary"})},i.onerror=()=>{clearTimeout(c),this.notifyListeners("webSocketConnectionChange",{connectionId:t,status:"error",error:"WebSocket connection error"})},i.onclose=()=>{this.wsConnections.delete(t),this.notifyListeners("webSocketConnectionChange",{connectionId:t,status:"disconnected"})},this.notifyListeners("webSocketConnectionChange",{connectionId:t,status:"connecting"})}))}async closeWebSocketConnection(e){const{connectionId:t}=e,n=this.wsConnections.get(t);n&&(n.close(),this.wsConnections.delete(t))}async sendWebSocketMessage(e){const{connectionId:t,message:n}=e,o=this.wsConnections.get(t);if(!o||o.readyState!==WebSocket.OPEN)throw new Error("WebSocket connection not found or not open");o.send(n)}async createMCPClient(e){var o,s;const r="mcp_"+ ++this.connectionCounter;try{let i;if(this.proxyServerUrl&&this.isCrossOrigin(e.sseUrl)){const t=`${this.proxyServerUrl}/sse-proxy/${encodeURIComponent(e.sseUrl)}`;i=new n(new URL(t))}else i=new n(new URL(e.sseUrl));const c=new t({name:e.clientInfo.name,version:e.clientInfo.version},{capabilities:{roots:(null==(o=e.capabilities)?void 0:o.roots)?{listChanged:!0}:void 0,sampling:(null==(s=e.capabilities)?void 0:s.sampling)?{}:void 0}});return await c.connect(i),this.mcpClients.set(r,c),this.mcpTransports.set(r,i),{connectionId:r,status:"connected",serverCapabilities:c.getServerCapabilities(),protocolVersion:"2025-03-26"}}catch(i){throw console.error("❌ MCP客户端连接失败:",i),new Error(`Failed to create MCP client: ${i}`)}}async listMCPResources(e){const t=this.mcpClients.get(e.connectionId);if(!t)throw new Error("MCP client not found");try{const n=await t.listResources(e.cursor?{cursor:e.cursor}:{});return{resources:n.resources||[],nextCursor:n.nextCursor}}catch(n){throw new Error(`Failed to list MCP resources: ${n}`)}}async readMCPResource(e){var t,n,o,s,r,i;const c=this.mcpClients.get(e.connectionId);if(!c)throw new Error("MCP client not found");try{const a=await c.readResource({uri:e.uri});return{uri:e.uri,mimeType:(null==(n=null==(t=a.contents)?void 0:t[0])?void 0:n.mimeType)||"text/plain",text:(null==(s=null==(o=a.contents)?void 0:o[0])?void 0:s.text)||"",blob:null==(i=null==(r=a.contents)?void 0:r[0])?void 0:i.data}}catch(a){throw new Error(`Failed to read MCP resource: ${a}`)}}async listMCPTools(e){const t=this.mcpClients.get(e.connectionId);if(!t)throw new Error("MCP client not found");try{const n=await t.listTools(e.cursor?{cursor:e.cursor}:{});return{tools:n.tools||[],nextCursor:n.nextCursor}}catch(n){throw new Error(`Failed to list MCP tools: ${n}`)}}async callMCPTool(e){const t=this.mcpClients.get(e.connectionId);if(!t)throw new Error("MCP client not found");try{const n=await t.callTool({name:e.name,arguments:e.arguments||{}});return{content:n.content||[],isError:n.isError||!1}}catch(n){throw new Error(`Failed to call MCP tool: ${n}`)}}async listMCPPrompts(e){const t=this.mcpClients.get(e.connectionId);if(!t)throw new Error("MCP client not found");try{const n=await t.listPrompts(e.cursor?{cursor:e.cursor}:{});return{prompts:n.prompts||[],nextCursor:n.nextCursor}}catch(n){throw new Error(`Failed to list MCP prompts: ${n}`)}}async getMCPPrompt(e){const t=this.mcpClients.get(e.connectionId);if(!t)throw new Error("MCP client not found");try{const n=await t.getPrompt({name:e.name,arguments:e.arguments||{}});return{description:n.description||"",messages:n.messages||[]}}catch(n){throw new Error(`Failed to get MCP prompt: ${n}`)}}async sendMCPSampling(e){throw new Error("MCP sampling not yet implemented with SDK")}}export{o as CorsBypassWeb};
