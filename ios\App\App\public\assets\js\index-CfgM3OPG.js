const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/web-K-0lS3A6.js","assets/js/index-BtK6VV6Z.js","assets/js/mui-vendor-hRDvsX89.js","assets/js/react-vendor-C9ilihHH.js","assets/js/utils-vendor-BDm_82Hk.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/css/index-C_2Yhxqf.css"])))=>i.map(i=>d[i]);
import{W as e,az as t,aw as a}from"./index-BtK6VV6Z.js";import{_ as n}from"./syntax-vendor-DfDNeb5M.js";var r,o,i,s,c,l;(o=r||(r={})).Prompt="PROMPT",o.Camera="CAMERA",o.Photos="PHOTOS",(s=i||(i={})).Rear="REAR",s.Front="FRONT",(l=c||(c={})).Uri="uri",l.Base64="base64",l.DataUrl="dataUrl";class p extends e{async getPhoto(e){return new Promise((async(t,a)=>{if(e.webUseInput||e.source===r.Photos)this.fileInputExperience(e,t,a);else if(e.source===r.Prompt){let n=document.querySelector("pwa-action-sheet");n||(n=document.createElement("pwa-action-sheet"),document.body.appendChild(n)),n.header=e.promptLabelHeader||"Photo",n.cancelable=!1,n.options=[{title:e.promptLabelPhoto||"From Photos"},{title:e.promptLabelPicture||"Take Picture"}],n.addEventListener("onSelection",(async n=>{0===n.detail?this.fileInputExperience(e,t,a):this.cameraExperience(e,t,a)}))}else this.cameraExperience(e,t,a)}))}async pickImages(e){return new Promise((async(e,t)=>{this.multipleFileInputExperience(e,t)}))}async cameraExperience(e,a,n){if(customElements.get("pwa-camera-modal")){const o=document.createElement("pwa-camera-modal");o.facingMode=e.direction===i.Front?"user":"environment",document.body.appendChild(o);try{await o.componentOnReady(),o.addEventListener("onPhoto",(async r=>{const i=r.detail;null===i?n(new t("User cancelled photos app")):i instanceof Error?n(i):a(await this._getCameraPhoto(i,e)),o.dismiss(),document.body.removeChild(o)})),o.present()}catch(r){this.fileInputExperience(e,a,n)}}else console.error("Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements."),this.fileInputExperience(e,a,n)}fileInputExperience(e,a,n){let o=document.querySelector("#_capacitor-camera-input");const s=()=>{var e;null===(e=o.parentNode)||void 0===e||e.removeChild(o)};o||(o=document.createElement("input"),o.id="_capacitor-camera-input",o.type="file",o.hidden=!0,document.body.appendChild(o),o.addEventListener("change",(t=>{const n=o.files[0];let r="jpeg";if("image/png"===n.type?r="png":"image/gif"===n.type&&(r="gif"),"dataUrl"===e.resultType||"base64"===e.resultType){const t=new FileReader;t.addEventListener("load",(()=>{if("dataUrl"===e.resultType)a({dataUrl:t.result,format:r});else if("base64"===e.resultType){const e=t.result.split(",")[1];a({base64String:e,format:r})}s()})),t.readAsDataURL(n)}else a({webPath:URL.createObjectURL(n),format:r}),s()})),o.addEventListener("cancel",(e=>{n(new t("User cancelled photos app")),s()}))),o.accept="image/*",o.capture=!0,e.source===r.Photos||e.source===r.Prompt?o.removeAttribute("capture"):e.direction===i.Front?o.capture="user":e.direction===i.Rear&&(o.capture="environment"),o.click()}multipleFileInputExperience(e,a){let n=document.querySelector("#_capacitor-camera-input-multiple");const r=()=>{var e;null===(e=n.parentNode)||void 0===e||e.removeChild(n)};n||(n=document.createElement("input"),n.id="_capacitor-camera-input-multiple",n.type="file",n.hidden=!0,n.multiple=!0,document.body.appendChild(n),n.addEventListener("change",(t=>{const a=[];for(let e=0;e<n.files.length;e++){const t=n.files[e];let r="jpeg";"image/png"===t.type?r="png":"image/gif"===t.type&&(r="gif"),a.push({webPath:URL.createObjectURL(t),format:r})}e({photos:a}),r()})),n.addEventListener("cancel",(e=>{a(new t("User cancelled photos app")),r()}))),n.accept="image/*",n.click()}_getCameraPhoto(e,t){return new Promise(((a,n)=>{const r=new FileReader,o=e.type.split("/")[1];"uri"===t.resultType?a({webPath:URL.createObjectURL(e),format:o,saved:!1}):(r.readAsDataURL(e),r.onloadend=()=>{const e=r.result;"dataUrl"===t.resultType?a({dataUrl:e,format:o,saved:!1}):a({base64String:e.split(",")[1],format:o,saved:!1})},r.onerror=e=>{n(e)})}))}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(await window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch(e){throw this.unavailable("Camera permissions are not available in this browser")}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}async pickLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}async getLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}}const d=a("Camera",{web:()=>new p}),m=a("Toast",{web:()=>n((()=>import("./web-K-0lS3A6.js")),__vite__mapDeps([0,1,2,3,4,5,6])).then((e=>new e.ToastWeb))});export{d as C,m as T,r as a,c as b};
