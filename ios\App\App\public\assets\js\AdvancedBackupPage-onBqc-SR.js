import{c as e,j as r,B as a,a as o,A as t,T as s,I as i,b as n,E as l,P as c,y as d,D as x,n as h,L as p,w as g,g as b,R as u,m,C as j,a1 as y,x as v,U as f}from"./mui-vendor-DsBXMegs.js";import{u as w,r as C}from"./react-vendor-Be-rfjCm.js";import{A as S,ag as k,ah as A,ai as E,aj as I,a8 as W,ak as D,al as F,am as L}from"./index-Dnlt-eWK.js";import{F as R}from"./Folder-5Ry-HErD.js";import{C as $}from"./CloudUpload-vPcGu9ot.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const B=e(r.jsx("path",{d:"M11 8v3H8v2h3v3h2v-3h3v-2h-3V8zm2-5.95v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.48 2.54l2.6 1.53c.56-1.24.88-2.62.88-4.07 0-5.18-3.95-9.45-9-9.95M12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19"})),T=e(r.jsx("path",{d:"M14 12c0-1.1-.9-2-2-2s-2 .9-2 2 .9 2 2 2 2-.9 2-2m-2-9c-4.97 0-9 4.03-9 9H0l4 4 4-4H5c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.51 0-2.91-.49-4.06-1.3l-1.42 1.44C8.04 20.3 9.94 21 12 21c4.97 0 9-4.03 9-9s-4.03-9-9-9"})),U=()=>{const e=w(),[U,z]=C.useState(!1),[O,P]=C.useState({open:!1,message:"",severity:"info"}),[G,H]=C.useState({includeChats:!0,includeAssistants:!0,includeSettings:!0,includeLocalStorage:!0,includeIndexedDB:!0}),J=(e,r="info")=>{P({open:!0,message:e,severity:r})},M=()=>{P({...O,open:!1})},N=async e=>{try{return await navigator.clipboard.writeText(e),!0}catch(r){console.error("复制到剪贴板失败:",r);try{const r=document.createElement("textarea");r.value=e,r.style.position="fixed",r.style.opacity="0",document.body.appendChild(r),r.select();const a=document.execCommand("copy");return document.body.removeChild(r),a}catch(a){return console.error("备用剪贴板方法也失败:",a),!1}}},_=e=>r=>{H({...G,[e]:r.target.checked})},V=async(e,r)=>{try{const t="Download";try{await I.mkdir({path:t,directory:D.External,recursive:!0})}catch(a){}const s=`${t}/${e}`;await I.writeFile({path:s,data:r,directory:D.External,encoding:W.UTF8});const i=await I.getUri({path:s,directory:D.External});if(i&&i.uri)try{await L.open({filePath:i.uri,contentType:"application/json"});const e=await N(i.uri);J(`备份已保存到下载目录: ${i.uri}${e?" (已复制到剪贴板)":""}`,"success")}catch(o){console.error("打开文件失败，但文件已保存:",o);const e=await N(i.uri);J(`备份已保存到下载目录: ${i.uri}${e?" (已复制到剪贴板)":""}`,"success")}else J("备份已保存到下载目录","success")}catch(t){console.error("保存到下载目录失败:",t);try{await I.writeFile({path:e,data:r,directory:D.External,encoding:W.UTF8});const a=await I.getUri({path:e,directory:D.External});if(a&&a.uri){const e=await N(a.uri);J(`备份已保存到内部存储根目录: ${a.uri}${e?" (已复制到剪贴板)":""}`,"success")}else J("备份已保存到内部存储根目录","success")}catch(s){console.error("保存到内部存储根目录也失败:",s),J("保存备份失败: "+s.message,"error")}}};return r.jsxs(a,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?o(e.palette.primary.main,.02):o(e.palette.background.default,.9)},children:[r.jsx(t,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(s,{children:[r.jsx(i,{edge:"start",onClick:()=>{e("/settings/data")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(S,{})}),r.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"高级备份"})]})}),r.jsx(a,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:r.jsxs(l,{maxWidth:"sm",sx:{my:2},children:[r.jsxs(c,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:3},children:[r.jsx(d,{sx:{width:56,height:56,bgcolor:"#9333EA",fontSize:"1.5rem",mr:2,boxShadow:"0 4px 8px rgba(0,0,0,0.1)"},children:r.jsx($,{})}),r.jsxs(a,{children:[r.jsx(n,{variant:"h6",sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"完整应用备份"}),r.jsx(n,{variant:"body2",color:"text.secondary",children:"自定义备份内容并导出到您选择的位置"})]})]}),r.jsx(x,{sx:{my:2}}),r.jsx(h,{severity:"info",variant:"outlined",sx:{mb:3,borderRadius:2,"& .MuiAlert-icon":{color:"#9333EA"}},children:"高级备份功能允许您选择需要备份的数据类型，并将所有数据保存到自定义位置。 此功能非常适合在应用更新前或跨设备迁移时使用。"}),r.jsx(n,{variant:"subtitle1",sx:{mb:2,fontWeight:600,color:"text.primary"},children:"选择要备份的数据:"}),r.jsxs(p,{sx:{mb:3},children:[r.jsx(c,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",transition:"all 0.2s","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.05)",borderColor:e=>o(e.palette.primary.main,.3)}},children:r.jsx(g,{sx:{p:0},children:r.jsx(b,{control:r.jsx(u,{checked:G.includeChats,onChange:_("includeChats"),color:"primary",sx:{ml:2}}),label:r.jsxs(a,{sx:{py:1},children:[r.jsx(n,{variant:"body1",fontWeight:500,children:"聊天记录"}),r.jsx(n,{variant:"caption",color:"text.secondary",children:"包含所有对话历史和消息"})]}),sx:{width:"100%"}})})}),r.jsx(c,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",transition:"all 0.2s","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.05)",borderColor:e=>o(e.palette.primary.main,.3)}},children:r.jsx(g,{sx:{p:0},children:r.jsx(b,{control:r.jsx(u,{checked:G.includeAssistants,onChange:_("includeAssistants"),color:"primary",sx:{ml:2}}),label:r.jsxs(a,{sx:{py:1},children:[r.jsx(n,{variant:"body1",fontWeight:500,children:"助手数据"}),r.jsx(n,{variant:"caption",color:"text.secondary",children:"包含所有自定义助手及其关联话题"})]}),sx:{width:"100%"}})})}),r.jsx(c,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",transition:"all 0.2s","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.05)",borderColor:e=>o(e.palette.primary.main,.3)}},children:r.jsx(g,{sx:{p:0},children:r.jsx(b,{control:r.jsx(u,{checked:G.includeSettings,onChange:_("includeSettings"),color:"primary",sx:{ml:2}}),label:r.jsxs(a,{sx:{py:1},children:[r.jsx(n,{variant:"body1",fontWeight:500,children:"应用设置"}),r.jsx(n,{variant:"caption",color:"text.secondary",children:"包含主题、模型、API密钥等设置"})]}),sx:{width:"100%"}})})}),r.jsx(c,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",transition:"all 0.2s","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.05)",borderColor:e=>o(e.palette.primary.main,.3)}},children:r.jsx(g,{sx:{p:0},children:r.jsx(b,{control:r.jsx(u,{checked:G.includeLocalStorage,onChange:_("includeLocalStorage"),color:"primary",sx:{ml:2}}),label:r.jsxs(a,{sx:{py:1},children:[r.jsx(n,{variant:"body1",fontWeight:500,children:"本地存储数据"}),r.jsx(n,{variant:"caption",color:"text.secondary",children:"包含所有其他应用数据（偏好设置、历史记录等）"})]}),sx:{width:"100%"}})})})]}),r.jsx(m,{variant:"contained",startIcon:U?r.jsx(j,{size:24,color:"inherit"}):r.jsx(k,{}),onClick:async()=>{try{z(!0);const o={timestamp:Date.now(),appInfo:{version:"1.0.0",name:"AetherLink",backupVersion:3}};if(G.includeChats){const e=await A();o.topics=e}if(G.includeAssistants){const e=await E();o.assistants=e}if(G.includeSettings){const e=localStorage.getItem("settings");o.settings=e?JSON.parse(e):{}}if(G.includeLocalStorage){const r={};for(let a=0;a<localStorage.length;a++){const o=localStorage.key(a);if(o&&"settings"!==o&&!o.startsWith("aetherlink-migration")&&"idb-migration-done"!==o)try{const e=localStorage.getItem(o);if(e)try{r[o]=JSON.parse(e)}catch{r[o]=e}}catch(e){console.error(`读取localStorage项 "${o}" 失败:`,e)}}o.localStorage=r}o.backupSettings={location:localStorage.getItem("backup-location")||"AetherLink/backups",storageType:localStorage.getItem("backup-storage-type")||"documents"};const t=(new Date).toISOString().replace(/[:.]/g,"-"),s=[];G.includeChats&&s.push("Chats"),G.includeAssistants&&s.push("Assistants"),G.includeSettings&&s.push("Settings"),G.includeLocalStorage&&s.push("LocalStorage");const i=`AetherLink_FullBackup_${s.join("_")}_${t}.json`,n=JSON.stringify(o,null,2),l=i;await I.writeFile({path:l,data:n,directory:D.Cache,encoding:W.UTF8});const c=await I.getUri({path:l,directory:D.Cache});if(c&&c.uri)try{await F.share({title:"保存完整应用备份",text:"选择位置保存完整备份文件",url:c.uri,dialogTitle:"选择保存位置"}),J('请在系统分享菜单中选择"保存到设备"或文件管理器应用',"info")}catch(r){console.error("分享文件失败:",r);try{await L.open({filePath:c.uri,contentType:"application/json"}),J('文件已打开，请使用"另存为"保存到您想要的位置',"info")}catch(a){console.error("打开文件失败:",a),await V(i,n)}}else await V(i,n)}catch(o){console.error("创建完整备份失败:",o),J("创建备份失败: "+o.message,"error")}finally{z(!1)}},disabled:U||!G.includeChats&&!G.includeAssistants&&!G.includeSettings&&!G.includeLocalStorage,fullWidth:!0,sx:{py:1.5,borderRadius:2,background:"linear-gradient(90deg, #9333EA, #754AB4)",fontWeight:600,"&:hover":{background:"linear-gradient(90deg, #8324DB, #6D3CAF)"}},children:U?"正在创建备份...":"创建完整备份"})]}),r.jsxs(c,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsx(n,{variant:"h6",sx:{mb:2,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"备份说明"}),r.jsx(x,{sx:{mb:2}}),r.jsxs(p,{disablePadding:!0,children:[r.jsxs(g,{sx:{px:0,py:1.5},children:[r.jsx(y,{children:r.jsx(T,{sx:{color:"#9333EA"}})}),r.jsx(v,{primary:r.jsx(n,{variant:"body1",fontWeight:500,children:"备份将保存为JSON文件"}),secondary:"您可以选择保存位置，便于跨设备迁移"})]}),r.jsxs(g,{sx:{px:0,py:1.5},children:[r.jsx(y,{children:r.jsx(R,{sx:{color:"#9333EA"}})}),r.jsx(v,{primary:r.jsx(n,{variant:"body1",fontWeight:500,children:"推荐保存到云端"}),secondary:"如Google Drive、Dropbox或其他云存储服务"})]}),r.jsxs(g,{sx:{px:0,py:1.5},children:[r.jsx(y,{children:r.jsx(B,{sx:{color:"#9333EA"}})}),r.jsx(v,{primary:r.jsx(n,{variant:"body1",fontWeight:500,children:"定期备份"}),secondary:"建议在重要更新或更改前创建备份"})]})]})]})]})}),r.jsx(f,{open:O.open,autoHideDuration:6e3,onClose:M,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:r.jsx(h,{onClose:M,severity:O.severity,sx:{width:"100%",borderRadius:2,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},variant:"filled",children:O.message})})]})};export{U as default};
