import{c as e,j as s,ae as t,A as a,T as n,I as r,b as l,m as i,B as o,n as c,C as d,P as x,l as h,y as m,D as j,K as u,o as p,p as g,q as f,r as v,k as y,s as w,t as b,aa as k,af as I,L as S,w as C,a1 as z,x as B,J as D,ag as R,ac as E,d as A,g as K,h as G,Z as $,_ as L}from"./mui-vendor-DsBXMegs.js";import{u as N,r as W,a as O,e as P,g as F,h as T}from"./react-vendor-Be-rfjCm.js";import{F as H}from"./Folder-5Ry-HErD.js";import{aX as M,aY as V,aZ as _,a_ as q,a$ as U,b0 as Q,b1 as X,b2 as Y,A as Z,e as J,M as ee,af as se,d as te,aQ as ae,ac as ne}from"./index-Dnlt-eWK.js";import{E as re}from"./Edit-_cexwR-h.js";import{A as le}from"./Add-CgwaVY5w.js";import{V as ie}from"./Visibility-C8zQpFeX.js";import{C as oe}from"./CreateKnowledgeDialog-DNgoj2e_.js";import{C as ce}from"./CloudUpload-vPcGu9ot.js";import{S as de}from"./Search-D7hYSVTA.js";import{C as xe,S as he}from"./Speed-C07AC-nb.js";import{C as me}from"./Clear-DBCbS65T.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";import"./ExpandLess-B3I8kUh8.js";const je=e(s.jsx("path",{d:"M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm7 7V3.5L18.5 9z"})),ue={async createMainTextBlock(e){const s={id:U("block"),messageId:e,type:X.MAIN_TEXT,content:"",createdAt:(new Date).toISOString(),status:Q.PENDING};return V.dispatch(_(s)),await q.blocks.save(s),s},async createThinkingBlock(e){const s={id:U("thinking"),messageId:e,type:X.THINKING,content:"",createdAt:(new Date).toISOString(),status:Q.PENDING};return V.dispatch(_(s)),await q.blocks.save(s),s},async createErrorBlock(e,s){const t={id:U("error"),messageId:e,type:X.ERROR,content:s,createdAt:(new Date).toISOString(),status:Q.ERROR};return V.dispatch(_(t)),await q.blocks.save(t),t},async createCodeBlock(e,s,t){const a={id:U("code"),messageId:e,type:X.CODE,content:s,language:t,createdAt:(new Date).toISOString(),status:Q.SUCCESS};return V.dispatch(_(a)),await q.blocks.save(a),a},async createKnowledgeReferenceBlock(e,s,t,a){const n=M(e,s,t,a);return n.id,V.dispatch(_(n)),await q.blocks.save(n),n},async createKnowledgeReferenceBlockFromSearchResult(e,s,t,a){return this.createKnowledgeReferenceBlock(e,s.content,t,{source:s.metadata.source,similarity:s.similarity,fileName:s.metadata.fileName,fileId:s.metadata.fileId,knowledgeDocumentId:s.documentId,searchQuery:a})}},pe=t(o)((()=>({flexGrow:1,display:"flex",flexDirection:"column",minHeight:"100vh",position:"relative",overflow:"hidden"}))),ge=t(y)((({theme:e})=>({height:"100%",display:"flex",flexDirection:"column",transition:"transform 0.2s, box-shadow 0.2s",cursor:"pointer",borderRadius:2*e.shape.borderRadius,"&:hover":{transform:"translateY(-4px)",boxShadow:e.shadows[4]}}))),fe=()=>{const e=N(),[t,y]=W.useState(!1),[w,b]=W.useState(null),[k,I]=W.useState(!1),[S,C]=W.useState(null),{knowledgeBases:z,isLoading:B,error:D,refreshKnowledgeBases:R}=Y(),E=s=>{e(`/knowledge/${s}`)},A=e=>{C(e||null),I(!0)},K=()=>{I(!1),C(null)},G=()=>{y(!1),b(null)};return s.jsxs(pe,{children:[s.jsx(a,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider"},children:s.jsxs(n,{children:[s.jsx(r,{edge:"start",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:s.jsx(Z,{})}),s.jsx(l,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600},children:"知识库管理"}),s.jsx(i,{variant:"contained",startIcon:s.jsx(le,{}),onClick:()=>A(),sx:{background:"linear-gradient(45deg, #059669 30%, #10b981 90%)","&:hover":{background:"linear-gradient(45deg, #047857 30%, #059669 90%)"}},children:"添加知识库"})]})}),s.jsxs(o,{sx:{flexGrow:1,overflow:"auto",px:2,py:2,mt:8},children:[D&&s.jsx(c,{severity:"error",sx:{mb:3},children:D}),B?s.jsx(o,{display:"flex",justifyContent:"center",my:5,children:s.jsx(d,{})}):0===z.length?s.jsxs(x,{sx:{p:4,textAlign:"center",my:5,borderRadius:2},children:[s.jsx(l,{variant:"body1",color:"textSecondary",gutterBottom:!0,children:"暂无知识库"}),s.jsx(i,{variant:"contained",startIcon:s.jsx(le,{}),onClick:()=>A(),sx:{mt:2,background:"linear-gradient(45deg, #059669 30%, #10b981 90%)","&:hover":{background:"linear-gradient(45deg, #047857 30%, #059669 90%)"}},children:"创建第一个知识库"})]}):s.jsx(o,{sx:{display:"flex",flexWrap:"wrap",margin:-1},children:z.map((e=>{return s.jsx(o,{sx:{width:{xs:"100%",sm:"50%",md:"33.33%"},p:1},children:s.jsxs(ge,{onClick:()=>E(e.id),children:[s.jsxs(h,{sx:{flexGrow:1},children:[s.jsxs(o,{display:"flex",alignItems:"center",mb:1,children:[s.jsx(m,{sx:{mr:1,bgcolor:"primary.main"},children:s.jsx(H,{})}),s.jsx(l,{variant:"h6",noWrap:!0,children:e.name})]}),s.jsx(l,{variant:"body2",color:"text.secondary",sx:{mb:2,height:40,overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:e.description||"无描述"}),s.jsxs(o,{display:"flex",flexWrap:"wrap",gap:.5,children:[s.jsxs(l,{variant:"caption",color:"text.secondary",children:["模型: ",e.model]}),s.jsxs(l,{variant:"caption",color:"text.secondary",children:["• 创建: ",(t=e.created_at,new Date(t).toLocaleDateString())]})]})]}),s.jsx(j,{}),s.jsxs(u,{children:[s.jsx(r,{size:"small",onClick:s=>{s.stopPropagation(),A(e)},children:s.jsx(re,{fontSize:"small"})}),s.jsx(r,{size:"small",onClick:s=>((e,s)=>{s.stopPropagation(),b(e),y(!0)})(e.id,s),children:s.jsx(J,{fontSize:"small"})}),s.jsx(o,{flexGrow:1}),s.jsx(i,{size:"small",variant:"outlined",startIcon:s.jsx(ie,{}),onClick:()=>E(e.id),children:"查看"})]})]})},e.id);var t}))})]}),s.jsx(oe,{open:k,onClose:K,onSave:async s=>{try{if(S)await ee.getInstance().updateKnowledgeBase(S.id,s);else{const t=await ee.getInstance().createKnowledgeBase(s);e(`/knowledge/${t.id}`)}K(),R()}catch(t){console.error("保存知识库失败:",t)}},initialData:S||void 0,isEditing:!!S}),s.jsxs(p,{open:t,onClose:G,children:[s.jsx(g,{children:"确认删除"}),s.jsx(f,{children:s.jsx(l,{children:"确认要删除这个知识库吗？此操作将删除所有相关文档，无法撤销。"})}),s.jsxs(v,{children:[s.jsx(i,{onClick:G,children:"取消"}),s.jsx(i,{onClick:async()=>{if(w)try{await ee.getInstance().deleteKnowledgeBase(w),G(),R()}catch(e){console.error("删除知识库失败:",e)}},color:"error",children:"删除"})]})]})]})},ve=t("input")({clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:1,overflow:"hidden",position:"absolute",bottom:0,left:0,whiteSpace:"nowrap",width:1}),ye=({knowledgeBaseId:e,onDocumentsAdded:t})=>{const[a,n]=W.useState([]),[h,m]=W.useState(!0),[u,y]=W.useState(!1),[A,K]=W.useState(""),[G,$]=W.useState({active:!1,current:0,total:0}),[L,N]=W.useState(!1),[P,F]=W.useState(null),T=W.useRef(null),H=ee.getInstance(),M=async()=>{try{m(!0);const s=await H.getDocumentsByKnowledgeBaseId(e);n(s)}catch(s){console.error("加载文档失败:",s),F("无法加载文档列表，请稍后再试")}finally{m(!1)}};W.useEffect((()=>{e&&M()}),[e]);const V=e=>new Promise(((s,t)=>{const a=new FileReader;a.onload=e=>{var a;(null==(a=e.target)?void 0:a.result)?s(e.target.result):t(new Error("读取文件失败"))},a.onerror=()=>t(new Error("读取文件出错")),a.readAsText(e)})),_=a.filter((e=>{var s;if(!A)return!0;const t=A.toLowerCase();return e.content.toLowerCase().includes(t)||(null==(s=e.metadata.fileName)?void 0:s.toLowerCase().includes(t))||e.metadata.source.toLowerCase().includes(t)}));return s.jsxs(o,{sx:{width:"100%",overflow:"auto",maxHeight:"calc(100vh - 300px)"},children:[s.jsxs(w,{direction:{xs:"column",sm:"row"},spacing:2,mb:2,alignItems:"center",children:[s.jsx(b,{placeholder:"搜索文档...",variant:"outlined",size:"small",fullWidth:!0,value:A,onChange:e=>{K(e.target.value)},InputProps:{startAdornment:s.jsx(k,{position:"start",children:s.jsx(de,{fontSize:"small"})}),endAdornment:A?s.jsx(k,{position:"end",children:s.jsx(r,{onClick:()=>K(""),size:"small","aria-label":"清除搜索",children:s.jsx(se,{fontSize:"small"})})}):null}}),s.jsxs(i,{component:"label",variant:"contained",startIcon:s.jsx(ce,{}),disabled:u,children:[u?"上传中...":"上传文档",s.jsx(ve,{ref:T,type:"file",onChange:async s=>{const a=s.target.files;if(a&&0!==a.length){y(!0),F(null);try{$({active:!0,current:0,total:a.length});for(let s=0;s<a.length;s++){const t=a[s];try{const a=await V(t);$((e=>({...e,current:s}))),await H.addDocument({knowledgeBaseId:e,content:a,metadata:{source:t.name,fileName:t.name}})}catch(n){console.error(`处理文件 ${t.name} 失败:`,n),F(`处理文件 ${t.name} 失败，请检查文件格式`)}}M(),t&&t()}catch(n){console.error("文件上传失败:",n),F("文件上传失败，请稍后再试")}finally{y(!1),$({active:!1,current:0,total:0}),T.current&&(T.current.value="")}}},multiple:!0,accept:".txt,.md,.csv,.json"})]}),a.length>0&&s.jsx(i,{variant:"outlined",color:"error",startIcon:s.jsx(xe,{}),onClick:()=>N(!0),disabled:u||h,children:"一键清理"})]}),P&&s.jsx(c,{severity:"error",sx:{mb:2},onClose:()=>F(null),children:P}),G.active&&s.jsxs(o,{mb:2,children:[s.jsxs(l,{variant:"body2",gutterBottom:!0,children:["处理文件 (",G.current,"/",G.total,")"]}),s.jsx(I,{variant:"determinate",value:G.current/G.total*100})]}),s.jsx(x,{variant:"outlined",children:h?s.jsxs(o,{display:"flex",justifyContent:"center",alignItems:"center",p:4,children:[s.jsx(d,{size:32}),s.jsx(l,{ml:2,variant:"body1",children:"加载文档中..."})]}):_.length>0?s.jsx(S,{children:_.map(((e,t)=>s.jsxs(O.Fragment,{children:[t>0&&s.jsx(j,{}),s.jsxs(C,{children:[s.jsx(z,{children:s.jsx(je,{})}),s.jsx(B,{primary:e.metadata.fileName||"未命名文档",secondary:s.jsxs(o,{component:"span",children:[s.jsxs(l,{sx:{display:"inline"},component:"span",variant:"body2",color:"text.primary",children:[e.content.substring(0,100),e.content.length>100?"...":""]}),s.jsx("br",{}),s.jsxs(o,{mt:.5,children:[s.jsx(D,{label:`块 #${e.metadata.chunkIndex||0}`,size:"small",sx:{mr:.5}}),s.jsx(D,{label:new Date(e.metadata.timestamp).toLocaleDateString(),size:"small",variant:"outlined"})]})]})}),s.jsx(R,{children:s.jsx(r,{edge:"end",onClick:()=>(async e=>{try{await H.deleteDocument(e),M()}catch(s){console.error("删除文档失败:",s),F("删除文档失败，请稍后再试")}})(e.id),"aria-label":"删除",size:"small",children:s.jsx(J,{fontSize:"small"})})})]})]},e.id)))}):s.jsx(o,{p:4,textAlign:"center",children:s.jsx(l,{color:"textSecondary",children:A?"没有找到匹配的文档":"暂无文档，请上传文件或添加URL"})})}),s.jsxs(p,{open:L,onClose:()=>N(!1),children:[s.jsx(g,{children:"确认清理所有文档"}),s.jsx(f,{children:s.jsxs(E,{children:["确定要删除知识库中的所有 ",a.length," 个文档吗？此操作不可撤销。"]})}),s.jsxs(v,{children:[s.jsx(i,{onClick:()=>N(!1),children:"取消"}),s.jsx(i,{onClick:async()=>{try{m(!0);for(const e of a)await H.deleteDocument(e.id);M()}catch(e){console.error("清理文档失败:",e),F("清理文档失败，请稍后再试")}finally{N(!1),m(!1)}},color:"error",children:"清理全部"})]})]})]})},we=({knowledgeBaseId:e,onInsertReference:t})=>{const[a,n]=W.useState(""),[m,j]=W.useState([]),[u,p]=W.useState(!1),[g,f]=W.useState(null),[v,I]=W.useState(.7),[C]=W.useState(5),[z,B]=W.useState(!0),[R,E]=W.useState(null),$=te((e=>e.messages.currentTopicId));W.useEffect((()=>{(async()=>{try{const s=await ee.getInstance().getKnowledgeBase(e);s&&I(s.threshold||.7)}catch(s){console.error("Error fetching knowledge base details:",s)}})()}),[e]);const L=async()=>{if(!a.trim())return void f("请输入搜索内容");p(!0),f(null),E(null);const s=Date.now();try{const t=await ee.getInstance().search({knowledgeBaseId:e,query:a.trim(),threshold:v,limit:C,useEnhancedRAG:z}),n=Date.now();E(n-s),j(t),0===t.length&&f("没有找到匹配的内容")}catch(t){console.error("Search error:",t),f("搜索过程中发生错误")}finally{p(!1)}};return s.jsxs(o,{sx:{width:"100%",overflow:"auto",maxHeight:"calc(100vh - 300px)"},children:[s.jsxs(x,{elevation:0,sx:{p:2,mb:2},children:[s.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[s.jsx(l,{variant:"subtitle1",children:"知识库搜索"}),s.jsx(A,{title:z?"增强RAG搜索：使用查询扩展、混合搜索和重排序":"简单搜索：仅使用向量相似度",children:s.jsx(K,{control:s.jsx(G,{checked:z,onChange:e=>B(e.target.checked),size:"small"}),label:s.jsxs(o,{display:"flex",alignItems:"center",gap:.5,children:[z?s.jsx(ae,{fontSize:"small"}):s.jsx(he,{fontSize:"small"}),s.jsx(l,{variant:"caption",children:z?"增强RAG":"简单搜索"})]})})})]}),s.jsx(b,{fullWidth:!0,variant:"outlined",size:"small",placeholder:"输入搜索内容...",value:a,onChange:e=>n(e.target.value),onKeyDown:e=>{"Enter"===e.key&&L()},slotProps:{input:{startAdornment:s.jsx(k,{position:"start",children:s.jsx(de,{})}),endAdornment:a&&s.jsx(k,{position:"end",children:s.jsx(r,{size:"small",onClick:()=>{n(""),j([]),f(null)},children:s.jsx(me,{})})})}},sx:{mb:1}}),s.jsx(i,{variant:"contained",color:"primary",onClick:L,disabled:u||!a.trim(),fullWidth:!0,children:u?s.jsx(d,{size:24,color:"inherit"}):"搜索"})]}),g&&s.jsx(c,{severity:"info",sx:{mb:2},children:g}),m.length>0&&s.jsxs(o,{children:[s.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1,children:[s.jsxs(l,{variant:"subtitle2",children:["搜索结果 (",m.length,")"]}),s.jsxs(o,{display:"flex",gap:1,children:[R&&s.jsx(D,{label:`${R}ms`,size:"small",variant:"outlined",color:"primary"}),s.jsx(D,{label:z?"增强RAG":"简单搜索",size:"small",color:z?"success":"default",icon:z?s.jsx(ae,{}):s.jsx(he,{})})]})]}),s.jsx(S,{disablePadding:!0,children:m.map((n=>s.jsx(y,{variant:"outlined",sx:{mb:1},children:s.jsx(h,{sx:{p:2,"&:last-child":{pb:2}},children:s.jsxs(w,{spacing:1,children:[s.jsx(l,{variant:"body2",component:"div",sx:{fontSize:"0.875rem",mb:1,maxHeight:100,overflow:"auto"},children:n.content}),s.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[s.jsxs(l,{variant:"caption",color:"text.secondary",children:["相似度: ",(100*n.similarity).toFixed(1),"%"]}),s.jsx(i,{size:"small",variant:"outlined",startIcon:s.jsx(ne,{}),onClick:()=>(async s=>{try{if(!$)return void f("未选择会话");await ue.createKnowledgeReferenceBlockFromSearchResult($,s,e,a),t&&t(s.documentId,s.content)}catch(n){console.error("Error inserting reference:",n),f("插入引用失败")}})(n),children:"插入引用"})]})]})})},n.documentId)))})]})]})},be=t(o)((({theme:e})=>({padding:e.spacing(2),maxWidth:1e3,margin:"0 auto",display:"flex",flexDirection:"column",minHeight:"100vh",position:"relative"})));function ke(e){const{children:t,value:a,index:n,...r}=e;return s.jsx("div",{role:"tabpanel",hidden:a!==n,id:`knowledge-tabpanel-${n}`,"aria-labelledby":`knowledge-tab-${n}`,style:{width:"100%",overflow:"visible"},...r,children:a===n&&s.jsx(o,{sx:{pt:2},children:t})})}const Ie=()=>{const{id:e}=P(),t=N(),[a,n]=W.useState(0),{selectKnowledgeBase:i,selectedKnowledgeBase:c}=Y();W.useEffect((()=>{e&&i(e)}),[e,i]);return e?s.jsxs(be,{children:[s.jsxs(o,{display:"flex",alignItems:"center",mb:2,position:"sticky",top:0,bgcolor:"background.paper",zIndex:1,children:[s.jsx(r,{onClick:()=>{t("/knowledge")},size:"small",sx:{mr:1},children:s.jsx(Z,{})}),s.jsx(l,{variant:"h5",component:"h1",sx:{flexGrow:1},children:(null==c?void 0:c.name)||"知识库详情"})]}),s.jsx(o,{sx:{flexGrow:1,overflow:"auto"},children:c?s.jsxs(s.Fragment,{children:[s.jsxs(x,{elevation:0,sx:{mb:2,p:2},children:[s.jsx(l,{variant:"body1",paragraph:!0,children:c.description||"没有描述"}),s.jsxs(o,{display:"flex",gap:1,flexWrap:"wrap",children:[s.jsx(D,{label:`模型: ${c.model}`,size:"small",color:"primary",variant:"outlined"}),s.jsx(D,{label:`维度: ${c.dimensions}`,size:"small",color:"primary",variant:"outlined"}),s.jsx(D,{label:`块大小: ${c.chunkSize}`,size:"small",color:"primary",variant:"outlined"}),s.jsx(D,{label:`重叠: ${c.chunkOverlap}`,size:"small",color:"primary",variant:"outlined"}),s.jsx(D,{label:`阈值: ${c.threshold}`,size:"small",color:"primary",variant:"outlined"})]})]}),s.jsx(o,{sx:{borderBottom:1,borderColor:"divider"},children:s.jsxs($,{value:a,onChange:(e,s)=>{n(s)},"aria-label":"知识库功能标签",variant:"fullWidth",children:[s.jsx(L,{label:"文档管理"}),s.jsx(L,{label:"知识搜索"})]})}),s.jsx(ke,{value:a,index:0,children:s.jsx(ye,{knowledgeBaseId:e})}),s.jsx(ke,{value:a,index:1,children:s.jsx(we,{knowledgeBaseId:e})})]}):s.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",height:"50vh",children:s.jsx(d,{})})})]}):(t("/knowledge"),null)},Se=()=>s.jsxs(F,{children:[s.jsx(T,{path:"/",element:s.jsx(fe,{})}),s.jsx(T,{path:"/:id",element:s.jsx(Ie,{})})]});export{Se as default};
