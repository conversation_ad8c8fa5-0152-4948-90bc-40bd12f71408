var e=Object.defineProperty,t=(t,n,r)=>((t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r)(t,"symbol"!=typeof n?n+"":n,r);import{l as n}from"./react-vendor-C9ilihHH.js";import{cN as r,cO as s,cP as o,cQ as a,cR as i,cS as l,cT as u,cU as c,cV as p,cW as d,cX as m,cY as g,cZ as h,c_ as f,c$ as y,d0 as v,d1 as b,d2 as w,d3 as _,d4 as x,d5 as k,d6 as T,d7 as S,d8 as I,d9 as E,da as R,b9 as C,bq as A,br as N,bw as P,db as M}from"./index-BtK6VV6Z.js";import"./mui-vendor-hRDvsX89.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";var O,j="vercel.ai.error",D=Symbol.for(j),$=class e extends Error{constructor({name:e,message:t,cause:n}){super(t),this[O]=!0,this.name=e,this.cause=n}static isInstance(t){return e.hasMarker(t,j)}static hasMarker(e,t){const n=Symbol.for(t);return null!=e&&"object"==typeof e&&n in e&&"boolean"==typeof e[n]&&!0===e[n]}};O=D;var q,F=$,L="AI_APICallError",U=`vercel.ai.error.${L}`,B=Symbol.for(U),V=class extends F{constructor({message:e,url:t,requestBodyValues:n,statusCode:r,responseHeaders:s,responseBody:o,cause:a,isRetryable:i=null!=r&&(408===r||409===r||429===r||r>=500),data:l}){super({name:L,message:e,cause:a}),this[q]=!0,this.url=t,this.requestBodyValues=n,this.statusCode=r,this.responseHeaders=s,this.responseBody=o,this.isRetryable=i,this.data=l}static isInstance(e){return F.hasMarker(e,U)}};q=B;var Z,J="AI_EmptyResponseBodyError",H=`vercel.ai.error.${J}`,W=Symbol.for(H),z=class extends F{constructor({message:e="Empty response body"}={}){super({name:J,message:e}),this[Z]=!0}static isInstance(e){return F.hasMarker(e,H)}};function K(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}Z=W;var G,Y="AI_InvalidArgumentError",X=`vercel.ai.error.${Y}`,Q=Symbol.for(X),ee=class extends F{constructor({message:e,cause:t,argument:n}){super({name:Y,message:e,cause:t}),this[G]=!0,this.argument=n}static isInstance(e){return F.hasMarker(e,X)}};G=Q;var te,ne="AI_InvalidPromptError",re=`vercel.ai.error.${ne}`,se=Symbol.for(re),oe=class extends F{constructor({prompt:e,message:t,cause:n}){super({name:ne,message:`Invalid prompt: ${t}`,cause:n}),this[te]=!0,this.prompt=e}static isInstance(e){return F.hasMarker(e,re)}};te=se;var ae,ie="AI_InvalidResponseDataError",le=`vercel.ai.error.${ie}`,ue=Symbol.for(le),ce=class extends F{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:ie,message:t}),this[ae]=!0,this.data=e}static isInstance(e){return F.hasMarker(e,le)}};ae=ue;var pe,de="AI_JSONParseError",me=`vercel.ai.error.${de}`,ge=Symbol.for(me),he=class extends F{constructor({text:e,cause:t}){super({name:de,message:`JSON parsing failed: Text: ${e}.\nError message: ${K(t)}`,cause:t}),this[pe]=!0,this.text=e}static isInstance(e){return F.hasMarker(e,me)}};pe=ge;var fe,ye="AI_LoadAPIKeyError",ve=`vercel.ai.error.${ye}`,be=Symbol.for(ve),we=class extends F{constructor({message:e}){super({name:ye,message:e}),this[fe]=!0}static isInstance(e){return F.hasMarker(e,ve)}};fe=be;var _e,xe="AI_TooManyEmbeddingValuesForCallError",ke=`vercel.ai.error.${xe}`,Te=Symbol.for(ke),Se=class extends F{constructor(e){super({name:xe,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[_e]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return F.hasMarker(e,ke)}};_e=Te;var Ie,Ee="AI_TypeValidationError",Re=`vercel.ai.error.${Ee}`,Ce=Symbol.for(Re);Ie=Ce;var Ae,Ne=class e extends F{constructor({value:e,cause:t}){super({name:Ee,message:`Type validation failed: Value: ${JSON.stringify(e)}.\nError message: ${K(t)}`,cause:t}),this[Ie]=!0,this.value=e}static isInstance(e){return F.hasMarker(e,Re)}static wrap({value:t,cause:n}){return e.isInstance(n)&&n.value===t?n:new e({value:t,cause:n})}},Pe="AI_UnsupportedFunctionalityError",Me=`vercel.ai.error.${Pe}`,Oe=Symbol.for(Me),je=class extends F{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:Pe,message:t}),this[Ae]=!0,this.functionality=e}static isInstance(e){return F.hasMarker(e,Me)}};Ae=Oe;var De,$e={exports:{}};const qe=n(function(){if(De)return $e.exports;De=1;const e="undefined"!=typeof Buffer,t=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,n=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function r(r,o,a){null==a&&null!==o&&"object"==typeof o&&(a=o,o=void 0),e&&Buffer.isBuffer(r)&&(r=r.toString()),r&&65279===r.charCodeAt(0)&&(r=r.slice(1));const i=JSON.parse(r,o);if(null===i||"object"!=typeof i)return i;const l=a&&a.protoAction||"error",u=a&&a.constructorAction||"error";if("ignore"===l&&"ignore"===u)return i;if("ignore"!==l&&"ignore"!==u){if(!1===t.test(r)&&!1===n.test(r))return i}else if("ignore"!==l&&"ignore"===u){if(!1===t.test(r))return i}else if(!1===n.test(r))return i;return s(i,{protoAction:l,constructorAction:u,safe:a&&a.safe})}function s(e,{protoAction:t="error",constructorAction:n="error",safe:r}={}){let s=[e];for(;s.length;){const e=s;s=[];for(const o of e){if("ignore"!==t&&Object.prototype.hasOwnProperty.call(o,"__proto__")){if(!0===r)return null;if("error"===t)throw new SyntaxError("Object contains forbidden prototype property");delete o.__proto__}if("ignore"!==n&&Object.prototype.hasOwnProperty.call(o,"constructor")&&Object.prototype.hasOwnProperty.call(o.constructor,"prototype")){if(!0===r)return null;if("error"===n)throw new SyntaxError("Object contains forbidden prototype property");delete o.constructor}for(const e in o){const t=o[e];t&&"object"==typeof t&&s.push(t)}}}return e}function o(e,t,n){const s=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return r(e,t,n)}finally{Error.stackTraceLimit=s}}return $e.exports=o,$e.exports.default=o,$e.exports.parse=o,$e.exports.safeParse=function(e,t){const n=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return r(e,t,{safe:!0})}catch(s){return null}finally{Error.stackTraceLimit=n}},$e.exports.scan=s,$e.exports}());var Fe={};function Le(...e){return e.reduce(((e,t)=>({...e,...null!=t?t:{}})),{})}function Ue(){let e,t,n,r="",s=[];function o(e,t){if(""===e)return void a(t);if(e.startsWith(":"))return;const n=e.indexOf(":");if(-1===n)return void i(e,"");const r=n+1;i(e.slice(0,n),r<e.length&&" "===e[r]?e.slice(r+1):e.slice(r))}function a(r){s.length>0&&(r.enqueue({event:e,data:s.join("\n"),id:t,retry:n}),s=[],e=void 0,n=void 0)}function i(r,o){switch(r){case"event":e=o;break;case"data":s.push(o);break;case"id":t=o;break;case"retry":const r=parseInt(o,10);isNaN(r)||(n=r)}}return new TransformStream({transform(e,t){const{lines:n,incompleteLine:s}=function(e,t){const n=[];let r=e;for(let s=0;s<t.length;){const e=t[s++];"\n"===e?(n.push(r),r=""):"\r"===e?(n.push(r),r="","\n"===t[s]&&s++):r+=e}return{lines:n,incompleteLine:r}}(r,e);r=s;for(let r=0;r<n.length;r++)o(n[r],t)},flush(e){o(r,e),a(e)}})}function Be(e){const t={};return e.headers.forEach(((e,n)=>{t[n]=e})),t}var Ve=({prefix:e,size:t=16,alphabet:n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:r="-"}={})=>{const s=((e,t=21)=>(n=t)=>{let r="",s=0|n;for(;s--;)r+=e[Math.random()*e.length|0];return r})(n,t);if(null==e)return s;if(n.includes(r))throw new ee({argument:"separator",message:`The separator "${r}" must not be part of the alphabet "${n}".`});return t=>`${e}${r}${s(t)}`},Ze=Ve();function Je(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}function He({apiKey:e,environmentVariableName:t,apiKeyParameterName:n="apiKey",description:r}){if("string"==typeof e)return e;if(null!=e)throw new we({message:`${r} API key must be a string.`});if("undefined"==typeof process)throw new we({message:`${r} API key is missing. Pass it using the '${n}' parameter. Environment variables is not supported in this environment.`});if(null==(e=Fe[t]))throw new we({message:`${r} API key is missing. Pass it using the '${n}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new we({message:`${r} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}var We=Symbol.for("vercel.ai.validator");function ze(e){return function(e){return"object"==typeof e&&null!==e&&We in e&&!0===e[We]&&"validate"in e}(e)?e:(t=e,n=e=>{const n=t.safeParse(e);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}},{[We]:!0,validate:n});var t,n}function Ke({value:e,schema:t}){const n=ze(t);try{if(null==n.validate)return{success:!0,value:e};const t=n.validate(e);return t.success?t:{success:!1,error:Ne.wrap({value:e,cause:t.error})}}catch(r){return{success:!1,error:Ne.wrap({value:e,cause:r})}}}function Ge({text:e,schema:t}){try{const n=qe.parse(e);return null==t?n:function({value:e,schema:t}){const n=Ke({value:e,schema:t});if(!n.success)throw Ne.wrap({value:e,cause:n.error});return n.value}({value:n,schema:t})}catch(n){if(he.isInstance(n)||Ne.isInstance(n))throw n;throw new he({text:e,cause:n})}}function Ye({text:e,schema:t}){try{const n=qe.parse(e);if(null==t)return{success:!0,value:n,rawValue:n};const r=Ke({value:n,schema:t});return r.success?{...r,rawValue:n}:r}catch(n){return{success:!1,error:he.isInstance(n)?n:new he({text:e,cause:n})}}}function Xe(e){try{return qe.parse(e),!0}catch(t){return!1}}function Qe({provider:e,providerOptions:t,schema:n}){if(null==(null==t?void 0:t[e]))return;const r=Ke({value:t[e],schema:n});if(!r.success)throw new ee({argument:"providerOptions",message:`invalid ${e} provider options`,cause:r.error});return r.value}var et=()=>globalThis.fetch,tt=async({url:e,headers:t,body:n,failedResponseHandler:r,successfulResponseHandler:s,abortSignal:o,fetch:a})=>nt({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(n),values:n},failedResponseHandler:r,successfulResponseHandler:s,abortSignal:o,fetch:a}),nt=async({url:e,headers:t={},body:n,successfulResponseHandler:r,failedResponseHandler:s,abortSignal:o,fetch:a=et()})=>{try{const u=await a(e,{method:"POST",headers:(i=t,Object.fromEntries(Object.entries(i).filter((([e,t])=>null!=t)))),body:n.content,signal:o}),c=Be(u);if(!u.ok){let t;try{t=await s({response:u,url:e,requestBodyValues:n.values})}catch(l){if(Je(l)||V.isInstance(l))throw l;throw new V({message:"Failed to process error response",cause:l,statusCode:u.status,url:e,responseHeaders:c,requestBodyValues:n.values})}throw t.value}try{return await r({response:u,url:e,requestBodyValues:n.values})}catch(l){if(l instanceof Error&&(Je(l)||V.isInstance(l)))throw l;throw new V({message:"Failed to process successful response",cause:l,statusCode:u.status,url:e,responseHeaders:c,requestBodyValues:n.values})}}catch(l){if(Je(l))throw l;if(l instanceof TypeError&&"fetch failed"===l.message){const t=l.cause;if(null!=t)throw new V({message:`Cannot connect to API: ${t.message}`,cause:t,url:e,requestBodyValues:n.values,isRetryable:!0})}throw l}var i},rt=e=>async({response:t})=>{const n=Be(t);if(null==t.body)throw new z({});return{responseHeaders:n,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(Ue()).pipeThrough(new TransformStream({transform({data:t},n){"[DONE]"!==t&&n.enqueue(Ye({text:t,schema:e}))}}))}},st=e=>async({response:t,url:n,requestBodyValues:r})=>{const s=await t.text(),o=Ye({text:s,schema:e}),a=Be(t);if(!o.success)throw new V({message:"Invalid JSON response",cause:o.error,statusCode:t.status,responseHeaders:a,responseBody:s,url:n,requestBodyValues:r});return{responseHeaders:a,value:o.value,rawValue:o.rawValue}},{btoa:ot,atob:at}=globalThis;function it(e){const t=e.replace(/-/g,"+").replace(/_/g,"/"),n=at(t);return Uint8Array.from(n,(e=>e.codePointAt(0)))}function lt(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCodePoint(e[n]);return ot(t)}function ut(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.content)?void 0:t.map((({token:e,logprob:t,top_logprobs:n})=>({token:e,logprob:t,topLogprobs:n?n.map((({token:e,logprob:t})=>({token:e,logprob:t}))):[]}))))?n:void 0}function ct(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var pt=r({error:r({message:o(),type:o().nullish(),param:p().nullish(),code:d([o(),a()]).nullish()})}),dt=(({errorSchema:e,errorToMessage:t,isRetryable:n})=>async({response:r,url:s,requestBodyValues:o})=>{const a=await r.text(),i=Be(r);if(""===a.trim())return{responseHeaders:i,value:new V({message:r.statusText,url:s,requestBodyValues:o,statusCode:r.status,responseHeaders:i,responseBody:a,isRetryable:null==n?void 0:n(r)})};try{const l=Ge({text:a,schema:e});return{responseHeaders:i,value:new V({message:t(l),url:s,requestBodyValues:o,statusCode:r.status,responseHeaders:i,responseBody:a,data:l,isRetryable:null==n?void 0:n(r,l)})}}catch(l){return{responseHeaders:i,value:new V({message:r.statusText,url:s,requestBodyValues:o,statusCode:r.status,responseHeaders:i,responseBody:a,isRetryable:null==n?void 0:n(r)})}}})({errorSchema:pt,errorToMessage:e=>e.error.message});function mt({id:e,model:t,created:n}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=n?new Date(1e3*n):void 0}}var gt=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get supportsStructuredOutputs(){var e;return null!=(e=this.settings.structuredOutputs)?e:vt(this.modelId)}get defaultObjectGenerationMode(){return this.modelId.startsWith("gpt-4o-audio-preview")?"tool":this.supportsStructuredOutputs?"json":"tool"}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:n,temperature:r,topP:s,topK:o,frequencyPenalty:a,presencePenalty:i,stopSequences:l,responseFormat:u,seed:c,providerMetadata:p}){var d,m,g,h,f,y,v,b;const w=e.type,_=[];null!=o&&_.push({type:"unsupported-setting",setting:"topK"}),"json"!==(null==u?void 0:u.type)||null==u.schema||this.supportsStructuredOutputs||_.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});const x=this.settings.useLegacyFunctionCalling;if(x&&!0===this.settings.parallelToolCalls)throw new je({functionality:"useLegacyFunctionCalling with parallelToolCalls"});if(x&&this.supportsStructuredOutputs)throw new je({functionality:"structuredOutputs with useLegacyFunctionCalling"});const{messages:k,warnings:T}=function({prompt:e,useLegacyFunctionCalling:t=!1,systemMessageMode:n="system"}){const r=[],s=[];for(const{role:o,content:a}of e)switch(o){case"system":switch(n){case"system":r.push({role:"system",content:a});break;case"developer":r.push({role:"developer",content:a});break;case"remove":s.push({type:"other",message:"system messages are removed for this model"});break;default:throw new Error(`Unsupported system message mode: ${n}`)}break;case"user":if(1===a.length&&"text"===a[0].type){r.push({role:"user",content:a[0].text});break}r.push({role:"user",content:a.map(((e,t)=>{var n,r,s,o;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(n=e.mimeType)?n:"image/jpeg"};base64,${lt(e.image)}`,detail:null==(s=null==(r=e.providerMetadata)?void 0:r.openai)?void 0:s.imageDetail}};case"file":if(e.data instanceof URL)throw new je({functionality:"'File content parts with URL data' functionality not supported."});switch(e.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:e.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:e.data,format:"mp3"}};case"application/pdf":return{type:"file",file:{filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new je({functionality:`File content part type ${e.mimeType} in user messages`})}}}))});break;case"assistant":{let e="";const n=[];for(const t of a)switch(t.type){case"text":e+=t.text;break;case"tool-call":n.push({id:t.toolCallId,type:"function",function:{name:t.toolName,arguments:JSON.stringify(t.args)}})}if(t){if(n.length>1)throw new je({functionality:"useLegacyFunctionCalling with multiple tool calls in one message"});r.push({role:"assistant",content:e,function_call:n.length>0?n[0].function:void 0})}else r.push({role:"assistant",content:e,tool_calls:n.length>0?n:void 0});break}case"tool":for(const e of a)t?r.push({role:"function",name:e.toolName,content:JSON.stringify(e.result)}):r.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:throw new Error(`Unsupported role: ${o}`)}return{messages:r,warnings:s}}({prompt:t,useLegacyFunctionCalling:x,systemMessageMode:bt(this.modelId)});_.push(...T);const S={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:n,temperature:r,top_p:s,frequency_penalty:a,presence_penalty:i,response_format:"json"===(null==u?void 0:u.type)?this.supportsStructuredOutputs&&null!=u.schema?{type:"json_schema",json_schema:{schema:u.schema,strict:!0,name:null!=(d=u.name)?d:"response",description:u.description}}:{type:"json_object"}:void 0,stop:l,seed:c,max_completion_tokens:null==(m=null==p?void 0:p.openai)?void 0:m.maxCompletionTokens,store:null==(g=null==p?void 0:p.openai)?void 0:g.store,metadata:null==(h=null==p?void 0:p.openai)?void 0:h.metadata,prediction:null==(f=null==p?void 0:p.openai)?void 0:f.prediction,reasoning_effort:null!=(v=null==(y=null==p?void 0:p.openai)?void 0:y.reasoningEffort)?v:this.settings.reasoningEffort,messages:k};switch(vt(this.modelId)?(null!=S.temperature&&(S.temperature=void 0,_.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=S.top_p&&(S.top_p=void 0,_.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"})),null!=S.frequency_penalty&&(S.frequency_penalty=void 0,_.push({type:"unsupported-setting",setting:"frequencyPenalty",details:"frequencyPenalty is not supported for reasoning models"})),null!=S.presence_penalty&&(S.presence_penalty=void 0,_.push({type:"unsupported-setting",setting:"presencePenalty",details:"presencePenalty is not supported for reasoning models"})),null!=S.logit_bias&&(S.logit_bias=void 0,_.push({type:"other",message:"logitBias is not supported for reasoning models"})),null!=S.logprobs&&(S.logprobs=void 0,_.push({type:"other",message:"logprobs is not supported for reasoning models"})),null!=S.top_logprobs&&(S.top_logprobs=void 0,_.push({type:"other",message:"topLogprobs is not supported for reasoning models"})),null!=S.max_tokens&&(null==S.max_completion_tokens&&(S.max_completion_tokens=S.max_tokens),S.max_tokens=void 0)):(this.modelId.startsWith("gpt-4o-search-preview")||this.modelId.startsWith("gpt-4o-mini-search-preview"))&&null!=S.temperature&&(S.temperature=void 0,_.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for the search preview models and has been removed."})),w){case"regular":{const{tools:t,tool_choice:n,functions:r,function_call:s,toolWarnings:o}=function({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:n}){var r;const s=(null==(r=e.tools)?void 0:r.length)?e.tools:void 0,o=[];if(null==s)return{tools:void 0,tool_choice:void 0,toolWarnings:o};const a=e.toolChoice;if(t){const e=[];for(const t of s)"provider-defined"===t.type?o.push({type:"unsupported-tool",tool:t}):e.push({name:t.name,description:t.description,parameters:t.parameters});if(null==a)return{functions:e,function_call:void 0,toolWarnings:o};switch(a.type){case"auto":case"none":case void 0:return{functions:e,function_call:void 0,toolWarnings:o};case"required":throw new je({functionality:"useLegacyFunctionCalling and toolChoice: required"});default:return{functions:e,function_call:{name:a.toolName},toolWarnings:o}}}const i=[];for(const u of s)"provider-defined"===u.type?o.push({type:"unsupported-tool",tool:u}):i.push({type:"function",function:{name:u.name,description:u.description,parameters:u.parameters,strict:!!n||void 0}});if(null==a)return{tools:i,tool_choice:void 0,toolWarnings:o};const l=a.type;switch(l){case"auto":case"none":case"required":return{tools:i,tool_choice:l,toolWarnings:o};case"tool":return{tools:i,tool_choice:{type:"function",function:{name:a.toolName}},toolWarnings:o};default:throw new je({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,useLegacyFunctionCalling:x,structuredOutputs:this.supportsStructuredOutputs});return{args:{...S,tools:t,tool_choice:n,functions:r,function_call:s},warnings:[..._,...o]}}case"object-json":return{args:{...S,response_format:this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,strict:!0,name:null!=(b=e.name)?b:"response",description:e.description}}:{type:"json_object"}},warnings:_};case"object-tool":return{args:x?{...S,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...S,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:!!this.supportsStructuredOutputs||void 0}}]},warnings:_};default:throw new Error(`Unsupported type: ${w}`)}}async doGenerate(e){var t,n,r,s,o,a,i,l;const{args:u,warnings:c}=this.getArgs(e),{responseHeaders:p,value:d,rawValue:m}=await tt({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:u,failedResponseHandler:dt,successfulResponseHandler:st(ft),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:g,...h}=u,f=d.choices[0],y=null==(t=d.usage)?void 0:t.completion_tokens_details,v=null==(n=d.usage)?void 0:n.prompt_tokens_details,b={openai:{}};return null!=(null==y?void 0:y.reasoning_tokens)&&(b.openai.reasoningTokens=null==y?void 0:y.reasoning_tokens),null!=(null==y?void 0:y.accepted_prediction_tokens)&&(b.openai.acceptedPredictionTokens=null==y?void 0:y.accepted_prediction_tokens),null!=(null==y?void 0:y.rejected_prediction_tokens)&&(b.openai.rejectedPredictionTokens=null==y?void 0:y.rejected_prediction_tokens),null!=(null==v?void 0:v.cached_tokens)&&(b.openai.cachedPromptTokens=null==v?void 0:v.cached_tokens),{text:null!=(r=f.message.content)?r:void 0,toolCalls:this.settings.useLegacyFunctionCalling&&f.message.function_call?[{toolCallType:"function",toolCallId:Ze(),toolName:f.message.function_call.name,args:f.message.function_call.arguments}]:null==(s=f.message.tool_calls)?void 0:s.map((e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:Ze(),toolName:e.function.name,args:e.function.arguments}})),finishReason:ct(f.finish_reason),usage:{promptTokens:null!=(a=null==(o=d.usage)?void 0:o.prompt_tokens)?a:NaN,completionTokens:null!=(l=null==(i=d.usage)?void 0:i.completion_tokens)?l:NaN},rawCall:{rawPrompt:g,rawSettings:h},rawResponse:{headers:p,body:m},request:{body:JSON.stringify(u)},response:mt(d),warnings:c,logprobs:ut(f.logprobs),providerMetadata:b}}async doStream(e){if(this.settings.simulateStreaming){const t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(const n of t.toolCalls)e.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:n.toolCallId,toolName:n.toolName,argsTextDelta:n.args}),e.enqueue({type:"tool-call",...n});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}const{args:t,warnings:n}=this.getArgs(e),r={...t,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:s,value:o}=await tt({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:r,failedResponseHandler:dt,successfulResponseHandler:rt(yt),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:a,...i}=t,l=[];let u,c="unknown",p={promptTokens:void 0,completionTokens:void 0},d=!0;const{useLegacyFunctionCalling:m}=this.settings,g={openai:{}};return{stream:o.pipeThrough(new TransformStream({transform(e,t){var n,r,s,o,a,i,h,f,y,v,b,w;if(!e.success)return c="error",void t.enqueue({type:"error",error:e.error});const _=e.value;if("error"in _)return c="error",void t.enqueue({type:"error",error:_.error});if(d&&(d=!1,t.enqueue({type:"response-metadata",...mt(_)})),null!=_.usage){const{prompt_tokens:e,completion_tokens:t,prompt_tokens_details:n,completion_tokens_details:r}=_.usage;p={promptTokens:null!=e?e:void 0,completionTokens:null!=t?t:void 0},null!=(null==r?void 0:r.reasoning_tokens)&&(g.openai.reasoningTokens=null==r?void 0:r.reasoning_tokens),null!=(null==r?void 0:r.accepted_prediction_tokens)&&(g.openai.acceptedPredictionTokens=null==r?void 0:r.accepted_prediction_tokens),null!=(null==r?void 0:r.rejected_prediction_tokens)&&(g.openai.rejectedPredictionTokens=null==r?void 0:r.rejected_prediction_tokens),null!=(null==n?void 0:n.cached_tokens)&&(g.openai.cachedPromptTokens=null==n?void 0:n.cached_tokens)}const x=_.choices[0];if(null!=(null==x?void 0:x.finish_reason)&&(c=ct(x.finish_reason)),null==(null==x?void 0:x.delta))return;const k=x.delta;null!=k.content&&t.enqueue({type:"text-delta",textDelta:k.content});const T=ut(null==x?void 0:x.logprobs);(null==T?void 0:T.length)&&(void 0===u&&(u=[]),u.push(...T));const S=m&&null!=k.function_call?[{type:"function",id:Ze(),function:k.function_call,index:0}]:k.tool_calls;if(null!=S)for(const u of S){const e=u.index;if(null==l[e]){if("function"!==u.type)throw new ce({data:u,message:"Expected 'function' type."});if(null==u.id)throw new ce({data:u,message:"Expected 'id' to be a string."});if(null==(null==(n=u.function)?void 0:n.name))throw new ce({data:u,message:"Expected 'function.name' to be a string."});l[e]={id:u.id,type:"function",function:{name:u.function.name,arguments:null!=(r=u.function.arguments)?r:""},hasFinished:!1};const i=l[e];null!=(null==(s=i.function)?void 0:s.name)&&null!=(null==(o=i.function)?void 0:o.arguments)&&(i.function.arguments.length>0&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:i.function.arguments}),Xe(i.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(a=i.id)?a:Ze(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0));continue}const c=l[e];c.hasFinished||(null!=(null==(i=u.function)?void 0:i.arguments)&&(c.function.arguments+=null!=(f=null==(h=u.function)?void 0:h.arguments)?f:""),t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:c.id,toolName:c.function.name,argsTextDelta:null!=(y=u.function.arguments)?y:""}),null!=(null==(v=c.function)?void 0:v.name)&&null!=(null==(b=c.function)?void 0:b.arguments)&&Xe(c.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(w=c.id)?w:Ze(),toolName:c.function.name,args:c.function.arguments}),c.hasFinished=!0))}},flush(e){var t,n;e.enqueue({type:"finish",finishReason:c,logprobs:u,usage:{promptTokens:null!=(t=p.promptTokens)?t:NaN,completionTokens:null!=(n=p.completionTokens)?n:NaN},...null!=g?{providerMetadata:g}:{}})}})),rawCall:{rawPrompt:a,rawSettings:i},rawResponse:{headers:s},request:{body:JSON.stringify(r)},warnings:n}}},ht=r({prompt_tokens:a().nullish(),completion_tokens:a().nullish(),prompt_tokens_details:r({cached_tokens:a().nullish()}).nullish(),completion_tokens_details:r({reasoning_tokens:a().nullish(),accepted_prediction_tokens:a().nullish(),rejected_prediction_tokens:a().nullish()}).nullish()}).nullish(),ft=r({id:o().nullish(),created:a().nullish(),model:o().nullish(),choices:s(r({message:r({role:l("assistant").nullish(),content:o().nullish(),function_call:r({arguments:o(),name:o()}).nullish(),tool_calls:s(r({id:o().nullish(),type:l("function"),function:r({name:o(),arguments:o()})})).nullish()}),index:a(),logprobs:r({content:s(r({token:o(),logprob:a(),top_logprobs:s(r({token:o(),logprob:a()}))})).nullable()}).nullish(),finish_reason:o().nullish()})),usage:ht}),yt=d([r({id:o().nullish(),created:a().nullish(),model:o().nullish(),choices:s(r({delta:r({role:u(["assistant"]).nullish(),content:o().nullish(),function_call:r({name:o().optional(),arguments:o().optional()}).nullish(),tool_calls:s(r({index:a(),id:o().nullish(),type:l("function").nullish(),function:r({name:o().nullish(),arguments:o().nullish()})})).nullish()}).nullish(),logprobs:r({content:s(r({token:o(),logprob:a(),top_logprobs:s(r({token:o(),logprob:a()}))})).nullable()}).nullish(),finish_reason:o().nullish(),index:a()})),usage:ht}),pt]);function vt(e){return e.startsWith("o")}function bt(e){var t,n;return vt(e)?null!=(n=null==(t=wt[e])?void 0:t.systemMessageMode)?n:"developer":"system"}var wt={"o1-mini":{systemMessageMode:"remove"},"o1-mini-2024-09-12":{systemMessageMode:"remove"},"o1-preview":{systemMessageMode:"remove"},"o1-preview-2024-09-12":{systemMessageMode:"remove"},o3:{systemMessageMode:"developer"},"o3-2025-04-16":{systemMessageMode:"developer"},"o3-mini":{systemMessageMode:"developer"},"o3-mini-2025-01-31":{systemMessageMode:"developer"},"o4-mini":{systemMessageMode:"developer"},"o4-mini-2025-04-16":{systemMessageMode:"developer"}};function _t(e){return null==e?void 0:e.tokens.map(((t,n)=>({token:t,logprob:e.token_logprobs[n],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[n]).map((([e,t])=>({token:e,logprob:t}))):[]})))}var xt=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:n,maxTokens:r,temperature:s,topP:o,topK:a,frequencyPenalty:i,presencePenalty:l,stopSequences:u,responseFormat:c,seed:p}){var d;const m=e.type,g=[];null!=a&&g.push({type:"unsupported-setting",setting:"topK"}),null!=c&&"text"!==c.type&&g.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});const{prompt:h,stopSequences:f}=function({prompt:e,inputFormat:t,user:n="user",assistant:r="assistant"}){if("prompt"===t&&1===e.length&&"user"===e[0].role&&1===e[0].content.length&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let s="";"system"===e[0].role&&(s+=`${e[0].content}\n\n`,e=e.slice(1));for(const{role:o,content:a}of e)switch(o){case"system":throw new oe({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":s+=`${n}:\n${a.map((e=>{switch(e.type){case"text":return e.text;case"image":throw new je({functionality:"images"})}})).join("")}\n\n`;break;case"assistant":s+=`${r}:\n${a.map((e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new je({functionality:"tool-call messages"})}})).join("")}\n\n`;break;case"tool":throw new je({functionality:"tool messages"});default:throw new Error(`Unsupported role: ${o}`)}return s+=`${r}:\n`,{prompt:s,stopSequences:[`\n${n}:`]}}({prompt:n,inputFormat:t}),y=[...null!=f?f:[],...null!=u?u:[]],v={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:r,temperature:s,top_p:o,frequency_penalty:i,presence_penalty:l,seed:p,prompt:h,stop:y.length>0?y:void 0};switch(m){case"regular":if(null==(d=e.tools)?void 0:d.length)throw new je({functionality:"tools"});if(e.toolChoice)throw new je({functionality:"toolChoice"});return{args:v,warnings:g};case"object-json":throw new je({functionality:"object-json mode"});case"object-tool":throw new je({functionality:"object-tool mode"});default:throw new Error(`Unsupported type: ${m}`)}}async doGenerate(e){const{args:t,warnings:n}=this.getArgs(e),{responseHeaders:r,value:s,rawValue:o}=await tt({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:t,failedResponseHandler:dt,successfulResponseHandler:st(kt),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:a,...i}=t,l=s.choices[0];return{text:l.text,usage:{promptTokens:s.usage.prompt_tokens,completionTokens:s.usage.completion_tokens},finishReason:ct(l.finish_reason),logprobs:_t(l.logprobs),rawCall:{rawPrompt:a,rawSettings:i},rawResponse:{headers:r,body:o},response:mt(s),warnings:n,request:{body:JSON.stringify(t)}}}async doStream(e){const{args:t,warnings:n}=this.getArgs(e),r={...t,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:s,value:o}=await tt({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:r,failedResponseHandler:dt,successfulResponseHandler:rt(Tt),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:a,...i}=t;let l,u="unknown",c={promptTokens:Number.NaN,completionTokens:Number.NaN},p=!0;return{stream:o.pipeThrough(new TransformStream({transform(e,t){if(!e.success)return u="error",void t.enqueue({type:"error",error:e.error});const n=e.value;if("error"in n)return u="error",void t.enqueue({type:"error",error:n.error});p&&(p=!1,t.enqueue({type:"response-metadata",...mt(n)})),null!=n.usage&&(c={promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens});const r=n.choices[0];null!=(null==r?void 0:r.finish_reason)&&(u=ct(r.finish_reason)),null!=(null==r?void 0:r.text)&&t.enqueue({type:"text-delta",textDelta:r.text});const s=_t(null==r?void 0:r.logprobs);(null==s?void 0:s.length)&&(void 0===l&&(l=[]),l.push(...s))},flush(e){e.enqueue({type:"finish",finishReason:u,logprobs:l,usage:c})}})),rawCall:{rawPrompt:a,rawSettings:i},rawResponse:{headers:s},warnings:n,request:{body:JSON.stringify(r)}}}},kt=r({id:o().nullish(),created:a().nullish(),model:o().nullish(),choices:s(r({text:o(),finish_reason:o(),logprobs:r({tokens:s(o()),token_logprobs:s(a()),top_logprobs:s(m(o(),a())).nullable()}).nullish()})),usage:r({prompt_tokens:a(),completion_tokens:a()})}),Tt=d([r({id:o().nullish(),created:a().nullish(),model:o().nullish(),choices:s(r({text:o(),finish_reason:o().nullish(),index:a(),logprobs:r({tokens:s(o()),token_logprobs:s(a()),top_logprobs:s(m(o(),a())).nullable()}).nullish()})),usage:r({prompt_tokens:a(),completion_tokens:a()}).nullish()}),pt]),St=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return null!=(e=this.settings.maxEmbeddingsPerCall)?e:2048}get supportsParallelCalls(){var e;return null==(e=this.settings.supportsParallelCalls)||e}async doEmbed({values:e,headers:t,abortSignal:n}){if(e.length>this.maxEmbeddingsPerCall)throw new Se({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});const{responseHeaders:r,value:s}=await tt({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:Le(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:dt,successfulResponseHandler:st(It),abortSignal:n,fetch:this.config.fetch});return{embeddings:s.data.map((e=>e.embedding)),usage:s.usage?{tokens:s.usage.prompt_tokens}:void 0,rawResponse:{headers:r}}}},It=r({data:s(r({embedding:s(a())})),usage:r({prompt_tokens:a()}).nullish()}),Et={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},Rt=new Set(["gpt-image-1"]),Ct=class{constructor(e,t,n){this.modelId=e,this.settings=t,this.config=n,this.specificationVersion="v1"}get maxImagesPerCall(){var e,t;return null!=(t=null!=(e=this.settings.maxImagesPerCall)?e:Et[this.modelId])?t:1}get provider(){return this.config.provider}async doGenerate({prompt:e,n:t,size:n,aspectRatio:r,seed:s,providerOptions:o,headers:a,abortSignal:i}){var l,u,c,p;const d=[];null!=r&&d.push({type:"unsupported-setting",setting:"aspectRatio",details:"This model does not support aspect ratio. Use `size` instead."}),null!=s&&d.push({type:"unsupported-setting",setting:"seed"});const m=null!=(c=null==(u=null==(l=this.config._internal)?void 0:l.currentDate)?void 0:u.call(l))?c:new Date,{value:g,responseHeaders:h}=await tt({url:this.config.url({path:"/images/generations",modelId:this.modelId}),headers:Le(this.config.headers(),a),body:{model:this.modelId,prompt:e,n:t,size:n,...null!=(p=o.openai)?p:{},...Rt.has(this.modelId)?{}:{response_format:"b64_json"}},failedResponseHandler:dt,successfulResponseHandler:st(At),abortSignal:i,fetch:this.config.fetch});return{images:g.data.map((e=>e.b64_json)),warnings:d,response:{timestamp:m,modelId:this.modelId,headers:h}}}},At=r({data:s(r({b64_json:o()}))}),Nt=r({include:s(o()).nullish(),language:o().nullish(),prompt:o().nullish(),temperature:a().min(0).max(1).nullish().default(0),timestampGranularities:s(u(["word","segment"])).nullish().default(["segment"])}),Pt={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},Mt=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:n}){var r,s,o,a,i;const l=Qe({provider:"openai",providerOptions:n,schema:Nt}),u=new FormData,c=e instanceof Uint8Array?new Blob([e]):new Blob([it(e)]);if(u.append("model",this.modelId),u.append("file",new File([c],"audio",{type:t})),l){const e={include:null!=(r=l.include)?r:void 0,language:null!=(s=l.language)?s:void 0,prompt:null!=(o=l.prompt)?o:void 0,temperature:null!=(a=l.temperature)?a:void 0,timestamp_granularities:null!=(i=l.timestampGranularities)?i:void 0};for(const t in e){const n=e[t];void 0!==n&&u.append(t,String(n))}}return{formData:u,warnings:[]}}async doGenerate(e){var t,n,r,s,o,a;const i=null!=(r=null==(n=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:n.call(t))?r:new Date,{formData:l,warnings:u}=this.getArgs(e),{value:c,responseHeaders:p,rawValue:d}=await(async({url:e,headers:t,formData:n,failedResponseHandler:r,successfulResponseHandler:s,abortSignal:o,fetch:a})=>nt({url:e,headers:t,body:{content:n,values:Object.fromEntries(n.entries())},failedResponseHandler:r,successfulResponseHandler:s,abortSignal:o,fetch:a}))({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),formData:l,failedResponseHandler:dt,successfulResponseHandler:st(Ot),abortSignal:e.abortSignal,fetch:this.config.fetch}),m=null!=c.language&&c.language in Pt?Pt[c.language]:void 0;return{text:c.text,segments:null!=(o=null==(s=c.words)?void 0:s.map((e=>({text:e.word,startSecond:e.start,endSecond:e.end}))))?o:[],language:m,durationInSeconds:null!=(a=c.duration)?a:void 0,warnings:u,response:{timestamp:i,modelId:this.modelId,headers:p,body:d}}}},Ot=r({text:o(),language:o().nullish(),duration:a().nullish(),words:s(r({word:o(),start:a(),end:a()})).nullish()});function jt({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return t?"tool-calls":"unknown"}}var Dt=class{constructor(e,t){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:n,stopSequences:r,topP:s,topK:o,presencePenalty:a,frequencyPenalty:i,seed:l,prompt:u,providerMetadata:c,responseFormat:p}){var d,m,g;const h=[],f=function(e){if(e.startsWith("o"))return e.startsWith("o1-mini")||e.startsWith("o1-preview")?{isReasoningModel:!0,systemMessageMode:"remove",requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:"developer",requiredAutoTruncation:!1};return{isReasoningModel:!1,systemMessageMode:"system",requiredAutoTruncation:!1}}(this.modelId),y=e.type;null!=o&&h.push({type:"unsupported-setting",setting:"topK"}),null!=l&&h.push({type:"unsupported-setting",setting:"seed"}),null!=a&&h.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=i&&h.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=r&&h.push({type:"unsupported-setting",setting:"stopSequences"});const{messages:v,warnings:b}=function({prompt:e,systemMessageMode:t}){const n=[],r=[];for(const{role:s,content:o}of e)switch(s){case"system":switch(t){case"system":n.push({role:"system",content:o});break;case"developer":n.push({role:"developer",content:o});break;case"remove":r.push({type:"other",message:"system messages are removed for this model"});break;default:throw new Error(`Unsupported system message mode: ${t}`)}break;case"user":n.push({role:"user",content:o.map(((e,t)=>{var n,r,s,o;switch(e.type){case"text":return{type:"input_text",text:e.text};case"image":return{type:"input_image",image_url:e.image instanceof URL?e.image.toString():`data:${null!=(n=e.mimeType)?n:"image/jpeg"};base64,${lt(e.image)}`,detail:null==(s=null==(r=e.providerMetadata)?void 0:r.openai)?void 0:s.imageDetail};case"file":if(e.data instanceof URL)throw new je({functionality:"File URLs in user messages"});if("application/pdf"===e.mimeType)return{type:"input_file",filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};throw new je({functionality:"Only PDF files are supported in user messages"})}}))});break;case"assistant":for(const e of o)switch(e.type){case"text":n.push({role:"assistant",content:[{type:"output_text",text:e.text}]});break;case"tool-call":n.push({type:"function_call",call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)})}break;case"tool":for(const e of o)n.push({type:"function_call_output",call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:throw new Error(`Unsupported role: ${s}`)}return{messages:n,warnings:r}}({prompt:u,systemMessageMode:f.systemMessageMode});h.push(...b);const w=Qe({provider:"openai",providerOptions:c,schema:Wt}),_=null==(d=null==w?void 0:w.strictSchemas)||d,x={model:this.modelId,input:v,temperature:n,top_p:s,max_output_tokens:t,..."json"===(null==p?void 0:p.type)&&{text:{format:null!=p.schema?{type:"json_schema",strict:_,name:null!=(m=p.name)?m:"response",description:p.description,schema:p.schema}:{type:"json_object"}}},metadata:null==w?void 0:w.metadata,parallel_tool_calls:null==w?void 0:w.parallelToolCalls,previous_response_id:null==w?void 0:w.previousResponseId,store:null==w?void 0:w.store,user:null==w?void 0:w.user,instructions:null==w?void 0:w.instructions,...f.isReasoningModel&&(null!=(null==w?void 0:w.reasoningEffort)||null!=(null==w?void 0:w.reasoningSummary))&&{reasoning:{...null!=(null==w?void 0:w.reasoningEffort)&&{effort:w.reasoningEffort},...null!=(null==w?void 0:w.reasoningSummary)&&{summary:w.reasoningSummary}}},...f.requiredAutoTruncation&&{truncation:"auto"}};switch(f.isReasoningModel&&(null!=x.temperature&&(x.temperature=void 0,h.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=x.top_p&&(x.top_p=void 0,h.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))),y){case"regular":{const{tools:t,tool_choice:n,toolWarnings:r}=function({mode:e,strict:t}){var n;const r=(null==(n=e.tools)?void 0:n.length)?e.tools:void 0,s=[];if(null==r)return{tools:void 0,tool_choice:void 0,toolWarnings:s};const o=e.toolChoice,a=[];for(const l of r)switch(l.type){case"function":a.push({type:"function",name:l.name,description:l.description,parameters:l.parameters,strict:!!t||void 0});break;case"provider-defined":"openai.web_search_preview"===l.id?a.push({type:"web_search_preview",search_context_size:l.args.searchContextSize,user_location:l.args.userLocation}):s.push({type:"unsupported-tool",tool:l});break;default:s.push({type:"unsupported-tool",tool:l})}if(null==o)return{tools:a,tool_choice:void 0,toolWarnings:s};const i=o.type;switch(i){case"auto":case"none":case"required":return{tools:a,tool_choice:i,toolWarnings:s};case"tool":return"web_search_preview"===o.toolName?{tools:a,tool_choice:{type:"web_search_preview"},toolWarnings:s}:{tools:a,tool_choice:{type:"function",name:o.toolName},toolWarnings:s};default:throw new je({functionality:`Unsupported tool choice type: ${i}`})}}({mode:e,strict:_});return{args:{...x,tools:t,tool_choice:n},warnings:[...h,...r]}}case"object-json":return{args:{...x,text:{format:null!=e.schema?{type:"json_schema",strict:_,name:null!=(g=e.name)?g:"response",description:e.description,schema:e.schema}:{type:"json_object"}}},warnings:h};case"object-tool":return{args:{...x,tool_choice:{type:"function",name:e.tool.name},tools:[{type:"function",name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:_}]},warnings:h};default:throw new Error(`Unsupported type: ${y}`)}}async doGenerate(e){var t,n,u,c,p,d,m;const{args:g,warnings:h}=this.getArgs(e),{responseHeaders:f,value:y,rawValue:v}=await tt({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:g,failedResponseHandler:dt,successfulResponseHandler:st(r({id:o(),created_at:a(),model:o(),output:s(i("type",[r({type:l("message"),role:l("assistant"),content:s(r({type:l("output_text"),text:o(),annotations:s(r({type:l("url_citation"),start_index:a(),end_index:a(),url:o(),title:o()}))}))}),r({type:l("function_call"),call_id:o(),name:o(),arguments:o()}),r({type:l("web_search_call")}),r({type:l("computer_call")}),r({type:l("reasoning"),summary:s(r({type:l("summary_text"),text:o()}))})])),incomplete_details:r({reason:o()}).nullable(),usage:$t})),abortSignal:e.abortSignal,fetch:this.config.fetch}),b=y.output.filter((e=>"message"===e.type)).flatMap((e=>e.content)).filter((e=>"output_text"===e.type)),w=y.output.filter((e=>"function_call"===e.type)).map((e=>({toolCallType:"function",toolCallId:e.call_id,toolName:e.name,args:e.arguments}))),_=null!=(n=null==(t=y.output.find((e=>"reasoning"===e.type)))?void 0:t.summary)?n:null;return{text:b.map((e=>e.text)).join("\n"),sources:b.flatMap((e=>e.annotations.map((e=>{var t,n,r;return{sourceType:"url",id:null!=(r=null==(n=(t=this.config).generateId)?void 0:n.call(t))?r:Ze(),url:e.url,title:e.title}})))),finishReason:jt({finishReason:null==(u=y.incomplete_details)?void 0:u.reason,hasToolCalls:w.length>0}),toolCalls:w.length>0?w:void 0,reasoning:_?_.map((e=>({type:"text",text:e.text}))):void 0,usage:{promptTokens:y.usage.input_tokens,completionTokens:y.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:f,body:v},request:{body:JSON.stringify(g)},response:{id:y.id,timestamp:new Date(1e3*y.created_at),modelId:y.model},providerMetadata:{openai:{responseId:y.id,cachedPromptTokens:null!=(p=null==(c=y.usage.input_tokens_details)?void 0:c.cached_tokens)?p:null,reasoningTokens:null!=(m=null==(d=y.usage.output_tokens_details)?void 0:d.reasoning_tokens)?m:null}},warnings:h}}async doStream(e){const{args:t,warnings:n}=this.getArgs(e),{responseHeaders:r,value:s}=await tt({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:{...t,stream:!0},failedResponseHandler:dt,successfulResponseHandler:rt(Ht),abortSignal:e.abortSignal,fetch:this.config.fetch}),o=this;let a="unknown",i=NaN,l=NaN,u=null,c=null,p=null;const d={};let m=!1;return{stream:s.pipeThrough(new TransformStream({transform(e,t){var n,r,s,g,h,f,y,v;if(!e.success)return a="error",void t.enqueue({type:"error",error:e.error});const b=e.value;if(function(e){return"response.output_item.added"===e.type}(b))"function_call"===b.item.type&&(d[b.output_index]={toolName:b.item.name,toolCallId:b.item.call_id},t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:b.item.call_id,toolName:b.item.name,argsTextDelta:b.item.arguments}));else if(function(e){return"response.function_call_arguments.delta"===e.type}(b)){const e=d[b.output_index];null!=e&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:b.delta})}else!function(e){return"response.created"===e.type}(b)?!function(e){return"response.output_text.delta"===e.type}(b)?!function(e){return"response.reasoning_summary_text.delta"===e.type}(b)?!function(e){return"response.output_item.done"===e.type}(b)||"function_call"!==b.item.type?!function(e){return"response.completed"===e.type||"response.incomplete"===e.type}(b)?function(e){return"response.output_text.annotation.added"===e.type}(b)&&t.enqueue({type:"source",source:{sourceType:"url",id:null!=(v=null==(y=(f=o.config).generateId)?void 0:y.call(f))?v:Ze(),url:b.annotation.url,title:b.annotation.title}}):(a=jt({finishReason:null==(n=b.response.incomplete_details)?void 0:n.reason,hasToolCalls:m}),i=b.response.usage.input_tokens,l=b.response.usage.output_tokens,u=null!=(s=null==(r=b.response.usage.input_tokens_details)?void 0:r.cached_tokens)?s:u,c=null!=(h=null==(g=b.response.usage.output_tokens_details)?void 0:g.reasoning_tokens)?h:c):(d[b.output_index]=void 0,m=!0,t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:b.item.call_id,toolName:b.item.name,args:b.item.arguments})):t.enqueue({type:"reasoning",textDelta:b.delta}):t.enqueue({type:"text-delta",textDelta:b.delta}):(p=b.response.id,t.enqueue({type:"response-metadata",id:b.response.id,timestamp:new Date(1e3*b.response.created_at),modelId:b.response.model}))},flush(e){e.enqueue({type:"finish",finishReason:a,usage:{promptTokens:i,completionTokens:l},...(null!=u||null!=c)&&{providerMetadata:{openai:{responseId:p,cachedPromptTokens:u,reasoningTokens:c}}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:r},request:{body:JSON.stringify(t)},warnings:n}}},$t=r({input_tokens:a(),input_tokens_details:r({cached_tokens:a().nullish()}).nullish(),output_tokens:a(),output_tokens_details:r({reasoning_tokens:a().nullish()}).nullish()}),qt=r({type:l("response.output_text.delta"),delta:o()}),Ft=r({type:u(["response.completed","response.incomplete"]),response:r({incomplete_details:r({reason:o()}).nullish(),usage:$t})}),Lt=r({type:l("response.created"),response:r({id:o(),created_at:a(),model:o()})}),Ut=r({type:l("response.output_item.done"),output_index:a(),item:i("type",[r({type:l("message")}),r({type:l("function_call"),id:o(),call_id:o(),name:o(),arguments:o(),status:l("completed")})])}),Bt=r({type:l("response.function_call_arguments.delta"),item_id:o(),output_index:a(),delta:o()}),Vt=r({type:l("response.output_item.added"),output_index:a(),item:i("type",[r({type:l("message")}),r({type:l("function_call"),id:o(),call_id:o(),name:o(),arguments:o()})])}),Zt=r({type:l("response.output_text.annotation.added"),annotation:r({type:l("url_citation"),url:o(),title:o()})}),Jt=r({type:l("response.reasoning_summary_text.delta"),item_id:o(),output_index:a(),summary_index:a(),delta:o()}),Ht=d([qt,Ft,Lt,Ut,Bt,Vt,Zt,Jt,r({type:o()}).passthrough()]);var Wt=r({metadata:p().nullish(),parallelToolCalls:c().nullish(),previousResponseId:o().nullish(),store:c().nullish(),user:o().nullish(),reasoningEffort:o().nullish(),strictSchemas:c().nullish(),instructions:o().nullish(),reasoningSummary:o().nullish()}),zt=r({});var Kt={webSearchPreview:function({searchContextSize:e,userLocation:t}={}){return{type:"provider-defined",id:"openai.web_search_preview",args:{searchContextSize:e,userLocation:t},parameters:zt}}},Gt=r({instructions:o().nullish(),speed:a().min(.25).max(4).default(1).nullish()}),Yt=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({text:e,voice:t="alloy",outputFormat:n="mp3",speed:r,instructions:s,providerOptions:o}){const a=[],i=Qe({provider:"openai",providerOptions:o,schema:Gt}),l={model:this.modelId,input:e,voice:t,response_format:"mp3",speed:r,instructions:s};if(n&&(["mp3","opus","aac","flac","wav","pcm"].includes(n)?l.response_format=n:a.push({type:"unsupported-setting",setting:"outputFormat",details:`Unsupported output format: ${n}. Using mp3 instead.`})),i){const e={};for(const t in e){const n=e[t];void 0!==n&&(l[t]=n)}}return{requestBody:l,warnings:a}}async doGenerate(e){var t,n,r;const s=null!=(r=null==(n=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:n.call(t))?r:new Date,{requestBody:o,warnings:a}=this.getArgs(e),{value:i,responseHeaders:l,rawValue:u}=await tt({url:this.config.url({path:"/audio/speech",modelId:this.modelId}),headers:Le(this.config.headers(),e.headers),body:o,failedResponseHandler:dt,successfulResponseHandler:async({response:e,url:t,requestBodyValues:n})=>{const r=Be(e);if(!e.body)throw new V({message:"Response body is empty",url:t,requestBodyValues:n,statusCode:e.status,responseHeaders:r,responseBody:void 0});try{const t=await e.arrayBuffer();return{responseHeaders:r,value:new Uint8Array(t)}}catch(s){throw new V({message:"Failed to read response as array buffer",url:t,requestBodyValues:n,statusCode:e.status,responseHeaders:r,responseBody:void 0,cause:s})}},abortSignal:e.abortSignal,fetch:this.config.fetch});return{audio:i,warnings:a,request:{body:JSON.stringify(o)},response:{timestamp:s,modelId:this.modelId,headers:l,body:u}}}};function Xt(e={}){var t,n,r;const s=null!=(t=null==(o=e.baseURL)?void 0:o.replace(/\/$/,""))?t:"https://api.openai.com/v1";var o;const a=null!=(n=e.compatibility)?n:"compatible",i=null!=(r=e.name)?r:"openai",l=()=>({Authorization:`Bearer ${He({apiKey:e.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),u=(t,n={})=>new gt(t,n,{provider:`${i}.chat`,url:({path:e})=>`${s}${e}`,headers:l,compatibility:a,fetch:e.fetch}),c=(t,n={})=>new xt(t,n,{provider:`${i}.completion`,url:({path:e})=>`${s}${e}`,headers:l,compatibility:a,fetch:e.fetch}),p=(t,n={})=>new St(t,n,{provider:`${i}.embedding`,url:({path:e})=>`${s}${e}`,headers:l,fetch:e.fetch}),d=(t,n={})=>new Ct(t,n,{provider:`${i}.image`,url:({path:e})=>`${s}${e}`,headers:l,fetch:e.fetch}),m=t=>new Mt(t,{provider:`${i}.transcription`,url:({path:e})=>`${s}${e}`,headers:l,fetch:e.fetch}),g=t=>new Yt(t,{provider:`${i}.speech`,url:({path:e})=>`${s}${e}`,headers:l,fetch:e.fetch}),h=(e,t)=>{if(new.target)throw new Error("The OpenAI model function cannot be called with the new keyword.");return"gpt-3.5-turbo-instruct"===e?c(e,t):u(e,t)},f=function(e,t){return h(e,t)};return f.languageModel=h,f.chat=u,f.completion=c,f.responses=t=>new Dt(t,{provider:`${i}.responses`,url:({path:e})=>`${s}${e}`,headers:l,fetch:e.fetch}),f.embedding=p,f.textEmbedding=p,f.textEmbeddingModel=p,f.image=d,f.imageModel=d,f.transcription=m,f.transcriptionModel=m,f.speech=g,f.speechModel=g,f.tools=Kt,f}function Qt(e){try{const t=e.apiKey;if(!t)throw console.error("[AI SDK createClient] 错误: 未提供API密钥"),new Error("未提供OpenAI API密钥，请在设置中配置");let n=e.baseUrl||"https://api.openai.com/v1";n.endsWith("/")&&(n=n.slice(0,-1)),n.includes("/v1")||(n=`${n}/v1`),e.id,n.substring(0,20);return Xt({apiKey:t,baseURL:n})}catch(t){console.error("[AI SDK createClient] 创建客户端失败:",t);const e=Xt({apiKey:"sk-missing-key-please-configure",baseURL:"https://api.openai.com/v1"});return console.warn("[AI SDK createClient] 使用后备客户端配置"),e}}Xt({compatibility:"strict"});const en=Symbol("Let zodToJsonSchema decide on which parser to use"),tn={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},nn=e=>{const t=(e=>"string"==typeof e?{...tn,name:e}:{...tn,...e})(e),n=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,currentPath:n,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map((([e,n])=>[n._def,{def:n._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}])))}};function rn(e,t,n,r){(null==r?void 0:r.errorMessages)&&n&&(e.errorMessage={...e.errorMessage,[t]:n})}function sn(e,t,n,r,s){e[t]=n,rn(e,t,r,s)}function on(e,t){return Mn(e.type._def,t)}function an(e,t,n){const r=n??t.dateStrategy;if(Array.isArray(r))return{anyOf:r.map(((n,r)=>an(e,t,n)))};switch(r){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return ln(e,t)}}const ln=(e,t)=>{const n={type:"integer",format:"unix-time"};if("openApi3"===t.target)return n;for(const r of e.checks)switch(r.kind){case"min":sn(n,"minimum",r.value,r.message,t);break;case"max":sn(n,"maximum",r.value,r.message,t)}return n};let un;const cn=/^[cC][^\s-]{8,}$/,pn=/^[0-9a-z]+$/,dn=/^[0-9A-HJKMNP-TV-Z]{26}$/,mn=/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,gn=()=>(void 0===un&&(un=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),un),hn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,fn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,yn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,vn=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,bn=/^[a-zA-Z0-9_-]{21}$/,wn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;function _n(e,t){const n={type:"string"};if(e.checks)for(const r of e.checks)switch(r.kind){case"min":sn(n,"minLength","number"==typeof n.minLength?Math.max(n.minLength,r.value):r.value,r.message,t);break;case"max":sn(n,"maxLength","number"==typeof n.maxLength?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case"email":switch(t.emailStrategy){case"format:email":Tn(n,"email",r.message,t);break;case"format:idn-email":Tn(n,"idn-email",r.message,t);break;case"pattern:zod":Sn(n,mn,r.message,t)}break;case"url":Tn(n,"uri",r.message,t);break;case"uuid":Tn(n,"uuid",r.message,t);break;case"regex":Sn(n,r.regex,r.message,t);break;case"cuid":Sn(n,cn,r.message,t);break;case"cuid2":Sn(n,pn,r.message,t);break;case"startsWith":Sn(n,RegExp(`^${xn(r.value,t)}`),r.message,t);break;case"endsWith":Sn(n,RegExp(`${xn(r.value,t)}$`),r.message,t);break;case"datetime":Tn(n,"date-time",r.message,t);break;case"date":Tn(n,"date",r.message,t);break;case"time":Tn(n,"time",r.message,t);break;case"duration":Tn(n,"duration",r.message,t);break;case"length":sn(n,"minLength","number"==typeof n.minLength?Math.max(n.minLength,r.value):r.value,r.message,t),sn(n,"maxLength","number"==typeof n.maxLength?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case"includes":Sn(n,RegExp(xn(r.value,t)),r.message,t);break;case"ip":"v6"!==r.version&&Tn(n,"ipv4",r.message,t),"v4"!==r.version&&Tn(n,"ipv6",r.message,t);break;case"base64url":Sn(n,vn,r.message,t);break;case"jwt":Sn(n,wn,r.message,t);break;case"cidr":"v6"!==r.version&&Sn(n,hn,r.message,t),"v4"!==r.version&&Sn(n,fn,r.message,t);break;case"emoji":Sn(n,gn(),r.message,t);break;case"ulid":Sn(n,dn,r.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":Tn(n,"binary",r.message,t);break;case"contentEncoding:base64":sn(n,"contentEncoding","base64",r.message,t);break;case"pattern:zod":Sn(n,yn,r.message,t)}break;case"nanoid":Sn(n,bn,r.message,t)}return n}function xn(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let n=0;n<e.length;n++)kn.has(e[n])||(t+="\\"),t+=e[n];return t}(e):e}const kn=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function Tn(e,t,n,r){var s;e.format||(null==(s=e.anyOf)?void 0:s.some((e=>e.format)))?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&r.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...n&&r.errorMessages&&{errorMessage:{format:n}}})):sn(e,"format",t,n,r)}function Sn(e,t,n,r){var s;e.pattern||(null==(s=e.allOf)?void 0:s.some((e=>e.pattern)))?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&r.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:In(t,r),...n&&r.errorMessages&&{errorMessage:{pattern:n}}})):sn(e,"pattern",In(t,r),n,r)}function In(e,t){var n;if(!t.applyRegexFlags||!e.flags)return e.source;const r=e.flags.includes("i"),s=e.flags.includes("m"),o=e.flags.includes("s"),a=r?e.source.toLowerCase():e.source;let i="",l=!1,u=!1,c=!1;for(let p=0;p<a.length;p++)if(l)i+=a[p],l=!1;else{if(r)if(u){if(a[p].match(/[a-z]/)){c?(i+=a[p],i+=`${a[p-2]}-${a[p]}`.toUpperCase(),c=!1):"-"===a[p+1]&&(null==(n=a[p+2])?void 0:n.match(/[a-z]/))?(i+=a[p],c=!0):i+=`${a[p]}${a[p].toUpperCase()}`;continue}}else if(a[p].match(/[a-z]/)){i+=`[${a[p]}${a[p].toUpperCase()}]`;continue}if(s){if("^"===a[p]){i+="(^|(?<=[\r\n]))";continue}if("$"===a[p]){i+="($|(?=[\r\n]))";continue}}o&&"."===a[p]?i+=u?`${a[p]}\r\n`:`[${a[p]}\r\n]`:(i+=a[p],"\\"===a[p]?l=!0:u&&"]"===a[p]?u=!1:u||"["!==a[p]||(u=!0))}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function En(e,t){var n,r,s,o,a,i;if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&(null==(n=e.keyType)?void 0:n._def.typeName)===g.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce(((n,r)=>({...n,[r]:Mn(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",r]})??{}})),{}),additionalProperties:t.rejectedAdditionalProperties};const l={type:"object",additionalProperties:Mn(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return l;if((null==(r=e.keyType)?void 0:r._def.typeName)===g.ZodString&&(null==(s=e.keyType._def.checks)?void 0:s.length)){const{type:n,...r}=_n(e.keyType._def,t);return{...l,propertyNames:r}}if((null==(o=e.keyType)?void 0:o._def.typeName)===g.ZodEnum)return{...l,propertyNames:{enum:e.keyType._def.values}};if((null==(a=e.keyType)?void 0:a._def.typeName)===g.ZodBranded&&e.keyType._def.type._def.typeName===g.ZodString&&(null==(i=e.keyType._def.type._def.checks)?void 0:i.length)){const{type:n,...r}=on(e.keyType._def,t);return{...l,propertyNames:r}}return l}const Rn={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};const Cn=(e,t)=>{const n=(e.options instanceof Map?Array.from(e.options.values()):e.options).map(((e,n)=>Mn(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${n}`]}))).filter((e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0)));return n.length?{anyOf:n}:void 0};function An(e,t){const n="openAi"===t.target,r={type:"object",properties:{}},s=[],o=e.shape();for(const i in o){let e=o[i];if(void 0===e||void 0===e._def)continue;let a=Nn(e);a&&n&&(e instanceof h&&(e=e._def.innerType),e.isNullable()||(e=e.nullable()),a=!1);const l=Mn(e._def,{...t,currentPath:[...t.currentPath,"properties",i],propertyPath:[...t.currentPath,"properties",i]});void 0!==l&&(r.properties[i]=l,a||s.push(i))}s.length&&(r.required=s);const a=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return Mn(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==a&&(r.additionalProperties=a),r}function Nn(e){try{return e.isOptional()}catch{return!0}}const Pn=(e,t,n)=>{switch(t){case g.ZodString:return _n(e,n);case g.ZodNumber:return function(e,t){const n={type:"number"};if(!e.checks)return n;for(const r of e.checks)switch(r.kind){case"int":n.type="integer",rn(n,"type",r.message,t);break;case"min":"jsonSchema7"===t.target?r.inclusive?sn(n,"minimum",r.value,r.message,t):sn(n,"exclusiveMinimum",r.value,r.message,t):(r.inclusive||(n.exclusiveMinimum=!0),sn(n,"minimum",r.value,r.message,t));break;case"max":"jsonSchema7"===t.target?r.inclusive?sn(n,"maximum",r.value,r.message,t):sn(n,"exclusiveMaximum",r.value,r.message,t):(r.inclusive||(n.exclusiveMaximum=!0),sn(n,"maximum",r.value,r.message,t));break;case"multipleOf":sn(n,"multipleOf",r.value,r.message,t)}return n}(e,n);case g.ZodObject:return An(e,n);case g.ZodBigInt:return function(e,t){const n={type:"integer",format:"int64"};if(!e.checks)return n;for(const r of e.checks)switch(r.kind){case"min":"jsonSchema7"===t.target?r.inclusive?sn(n,"minimum",r.value,r.message,t):sn(n,"exclusiveMinimum",r.value,r.message,t):(r.inclusive||(n.exclusiveMinimum=!0),sn(n,"minimum",r.value,r.message,t));break;case"max":"jsonSchema7"===t.target?r.inclusive?sn(n,"maximum",r.value,r.message,t):sn(n,"exclusiveMaximum",r.value,r.message,t):(r.inclusive||(n.exclusiveMaximum=!0),sn(n,"maximum",r.value,r.message,t));break;case"multipleOf":sn(n,"multipleOf",r.value,r.message,t)}return n}(e,n);case g.ZodBoolean:return{type:"boolean"};case g.ZodDate:return an(e,n);case g.ZodUndefined:return{not:{}};case g.ZodNull:return function(e){return"openApi3"===e.target?{enum:["null"],nullable:!0}:{type:"null"}}(n);case g.ZodArray:return function(e,t){var n,r,s;const o={type:"array"};return(null==(n=e.type)?void 0:n._def)&&(null==(s=null==(r=e.type)?void 0:r._def)?void 0:s.typeName)!==g.ZodAny&&(o.items=Mn(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&sn(o,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&sn(o,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(sn(o,"minItems",e.exactLength.value,e.exactLength.message,t),sn(o,"maxItems",e.exactLength.value,e.exactLength.message,t)),o}(e,n);case g.ZodUnion:case g.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return Cn(e,t);const n=e.options instanceof Map?Array.from(e.options.values()):e.options;if(n.every((e=>e._def.typeName in Rn&&(!e._def.checks||!e._def.checks.length)))){const e=n.reduce(((e,t)=>{const n=Rn[t._def.typeName];return n&&!e.includes(n)?[...e,n]:e}),[]);return{type:e.length>1?e:e[0]}}if(n.every((e=>"ZodLiteral"===e._def.typeName&&!e.description))){const e=n.reduce(((e,t)=>{const n=typeof t._def.value;switch(n){case"string":case"number":case"boolean":return[...e,n];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}}),[]);if(e.length===n.length){const t=e.filter(((e,t,n)=>n.indexOf(e)===t));return{type:t.length>1?t:t[0],enum:n.reduce(((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value]),[])}}}else if(n.every((e=>"ZodEnum"===e._def.typeName)))return{type:"string",enum:n.reduce(((e,t)=>[...e,...t._def.values.filter((t=>!e.includes(t)))]),[])};return Cn(e,t)}(e,n);case g.ZodIntersection:return function(e,t){const n=[Mn(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),Mn(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter((e=>!!e));let r="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0;const s=[];return n.forEach((e=>{if("type"in(t=e)&&"string"===t.type||!("allOf"in t)){let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){const{additionalProperties:n,...r}=e;t=r}else r=void 0;s.push(t)}else s.push(...e.allOf),void 0===e.unevaluatedProperties&&(r=void 0);var t})),s.length?{allOf:s,...r}:void 0}(e,n);case g.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map(((e,n)=>Mn(e._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]}))).reduce(((e,t)=>void 0===t?e:[...e,t]),[]),additionalItems:Mn(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map(((e,n)=>Mn(e._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]}))).reduce(((e,t)=>void 0===t?e:[...e,t]),[])}}(e,n);case g.ZodRecord:return En(e,n);case g.ZodLiteral:return function(e,t){const n=typeof e.value;return"bigint"!==n&&"number"!==n&&"boolean"!==n&&"string"!==n?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===n?"integer":n,enum:[e.value]}:{type:"bigint"===n?"integer":n,const:e.value}}(e,n);case g.ZodEnum:return function(e){return{type:"string",enum:Array.from(e.values)}}(e);case g.ZodNativeEnum:return function(e){const t=e.values,n=Object.keys(e.values).filter((e=>"number"!=typeof t[t[e]])).map((e=>t[e])),r=Array.from(new Set(n.map((e=>typeof e))));return{type:1===r.length?"string"===r[0]?"string":"number":["string","number"],enum:n}}(e);case g.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:Rn[e.innerType._def.typeName],nullable:!0}:{type:[Rn[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){const n=Mn(e.innerType._def,{...t,currentPath:[...t.currentPath]});return n&&"$ref"in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}const n=Mn(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return n&&{anyOf:[n,{type:"null"}]}}(e,n);case g.ZodOptional:return((e,t)=>{var n;if(t.currentPath.toString()===(null==(n=t.propertyPath)?void 0:n.toString()))return Mn(e.innerType._def,t);const r=Mn(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:{}},r]}:{}})(e,n);case g.ZodMap:return function(e,t){return"record"===t.mapStrategy?En(e,t):{type:"array",maxItems:125,items:{type:"array",items:[Mn(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||{},Mn(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||{}],minItems:2,maxItems:2}}}(e,n);case g.ZodSet:return function(e,t){const n={type:"array",uniqueItems:!0,items:Mn(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&sn(n,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&sn(n,"maxItems",e.maxSize.value,e.maxSize.message,t),n}(e,n);case g.ZodLazy:return()=>e.getter()._def;case g.ZodPromise:return function(e,t){return Mn(e.type._def,t)}(e,n);case g.ZodNaN:case g.ZodNever:return{not:{}};case g.ZodEffects:return function(e,t){return"input"===t.effectStrategy?Mn(e.schema._def,t):{}}(e,n);case g.ZodAny:case g.ZodUnknown:return{};case g.ZodDefault:return function(e,t){return{...Mn(e.innerType._def,t),default:e.defaultValue()}}(e,n);case g.ZodBranded:return on(e,n);case g.ZodReadonly:case g.ZodCatch:return((e,t)=>Mn(e.innerType._def,t))(e,n);case g.ZodPipeline:return((e,t)=>{if("input"===t.pipeStrategy)return Mn(e.in._def,t);if("output"===t.pipeStrategy)return Mn(e.out._def,t);const n=Mn(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]});return{allOf:[n,Mn(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",n?"1":"0"]})].filter((e=>void 0!==e))}})(e,n);case g.ZodFunction:case g.ZodVoid:case g.ZodSymbol:default:return}};function Mn(e,t,n=!1){var r;const s=t.seen.get(e);if(t.override){const o=null==(r=t.override)?void 0:r.call(t,e,t,s,n);if(o!==en)return o}if(s&&!n){const e=On(s,t);if(void 0!==e)return e}const o={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,o);const a=Pn(e,e.typeName,t),i="function"==typeof a?Mn(a(),t):a;if(i&&Dn(e,t,i),t.postProcess){const n=t.postProcess(i,e,t);return o.jsonSchema=i,n}return o.jsonSchema=i,i}const On=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:jn(t.currentPath,e.path)};case"none":case"seen":return e.path.length<t.currentPath.length&&e.path.every(((e,n)=>t.currentPath[n]===e))?(console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),{}):"seen"===t.$refStrategy?{}:void 0}},jn=(e,t)=>{let n=0;for(;n<e.length&&n<t.length&&e[n]===t[n];n++);return[(e.length-n).toString(),...t.slice(n)].join("/")},Dn=(e,t,n)=>(e.description&&(n.description=e.description,t.markdownDescription&&(n.markdownDescription=e.description)),n);var $n={code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw new Error('"text" parts expect a string value.');return{type:"text",value:e}}},qn={code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw new Error('"error" parts expect a string value.');return{type:"error",value:e}}},Fn={code:"4",name:"assistant_message",parse:e=>{if(!(null!=e&&"object"==typeof e&&"id"in e&&"role"in e&&"content"in e&&"string"==typeof e.id&&"string"==typeof e.role&&"assistant"===e.role&&Array.isArray(e.content)&&e.content.every((e=>null!=e&&"object"==typeof e&&"type"in e&&"text"===e.type&&"text"in e&&null!=e.text&&"object"==typeof e.text&&"value"in e.text&&"string"==typeof e.text.value))))throw new Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');return{type:"assistant_message",value:e}}},Ln={code:"5",name:"assistant_control_data",parse:e=>{if(null==e||"object"!=typeof e||!("threadId"in e)||!("messageId"in e)||"string"!=typeof e.threadId||"string"!=typeof e.messageId)throw new Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');return{type:"assistant_control_data",value:{threadId:e.threadId,messageId:e.messageId}}}},Un={code:"6",name:"data_message",parse:e=>{if(null==e||"object"!=typeof e||!("role"in e)||!("data"in e)||"string"!=typeof e.role||"data"!==e.role)throw new Error('"data_message" parts expect an object with a "role" and "data" property.');return{type:"data_message",value:e}}};function Bn(e){const t=["ROOT"];let n=-1,r=null;function s(e,s,o){switch(e){case'"':n=s,t.pop(),t.push(o),t.push("INSIDE_STRING");break;case"f":case"t":case"n":n=s,r=s,t.pop(),t.push(o),t.push("INSIDE_LITERAL");break;case"-":t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n=s,t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"{":n=s,t.pop(),t.push(o),t.push("INSIDE_OBJECT_START");break;case"[":n=s,t.pop(),t.push(o),t.push("INSIDE_ARRAY_START")}}function o(e,r){switch(e){case",":t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":n=r,t.pop()}}function a(e,r){switch(e){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":n=r,t.pop()}}for(let l=0;l<e.length;l++){const i=e[l];switch(t[t.length-1]){case"ROOT":s(i,l,"FINISH");break;case"INSIDE_OBJECT_START":switch(i){case'"':t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"}":n=l,t.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":if('"'===i)t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"INSIDE_OBJECT_KEY":if('"'===i)t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY");break;case"INSIDE_OBJECT_AFTER_KEY":if(":"===i)t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE");break;case"INSIDE_OBJECT_BEFORE_VALUE":s(i,l,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":o(i,l);break;case"INSIDE_STRING":switch(i){case'"':t.pop(),n=l;break;case"\\":t.push("INSIDE_STRING_ESCAPE");break;default:n=l}break;case"INSIDE_ARRAY_START":if("]"===i)n=l,t.pop();else n=l,s(i,l,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_ARRAY_AFTER_VALUE":switch(i){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":n=l,t.pop();break;default:n=l}break;case"INSIDE_ARRAY_AFTER_COMMA":s(i,l,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":t.pop(),n=l;break;case"INSIDE_NUMBER":switch(i){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n=l;break;case"e":case"E":case"-":case".":break;case",":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&a(i,l),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(i,l);break;case"}":t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(i,l);break;case"]":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&a(i,l);break;default:t.pop()}break;case"INSIDE_LITERAL":{const s=e.substring(r,l+1);"false".startsWith(s)||"true".startsWith(s)||"null".startsWith(s)?n=l:(t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]?o(i,l):"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&a(i,l));break}}}let i=e.slice(0,n+1);for(let l=t.length-1;l>=0;l--){switch(t[l]){case"INSIDE_STRING":i+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":i+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":i+="]";break;case"INSIDE_LITERAL":{const t=e.substring(r,e.length);"true".startsWith(t)?i+="true".slice(t.length):"false".startsWith(t)?i+="false".slice(t.length):"null".startsWith(t)&&(i+="null".slice(t.length))}}}return i}[$n,qn,Fn,Ln,Un].map((e=>e.code));var Vn=[{code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw new Error('"text" parts expect a string value.');return{type:"text",value:e}}},{code:"2",name:"data",parse:e=>{if(!Array.isArray(e))throw new Error('"data" parts expect an array value.');return{type:"data",value:e}}},{code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw new Error('"error" parts expect a string value.');return{type:"error",value:e}}},{code:"8",name:"message_annotations",parse:e=>{if(!Array.isArray(e))throw new Error('"message_annotations" parts expect an array value.');return{type:"message_annotations",value:e}}},{code:"9",name:"tool_call",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName||!("args"in e)||"object"!=typeof e.args)throw new Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');return{type:"tool_call",value:e}}},{code:"a",name:"tool_result",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("result"in e))throw new Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');return{type:"tool_result",value:e}}},{code:"b",name:"tool_call_streaming_start",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName)throw new Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');return{type:"tool_call_streaming_start",value:e}}},{code:"c",name:"tool_call_delta",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("argsTextDelta"in e)||"string"!=typeof e.argsTextDelta)throw new Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');return{type:"tool_call_delta",value:e}}},{code:"d",name:"finish_message",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw new Error('"finish_message" parts expect an object with a "finishReason" property.');const t={finishReason:e.finishReason};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),{type:"finish_message",value:t}}},{code:"e",name:"finish_step",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw new Error('"finish_step" parts expect an object with a "finishReason" property.');const t={finishReason:e.finishReason,isContinued:!1};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),"isContinued"in e&&"boolean"==typeof e.isContinued&&(t.isContinued=e.isContinued),{type:"finish_step",value:t}}},{code:"f",name:"start_step",parse:e=>{if(null==e||"object"!=typeof e||!("messageId"in e)||"string"!=typeof e.messageId)throw new Error('"start_step" parts expect an object with an "id" property.');return{type:"start_step",value:{messageId:e.messageId}}}},{code:"g",name:"reasoning",parse:e=>{if("string"!=typeof e)throw new Error('"reasoning" parts expect a string value.');return{type:"reasoning",value:e}}},{code:"h",name:"source",parse:e=>{if(null==e||"object"!=typeof e)throw new Error('"source" parts expect a Source object.');return{type:"source",value:e}}},{code:"i",name:"redacted_reasoning",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data)throw new Error('"redacted_reasoning" parts expect an object with a "data" property.');return{type:"redacted_reasoning",value:{data:e.data}}}},{code:"j",name:"reasoning_signature",parse:e=>{if(null==e||"object"!=typeof e||!("signature"in e)||"string"!=typeof e.signature)throw new Error('"reasoning_signature" parts expect an object with a "signature" property.');return{type:"reasoning_signature",value:{signature:e.signature}}}},{code:"k",name:"file",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data||!("mimeType"in e)||"string"!=typeof e.mimeType)throw new Error('"file" parts expect an object with a "data" and "mimeType" property.');return{type:"file",value:e}}}];function Zn(e,t){const n=Vn.find((t=>t.name===e));if(!n)throw new Error(`Invalid stream part type: ${e}`);return`${n.code}:${JSON.stringify(t)}\n`}function Jn(e,t){const n=null!=void 0&&undefined;return function(e,{validate:t}={}){return{[Hn]:!0,_type:void 0,[We]:!0,jsonSchema:e,validate:t}}(((e,t)=>{const n=nn(t),r="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce(((e,[t,r])=>({...e,[t]:Mn(r._def,{...n,currentPath:[...n.basePath,n.definitionPath,t]},!0)??{}})),{}):void 0,s="string"==typeof t?t:"title"===(null==t?void 0:t.nameStrategy)||null==t?void 0:t.name,o=Mn(e._def,void 0===s?n:{...n,currentPath:[...n.basePath,n.definitionPath,s]},!1)??{},a="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==a&&(o.title=a);const i=void 0===s?r?{...o,[n.definitionPath]:r}:o:{$ref:[..."relative"===n.$refStrategy?[]:n.basePath,n.definitionPath,s].join("/"),[n.definitionPath]:{...r,[s]:o}};return"jsonSchema7"===n.target?i.$schema="http://json-schema.org/draft-07/schema#":"jsonSchema2019-09"!==n.target&&"openAi"!==n.target||(i.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===n.target&&("anyOf"in i||"oneOf"in i||"allOf"in i||"type"in i&&Array.isArray(i.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),i})(e,{$refStrategy:n?"root":"none",target:"jsonSchema7"}),{validate:t=>{const n=e.safeParse(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}})}Object.fromEntries(Vn.map((e=>[e.code,e]))),Object.fromEntries(Vn.map((e=>[e.name,e.code]))),Vn.map((e=>e.code));var Hn=Symbol.for("vercel.ai.schema");function Wn(e){return"object"==typeof(t=e)&&null!==t&&Hn in t&&!0===t[Hn]&&"jsonSchema"in t&&"validate"in t?e:Jn(e);var t}var zn="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{},Kn="1.9.0",Gn=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;var Yn=function(e){var t=new Set([e]),n=new Set,r=e.match(Gn);if(!r)return function(){return!1};var s=+r[1],o=+r[2],a=+r[3];if(null!=r[4])return function(t){return t===e};function i(e){return n.add(e),!1}function l(e){return t.add(e),!0}return function(e){if(t.has(e))return!0;if(n.has(e))return!1;var r=e.match(Gn);if(!r)return i(e);var u=+r[1],c=+r[2],p=+r[3];return null!=r[4]||s!==u?i(e):0===s?o===c&&a<=p?l(e):i(e):o<=c?l(e):i(e)}}(Kn),Xn=Kn.split(".")[0],Qn=Symbol.for("opentelemetry.js.api."+Xn),er=zn;function tr(e,t,n,r){var s;void 0===r&&(r=!1);var o=er[Qn]=null!==(s=er[Qn])&&void 0!==s?s:{version:Kn};if(!r&&o[e]){var a=new Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return n.error(a.stack||a.message),!1}if(o.version!==Kn){a=new Error("@opentelemetry/api: Registration of version v"+o.version+" for "+e+" does not match previously registered API v"+Kn);return n.error(a.stack||a.message),!1}return o[e]=t,n.debug("@opentelemetry/api: Registered a global for "+e+" v"+Kn+"."),!0}function nr(e){var t,n,r=null===(t=er[Qn])||void 0===t?void 0:t.version;if(r&&Yn(r))return null===(n=er[Qn])||void 0===n?void 0:n[e]}function rr(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+Kn+".");var n=er[Qn];n&&delete n[e]}var sr,or,ar=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ir("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ir("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ir("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ir("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ir("verbose",this._namespace,e)},e}();function ir(e,t,n){var r=nr("diag");if(r)return n.unshift(t),r[e].apply(r,function(e,t,n){if(n||2===arguments.length)for(var r,s=0,o=t.length;s<o;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))}([],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,s,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(i){s={error:i}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(s)throw s.error}}return a}(n),!1))}(or=sr||(sr={}))[or.NONE=0]="NONE",or[or.ERROR=30]="ERROR",or[or.WARN=50]="WARN",or[or.INFO=60]="INFO",or[or.DEBUG=70]="DEBUG",or[or.VERBOSE=80]="VERBOSE",or[or.ALL=9999]="ALL";var lr=function(){function e(){function e(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=nr("diag");if(r)return r[e].apply(r,function(e,t,n){if(n||2===arguments.length)for(var r,s=0,o=t.length;s<o;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))}([],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,s,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(i){s={error:i}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(s)throw s.error}}return a}(t),!1))}}var t=this;t.setLogger=function(e,n){var r,s,o;if(void 0===n&&(n={logLevel:sr.INFO}),e===t){var a=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(r=a.stack)&&void 0!==r?r:a.message),!1}"number"==typeof n&&(n={logLevel:n});var i=nr("diag"),l=function(e,t){function n(n,r){var s=t[n];return"function"==typeof s&&e>=r?s.bind(t):function(){}}return e<sr.NONE?e=sr.NONE:e>sr.ALL&&(e=sr.ALL),t=t||{},{error:n("error",sr.ERROR),warn:n("warn",sr.WARN),info:n("info",sr.INFO),debug:n("debug",sr.DEBUG),verbose:n("verbose",sr.VERBOSE)}}(null!==(s=n.logLevel)&&void 0!==s?s:sr.INFO,e);if(i&&!n.suppressOverrideMessage){var u=null!==(o=(new Error).stack)&&void 0!==o?o:"<failed to generate stacktrace>";i.warn("Current logger will be overwritten from "+u),l.warn("Current logger will overwrite one already registered from "+u)}return tr("diag",l,t,!0)},t.disable=function(){rr("diag",t)},t.createComponentLogger=function(e){return new ar(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}();var ur,cr,pr=new(function(){return function e(t){var n=this;n._currentContext=t?new Map(t):new Map,n.getValue=function(e){return n._currentContext.get(e)},n.setValue=function(t,r){var s=new e(n._currentContext);return s._currentContext.set(t,r),s},n.deleteValue=function(t){var r=new e(n._currentContext);return r._currentContext.delete(t),r}}}()),dr=function(){function e(){}return e.prototype.active=function(){return pr},e.prototype.with=function(e,t,n){for(var r=[],s=3;s<arguments.length;s++)r[s-3]=arguments[s];return t.call.apply(t,function(e,t,n){if(n||2===arguments.length)for(var r,s=0,o=t.length;s<o;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))}([n],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,s,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(i){s={error:i}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(s)throw s.error}}return a}(r),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),mr="context",gr=new dr,hr=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return tr(mr,e,lr.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,n){for(var r,s=[],o=3;o<arguments.length;o++)s[o-3]=arguments[o];return(r=this._getContextManager()).with.apply(r,function(e,t,n){if(n||2===arguments.length)for(var r,s=0,o=t.length;s<o;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))}([e,t,n],function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,s,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(i){s={error:i}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(s)throw s.error}}return a}(s),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return nr(mr)||gr},e.prototype.disable=function(){this._getContextManager().disable(),rr(mr,lr.instance())},e}();(cr=ur||(ur={}))[cr.NONE=0]="NONE",cr[cr.SAMPLED=1]="SAMPLED";var fr,yr="0000000000000000",vr="00000000000000000000000000000000",br={traceId:vr,spanId:yr,traceFlags:ur.NONE},wr=function(){function e(e){void 0===e&&(e=br),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),_r=(fr="OpenTelemetry Context Key SPAN",Symbol.for(fr));function xr(e){return e.getValue(_r)||void 0}function kr(){return xr(hr.getInstance().active())}function Tr(e,t){return e.setValue(_r,t)}function Sr(e){return e.deleteValue(_r)}function Ir(e,t){return Tr(e,new wr(t))}function Er(e){var t;return null===(t=xr(e))||void 0===t?void 0:t.spanContext()}var Rr=/^([0-9a-f]{32})$/i,Cr=/^[0-9a-f]{16}$/i;function Ar(e){return n=e.traceId,Rr.test(n)&&n!==vr&&(t=e.spanId,Cr.test(t)&&t!==yr);var t,n}function Nr(e){return new wr(e)}var Pr=hr.getInstance(),Mr=function(){function e(){}return e.prototype.startSpan=function(e,t,n){if(void 0===n&&(n=Pr.active()),Boolean(null==t?void 0:t.root))return new wr;var r,s=n&&Er(n);return"object"==typeof(r=s)&&"string"==typeof r.spanId&&"string"==typeof r.traceId&&"number"==typeof r.traceFlags&&Ar(s)?new wr(s):new wr},e.prototype.startActiveSpan=function(e,t,n,r){var s,o,a;if(!(arguments.length<2)){2===arguments.length?a=t:3===arguments.length?(s=t,a=n):(s=t,o=n,a=r);var i=null!=o?o:Pr.active(),l=this.startSpan(e,s,i),u=Tr(i,l);return Pr.with(u,a,void 0,l)}},e}();var Or,jr,Dr=new Mr,$r=function(){function e(e,t,n,r){this._provider=e,this.name=t,this.version=n,this.options=r}return e.prototype.startSpan=function(e,t,n){return this._getTracer().startSpan(e,t,n)},e.prototype.startActiveSpan=function(e,t,n,r){var s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):Dr},e}(),qr=new(function(){function e(){}return e.prototype.getTracer=function(e,t,n){return new Mr},e}()),Fr=function(){function e(){}return e.prototype.getTracer=function(e,t,n){var r;return null!==(r=this.getDelegateTracer(e,t,n))&&void 0!==r?r:new $r(this,e,t,n)},e.prototype.getDelegate=function(){var e;return null!==(e=this._delegate)&&void 0!==e?e:qr},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,n){var r;return null===(r=this._delegate)||void 0===r?void 0:r.getTracer(e,t,n)},e}();(jr=Or||(Or={}))[jr.UNSET=0]="UNSET",jr[jr.OK=1]="OK",jr[jr.ERROR=2]="ERROR";var Lr="trace",Ur=function(){function e(){this._proxyTracerProvider=new Fr,this.wrapSpanContext=Nr,this.isSpanContextValid=Ar,this.deleteSpan=Sr,this.getSpan=xr,this.getActiveSpan=kr,this.getSpanContext=Er,this.setSpan=Tr,this.setSpanContext=Ir}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=tr(Lr,this._proxyTracerProvider,lr.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return nr(Lr)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){rr(Lr,lr.instance()),this._proxyTracerProvider=new Fr},e}().getInstance(),Br=Object.defineProperty,Vr=(e,t)=>{for(var n in t)Br(e,n,{get:t[n],enumerable:!0})};function Zr(e,{contentType:t,dataStreamVersion:n}){const r=new Headers(null!=e?e:{});return r.has("Content-Type")||r.set("Content-Type",t),void 0!==n&&r.set("X-Vercel-AI-Data-Stream",n),r}function Jr(e,{contentType:t,dataStreamVersion:n}){const r={};if(null!=e)for(const[s,o]of Object.entries(e))r[s]=o;return null==r["Content-Type"]&&(r["Content-Type"]=t),void 0!==n&&(r["X-Vercel-AI-Data-Stream"]=n),r}function Hr({response:e,status:t,statusText:n,headers:r,stream:s}){e.writeHead(null!=t?t:200,n,r);const o=s.getReader();(async()=>{try{for(;;){const{done:t,value:n}=await o.read();if(t)break;e.write(n)}}catch(t){throw t}finally{e.end()}})()}var Wr,zr="AI_InvalidArgumentError",Kr=`vercel.ai.error.${zr}`,Gr=Symbol.for(Kr),Yr=class extends F{constructor({parameter:e,value:t,message:n}){super({name:zr,message:`Invalid argument for parameter ${e}: ${n}`}),this[Wr]=!0,this.parameter=e,this.value=t}static isInstance(e){return F.hasMarker(e,Kr)}};Wr=Gr;var Xr,Qr="AI_RetryError",es=`vercel.ai.error.${Qr}`,ts=Symbol.for(es),ns=class extends F{constructor({message:e,reason:t,errors:n}){super({name:Qr,message:e}),this[Xr]=!0,this.reason=t,this.errors=n,this.lastError=n[n.length-1]}static isInstance(e){return F.hasMarker(e,es)}};Xr=ts;var rs=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:n=2}={})=>async r=>ss(r,{maxRetries:e,delayInMs:t,backoffFactor:n});async function ss(e,{maxRetries:t,delayInMs:n,backoffFactor:r},s=[]){try{return await e()}catch(o){if(Je(o))throw o;if(0===t)throw o;const a=function(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}(o),i=[...s,o],l=i.length;if(l>t)throw new ns({message:`Failed after ${l} attempts. Last error: ${a}`,reason:"maxRetriesExceeded",errors:i});if(o instanceof Error&&V.isInstance(o)&&!0===o.isRetryable&&l<=t)return await async function(e){return null==e?Promise.resolve():new Promise((t=>setTimeout(t,e)))}(n),ss(e,{maxRetries:t,delayInMs:r*n,backoffFactor:r},i);if(1===l)throw o;throw new ns({message:`Failed after ${l} attempts with non-retryable error: '${a}'`,reason:"errorNotRetryable",errors:i})}}function os({operationId:e,telemetry:t}){return{"operation.name":`${e}${null!=(null==t?void 0:t.functionId)?` ${t.functionId}`:""}`,"resource.name":null==t?void 0:t.functionId,"ai.operationId":e,"ai.telemetry.functionId":null==t?void 0:t.functionId}}var as={startSpan:()=>is,startActiveSpan:(e,t,n,r)=>"function"==typeof t?t(is):"function"==typeof n?n(is):"function"==typeof r?r(is):void 0},is={spanContext:()=>ls,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},ls={traceId:"",spanId:"",traceFlags:0};function us({name:e,tracer:t,attributes:n,fn:r,endWhenDone:s=!0}){return t.startActiveSpan(e,{attributes:n},(async e=>{try{const t=await r(e);return s&&e.end(),t}catch(t){try{t instanceof Error?(e.recordException({name:t.name,message:t.message,stack:t.stack}),e.setStatus({code:Or.ERROR,message:t.message})):e.setStatus({code:Or.ERROR})}finally{e.end()}throw t}}))}function cs({telemetry:e,attributes:t}){return!0!==(null==e?void 0:e.isEnabled)?{}:Object.entries(t).reduce(((t,[n,r])=>{if(void 0===r)return t;if("object"==typeof r&&"input"in r&&"function"==typeof r.input){if(!1===(null==e?void 0:e.recordInputs))return t;const s=r.input();return void 0===s?t:{...t,[n]:s}}if("object"==typeof r&&"output"in r&&"function"==typeof r.output){if(!1===(null==e?void 0:e.recordOutputs))return t;const s=r.output();return void 0===s?t:{...t,[n]:s}}return{...t,[n]:r}}),{})}var ps=class{constructor({data:e,mimeType:t}){const n=e instanceof Uint8Array;this.base64Data=n?void 0:e,this.uint8ArrayData=n?e:void 0,this.mimeType=t}get base64(){return null==this.base64Data&&(this.base64Data=lt(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=it(this.base64Data)),this.uint8ArrayData}},ds=class extends ps{constructor(e){super(e),this.type="file"}},ms=[{mimeType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mimeType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mimeType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mimeType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mimeType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mimeType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mimeType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mimeType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mimeType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}];function gs(e){return"string"==typeof e&&e.startsWith("SUQz")||"string"!=typeof e&&e.length>10&&73===e[0]&&68===e[1]&&51===e[2]?(e=>{const t="string"==typeof e?it(e):e,n=(127&t[6])<<21|(127&t[7])<<14|(127&t[8])<<7|127&t[9];return t.slice(n+10)})(e):e}var hs,fs="AI_NoObjectGeneratedError",ys=`vercel.ai.error.${fs}`,vs=Symbol.for(ys),bs=class extends F{constructor({message:e="No object generated.",cause:t,text:n,response:r,usage:s,finishReason:o}){super({name:fs,message:e,cause:t}),this[hs]=!0,this.text=n,this.response=r,this.usage=s,this.finishReason=o}static isInstance(e){return F.hasMarker(e,ys)}};hs=vs;var ws,_s="AI_DownloadError",xs=`vercel.ai.error.${_s}`,ks=Symbol.for(xs),Ts=class extends F{constructor({url:e,statusCode:t,statusText:n,cause:r,message:s=(null==r?`Failed to download ${e}: ${t} ${n}`:`Failed to download ${e}: ${r}`)}){super({name:_s,message:s,cause:r}),this[ws]=!0,this.url=e,this.statusCode=t,this.statusText=n}static isInstance(e){return F.hasMarker(e,xs)}};async function Ss({url:e}){var t;const n=e.toString();try{const e=await fetch(n);if(!e.ok)throw new Ts({url:n,statusCode:e.status,statusText:e.statusText});return{data:new Uint8Array(await e.arrayBuffer()),mimeType:null!=(t=e.headers.get("content-type"))?t:void 0}}catch(r){if(Ts.isInstance(r))throw r;throw new Ts({url:n,cause:r})}}ws=ks;var Is,Es="AI_InvalidDataContentError",Rs=`vercel.ai.error.${Es}`,Cs=Symbol.for(Rs),As=class extends F{constructor({content:e,cause:t,message:n=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof e}.`}){super({name:Es,message:n,cause:t}),this[Is]=!0,this.content=e}static isInstance(e){return F.hasMarker(e,Rs)}};Is=Cs;var Ns=d([o(),y(Uint8Array),y(ArrayBuffer),w((e=>{var t,n;return null!=(n=null==(t=globalThis.Buffer)?void 0:t.isBuffer(e))&&n}),{message:"Must be a Buffer"})]);function Ps(e){return"string"==typeof e?e:e instanceof ArrayBuffer?lt(new Uint8Array(e)):lt(e)}function Ms(e){if(e instanceof Uint8Array)return e;if("string"==typeof e)try{return it(e)}catch(t){throw new As({message:"Invalid data content. Content string is not a base64-encoded media.",content:e,cause:t})}if(e instanceof ArrayBuffer)return new Uint8Array(e);throw new As({content:e})}function Os(e){try{return(new TextDecoder).decode(e)}catch(t){throw new Error("Error decoding Uint8Array to text")}}var js,Ds="AI_InvalidMessageRoleError",$s=`vercel.ai.error.${Ds}`,qs=Symbol.for($s),Fs=class extends F{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:Ds,message:t}),this[js]=!0,this.role=e}static isInstance(e){return F.hasMarker(e,$s)}};async function Ls({prompt:e,modelSupportsImageUrls:t=!0,modelSupportsUrl:n=()=>!1,downloadImplementation:r=Ss}){const s=await async function(e,t,n,r){const s=e.filter((e=>"user"===e.role)).map((e=>e.content)).filter((e=>Array.isArray(e))).flat().filter((e=>"image"===e.type||"file"===e.type)).filter((e=>!("image"===e.type&&!0===n))).map((e=>"image"===e.type?e.image:e.data)).map((e=>"string"==typeof e&&(e.startsWith("http:")||e.startsWith("https:"))?new URL(e):e)).filter((e=>e instanceof URL)).filter((e=>!r(e))),o=await Promise.all(s.map((async e=>({url:e,data:await t({url:e})}))));return Object.fromEntries(o.map((({url:e,data:t})=>[e.toString(),t])))}(e.messages,r,t,n);return[...null!=e.system?[{role:"system",content:e.system}]:[],...e.messages.map((e=>function(e,t){var n,r,s,o,a,i;const l=e.role;switch(l){case"system":return{role:"system",content:e.content,providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata};case"user":return"string"==typeof e.content?{role:"user",content:[{type:"text",text:e.content}],providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata}:{role:"user",content:e.content.map((e=>function(e,t){var n,r,s,o;if("text"===e.type)return{type:"text",text:e.text,providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata};let a,i,l,u=e.mimeType;const c=e.type;switch(c){case"image":a=e.image;break;case"file":a=e.data;break;default:throw new Error(`Unsupported part type: ${c}`)}try{i="string"==typeof a?new URL(a):a}catch(p){i=a}if(i instanceof URL)if("data:"===i.protocol){const{mimeType:e,base64Content:t}=function(e){try{const[t,n]=e.split(",");return{mimeType:t.split(";")[0].split(":")[1],base64Content:n}}catch(p){return{mimeType:void 0,base64Content:void 0}}}(i.toString());if(null==e||null==t)throw new Error(`Invalid data URL format in part ${c}`);u=e,l=Ms(t)}else{const e=t[i.toString()];e?(l=e.data,null!=u||(u=e.mimeType)):l=i}else l=Ms(i);switch(c){case"image":return l instanceof Uint8Array&&(u=null!=(r=function({data:e,signatures:t}){const n=gs(e);for(const r of t)if("string"==typeof n?n.startsWith(r.base64Prefix):n.length>=r.bytesPrefix.length&&r.bytesPrefix.every(((e,t)=>n[t]===e)))return r.mimeType}({data:l,signatures:ms}))?r:u),{type:"image",image:l,mimeType:u,providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"file":if(null==u)throw new Error("Mime type is missing for file part");return{type:"file",data:l instanceof Uint8Array?Ps(l):l,filename:e.filename,mimeType:u,providerMetadata:null!=(o=e.providerOptions)?o:e.experimental_providerMetadata}}}(e,t))).filter((e=>"text"!==e.type||""!==e.text)),providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"assistant":return"string"==typeof e.content?{role:"assistant",content:[{type:"text",text:e.content}],providerMetadata:null!=(o=e.providerOptions)?o:e.experimental_providerMetadata}:{role:"assistant",content:e.content.filter((e=>"text"!==e.type||""!==e.text)).map((e=>{var t;const n=null!=(t=e.providerOptions)?t:e.experimental_providerMetadata;switch(e.type){case"file":return{type:"file",data:e.data instanceof URL?e.data:Ps(e.data),filename:e.filename,mimeType:e.mimeType,providerMetadata:n};case"reasoning":return{type:"reasoning",text:e.text,signature:e.signature,providerMetadata:n};case"redacted-reasoning":return{type:"redacted-reasoning",data:e.data,providerMetadata:n};case"text":return{type:"text",text:e.text,providerMetadata:n};case"tool-call":return{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,args:e.args,providerMetadata:n}}})),providerMetadata:null!=(a=e.providerOptions)?a:e.experimental_providerMetadata};case"tool":return{role:"tool",content:e.content.map((e=>{var t;return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result,content:e.experimental_content,isError:e.isError,providerMetadata:null!=(t=e.providerOptions)?t:e.experimental_providerMetadata}})),providerMetadata:null!=(i=e.providerOptions)?i:e.experimental_providerMetadata};default:throw new Fs({role:l})}}(e,s)))]}function Us({maxTokens:e,temperature:t,topP:n,topK:r,presencePenalty:s,frequencyPenalty:o,stopSequences:a,seed:i}){if(null!=e){if(!Number.isInteger(e))throw new Yr({parameter:"maxTokens",value:e,message:"maxTokens must be an integer"});if(e<1)throw new Yr({parameter:"maxTokens",value:e,message:"maxTokens must be >= 1"})}if(null!=t&&"number"!=typeof t)throw new Yr({parameter:"temperature",value:t,message:"temperature must be a number"});if(null!=n&&"number"!=typeof n)throw new Yr({parameter:"topP",value:n,message:"topP must be a number"});if(null!=r&&"number"!=typeof r)throw new Yr({parameter:"topK",value:r,message:"topK must be a number"});if(null!=s&&"number"!=typeof s)throw new Yr({parameter:"presencePenalty",value:s,message:"presencePenalty must be a number"});if(null!=o&&"number"!=typeof o)throw new Yr({parameter:"frequencyPenalty",value:o,message:"frequencyPenalty must be a number"});if(null!=i&&!Number.isInteger(i))throw new Yr({parameter:"seed",value:i,message:"seed must be an integer"});return{maxTokens:e,temperature:null!=t?t:0,topP:n,topK:r,presencePenalty:s,frequencyPenalty:o,stopSequences:null!=a&&a.length>0?a:void 0,seed:i}}function Bs(e){var t,n,r;const s=[];for(const a of e){let e;try{e=new URL(a.url)}catch(o){throw new Error(`Invalid URL: ${a.url}`)}switch(e.protocol){case"http:":case"https:":if(null==(t=a.contentType)?void 0:t.startsWith("image/"))s.push({type:"image",image:e});else{if(!a.contentType)throw new Error("If the attachment is not an image, it must specify a content type");s.push({type:"file",data:e,mimeType:a.contentType})}break;case"data:":{let e,t,i;try{[e,t]=a.url.split(","),i=e.split(";")[0].split(":")[1]}catch(o){throw new Error(`Error processing data URL: ${a.url}`)}if(null==i||null==t)throw new Error(`Invalid data URL format: ${a.url}`);if(null==(n=a.contentType)?void 0:n.startsWith("image/"))s.push({type:"image",image:Ms(t)});else if(null==(r=a.contentType)?void 0:r.startsWith("text/"))s.push({type:"text",text:Os(Ms(t))});else{if(!a.contentType)throw new Error("If the attachment is not an image or text, it must specify a content type");s.push({type:"file",data:t,mimeType:a.contentType})}break}default:throw new Error(`Unsupported URL protocol: ${e.protocol}`)}}return s}js=qs;var Vs,Zs="AI_MessageConversionError",Js=`vercel.ai.error.${Zs}`,Hs=Symbol.for(Js),Ws=class extends F{constructor({originalMessage:e,message:t}){super({name:Zs,message:t}),this[Vs]=!0,this.originalMessage=e}static isInstance(e){return F.hasMarker(e,Js)}};Vs=Hs;var zs=f((()=>d([b(),o(),a(),c(),m(o(),zs),s(zs)]))),Ks=m(o(),m(o(),zs)),Gs=s(d([r({type:l("text"),text:o()}),r({type:l("image"),data:o(),mimeType:o().optional()})])),Ys=r({type:l("text"),text:o(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),Xs=r({type:l("image"),image:d([Ns,y(URL)]),mimeType:o().optional(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),Qs=r({type:l("file"),data:d([Ns,y(URL)]),filename:o().optional(),mimeType:o(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),eo=r({type:l("reasoning"),text:o(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),to=r({type:l("redacted-reasoning"),data:o(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),no=r({type:l("tool-call"),toolCallId:o(),toolName:o(),args:v(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),ro=r({type:l("tool-result"),toolCallId:o(),toolName:o(),result:v(),content:Gs.optional(),isError:c().optional(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),so=r({role:l("system"),content:o(),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),oo=r({role:l("user"),content:d([o(),s(d([Ys,Xs,Qs]))]),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),ao=r({role:l("assistant"),content:d([o(),s(d([Ys,Qs,eo,to,no]))]),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),io=r({role:l("tool"),content:s(ro),providerOptions:Ks.optional(),experimental_providerMetadata:Ks.optional()}),lo=d([so,oo,ao,io]);function uo({prompt:e,tools:t}){if(null==e.prompt&&null==e.messages)throw new oe({prompt:e,message:"prompt or messages must be defined"});if(null!=e.prompt&&null!=e.messages)throw new oe({prompt:e,message:"prompt and messages cannot be defined at the same time"});if(null!=e.system&&"string"!=typeof e.system)throw new oe({prompt:e,message:"system must be a string"});if(null!=e.prompt){if("string"!=typeof e.prompt)throw new oe({prompt:e,message:"prompt must be a string"});return{type:"prompt",system:e.system,messages:[{role:"user",content:e.prompt}]}}if(null!=e.messages){const n=function(e){if(!Array.isArray(e))throw new oe({prompt:e,message:["messages must be an array of CoreMessage or UIMessage",`Received non-array value: ${JSON.stringify(e)}`].join("\n"),cause:e});if(0===e.length)return"messages";const t=e.map(co);if(t.some((e=>"has-ui-specific-parts"===e)))return"ui-messages";const n=t.findIndex((e=>"has-core-specific-parts"!==e&&"message"!==e));if(-1===n)return"messages";throw new oe({prompt:e,message:["messages must be an array of CoreMessage or UIMessage",`Received message of type: "${t[n]}" at index ${n}`,`messages[${n}]: ${JSON.stringify(e[n])}`].join("\n"),cause:e})}(e.messages),r="ui-messages"===n?function(e,t){var n,r;const s=null!=(n=null==t?void 0:t.tools)?n:{},o=[];for(let a=0;a<e.length;a++){const t=e[a],n=a===e.length-1,{role:i,content:l,experimental_attachments:u}=t;switch(i){case"system":o.push({role:"system",content:l});break;case"user":if(null==t.parts)o.push({role:"user",content:u?[{type:"text",text:l},...Bs(u)]:l});else{const e=t.parts.filter((e=>"text"===e.type)).map((e=>({type:"text",text:e.text})));o.push({role:"user",content:u?[...e,...Bs(u)]:e})}break;case"assistant":{if(null!=t.parts){let e=function(){const e=[];for(const t of i)switch(t.type){case"file":case"text":e.push(t);break;case"reasoning":for(const n of t.details)switch(n.type){case"text":e.push({type:"reasoning",text:n.text,signature:n.signature});break;case"redacted":e.push({type:"redacted-reasoning",data:n.data})}break;case"tool-invocation":e.push({type:"tool-call",toolCallId:t.toolInvocation.toolCallId,toolName:t.toolInvocation.toolName,args:t.toolInvocation.args});break;default:throw new Error(`Unsupported part: ${t}`)}o.push({role:"assistant",content:e});const r=i.filter((e=>"tool-invocation"===e.type)).map((e=>e.toolInvocation));r.length>0&&o.push({role:"tool",content:r.map((e=>{if(!("result"in e))throw new Ws({originalMessage:t,message:"ToolInvocation must have a result: "+JSON.stringify(e)});const{toolCallId:n,toolName:r,result:o}=e,a=s[r];return null!=(null==a?void 0:a.experimental_toToolResultContent)?{type:"tool-result",toolCallId:n,toolName:r,result:a.experimental_toToolResultContent(o),experimental_content:a.experimental_toToolResultContent(o)}:{type:"tool-result",toolCallId:n,toolName:r,result:o}}))}),i=[],a=!1,n++},n=0,a=!1,i=[];for(const s of t.parts)switch(s.type){case"text":a&&e(),i.push(s);break;case"file":case"reasoning":i.push(s);break;case"tool-invocation":(null!=(r=s.toolInvocation.step)?r:0)!==n&&e(),i.push(s),a=!0}e();break}const e=t.toolInvocations;if(null==e||0===e.length){o.push({role:"assistant",content:l});break}const a=e.reduce(((e,t)=>{var n;return Math.max(e,null!=(n=t.step)?n:0)}),0);for(let r=0;r<=a;r++){const a=e.filter((e=>{var t;return(null!=(t=e.step)?t:0)===r}));0!==a.length&&(o.push({role:"assistant",content:[...n&&l&&0===r?[{type:"text",text:l}]:[],...a.map((({toolCallId:e,toolName:t,args:n})=>({type:"tool-call",toolCallId:e,toolName:t,args:n})))]}),o.push({role:"tool",content:a.map((e=>{if(!("result"in e))throw new Ws({originalMessage:t,message:"ToolInvocation must have a result: "+JSON.stringify(e)});const{toolCallId:n,toolName:r,result:o}=e,a=s[r];return null!=(null==a?void 0:a.experimental_toToolResultContent)?{type:"tool-result",toolCallId:n,toolName:r,result:a.experimental_toToolResultContent(o),experimental_content:a.experimental_toToolResultContent(o)}:{type:"tool-result",toolCallId:n,toolName:r,result:o}}))}))}l&&!n&&o.push({role:"assistant",content:l});break}case"data":break;default:throw new Ws({originalMessage:t,message:`Unsupported role: ${i}`})}}return o}(e.messages,{tools:t}):e.messages;if(0===r.length)throw new oe({prompt:e,message:"messages must not be empty"});const o=Ke({value:r,schema:s(lo)});if(!o.success)throw new oe({prompt:e,message:["message must be a CoreMessage or a UI message",`Validation error: ${o.error.message}`].join("\n"),cause:o.error});return{type:"messages",messages:r,system:e.system}}throw new Error("unreachable")}function co(e){return"object"==typeof e&&null!==e&&("function"===e.role||"data"===e.role||"toolInvocations"in e||"parts"in e||"experimental_attachments"in e)?"has-ui-specific-parts":"object"==typeof e&&null!==e&&"content"in e&&(Array.isArray(e.content)||"experimental_providerMetadata"in e||"providerOptions"in e)?"has-core-specific-parts":"object"==typeof e&&null!==e&&"role"in e&&"content"in e&&"string"==typeof e.content&&["system","user","assistant","tool"].includes(e.role)?"message":"other"}function po({promptTokens:e,completionTokens:t}){return{promptTokens:e,completionTokens:t,totalTokens:e+t}}function mo(e){const t=e.pipeThrough(new TransformStream);return t[Symbol.asyncIterator]=()=>{const e=t.getReader();return{async next(){const{done:t,value:n}=await e.read();return t?{done:!0,value:void 0}:{done:!1,value:n}}}},t}function go(e){return"image"===e.type?{...e,image:e.image instanceof Uint8Array?Ps(e.image):e.image}:e}Ve({prefix:"aiobj",size:24});var ho=class{constructor(){this.status={type:"pending"},this._resolve=void 0,this._reject=void 0}get value(){return this.promise||(this.promise=new Promise(((e,t)=>{"resolved"===this.status.type?e(this.status.value):"rejected"===this.status.type&&t(this.status.error),this._resolve=e,this._reject=t}))),this.promise}resolve(e){var t;this.status={type:"resolved",value:e},this.promise&&(null==(t=this._resolve)||t.call(this,e))}reject(e){var t;this.status={type:"rejected",error:e},this.promise&&(null==(t=this._reject)||t.call(this,e))}};function fo(){let e,t;return{promise:new Promise(((n,r)=>{e=n,t=r})),resolve:e,reject:t}}function yo(){var e,t;return null!=(t=null==(e=null==globalThis?void 0:globalThis.performance)?void 0:e.now())?t:Date.now()}Ve({prefix:"aiobj",size:24});var vo,bo="AI_NoOutputSpecifiedError",wo=`vercel.ai.error.${bo}`,_o=Symbol.for(wo),xo=class extends F{constructor({message:e="No output specified."}={}){super({name:bo,message:e}),this[vo]=!0}static isInstance(e){return F.hasMarker(e,wo)}};vo=_o;var ko,To="AI_ToolExecutionError",So=`vercel.ai.error.${To}`,Io=Symbol.for(So),Eo=class extends F{constructor({toolArgs:e,toolName:t,toolCallId:n,cause:r,message:s=`Error executing tool ${t}: ${K(r)}`}){super({name:To,message:s,cause:r}),this[ko]=!0,this.toolArgs=e,this.toolName=t,this.toolCallId=n}static isInstance(e){return F.hasMarker(e,So)}};function Ro({tools:e,toolChoice:t,activeTools:n}){if(!(null!=(r=e)&&Object.keys(r).length>0))return{tools:void 0,toolChoice:void 0};var r;return{tools:(null!=n?Object.entries(e).filter((([e])=>n.includes(e))):Object.entries(e)).map((([e,t])=>{const n=t.type;switch(n){case void 0:case"function":return{type:"function",name:e,description:t.description,parameters:Wn(t.parameters).jsonSchema};case"provider-defined":return{type:"provider-defined",name:e,id:t.id,args:t.args};default:throw new Error(`Unsupported tool type: ${n}`)}})),toolChoice:null==t?{type:"auto"}:"string"==typeof t?{type:t}:{type:"tool",toolName:t.toolName}}}ko=Io;var Co=/^([\s\S]*?)(\s+)(\S*)$/;var Ao,No="AI_InvalidToolArgumentsError",Po=`vercel.ai.error.${No}`,Mo=Symbol.for(Po),Oo=class extends F{constructor({toolArgs:e,toolName:t,cause:n,message:r=`Invalid arguments for tool ${t}: ${K(n)}`}){super({name:No,message:r,cause:n}),this[Ao]=!0,this.toolArgs=e,this.toolName=t}static isInstance(e){return F.hasMarker(e,Po)}};Ao=Mo;var jo,Do="AI_NoSuchToolError",$o=`vercel.ai.error.${Do}`,qo=Symbol.for($o),Fo=class extends F{constructor({toolName:e,availableTools:t,message:n=`Model tried to call unavailable tool '${e}'. ${void 0===t?"No tools are available.":`Available tools: ${t.join(", ")}.`}`}){super({name:Do,message:n}),this[jo]=!0,this.toolName=e,this.availableTools=t}static isInstance(e){return F.hasMarker(e,$o)}};jo=qo;var Lo,Uo="AI_ToolCallRepairError",Bo=`vercel.ai.error.${Uo}`,Vo=Symbol.for(Bo),Zo=class extends F{constructor({cause:e,originalError:t,message:n=`Error repairing tool call: ${K(e)}`}){super({name:Uo,message:n,cause:e}),this[Lo]=!0,this.originalError=t}static isInstance(e){return F.hasMarker(e,Bo)}};async function Jo({toolCall:e,tools:t}){const n=e.toolName,r=t[n];if(null==r)throw new Fo({toolName:e.toolName,availableTools:Object.keys(t)});const s=Wn(r.parameters),o=""===e.args.trim()?Ke({value:{},schema:s}):Ye({text:e.args,schema:s});if(!1===o.success)throw new Oo({toolName:n,toolArgs:e.args,cause:o.error});return{type:"tool-call",toolCallId:e.toolCallId,toolName:n,args:o.value}}function Ho(e){const t=e.filter((e=>"text"===e.type)).map((e=>e.text)).join("");return t.length>0?t:void 0}function Wo({text:e="",files:t,reasoning:n,tools:r,toolCalls:s,toolResults:o,messageId:a,generateMessageId:i}){const l=[],u=[];return n.length>0&&u.push(...n.map((e=>"text"===e.type?{...e,type:"reasoning"}:{...e,type:"redacted-reasoning"}))),t.length>0&&u.push(...t.map((e=>({type:"file",data:e.base64,mimeType:e.mimeType})))),e.length>0&&u.push({type:"text",text:e}),s.length>0&&u.push(...s),u.length>0&&l.push({role:"assistant",content:u,id:a}),o.length>0&&l.push({role:"tool",id:i(),content:o.map((e=>{const t=r[e.toolName];return null!=(null==t?void 0:t.experimental_toToolResultContent)?{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:t.experimental_toToolResultContent(e.result),experimental_content:t.experimental_toToolResultContent(e.result)}:{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result}}))}),l}Lo=Vo,Ve({prefix:"aitxt",size:24}),Ve({prefix:"msg",size:24});Vr({},{object:()=>ea,text:()=>Qo});var zo,Ko="AI_InvalidStreamPartError",Go=`vercel.ai.error.${Ko}`,Yo=Symbol.for(Go),Xo=class extends F{constructor({chunk:e,message:t}){super({name:Ko,message:t}),this[zo]=!0,this.chunk=e}static isInstance(e){return F.hasMarker(e,Go)}};zo=Yo;var Qo=()=>({type:"text",responseFormat:()=>({type:"text"}),injectIntoSystemPrompt:({system:e})=>e,parsePartial:({text:e})=>({partial:e}),parseOutput:({text:e})=>e}),ea=({schema:e})=>{const t=Wn(e);return{type:"object",responseFormat:({model:e})=>({type:"json",schema:e.supportsStructuredOutputs?t.jsonSchema:void 0}),injectIntoSystemPrompt:({system:e,model:n})=>n.supportsStructuredOutputs?e:function({prompt:e,schema:t,schemaPrefix:n=(null!=t?"JSON schema:":void 0),schemaSuffix:r=(null!=t?"You MUST answer with a JSON object that matches the JSON schema above.":"You MUST answer with JSON.")}){return[null!=e&&e.length>0?e:void 0,null!=e&&e.length>0?"":void 0,n,null!=t?JSON.stringify(t):void 0,r].filter((e=>null!=e)).join("\n")}({prompt:e,schema:t.jsonSchema}),parsePartial({text:e}){const t=function(e){if(void 0===e)return{value:void 0,state:"undefined-input"};let t=Ye({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=Ye({text:Bn(e)}),t.success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"})}(e);switch(t.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:t.value};default:{const e=t.state;throw new Error(`Unsupported parse state: ${e}`)}}},parseOutput({text:e},n){const r=Ye({text:e});if(!r.success)throw new bs({message:"No object generated: could not parse the response.",cause:r.error,text:e,response:n.response,usage:n.usage,finishReason:n.finishReason});const s=Ke({value:r.value,schema:t});if(!s.success)throw new bs({message:"No object generated: response did not match schema.",cause:s.error,text:e,response:n.response,usage:n.usage,finishReason:n.finishReason});return s.value}}};function ta(e,t){const n=e.getReader(),r=t.getReader();let s,o,a=!1,i=!1;async function l(e){try{null==s&&(s=n.read());const t=await s;s=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}async function u(e){try{null==o&&(o=r.read());const t=await o;o=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}return new ReadableStream({async pull(e){try{if(a)return void(await u(e));if(i)return void(await l(e));null==s&&(s=n.read()),null==o&&(o=r.read());const{result:t,reader:c}=await Promise.race([s.then((e=>({result:e,reader:n}))),o.then((e=>({result:e,reader:r})))]);t.done||e.enqueue(t.value),c===n?(s=void 0,t.done&&(await u(e),a=!0)):(o=void 0,t.done&&(i=!0,await l(e)))}catch(t){e.error(t)}},cancel(){n.cancel(),r.cancel()}})}function na({tools:e,generatorStream:t,toolCallStreaming:n,tracer:r,telemetry:s,system:o,messages:a,abortSignal:i,repairToolCall:l}){let u=null;const c=new ReadableStream({start(e){u=e}}),p={},d=new Set;let m,g=!1;function h(){g&&0===d.size&&(null!=m&&u.enqueue(m),u.close())}const f=new TransformStream({async transform(t,c){const g=t.type;switch(g){case"text-delta":case"reasoning":case"reasoning-signature":case"redacted-reasoning":case"source":case"response-metadata":case"error":c.enqueue(t);break;case"file":c.enqueue(new ds({data:t.data,mimeType:t.mimeType}));break;case"tool-call-delta":n&&(p[t.toolCallId]||(c.enqueue({type:"tool-call-streaming-start",toolCallId:t.toolCallId,toolName:t.toolName}),p[t.toolCallId]=!0),c.enqueue({type:"tool-call-delta",toolCallId:t.toolCallId,toolName:t.toolName,argsTextDelta:t.argsTextDelta}));break;case"tool-call":try{const n=await async function({toolCall:e,tools:t,repairToolCall:n,system:r,messages:s}){if(null==t)throw new Fo({toolName:e.toolName});try{return await Jo({toolCall:e,tools:t})}catch(o){if(null==n||!Fo.isInstance(o)&&!Oo.isInstance(o))throw o;let i=null;try{i=await n({toolCall:e,tools:t,parameterSchema:({toolName:e})=>Wn(t[e].parameters).jsonSchema,system:r,messages:s,error:o})}catch(a){throw new Zo({cause:a,originalError:o})}if(null==i)throw o;return await Jo({toolCall:i,tools:t})}}({toolCall:t,tools:e,repairToolCall:l,system:o,messages:a});c.enqueue(n);const p=e[n.toolName];if(null!=p.execute){const e=Ze();d.add(e),us({name:"ai.toolCall",attributes:cs({telemetry:s,attributes:{...os({operationId:"ai.toolCall",telemetry:s}),"ai.toolCall.name":n.toolName,"ai.toolCall.id":n.toolCallId,"ai.toolCall.args":{output:()=>JSON.stringify(n.args)}}}),tracer:r,fn:async t=>p.execute(n.args,{toolCallId:n.toolCallId,messages:a,abortSignal:i}).then((r=>{u.enqueue({...n,type:"tool-result",result:r}),d.delete(e),h();try{t.setAttributes(cs({telemetry:s,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(r)}}}))}catch(o){}}),(t=>{u.enqueue({type:"error",error:new Eo({toolCallId:n.toolCallId,toolName:n.toolName,toolArgs:n.args,cause:t})}),d.delete(e),h()}))})}}catch(f){u.enqueue({type:"error",error:f})}break;case"finish":m={type:"finish",finishReason:t.finishReason,logprobs:t.logprobs,usage:po(t.usage),experimental_providerMetadata:t.providerMetadata};break;default:throw new Error(`Unhandled chunk type: ${g}`)}},flush(){g=!0,h()}});return new ReadableStream({start:async e=>Promise.all([t.pipeThrough(f).pipeTo(new WritableStream({write(t){e.enqueue(t)},close(){}})),c.pipeTo(new WritableStream({write(t){e.enqueue(t)},close(){e.close()}}))])})}var ra=Ve({prefix:"aitxt",size:24}),sa=Ve({prefix:"msg",size:24});var oa=class{constructor({model:e,telemetry:t,headers:n,settings:r,maxRetries:s,abortSignal:o,system:a,prompt:i,messages:l,tools:u,toolChoice:c,toolCallStreaming:p,transforms:d,activeTools:m,repairToolCall:g,maxSteps:h,output:f,continueSteps:y,providerOptions:v,now:b,currentDate:w,generateId:_,generateMessageId:x,onChunk:k,onError:T,onFinish:S,onStepFinish:I}){var E;if(this.warningsPromise=new ho,this.usagePromise=new ho,this.finishReasonPromise=new ho,this.providerMetadataPromise=new ho,this.textPromise=new ho,this.reasoningPromise=new ho,this.reasoningDetailsPromise=new ho,this.sourcesPromise=new ho,this.filesPromise=new ho,this.toolCallsPromise=new ho,this.toolResultsPromise=new ho,this.requestPromise=new ho,this.responsePromise=new ho,this.stepsPromise=new ho,h<1)throw new Yr({parameter:"maxSteps",value:h,message:"maxSteps must be at least 1"});this.output=f;let R,C="",A="",N="",P=[],M=[],O=[];const j=[],D={id:_(),timestamp:w(),modelId:e.modelId,messages:[]};let $,q,L=[],U=[],B="initial";const V=[];let Z;const J=new TransformStream({async transform(e,t){t.enqueue(e);const{part:n}=e;if("text-delta"!==n.type&&"reasoning"!==n.type&&"source"!==n.type&&"tool-call"!==n.type&&"tool-result"!==n.type&&"tool-call-streaming-start"!==n.type&&"tool-call-delta"!==n.type||await(null==k?void 0:k({chunk:n})),"error"===n.type&&await(null==T?void 0:T({error:n.error})),"text-delta"===n.type&&(C+=n.textDelta,A+=n.textDelta,N+=n.textDelta),"reasoning"===n.type&&(null==R?(R={type:"text",text:n.textDelta},P.push(R)):R.text+=n.textDelta),"reasoning-signature"===n.type){if(null==R)throw new F({name:"InvalidStreamPart",message:"reasoning-signature without reasoning"});R.signature=n.signature,R=void 0}if("redacted-reasoning"===n.type&&P.push({type:"redacted",data:n.data}),"file"===n.type&&M.push(n),"source"===n.type&&(j.push(n.source),O.push(n.source)),"tool-call"===n.type&&L.push(n),"tool-result"===n.type&&U.push(n),"step-finish"===n.type){const e=Wo({text:A,files:M,reasoning:P,tools:null!=u?u:{},toolCalls:L,toolResults:U,messageId:n.messageId,generateMessageId:x});let t="done";V.length+1<h&&(y&&"length"===n.finishReason&&0===L.length?t="continue":L.length>0&&U.length===L.length&&(t="tool-result"));const r={stepType:B,text:C,reasoning:Ho(P),reasoningDetails:P,files:M,sources:O,toolCalls:L,toolResults:U,finishReason:n.finishReason,usage:n.usage,warnings:n.warnings,logprobs:n.logprobs,request:n.request,response:{...n.response,messages:[...D.messages,...e]},providerMetadata:n.experimental_providerMetadata,experimental_providerMetadata:n.experimental_providerMetadata,isContinued:n.isContinued};await(null==I?void 0:I(r)),V.push(r),L=[],U=[],C="",O=[],P=[],M=[],R=void 0,"done"!==t&&(B=t),"continue"!==t&&(D.messages.push(...e),A="")}"finish"===n.type&&(D.id=n.response.id,D.timestamp=n.response.timestamp,D.modelId=n.response.modelId,D.headers=n.response.headers,q=n.usage,$=n.finishReason)},async flush(e){var n;try{if(0===V.length)return;const e=V[V.length-1];Q.warningsPromise.resolve(e.warnings),Q.requestPromise.resolve(e.request),Q.responsePromise.resolve(e.response),Q.toolCallsPromise.resolve(e.toolCalls),Q.toolResultsPromise.resolve(e.toolResults),Q.providerMetadataPromise.resolve(e.experimental_providerMetadata),Q.reasoningPromise.resolve(e.reasoning),Q.reasoningDetailsPromise.resolve(e.reasoningDetails);const r=null!=$?$:"unknown",s=null!=q?q:{completionTokens:NaN,promptTokens:NaN,totalTokens:NaN};Q.finishReasonPromise.resolve(r),Q.usagePromise.resolve(s),Q.textPromise.resolve(N),Q.sourcesPromise.resolve(j),Q.filesPromise.resolve(e.files),Q.stepsPromise.resolve(V),await(null==S?void 0:S({finishReason:r,logprobs:void 0,usage:s,text:N,reasoning:e.reasoning,reasoningDetails:e.reasoningDetails,files:e.files,sources:e.sources,toolCalls:e.toolCalls,toolResults:e.toolResults,request:null!=(n=e.request)?n:{},response:e.response,warnings:e.warnings,providerMetadata:e.providerMetadata,experimental_providerMetadata:e.experimental_providerMetadata,steps:V})),Z.setAttributes(cs({telemetry:t,attributes:{"ai.response.finishReason":r,"ai.response.text":{output:()=>N},"ai.response.toolCalls":{output:()=>{var t;return(null==(t=e.toolCalls)?void 0:t.length)?JSON.stringify(e.toolCalls):void 0}},"ai.usage.promptTokens":s.promptTokens,"ai.usage.completionTokens":s.completionTokens}}))}catch(r){e.error(r)}finally{Z.end()}}}),H=function(){let e=[],t=null,n=!1,r=fo();const s=async()=>{if(n&&0===e.length)null==t||t.close();else{if(0===e.length)return r=fo(),await r.promise,s();try{const{value:r,done:o}=await e[0].read();o?(e.shift(),e.length>0?await s():n&&(null==t||t.close())):null==t||t.enqueue(r)}catch(o){null==t||t.error(o),e.shift(),n&&0===e.length&&(null==t||t.close())}}};return{stream:new ReadableStream({start(e){t=e},pull:s,async cancel(){for(const t of e)await t.cancel();e=[],n=!0}}),addStream:t=>{if(n)throw new Error("Cannot add inner stream: outer stream is closed");e.push(t.getReader()),r.resolve()},close:()=>{n=!0,r.resolve(),0===e.length&&(null==t||t.close())},terminate:()=>{n=!0,r.resolve(),e.forEach((e=>e.cancel())),e=[],null==t||t.close()}}}();this.addStream=H.addStream,this.closeStream=H.close;let W=H.stream;for(const F of d)W=W.pipeThrough(F({tools:u,stopStream(){H.terminate()}}));this.baseStream=W.pipeThrough(function(e){if(!e)return new TransformStream({transform(e,t){t.enqueue({part:e,partialOutput:void 0})}});let t="",n="",r="";function s({controller:e,partialOutput:t}){e.enqueue({part:{type:"text-delta",textDelta:n},partialOutput:t}),n=""}return new TransformStream({transform(o,a){if("step-finish"===o.type&&s({controller:a}),"text-delta"!==o.type)return void a.enqueue({part:o,partialOutput:void 0});t+=o.textDelta,n+=o.textDelta;const i=e.parsePartial({text:t});if(null!=i){const e=JSON.stringify(i.partial);e!==r&&(s({controller:a,partialOutput:i.partial}),r=e)}},flush(e){n.length>0&&s({controller:e})}})}(f)).pipeThrough(J);const{maxRetries:z,retry:K}=function({maxRetries:e}){if(null!=e){if(!Number.isInteger(e))throw new Yr({parameter:"maxRetries",value:e,message:"maxRetries must be an integer"});if(e<0)throw new Yr({parameter:"maxRetries",value:e,message:"maxRetries must be >= 0"})}const t=null!=e?e:2;return{maxRetries:t,retry:rs({maxRetries:t})}}({maxRetries:s}),G=function({isEnabled:e=!1,tracer:t}={}){return e?t||Ur.getTracer("ai"):as}(t),Y=function({model:e,settings:t,telemetry:n,headers:r}){var s;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce(((e,[t,n])=>(e[`ai.settings.${t}`]=n,e)),{}),...Object.entries(null!=(s=null==n?void 0:n.metadata)?s:{}).reduce(((e,[t,n])=>(e[`ai.telemetry.metadata.${t}`]=n,e)),{}),...Object.entries(null!=r?r:{}).reduce(((e,[t,n])=>(void 0!==n&&(e[`ai.request.headers.${t}`]=n),e)),{})}}({model:e,telemetry:t,headers:n,settings:{...r,maxRetries:z}}),X=uo({prompt:{system:null!=(E=null==f?void 0:f.injectIntoSystemPrompt({system:a,model:e}))?E:a,prompt:i,messages:l},tools:u}),Q=this;us({name:"ai.streamText",attributes:cs({telemetry:t,attributes:{...os({operationId:"ai.streamText",telemetry:t}),...Y,"ai.prompt":{input:()=>JSON.stringify({system:a,prompt:i,messages:l})},"ai.settings.maxSteps":h}}),tracer:G,endWhenDone:!1,fn:async s=>{Z=s,await async function s({currentStep:i,responseMessages:l,usage:d,stepType:k,previousStepText:T,hasLeadingWhitespace:S,messageId:I}){var E;const R=0===l.length?X.type:"messages",C=[...X.messages,...l],A=await Ls({prompt:{type:R,system:X.system,messages:C},modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:null==(E=e.supportsUrl)?void 0:E.bind(e)}),N={type:"regular",...Ro({tools:u,toolChoice:c,activeTools:m})},{result:{stream:P,warnings:M,rawResponse:O,request:j},doStreamSpan:D,startTimestampMs:$}=await K((()=>us({name:"ai.streamText.doStream",attributes:cs({telemetry:t,attributes:{...os({operationId:"ai.streamText.doStream",telemetry:t}),...Y,"ai.prompt.format":{input:()=>R},"ai.prompt.messages":{input:()=>function(e){const t=e.map((e=>({...e,content:"string"==typeof e.content?e.content:e.content.map(go)})));return JSON.stringify(t)}(A)},"ai.prompt.tools":{input:()=>{var e;return null==(e=N.tools)?void 0:e.map((e=>JSON.stringify(e)))}},"ai.prompt.toolChoice":{input:()=>null!=N.toolChoice?JSON.stringify(N.toolChoice):void 0},"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":r.frequencyPenalty,"gen_ai.request.max_tokens":r.maxTokens,"gen_ai.request.presence_penalty":r.presencePenalty,"gen_ai.request.stop_sequences":r.stopSequences,"gen_ai.request.temperature":r.temperature,"gen_ai.request.top_k":r.topK,"gen_ai.request.top_p":r.topP}}),tracer:G,endWhenDone:!1,fn:async t=>({startTimestampMs:b(),doStreamSpan:t,result:await e.doStream({mode:N,...Us(r),inputFormat:R,responseFormat:null==f?void 0:f.responseFormat({model:e}),prompt:A,providerMetadata:v,abortSignal:o,headers:n})})}))),q=na({tools:u,generatorStream:P,toolCallStreaming:p,tracer:G,telemetry:t,system:a,messages:C,repairToolCall:g,abortSignal:o}),F=null!=j?j:{},L=[],U=[],B=[],V=[];let Z,J,H,W="unknown",z={promptTokens:0,completionTokens:0,totalTokens:0},ee=!0,te="",ne="continue"===k?T:"",re={id:_(),timestamp:w(),modelId:e.modelId},se="",oe=!1,ae=!0,ie=!1;async function le({controller:e,chunk:t}){e.enqueue(t),te+=t.textDelta,ne+=t.textDelta,oe=!0,ie=t.textDelta.trimEnd()!==t.textDelta}Q.addStream(q.pipeThrough(new TransformStream({async transform(e,t){var n,r,s;if(ee){const e=b()-$;ee=!1,D.addEvent("ai.stream.firstChunk",{"ai.response.msToFirstChunk":e}),D.setAttributes({"ai.response.msToFirstChunk":e}),t.enqueue({type:"step-start",messageId:I,request:F,warnings:null!=M?M:[]})}if("text-delta"===e.type&&0===e.textDelta.length)return;const o=e.type;switch(o){case"text-delta":if(y){const n=ae&&S?e.textDelta.trimStart():e.textDelta;if(0===n.length)break;ae=!1,se+=n;const r=function(e){const t=e.match(Co);return t?{prefix:t[1],whitespace:t[2],suffix:t[3]}:void 0}(se);null!=r&&(se=r.suffix,await le({controller:t,chunk:{type:"text-delta",textDelta:r.prefix+r.whitespace}}))}else await le({controller:t,chunk:e});break;case"reasoning":t.enqueue(e),null==Z?(Z={type:"text",text:e.textDelta},B.push(Z)):Z.text+=e.textDelta;break;case"reasoning-signature":if(t.enqueue(e),null==Z)throw new Xo({chunk:e,message:"reasoning-signature without reasoning"});Z.signature=e.signature,Z=void 0;break;case"redacted-reasoning":t.enqueue(e),B.push({type:"redacted",data:e.data});break;case"tool-call":t.enqueue(e),L.push(e);break;case"tool-result":t.enqueue(e),U.push(e);break;case"response-metadata":re={id:null!=(n=e.id)?n:re.id,timestamp:null!=(r=e.timestamp)?r:re.timestamp,modelId:null!=(s=e.modelId)?s:re.modelId};break;case"finish":{z=e.usage,W=e.finishReason,J=e.experimental_providerMetadata,H=e.logprobs;const t=b()-$;D.addEvent("ai.stream.finish"),D.setAttributes({"ai.response.msToFinish":t,"ai.response.avgCompletionTokensPerSecond":1e3*z.completionTokens/t});break}case"file":V.push(e),t.enqueue(e);break;case"source":case"tool-call-streaming-start":case"tool-call-delta":t.enqueue(e);break;case"error":t.enqueue(e),W="error";break;default:throw new Error(`Unknown chunk type: ${o}`)}},async flush(e){const n=L.length>0?JSON.stringify(L):void 0;let r="done";i+1<h&&(y&&"length"===W&&0===L.length?r="continue":L.length>0&&U.length===L.length&&(r="tool-result")),y&&se.length>0&&("continue"!==r||"continue"===k&&!oe)&&(await le({controller:e,chunk:{type:"text-delta",textDelta:se}}),se="");try{D.setAttributes(cs({telemetry:t,attributes:{"ai.response.finishReason":W,"ai.response.text":{output:()=>te},"ai.response.toolCalls":{output:()=>n},"ai.response.id":re.id,"ai.response.model":re.modelId,"ai.response.timestamp":re.timestamp.toISOString(),"ai.usage.promptTokens":z.promptTokens,"ai.usage.completionTokens":z.completionTokens,"gen_ai.response.finish_reasons":[W],"gen_ai.response.id":re.id,"gen_ai.response.model":re.modelId,"gen_ai.usage.input_tokens":z.promptTokens,"gen_ai.usage.output_tokens":z.completionTokens}}))}catch(p){}finally{D.end()}e.enqueue({type:"step-finish",finishReason:W,usage:z,providerMetadata:J,experimental_providerMetadata:J,logprobs:H,request:F,response:{...re,headers:null==O?void 0:O.headers},warnings:M,isContinued:"continue"===r,messageId:I});const o=(c=z,{promptTokens:(a=d).promptTokens+c.promptTokens,completionTokens:a.completionTokens+c.completionTokens,totalTokens:a.totalTokens+c.totalTokens});var a,c;if("done"===r)e.enqueue({type:"finish",finishReason:W,usage:o,providerMetadata:J,experimental_providerMetadata:J,logprobs:H,response:{...re,headers:null==O?void 0:O.headers}}),Q.closeStream();else{if("continue"===k){const e=l[l.length-1];"string"==typeof e.content?e.content+=te:e.content.push({text:te,type:"text"})}else l.push(...Wo({text:te,files:V,reasoning:B,tools:null!=u?u:{},toolCalls:L,toolResults:U,messageId:I,generateMessageId:x}));await s({currentStep:i+1,responseMessages:l,usage:o,stepType:r,previousStepText:ne,hasLeadingWhitespace:ie,messageId:"continue"===r?I:x()})}}})))}({currentStep:0,responseMessages:[],usage:{promptTokens:0,completionTokens:0,totalTokens:0},previousStepText:"",stepType:"initial",hasLeadingWhitespace:!1,messageId:x()})}}).catch((e=>{Q.addStream(new ReadableStream({start(t){t.enqueue({type:"error",error:e}),t.close()}})),Q.closeStream()}))}get warnings(){return this.warningsPromise.value}get usage(){return this.usagePromise.value}get finishReason(){return this.finishReasonPromise.value}get experimental_providerMetadata(){return this.providerMetadataPromise.value}get providerMetadata(){return this.providerMetadataPromise.value}get text(){return this.textPromise.value}get reasoning(){return this.reasoningPromise.value}get reasoningDetails(){return this.reasoningDetailsPromise.value}get sources(){return this.sourcesPromise.value}get files(){return this.filesPromise.value}get toolCalls(){return this.toolCallsPromise.value}get toolResults(){return this.toolResultsPromise.value}get request(){return this.requestPromise.value}get response(){return this.responsePromise.value}get steps(){return this.stepsPromise.value}teeStream(){const[e,t]=this.baseStream.tee();return this.baseStream=t,e}get textStream(){return mo(this.teeStream().pipeThrough(new TransformStream({transform({part:e},t){"text-delta"===e.type&&t.enqueue(e.textDelta)}})))}get fullStream(){return mo(this.teeStream().pipeThrough(new TransformStream({transform({part:e},t){t.enqueue(e)}})))}async consumeStream(e){var t;try{await async function({stream:e,onError:t}){const n=e.getReader();try{for(;;){const{done:e}=await n.read();if(e)break}}catch(r){null==t||t(r)}finally{n.releaseLock()}}({stream:this.fullStream,onError:null==e?void 0:e.onError})}catch(n){null==(t=null==e?void 0:e.onError)||t.call(e,n)}}get experimental_partialOutputStream(){if(null==this.output)throw new xo;return mo(this.teeStream().pipeThrough(new TransformStream({transform({partialOutput:e},t){null!=e&&t.enqueue(e)}})))}toDataStreamInternal({getErrorMessage:e=()=>"An error occurred.",sendUsage:t=!0,sendReasoning:n=!1,sendSources:r=!1,experimental_sendFinish:s=!0}){return this.fullStream.pipeThrough(new TransformStream({transform:async(o,a)=>{const i=o.type;switch(i){case"text-delta":a.enqueue(Zn("text",o.textDelta));break;case"reasoning":n&&a.enqueue(Zn("reasoning",o.textDelta));break;case"redacted-reasoning":n&&a.enqueue(Zn("redacted_reasoning",{data:o.data}));break;case"reasoning-signature":n&&a.enqueue(Zn("reasoning_signature",{signature:o.signature}));break;case"file":a.enqueue(Zn("file",{mimeType:o.mimeType,data:o.base64}));break;case"source":r&&a.enqueue(Zn("source",o.source));break;case"tool-call-streaming-start":a.enqueue(Zn("tool_call_streaming_start",{toolCallId:o.toolCallId,toolName:o.toolName}));break;case"tool-call-delta":a.enqueue(Zn("tool_call_delta",{toolCallId:o.toolCallId,argsTextDelta:o.argsTextDelta}));break;case"tool-call":a.enqueue(Zn("tool_call",{toolCallId:o.toolCallId,toolName:o.toolName,args:o.args}));break;case"tool-result":a.enqueue(Zn("tool_result",{toolCallId:o.toolCallId,result:o.result}));break;case"error":a.enqueue(Zn("error",e(o.error)));break;case"step-start":a.enqueue(Zn("start_step",{messageId:o.messageId}));break;case"step-finish":a.enqueue(Zn("finish_step",{finishReason:o.finishReason,usage:t?{promptTokens:o.usage.promptTokens,completionTokens:o.usage.completionTokens}:void 0,isContinued:o.isContinued}));break;case"finish":s&&a.enqueue(Zn("finish_message",{finishReason:o.finishReason,usage:t?{promptTokens:o.usage.promptTokens,completionTokens:o.usage.completionTokens}:void 0}));break;default:throw new Error(`Unknown chunk type: ${i}`)}}}))}pipeDataStreamToResponse(e,{status:t,statusText:n,headers:r,data:s,getErrorMessage:o,sendUsage:a,sendReasoning:i,sendSources:l,experimental_sendFinish:u}={}){Hr({response:e,status:t,statusText:n,headers:Jr(r,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"}),stream:this.toDataStream({data:s,getErrorMessage:o,sendUsage:a,sendReasoning:i,sendSources:l,experimental_sendFinish:u})})}pipeTextStreamToResponse(e,t){Hr({response:e,status:null==t?void 0:t.status,statusText:null==t?void 0:t.statusText,headers:Jr(null==t?void 0:t.headers,{contentType:"text/plain; charset=utf-8"}),stream:this.textStream.pipeThrough(new TextEncoderStream)})}toDataStream(e){const t=this.toDataStreamInternal({getErrorMessage:null==e?void 0:e.getErrorMessage,sendUsage:null==e?void 0:e.sendUsage,sendReasoning:null==e?void 0:e.sendReasoning,sendSources:null==e?void 0:e.sendSources,experimental_sendFinish:null==e?void 0:e.experimental_sendFinish}).pipeThrough(new TextEncoderStream);return(null==e?void 0:e.data)?ta(null==e?void 0:e.data.stream,t):t}mergeIntoDataStream(e,t){e.merge(this.toDataStreamInternal({getErrorMessage:e.onError,sendUsage:null==t?void 0:t.sendUsage,sendReasoning:null==t?void 0:t.sendReasoning,sendSources:null==t?void 0:t.sendSources,experimental_sendFinish:null==t?void 0:t.experimental_sendFinish}))}toDataStreamResponse({headers:e,status:t,statusText:n,data:r,getErrorMessage:s,sendUsage:o,sendReasoning:a,sendSources:i,experimental_sendFinish:l}={}){return new Response(this.toDataStream({data:r,getErrorMessage:s,sendUsage:o,sendReasoning:a,sendSources:i,experimental_sendFinish:l}),{status:t,statusText:n,headers:Zr(e,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}toTextStreamResponse(e){var t;return new Response(this.textStream.pipeThrough(new TextEncoderStream),{status:null!=(t=null==e?void 0:e.status)?t:200,headers:Zr(null==e?void 0:e.headers,{contentType:"text/plain; charset=utf-8"})})}},aa=r({name:o(),version:o()}).passthrough(),ia=r({_meta:_(r({}).passthrough())}).passthrough(),la=ia,ua=r({method:o(),params:_(ia)}),ca=r({experimental:_(r({}).passthrough()),logging:_(r({}).passthrough()),prompts:_(r({listChanged:_(c())}).passthrough()),resources:_(r({subscribe:_(c()),listChanged:_(c())}).passthrough()),tools:_(r({listChanged:_(c())}).passthrough())}).passthrough();la.extend({protocolVersion:o(),capabilities:ca,serverInfo:aa,instructions:_(o())});var pa=la.extend({nextCursor:_(o())}),da=r({name:o(),description:_(o()),inputSchema:r({type:l("object"),properties:_(r({}).passthrough())}).passthrough()}).passthrough();pa.extend({tools:s(da)});var ma=r({type:l("text"),text:o()}).passthrough(),ga=r({type:l("image"),data:o().base64(),mimeType:o()}).passthrough(),ha=r({uri:o(),mimeType:_(o())}).passthrough(),fa=ha.extend({text:o()}),ya=ha.extend({blob:o().base64()}),va=r({type:l("resource"),resource:d([fa,ya])}).passthrough();la.extend({content:s(d([ma,ga,va])),isError:c().default(!1).optional()}).or(la.extend({toolResult:v()}));var ba="2.0",wa=r({jsonrpc:l(ba),id:d([o(),a().int()])}).merge(ua).strict(),_a=r({jsonrpc:l(ba),id:d([o(),a().int()]),result:la}).strict(),xa=r({jsonrpc:l(ba),id:d([o(),a().int()]),error:r({code:a().int(),message:o(),data:_(v())})}).strict(),ka=r({jsonrpc:l(ba)}).merge(r({method:o(),params:_(ia)})).strict();d([wa,ka,_a,xa]);function Ta(e={}){const t=new TextEncoder;let n="";return new TransformStream({async start(){e.onStart&&await e.onStart()},async transform(r,s){s.enqueue(t.encode(r)),n+=r,e.onToken&&await e.onToken(r),e.onText&&"string"==typeof r&&await e.onText(r)},async flush(){e.onCompletion&&await e.onCompletion(n),e.onFinal&&await e.onFinal(n)}})}function Sa(e,t){return e.pipeThrough(new TransformStream({transform:async(e,t)=>{var n;"string"!=typeof e?"event"in e?"on_chat_model_stream"===e.event&&Ca(null==(n=e.data)?void 0:n.chunk,t):Ca(e,t):t.enqueue(e)}})).pipeThrough(Ta(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(Zn("text",e))}}))}function Ia(e,t){return Sa(e,t).pipeThrough(new TextEncoderStream)}function Ea(e,t){var n;const r=Sa(e,null==t?void 0:t.callbacks).pipeThrough(new TextEncoderStream),s=null==t?void 0:t.data,o=null==t?void 0:t.init,a=s?ta(s.stream,r):r;return new Response(a,{status:null!=(n=null==o?void 0:o.status)?n:200,statusText:null==o?void 0:o.statusText,headers:Zr(null==o?void 0:o.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function Ra(e,t){t.dataStream.merge(Sa(e,t.callbacks))}function Ca(e,t){if("string"==typeof e.content)t.enqueue(e.content);else{const n=e.content;for(const e of n)"text"===e.type&&t.enqueue(e.text)}}Vr({},{mergeIntoDataStream:()=>Ra,toDataStream:()=>Ia,toDataStreamResponse:()=>Ea});function Aa(e,t){const n=function(){let e=!0;return t=>(e&&(t=t.trimStart())&&(e=!1),t)}();return(r=e[Symbol.asyncIterator](),new ReadableStream({async pull(e){try{const{value:t,done:n}=await r.next();n?e.close():e.enqueue(t)}catch(t){e.error(t)}},cancel(){}})).pipeThrough(new TransformStream({async transform(e,t){t.enqueue(n(e.delta))}})).pipeThrough(Ta(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(Zn("text",e))}}));var r}function Na(e,t){return Aa(e,t).pipeThrough(new TextEncoderStream)}function Pa(e,t={}){var n;const{init:r,data:s,callbacks:o}=t,a=Aa(e,o).pipeThrough(new TextEncoderStream),i=s?ta(s.stream,a):a;return new Response(i,{status:null!=(n=null==r?void 0:r.status)?n:200,statusText:null==r?void 0:r.statusText,headers:Zr(null==r?void 0:r.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function Ma(e,t){t.dataStream.merge(Aa(e,t.callbacks))}Vr({},{mergeIntoDataStream:()=>Ma,toDataStream:()=>Na,toDataStreamResponse:()=>Pa});async function Oa(e,t,n,r,s,o,a,i){try{const l=null==a?void 0:a.signal,u=(null==a||a.enableReasoning,function(e){x(e)?e.id.includes("qwen3"):k(e)||(T(e)||(S(e)||(I(e)||(E(e)||R(e)))))}((null==a?void 0:a.model)||{id:t,provider:"openai-aisdk"}),n.map((e=>({role:e.role,content:e.content}))));C("OpenAI AI SDK Chat Completions Stream","INFO",{provider:"openai-aisdk",model:t,messages:u,temperature:r,maxTokens:s,timestamp:Date.now()});const c=await function({model:e,tools:t,toolChoice:n,system:r,prompt:s,messages:o,maxRetries:a,abortSignal:i,headers:l,maxSteps:u=1,experimental_generateMessageId:c=sa,experimental_output:p,experimental_continueSteps:d=!1,experimental_telemetry:m,experimental_providerMetadata:g,providerOptions:h=g,experimental_toolCallStreaming:f=!1,toolCallStreaming:y=f,experimental_activeTools:v,experimental_repairToolCall:b,experimental_transform:w,onChunk:_,onError:x,onFinish:k,onStepFinish:T,_internal:{now:S=yo,generateId:I=ra,currentDate:E=()=>new Date}={},...R}){return new oa({model:e,telemetry:m,headers:l,settings:R,maxRetries:a,abortSignal:i,system:r,prompt:s,messages:o,tools:t,toolChoice:n,toolCallStreaming:y,transforms:(C=w,void 0===C?[]:Array.isArray(C)?C:[C]),activeTools:v,repairToolCall:b,maxSteps:u,output:p,continueSteps:d,providerOptions:h,onChunk:_,onError:x,onFinish:k,onStepFinish:T,now:S,currentDate:E,generateId:I,generateMessageId:c});var C}({model:e(t),messages:u,temperature:r||1,maxTokens:s||2e3,abortSignal:l});let p="",d="",m="",g=!1,h="",f=!1,y=0;for await(const e of c.textStream){m+=e;let t=!0;for(;t&&m.length>0;)if(t=!1,g){const e=m.indexOf("</think>");if(-1!==e){const n=m.substring(0,e);n&&(h+=n,d+=n,i&&i({type:"thinking.delta",text:n,thinking_millsec:Date.now()-y})),g=!1,m=m.substring(e+8),t=!0}else if(m.length>10){const e=m.substring(0,m.length-10),n=m.substring(m.length-10);e&&(h+=e,d+=e,i&&i({type:"thinking.delta",text:e,thinking_millsec:Date.now()-y})),m=n,t=!0}}else{const e=m.indexOf("<think>");if(-1!==e){const n=m.substring(0,e);n&&(p+=n,o&&o(n)),g=!0,f||(f=!0,y=Date.now()),m=m.substring(e+7),t=!0}else if(m.length>10){const e=m.substring(0,m.length-10),n=m.substring(m.length-10);e&&(p+=e,o&&o(e)),m=n,t=!0}}}return m.length>0&&(g?(h+=m,d+=m,i&&i({type:"thinking.delta",text:m,thinking_millsec:Date.now()-y})):(p+=m,o&&o(m))),p.length,A.emit(N.STREAM_COMPLETE,{provider:"openai-aisdk",model:t,content:p,reasoning:d,timestamp:Date.now()}),P(p)&&A.emit(N.TOOL_USE_DETECTED,{content:p,model:t}),p}catch(l){throw console.error("[AI SDK streamCompletion] 流式响应失败:",l),A.emit(N.STREAM_ERROR,{provider:"openai-aisdk",model:t,error:l.message,timestamp:Date.now()}),l}}class ja extends M{constructor(e){super(e),t(this,"aiSdkClient"),this.aiSdkClient=Qt(e),e.id}async sendChatMessage(e,t){e.length;const n=Date.now();try{const r={messages:e,temperature:this.getTemperature(null==t?void 0:t.assistant),max_tokens:this.getMaxTokens(null==t?void 0:t.assistant),enableReasoning:this.supportsReasoning(),model:this.model,signal:null==t?void 0:t.abortSignal,enableTools:null==t?void 0:t.enableTools,mcpTools:null==t?void 0:t.mcpTools,mcpMode:null==t?void 0:t.mcpMode};(null==t?void 0:t.systemPrompt)&&(r.messages=[{role:"system",content:t.systemPrompt},...e.filter((e=>"system"!==e.role))]);const s=await Oa(this.aiSdkClient,this.model.id,r.messages,r.temperature,r.max_tokens,null==t?void 0:t.onUpdate,r,null==t?void 0:t.onChunk),o=Date.now()-n;return this.supportsReasoning()?{content:s,reasoningTime:o}:s}catch(r){throw console.error("[OpenAIAISDKProvider] 发送聊天消息失败:",r),r}}getTemperature(e){var t,n;const r=(null==(t=null==e?void 0:e.settings)?void 0:t.temperature)??(null==e?void 0:e.temperature)??this.model.temperature??1;return null==(n=null==e?void 0:e.settings)||n.temperature,null==e||e.temperature,this.model.temperature,r}getMaxTokens(e){var t,n;const r=(null==(t=null==e?void 0:e.settings)?void 0:t.maxTokens)??(null==e?void 0:e.maxTokens)??this.model.maxTokens??2e3,s=Math.max(r,1);return null==(n=null==e?void 0:e.settings)||n.maxTokens,null==e||e.maxTokens,this.model.maxTokens,s}supportsReasoning(){const e=this.model.id.toLowerCase();return e.includes("deepseek")||e.includes("o1")||e.includes("reasoning")||"deepseek"===this.model.provider}supportsMultimodal(){const e=this.model.id.toLowerCase();return e.includes("gpt-4")&&(e.includes("vision")||e.includes("4o")||e.includes("4.1"))}supportsWebSearch(){return!1}getClientInfo(){return{type:"ai-sdk",model:this.model,supportsReasoning:this.supportsReasoning(),supportsMultimodal:this.supportsMultimodal(),supportsWebSearch:this.supportsWebSearch()}}}export{ja as OpenAIAISDKProvider,Qt as createAISDKClient,Oa as streamCompletionAISDK};
