import{a6 as e}from"./index-Dnlt-eWK.js";import"./mui-vendor-DsBXMegs.js";import"./react-vendor-Be-rfjCm.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";class t extends e{async show(e){if("undefined"!=typeof document){let t=2e3;e.duration&&(t="long"===e.duration?3500:2e3);const o=document.createElement("pwa-toast");o.duration=t,o.message=e.text,document.body.appendChild(o)}}}export{t as ToastWeb};
