const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/index-BtK6VV6Z.js","assets/js/mui-vendor-hRDvsX89.js","assets/js/react-vendor-C9ilihHH.js","assets/js/utils-vendor-BDm_82Hk.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/css/index-C_2Yhxqf.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,r,i)=>((t,r,i)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[r]=i)(t,"symbol"!=typeof r?r+"":r,i);import{_ as r}from"./syntax-vendor-DfDNeb5M.js";import{j as i}from"./react-vendor-C9ilihHH.js";var n,o={},s={},a={},c={},p={},u={},h={},d={},v={},l={};function g(){if(n)return l;let e;n=1,Object.defineProperty(l,"__esModule",{value:!0}),l.default=function(){if(!e&&(e="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!e))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return e(t)};const t=new Uint8Array(16);return l}var f,m,S,y,C={},P={},R={};function I(){if(f)return R;f=1,Object.defineProperty(R,"__esModule",{value:!0}),R.default=void 0;return R.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,R}function w(){if(m)return P;m=1,Object.defineProperty(P,"__esModule",{value:!0}),P.default=void 0;var e,t=(e=I())&&e.__esModule?e:{default:e};var r=function(e){return"string"==typeof e&&t.default.test(e)};return P.default=r,P}function T(){if(S)return C;S=1,Object.defineProperty(C,"__esModule",{value:!0}),C.default=void 0,C.unsafeStringify=i;var e,t=(e=w())&&e.__esModule?e:{default:e};const r=[];for(let o=0;o<256;++o)r.push((o+256).toString(16).slice(1));function i(e,t=0){return r[e[t+0]]+r[e[t+1]]+r[e[t+2]]+r[e[t+3]]+"-"+r[e[t+4]]+r[e[t+5]]+"-"+r[e[t+6]]+r[e[t+7]]+"-"+r[e[t+8]]+r[e[t+9]]+"-"+r[e[t+10]]+r[e[t+11]]+r[e[t+12]]+r[e[t+13]]+r[e[t+14]]+r[e[t+15]]}var n=function(e,r=0){const n=i(e,r);if(!(0,t.default)(n))throw TypeError("Stringified UUID is invalid");return n};return C.default=n,C}function A(){if(y)return v;y=1,Object.defineProperty(v,"__esModule",{value:!0}),v.default=void 0;var e,t=(e=g())&&e.__esModule?e:{default:e},r=T();let i,n,o=0,s=0;var a=function(e,a,c){let p=a&&c||0;const u=a||new Array(16);let h=(e=e||{}).node||i,d=void 0!==e.clockseq?e.clockseq:n;if(null==h||null==d){const r=e.random||(e.rng||t.default)();null==h&&(h=i=[1|r[0],r[1],r[2],r[3],r[4],r[5]]),null==d&&(d=n=16383&(r[6]<<8|r[7]))}let v=void 0!==e.msecs?e.msecs:Date.now(),l=void 0!==e.nsecs?e.nsecs:s+1;const g=v-o+(l-s)/1e4;if(g<0&&void 0===e.clockseq&&(d=d+1&16383),(g<0||v>o)&&void 0===e.nsecs&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");o=v,s=l,n=d,v+=122192928e5;const f=(1e4*(268435455&v)+l)%4294967296;u[p++]=f>>>24&255,u[p++]=f>>>16&255,u[p++]=f>>>8&255,u[p++]=255&f;const m=v/4294967296*1e4&268435455;u[p++]=m>>>8&255,u[p++]=255&m,u[p++]=m>>>24&15|16,u[p++]=m>>>16&255,u[p++]=d>>>8|128,u[p++]=255&d;for(let t=0;t<6;++t)u[p+t]=h[t];return a||(0,r.unsafeStringify)(u)};return v.default=a,v}var E,b,O={},M={},D={};function k(){if(E)return D;E=1,Object.defineProperty(D,"__esModule",{value:!0}),D.default=void 0;var e,t=(e=w())&&e.__esModule?e:{default:e};var r=function(e){if(!(0,t.default)(e))throw TypeError("Invalid UUID");let r;const i=new Uint8Array(16);return i[0]=(r=parseInt(e.slice(0,8),16))>>>24,i[1]=r>>>16&255,i[2]=r>>>8&255,i[3]=255&r,i[4]=(r=parseInt(e.slice(9,13),16))>>>8,i[5]=255&r,i[6]=(r=parseInt(e.slice(14,18),16))>>>8,i[7]=255&r,i[8]=(r=parseInt(e.slice(19,23),16))>>>8,i[9]=255&r,i[10]=(r=parseInt(e.slice(24,36),16))/1099511627776&255,i[11]=r/4294967296&255,i[12]=r>>>24&255,i[13]=r>>>16&255,i[14]=r>>>8&255,i[15]=255&r,i};return D.default=r,D}function z(){if(b)return M;b=1,Object.defineProperty(M,"__esModule",{value:!0}),M.URL=M.DNS=void 0,M.default=function(e,o,s){function a(e,i,n,a){var c;if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));const t=[];for(let r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof i&&(i=(0,r.default)(i)),16!==(null===(c=i)||void 0===c?void 0:c.length))throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let p=new Uint8Array(16+e.length);if(p.set(i),p.set(e,i.length),p=s(p),p[6]=15&p[6]|o,p[8]=63&p[8]|128,n){a=a||0;for(let e=0;e<16;++e)n[a+e]=p[e];return n}return(0,t.unsafeStringify)(p)}try{a.name=e}catch(c){}return a.DNS=i,a.URL=n,a};var e,t=T(),r=(e=k())&&e.__esModule?e:{default:e};const i="6ba7b810-9dad-11d1-80b4-00c04fd430c8";M.DNS=i;const n="6ba7b811-9dad-11d1-80b4-00c04fd430c8";return M.URL=n,M}var N,_,L={};function x(){if(N)return L;function e(e){return 14+(e+64>>>9<<4)+1}function t(e,t){const r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function r(e,r,i,n,o,s){return t((a=t(t(r,e),t(n,s)))<<(c=o)|a>>>32-c,i);var a,c}function i(e,t,i,n,o,s,a){return r(t&i|~t&n,e,t,o,s,a)}function n(e,t,i,n,o,s,a){return r(t&n|i&~n,e,t,o,s,a)}function o(e,t,i,n,o,s,a){return r(t^i^n,e,t,o,s,a)}function s(e,t,i,n,o,s,a){return r(i^(t|~n),e,t,o,s,a)}N=1,Object.defineProperty(L,"__esModule",{value:!0}),L.default=void 0;var a=function(r){if("string"==typeof r){const e=unescape(encodeURIComponent(r));r=new Uint8Array(e.length);for(let t=0;t<e.length;++t)r[t]=e.charCodeAt(t)}return function(e){const t=[],r=32*e.length,i="0123456789abcdef";for(let n=0;n<r;n+=8){const r=e[n>>5]>>>n%32&255,o=parseInt(i.charAt(r>>>4&15)+i.charAt(15&r),16);t.push(o)}return t}(function(r,a){r[a>>5]|=128<<a%32,r[e(a)-1]=a;let c=1732584193,p=-271733879,u=-1732584194,h=271733878;for(let e=0;e<r.length;e+=16){const a=c,d=p,v=u,l=h;c=i(c,p,u,h,r[e],7,-680876936),h=i(h,c,p,u,r[e+1],12,-389564586),u=i(u,h,c,p,r[e+2],17,606105819),p=i(p,u,h,c,r[e+3],22,-1044525330),c=i(c,p,u,h,r[e+4],7,-176418897),h=i(h,c,p,u,r[e+5],12,1200080426),u=i(u,h,c,p,r[e+6],17,-1473231341),p=i(p,u,h,c,r[e+7],22,-45705983),c=i(c,p,u,h,r[e+8],7,1770035416),h=i(h,c,p,u,r[e+9],12,-1958414417),u=i(u,h,c,p,r[e+10],17,-42063),p=i(p,u,h,c,r[e+11],22,-1990404162),c=i(c,p,u,h,r[e+12],7,1804603682),h=i(h,c,p,u,r[e+13],12,-40341101),u=i(u,h,c,p,r[e+14],17,-1502002290),p=i(p,u,h,c,r[e+15],22,1236535329),c=n(c,p,u,h,r[e+1],5,-165796510),h=n(h,c,p,u,r[e+6],9,-1069501632),u=n(u,h,c,p,r[e+11],14,643717713),p=n(p,u,h,c,r[e],20,-373897302),c=n(c,p,u,h,r[e+5],5,-701558691),h=n(h,c,p,u,r[e+10],9,38016083),u=n(u,h,c,p,r[e+15],14,-660478335),p=n(p,u,h,c,r[e+4],20,-405537848),c=n(c,p,u,h,r[e+9],5,568446438),h=n(h,c,p,u,r[e+14],9,-1019803690),u=n(u,h,c,p,r[e+3],14,-187363961),p=n(p,u,h,c,r[e+8],20,1163531501),c=n(c,p,u,h,r[e+13],5,-1444681467),h=n(h,c,p,u,r[e+2],9,-51403784),u=n(u,h,c,p,r[e+7],14,1735328473),p=n(p,u,h,c,r[e+12],20,-1926607734),c=o(c,p,u,h,r[e+5],4,-378558),h=o(h,c,p,u,r[e+8],11,-2022574463),u=o(u,h,c,p,r[e+11],16,1839030562),p=o(p,u,h,c,r[e+14],23,-35309556),c=o(c,p,u,h,r[e+1],4,-1530992060),h=o(h,c,p,u,r[e+4],11,1272893353),u=o(u,h,c,p,r[e+7],16,-155497632),p=o(p,u,h,c,r[e+10],23,-1094730640),c=o(c,p,u,h,r[e+13],4,681279174),h=o(h,c,p,u,r[e],11,-358537222),u=o(u,h,c,p,r[e+3],16,-722521979),p=o(p,u,h,c,r[e+6],23,76029189),c=o(c,p,u,h,r[e+9],4,-640364487),h=o(h,c,p,u,r[e+12],11,-421815835),u=o(u,h,c,p,r[e+15],16,530742520),p=o(p,u,h,c,r[e+2],23,-995338651),c=s(c,p,u,h,r[e],6,-198630844),h=s(h,c,p,u,r[e+7],10,1126891415),u=s(u,h,c,p,r[e+14],15,-1416354905),p=s(p,u,h,c,r[e+5],21,-57434055),c=s(c,p,u,h,r[e+12],6,1700485571),h=s(h,c,p,u,r[e+3],10,-1894986606),u=s(u,h,c,p,r[e+10],15,-1051523),p=s(p,u,h,c,r[e+1],21,-2054922799),c=s(c,p,u,h,r[e+8],6,1873313359),h=s(h,c,p,u,r[e+15],10,-30611744),u=s(u,h,c,p,r[e+6],15,-1560198380),p=s(p,u,h,c,r[e+13],21,1309151649),c=s(c,p,u,h,r[e+4],6,-145523070),h=s(h,c,p,u,r[e+11],10,-1120210379),u=s(u,h,c,p,r[e+2],15,718787259),p=s(p,u,h,c,r[e+9],21,-343485551),c=t(c,a),p=t(p,d),u=t(u,v),h=t(h,l)}return[c,p,u,h]}(function(t){if(0===t.length)return[];const r=8*t.length,i=new Uint32Array(e(r));for(let e=0;e<r;e+=8)i[e>>5]|=(255&t[e/8])<<e%32;return i}(r),8*r.length))};return L.default=a,L}function F(){if(_)return O;_=1,Object.defineProperty(O,"__esModule",{value:!0}),O.default=void 0;var e=r(z()),t=r(x());function r(e){return e&&e.__esModule?e:{default:e}}var i=(0,e.default)("v3",48,t.default);return O.default=i,O}var B,j,U={},q={};function W(){if(B)return q;B=1,Object.defineProperty(q,"__esModule",{value:!0}),q.default=void 0;var e={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};return q.default=e,q}function H(){if(j)return U;j=1,Object.defineProperty(U,"__esModule",{value:!0}),U.default=void 0;var e=i(W()),t=i(g()),r=T();function i(e){return e&&e.__esModule?e:{default:e}}var n=function(i,n,o){if(e.default.randomUUID&&!n&&!i)return e.default.randomUUID();const s=(i=i||{}).random||(i.rng||t.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,n){o=o||0;for(let e=0;e<16;++e)n[o+e]=s[e];return n}return(0,r.unsafeStringify)(s)};return U.default=n,U}var V,K,J={},G={};function $(){if(V)return G;function e(e,t,r,i){switch(e){case 0:return t&r^~t&i;case 1:case 3:return t^r^i;case 2:return t&r^t&i^r&i}}function t(e,t){return e<<t|e>>>32-t}V=1,Object.defineProperty(G,"__esModule",{value:!0}),G.default=void 0;var r=function(r){const i=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof r){const e=unescape(encodeURIComponent(r));r=[];for(let t=0;t<e.length;++t)r.push(e.charCodeAt(t))}else Array.isArray(r)||(r=Array.prototype.slice.call(r));r.push(128);const o=r.length/4+2,s=Math.ceil(o/16),a=new Array(s);for(let e=0;e<s;++e){const t=new Uint32Array(16);for(let i=0;i<16;++i)t[i]=r[64*e+4*i]<<24|r[64*e+4*i+1]<<16|r[64*e+4*i+2]<<8|r[64*e+4*i+3];a[e]=t}a[s-1][14]=8*(r.length-1)/Math.pow(2,32),a[s-1][14]=Math.floor(a[s-1][14]),a[s-1][15]=8*(r.length-1)&4294967295;for(let c=0;c<s;++c){const r=new Uint32Array(80);for(let e=0;e<16;++e)r[e]=a[c][e];for(let e=16;e<80;++e)r[e]=t(r[e-3]^r[e-8]^r[e-14]^r[e-16],1);let o=n[0],s=n[1],p=n[2],u=n[3],h=n[4];for(let n=0;n<80;++n){const a=Math.floor(n/20),c=t(o,5)+e(a,s,p,u)+h+i[a]+r[n]>>>0;h=u,u=p,p=t(s,30)>>>0,s=o,o=c}n[0]=n[0]+o>>>0,n[1]=n[1]+s>>>0,n[2]=n[2]+p>>>0,n[3]=n[3]+u>>>0,n[4]=n[4]+h>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};return G.default=r,G}function Q(){if(K)return J;K=1,Object.defineProperty(J,"__esModule",{value:!0}),J.default=void 0;var e=r(z()),t=r($());function r(e){return e&&e.__esModule?e:{default:e}}var i=(0,e.default)("v5",80,t.default);return J.default=i,J}var X,Z={};function Y(){if(X)return Z;X=1,Object.defineProperty(Z,"__esModule",{value:!0}),Z.default=void 0;return Z.default="00000000-0000-0000-0000-000000000000",Z}var ee,te,re,ie,ne,oe={};function se(){if(ee)return oe;ee=1,Object.defineProperty(oe,"__esModule",{value:!0}),oe.default=void 0;var e,t=(e=w())&&e.__esModule?e:{default:e};var r=function(e){if(!(0,t.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};return oe.default=r,oe}function ae(){return te||(te=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return p.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return s.default}});var t=u(A()),r=u(F()),i=u(H()),n=u(Q()),o=u(Y()),s=u(se()),a=u(w()),c=u(T()),p=u(k());function u(e){return e&&e.__esModule?e:{default:e}}}(d)),d}function ce(){if(re)return h;re=1,Object.defineProperty(h,"__esModule",{value:!0}),h.createNoDashGuid=h.createGuid=void 0;const e=ae(),t=()=>e.v4();h.createGuid=t;return h.createNoDashGuid=()=>t().replace(new RegExp("-","g"),"").toUpperCase(),h}function pe(){return ie||(ie=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.PlatformEvent=e.EventType=void 0;const t=ce();var r;(r=e.EventType||(e.EventType={}))[r.Debug=0]="Debug",r[r.Info=1]="Info",r[r.Warning=2]="Warning",r[r.Error=3]="Error",r[r.None=4]="None";e.PlatformEvent=class{constructor(e,r){this.privName=e,this.privEventId=t.createNoDashGuid(),this.privEventTime=(new Date).toISOString(),this.privEventType=r,this.privMetadata={}}get name(){return this.privName}get eventId(){return this.privEventId}get eventTime(){return this.privEventTime}get eventType(){return this.privEventType}get metadata(){return this.privMetadata}}}(u)),u}function ue(){if(ne)return p;ne=1,Object.defineProperty(p,"__esModule",{value:!0}),p.AudioStreamNodeErrorEvent=p.AudioStreamNodeDetachedEvent=p.AudioStreamNodeAttachedEvent=p.AudioStreamNodeAttachingEvent=p.AudioStreamNodeEvent=p.AudioSourceErrorEvent=p.AudioSourceOffEvent=p.AudioSourceReadyEvent=p.AudioSourceInitializingEvent=p.AudioSourceEvent=void 0;const e=pe();class t extends e.PlatformEvent{constructor(t,r,i=e.EventType.Info){super(t,i),this.privAudioSourceId=r}get audioSourceId(){return this.privAudioSourceId}}p.AudioSourceEvent=t;p.AudioSourceInitializingEvent=class extends t{constructor(e){super("AudioSourceInitializingEvent",e)}};p.AudioSourceReadyEvent=class extends t{constructor(e){super("AudioSourceReadyEvent",e)}};p.AudioSourceOffEvent=class extends t{constructor(e){super("AudioSourceOffEvent",e)}};p.AudioSourceErrorEvent=class extends t{constructor(t,r){super("AudioSourceErrorEvent",t,e.EventType.Error),this.privError=r}get error(){return this.privError}};class r extends t{constructor(e,t,r){super(e,t),this.privAudioNodeId=r}get audioNodeId(){return this.privAudioNodeId}}p.AudioStreamNodeEvent=r;p.AudioStreamNodeAttachingEvent=class extends r{constructor(e,t){super("AudioStreamNodeAttachingEvent",e,t)}};p.AudioStreamNodeAttachedEvent=class extends r{constructor(e,t){super("AudioStreamNodeAttachedEvent",e,t)}};p.AudioStreamNodeDetachedEvent=class extends r{constructor(e,t){super("AudioStreamNodeDetachedEvent",e,t)}};return p.AudioStreamNodeErrorEvent=class extends r{constructor(e,t,r){super("AudioStreamNodeErrorEvent",e,t),this.privError=r}get error(){return this.privError}},p}var he,de={};function ve(){if(he)return de;he=1,Object.defineProperty(de,"__esModule",{value:!0}),de.ConnectionMessageSentEvent=de.ConnectionMessageReceivedEvent=de.ConnectionEstablishErrorEvent=de.ConnectionErrorEvent=de.ConnectionClosedEvent=de.ConnectionEstablishedEvent=de.ConnectionStartEvent=de.ConnectionEvent=de.ServiceEvent=void 0;const e=pe();class t extends e.PlatformEvent{constructor(t,r,i=e.EventType.Info){super(t,i),this.privJsonResult=r}get jsonString(){return this.privJsonResult}}de.ServiceEvent=t;class r extends e.PlatformEvent{constructor(t,r,i=e.EventType.Info){super(t,i),this.privConnectionId=r}get connectionId(){return this.privConnectionId}}de.ConnectionEvent=r;de.ConnectionStartEvent=class extends r{constructor(e,t,r){super("ConnectionStartEvent",e),this.privUri=t,this.privHeaders=r}get uri(){return this.privUri}get headers(){return this.privHeaders}};de.ConnectionEstablishedEvent=class extends r{constructor(e){super("ConnectionEstablishedEvent",e)}};de.ConnectionClosedEvent=class extends r{constructor(t,r,i){super("ConnectionClosedEvent",t,e.EventType.Debug),this.privReason=i,this.privStatusCode=r}get reason(){return this.privReason}get statusCode(){return this.privStatusCode}};de.ConnectionErrorEvent=class extends r{constructor(t,r,i){super("ConnectionErrorEvent",t,e.EventType.Debug),this.privMessage=r,this.privType=i}get message(){return this.privMessage}get type(){return this.privType}};de.ConnectionEstablishErrorEvent=class extends r{constructor(t,r,i){super("ConnectionEstablishErrorEvent",t,e.EventType.Error),this.privStatusCode=r,this.privReason=i}get reason(){return this.privReason}get statusCode(){return this.privStatusCode}};de.ConnectionMessageReceivedEvent=class extends r{constructor(e,t,r){super("ConnectionMessageReceivedEvent",e),this.privNetworkReceivedTime=t,this.privMessage=r}get networkReceivedTime(){return this.privNetworkReceivedTime}get message(){return this.privMessage}};return de.ConnectionMessageSentEvent=class extends r{constructor(e,t,r){super("ConnectionMessageSentEvent",e),this.privNetworkSentTime=t,this.privMessage=r}get networkSentTime(){return this.privNetworkSentTime}get message(){return this.privMessage}},de}var le,ge,fe={},me={};function Se(){if(le)return me;le=1,Object.defineProperty(me,"__esModule",{value:!0}),me.ObjectDisposedError=me.InvalidOperationError=me.ArgumentNullError=void 0;class e extends Error{constructor(e){super(e),this.name="ArgumentNull",this.message=e}}me.ArgumentNullError=e;class t extends Error{constructor(e){super(e),this.name="InvalidOperation",this.message=e}}me.InvalidOperationError=t;class r extends Error{constructor(e,t){super(t),this.name=e+"ObjectDisposed",this.message=t}}return me.ObjectDisposedError=r,me}function ye(){return ge||(ge=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ConnectionMessage=e.MessageType=void 0;const t=Se(),r=ce();var i,n;(n=i=e.MessageType||(e.MessageType={}))[n.Text=0]="Text",n[n.Binary=1]="Binary";e.ConnectionMessage=class{constructor(e,n,o,s){if(this.privBody=null,e===i.Text&&n&&"string"!=typeof n)throw new t.InvalidOperationError("Payload must be a string");if(e===i.Binary&&n&&!(n instanceof ArrayBuffer))throw new t.InvalidOperationError("Payload must be ArrayBuffer");switch(this.privMessageType=e,this.privBody=n,this.privHeaders=o||{},this.privId=s||r.createNoDashGuid(),this.messageType){case i.Binary:this.privSize=null!==this.binaryBody?this.binaryBody.byteLength:0;break;case i.Text:this.privSize=this.textBody.length}}get messageType(){return this.privMessageType}get headers(){return this.privHeaders}get body(){return this.privBody}get textBody(){if(this.privMessageType===i.Binary)throw new t.InvalidOperationError("Not supported for binary message");return this.privBody}get binaryBody(){if(this.privMessageType===i.Text)throw new t.InvalidOperationError("Not supported for text message");return this.privBody}get id(){return this.privId}}}(fe)),fe}var Ce,Pe={};function Re(){if(Ce)return Pe;Ce=1,Object.defineProperty(Pe,"__esModule",{value:!0}),Pe.ConnectionOpenResponse=void 0;return Pe.ConnectionOpenResponse=class{constructor(e,t){this.privStatusCode=e,this.privReason=t}get statusCode(){return this.privStatusCode}get reason(){return this.privReason}},Pe}var Ie,we={};function Te(){if(Ie)return we;Ie=1,Object.defineProperty(we,"__esModule",{value:!0}),we.DeferralMap=void 0;return we.DeferralMap=class{constructor(){this.privMap={}}add(e,t){this.privMap[e]=t}getId(e){return this.privMap[e]}complete(e,t){try{this.privMap[e].resolve(t)}catch(r){this.privMap[e].reject(r)}finally{this.privMap[e]=void 0}}},we}var Ae,Ee={};function be(){if(Ae)return Ee;Ae=1,Object.defineProperty(Ee,"__esModule",{value:!0}),Ee.SendingAgentContextMessageEvent=Ee.DialogEvent=void 0;const e=pe();class t extends e.PlatformEvent{constructor(t,r=e.EventType.Info){super(t,r)}}Ee.DialogEvent=t;return Ee.SendingAgentContextMessageEvent=class extends t{constructor(e){super("SendingAgentContextMessageEvent"),this.privAgentConfig=e}get agentConfig(){return this.privAgentConfig}},Ee}var Oe,Me,De={},ke={};function ze(){if(Oe)return ke;Oe=1,Object.defineProperty(ke,"__esModule",{value:!0}),ke.EventSource=void 0;const e=Se(),t=ce();return ke.EventSource=class{constructor(e){this.privEventListeners={},this.privIsDisposed=!1,this.privConsoleListener=void 0,this.privMetadata=e}onEvent(t){if(this.isDisposed())throw new e.ObjectDisposedError("EventSource");if(this.metadata)for(const e in this.metadata)e&&t.metadata&&(t.metadata[e]||(t.metadata[e]=this.metadata[e]));for(const e in this.privEventListeners)e&&this.privEventListeners[e]&&this.privEventListeners[e](t)}attach(e){const r=t.createNoDashGuid();return this.privEventListeners[r]=e,{detach:()=>(delete this.privEventListeners[r],Promise.resolve())}}attachListener(e){return this.attach((t=>e.onEvent(t)))}attachConsoleListener(e){return this.privConsoleListener&&this.privConsoleListener.detach(),this.privConsoleListener=this.attach((t=>e.onEvent(t))),this.privConsoleListener}isDisposed(){return this.privIsDisposed}dispose(){this.privEventListeners=null,this.privIsDisposed=!0}get metadata(){return this.privMetadata}},ke}function Ne(){if(Me)return De;Me=1,Object.defineProperty(De,"__esModule",{value:!0}),De.Events=void 0;const e=Se(),t=ze();let r=class t{static setEventSource(r){if(!r)throw new e.ArgumentNullError("eventSource");t.privInstance=r}static get instance(){return t.privInstance}};return De.Events=r,r.privInstance=new t.EventSource,De}var _e,Le={};function xe(){return _e||(_e=1,Object.defineProperty(Le,"__esModule",{value:!0})),Le}var Fe,Be={};function je(){return Fe||(Fe=1,e=Be,Object.defineProperty(e,"__esModule",{value:!0}),e.ConnectionState=void 0,(t=e.ConnectionState||(e.ConnectionState={}))[t.None=0]="None",t[t.Connected=1]="Connected",t[t.Connecting=2]="Connecting",t[t.Disconnected=3]="Disconnected"),Be;var e,t}var Ue,qe={};function We(){return Ue||(Ue=1,Object.defineProperty(qe,"__esModule",{value:!0})),qe}var He,Ve={};function Ke(){return He||(He=1,Object.defineProperty(Ve,"__esModule",{value:!0})),Ve}var Je,Ge={};function $e(){return Je||(Je=1,Object.defineProperty(Ge,"__esModule",{value:!0})),Ge}var Qe,Xe={};function Ze(){return Qe||(Qe=1,Object.defineProperty(Xe,"__esModule",{value:!0})),Xe}var Ye,et={};function tt(){return Ye||(Ye=1,Object.defineProperty(et,"__esModule",{value:!0})),et}var rt,it={};function nt(){return rt||(rt=1,Object.defineProperty(it,"__esModule",{value:!0})),it}var ot,st={};function at(){return ot||(ot=1,Object.defineProperty(st,"__esModule",{value:!0})),st}var ct,pt={};function ut(){return ct||(ct=1,Object.defineProperty(pt,"__esModule",{value:!0})),pt}var ht,dt={};function vt(){if(ht)return dt;ht=1,Object.defineProperty(dt,"__esModule",{value:!0}),dt.List=void 0;const e=Se();return dt.List=class t{constructor(e){if(this.privSubscriptionIdCounter=0,this.privAddSubscriptions={},this.privRemoveSubscriptions={},this.privDisposedSubscriptions={},this.privDisposeReason=null,this.privList=[],e)for(const t of e)this.privList.push(t)}get(e){return this.throwIfDisposed(),this.privList[e]}first(){return this.get(0)}last(){return this.get(this.length()-1)}add(e){this.throwIfDisposed(),this.insertAt(this.privList.length,e)}insertAt(e,t){this.throwIfDisposed(),0===e?this.privList.unshift(t):e===this.privList.length?this.privList.push(t):this.privList.splice(e,0,t),this.triggerSubscriptions(this.privAddSubscriptions)}removeFirst(){return this.throwIfDisposed(),this.removeAt(0)}removeLast(){return this.throwIfDisposed(),this.removeAt(this.length()-1)}removeAt(e){return this.throwIfDisposed(),this.remove(e,1)[0]}remove(e,t){this.throwIfDisposed();const r=this.privList.splice(e,t);return this.triggerSubscriptions(this.privRemoveSubscriptions),r}clear(){this.throwIfDisposed(),this.remove(0,this.length())}length(){return this.throwIfDisposed(),this.privList.length}onAdded(e){this.throwIfDisposed();const t=this.privSubscriptionIdCounter++;return this.privAddSubscriptions[t]=e,{detach:()=>(delete this.privAddSubscriptions[t],Promise.resolve())}}onRemoved(e){this.throwIfDisposed();const t=this.privSubscriptionIdCounter++;return this.privRemoveSubscriptions[t]=e,{detach:()=>(delete this.privRemoveSubscriptions[t],Promise.resolve())}}onDisposed(e){this.throwIfDisposed();const t=this.privSubscriptionIdCounter++;return this.privDisposedSubscriptions[t]=e,{detach:()=>(delete this.privDisposedSubscriptions[t],Promise.resolve())}}join(e){return this.throwIfDisposed(),this.privList.join(e)}toArray(){const e=Array();return this.privList.forEach((t=>{e.push(t)})),e}any(e){return this.throwIfDisposed(),e?this.where(e).length()>0:this.length()>0}all(e){return this.throwIfDisposed(),this.where(e).length()===this.length()}forEach(e){this.throwIfDisposed();for(let t=0;t<this.length();t++)e(this.privList[t],t)}select(e){this.throwIfDisposed();const r=[];for(let t=0;t<this.privList.length;t++)r.push(e(this.privList[t],t));return new t(r)}where(e){this.throwIfDisposed();const r=new t;for(let t=0;t<this.privList.length;t++)e(this.privList[t],t)&&r.add(this.privList[t]);return r}orderBy(e){this.throwIfDisposed();const r=this.toArray().sort(e);return new t(r)}orderByDesc(e){return this.throwIfDisposed(),this.orderBy(((t,r)=>e(r,t)))}clone(){return this.throwIfDisposed(),new t(this.toArray())}concat(e){return this.throwIfDisposed(),new t(this.privList.concat(e.toArray()))}concatArray(e){return this.throwIfDisposed(),new t(this.privList.concat(e))}isDisposed(){return null==this.privList}dispose(e){this.isDisposed()||(this.privDisposeReason=e,this.privList=null,this.privAddSubscriptions=null,this.privRemoveSubscriptions=null,this.triggerSubscriptions(this.privDisposedSubscriptions))}throwIfDisposed(){if(this.isDisposed())throw new e.ObjectDisposedError("List",this.privDisposeReason)}triggerSubscriptions(e){if(e)for(const t in e)t&&e[t]()}},dt}var lt,gt={};function ft(){return lt||(lt=1,function(e){var t,r;Object.defineProperty(e,"__esModule",{value:!0}),e.marshalPromiseToCallbacks=e.Sink=e.Deferred=e.PromiseResultEventSource=e.PromiseResult=e.PromiseState=void 0,(r=t=e.PromiseState||(e.PromiseState={}))[r.None=0]="None",r[r.Resolved=1]="Resolved",r[r.Rejected=2]="Rejected";class i{constructor(e){this.throwIfError=()=>{if(this.isError)throw this.error},e.on((e=>{this.privIsCompleted||(this.privIsCompleted=!0,this.privIsError=!1,this.privResult=e)}),(e=>{this.privIsCompleted||(this.privIsCompleted=!0,this.privIsError=!0,this.privError=e)}))}get isCompleted(){return this.privIsCompleted}get isError(){return this.privIsError}get error(){return this.privError}get result(){return this.privResult}}e.PromiseResult=i;class n{constructor(){this.setResult=e=>{this.privOnSetResult(e)},this.setError=e=>{this.privOnSetError(e)},this.on=(e,t)=>{this.privOnSetResult=e,this.privOnSetError=t}}}e.PromiseResultEventSource=n;e.Deferred=class{constructor(){this.resolve=e=>(this.privResolve(e),this),this.reject=e=>(this.privReject(e),this),this.privPromise=new Promise(((e,t)=>{this.privResolve=e,this.privReject=t}))}get promise(){return this.privPromise}};e.Sink=class{constructor(){this.privState=t.None,this.privPromiseResult=null,this.privPromiseResultEvents=null,this.privSuccessHandlers=[],this.privErrorHandlers=[],this.privPromiseResultEvents=new n,this.privPromiseResult=new i(this.privPromiseResultEvents)}get state(){return this.privState}get result(){return this.privPromiseResult}resolve(e){if(this.privState!==t.None)throw new Error("'Cannot resolve a completed promise'");this.privState=t.Resolved,this.privPromiseResultEvents.setResult(e);for(let t=0;t<this.privSuccessHandlers.length;t++)this.executeSuccessCallback(e,this.privSuccessHandlers[t],this.privErrorHandlers[t]);this.detachHandlers()}reject(e){if(this.privState!==t.None)throw new Error("'Cannot reject a completed promise'");this.privState=t.Rejected,this.privPromiseResultEvents.setError(e);for(const t of this.privErrorHandlers)this.executeErrorCallback(e,t);this.detachHandlers()}on(e,r){null==e&&(e=()=>{}),this.privState===t.None?(this.privSuccessHandlers.push(e),this.privErrorHandlers.push(r)):(this.privState===t.Resolved?this.executeSuccessCallback(this.privPromiseResult.result,e,r):this.privState===t.Rejected&&this.executeErrorCallback(this.privPromiseResult.error,r),this.detachHandlers())}executeSuccessCallback(e,t,r){try{t(e)}catch(i){this.executeErrorCallback(`'Unhandled callback error: ${i}'`,r)}}executeErrorCallback(e,t){if(!t)throw new Error(`'Unhandled error: ${e}'`);try{t(e)}catch(r){throw new Error(`'Unhandled callback error: ${r}. InnerError: ${e}'`)}}detachHandlers(){this.privErrorHandlers=[],this.privSuccessHandlers=[]}},e.marshalPromiseToCallbacks=function(e,t,r){e.then((e=>{try{t&&t(e)}catch(i){if(r)try{if(i instanceof Error){const e=i;r(e.name+": "+e.message)}else r(i)}catch(n){}}}),(e=>{if(r)try{if(e instanceof Error){const t=e;r(t.name+": "+t.message)}else r(e)}catch(t){}}))}}(gt)),gt}var mt,St={};function yt(){if(mt)return St;mt=1,Object.defineProperty(St,"__esModule",{value:!0}),St.Queue=void 0;const e=Se(),t=vt(),r=ft();var i,n;(n=i||(i={}))[n.Dequeue=0]="Dequeue",n[n.Peek=1]="Peek";return St.Queue=class{constructor(e){this.privPromiseStore=new t.List,this.privIsDrainInProgress=!1,this.privIsDisposing=!1,this.privDisposeReason=null,this.privList=e||new t.List,this.privDetachables=[],this.privSubscribers=new t.List,this.privDetachables.push(this.privList.onAdded((()=>this.drain())))}enqueue(e){this.throwIfDispose(),this.enqueueFromPromise(new Promise((t=>t(e))))}enqueueFromPromise(e){this.throwIfDispose(),e.then((e=>{this.privList.add(e)}),(()=>{}))}dequeue(){this.throwIfDispose();const e=new r.Deferred;return this.privSubscribers&&(this.privSubscribers.add({deferral:e,type:i.Dequeue}),this.drain()),e.promise}peek(){this.throwIfDispose();const e=new r.Deferred;return this.privSubscribers&&(this.privSubscribers.add({deferral:e,type:i.Peek}),this.drain()),e.promise}length(){return this.throwIfDispose(),this.privList.length()}isDisposed(){return null==this.privSubscribers}async drainAndDispose(e,t){if(!this.isDisposed()&&!this.privIsDisposing){this.privDisposeReason=t,this.privIsDisposing=!0;const r=this.privSubscribers;if(r){for(;r.length()>0;){r.removeFirst().deferral.resolve(void 0)}this.privSubscribers===r&&(this.privSubscribers=r)}for(const e of this.privDetachables)await e.detach();if(this.privPromiseStore.length()>0&&e){const t=[];return this.privPromiseStore.toArray().forEach((e=>{t.push(e)})),Promise.all(t).finally((()=>{this.privSubscribers=null,this.privList.forEach((t=>{e(t)})),this.privList=null})).then()}this.privSubscribers=null,this.privList=null}}async dispose(e){await this.drainAndDispose(null,e)}drain(){if(!this.privIsDrainInProgress&&!this.privIsDisposing){this.privIsDrainInProgress=!0;const e=this.privSubscribers,t=this.privList;if(e&&t){for(;t.length()>0&&e.length()>0&&!this.privIsDisposing;){const r=e.removeFirst();if(r.type===i.Peek)r.deferral.resolve(t.first());else{const e=t.removeFirst();r.deferral.resolve(e)}}this.privSubscribers===e&&(this.privSubscribers=e),this.privList===t&&(this.privList=t)}this.privIsDrainInProgress=!1}}throwIfDispose(){if(this.isDisposed()){if(this.privDisposeReason)throw new e.InvalidOperationError(this.privDisposeReason);throw new e.ObjectDisposedError("Queue")}if(this.privIsDisposing)throw new e.InvalidOperationError("Queue disposing")}},St}var Ct,Pt={};function Rt(){if(Ct)return Pt;Ct=1,Object.defineProperty(Pt,"__esModule",{value:!0}),Pt.RawWebsocketMessage=void 0;const e=ye(),t=Se(),r=ce();return Pt.RawWebsocketMessage=class{constructor(i,n,o){if(this.privPayload=null,!n)throw new t.ArgumentNullError("payload");if(i===e.MessageType.Binary&&"ArrayBuffer"!==Object.getPrototypeOf(n).constructor.name)throw new t.InvalidOperationError("Payload must be ArrayBuffer");if(i===e.MessageType.Text&&"string"!=typeof n)throw new t.InvalidOperationError("Payload must be a string");this.privMessageType=i,this.privPayload=n,this.privId=o||r.createNoDashGuid()}get messageType(){return this.privMessageType}get payload(){return this.privPayload}get textContent(){if(this.privMessageType===e.MessageType.Binary)throw new t.InvalidOperationError("Not supported for binary message");return this.privPayload}get binaryContent(){if(this.privMessageType===e.MessageType.Text)throw new t.InvalidOperationError("Not supported for text message");return this.privPayload}get id(){return this.privId}},Pt}var It,wt={};function Tt(){if(It)return wt;It=1,Object.defineProperty(wt,"__esModule",{value:!0}),wt.RiffPcmEncoder=void 0;return wt.RiffPcmEncoder=class{constructor(e,t){this.privActualSampleRate=e,this.privDesiredSampleRate=t}encode(e){const t=this.downSampleAudioFrame(e,this.privActualSampleRate,this.privDesiredSampleRate);if(!t)return null;const r=2*t.length,i=new ArrayBuffer(r),n=new DataView(i);return this.floatTo16BitPCM(n,0,t),i}setString(e,t,r){for(let i=0;i<r.length;i++)e.setUint8(t+i,r.charCodeAt(i))}floatTo16BitPCM(e,t,r){for(let i=0;i<r.length;i++,t+=2){const n=Math.max(-1,Math.min(1,r[i]));e.setInt16(t,n<0?32768*n:32767*n,!0)}}downSampleAudioFrame(e,t,r){if(!e)return null;if(r===t||r>t)return e;const i=t/r,n=Math.round(e.length/i),o=new Float32Array(n);let s=0,a=0;for(;a<n;){const t=Math.round((a+1)*i);let r=0,n=0;for(;s<t&&s<e.length;)r+=e[s++],n++;o[a++]=r/n}return o}},wt}var At,Et={};function bt(){if(At)return Et;At=1,Object.defineProperty(Et,"__esModule",{value:!0}),Et.Stream=void 0;const e=Se(),t=ce(),r=yt();return Et.Stream=class{constructor(e){this.privIsWriteEnded=!1,this.privIsReadEnded=!1,this.privId=e||t.createNoDashGuid(),this.privReaderQueue=new r.Queue}get isClosed(){return this.privIsWriteEnded}get isReadEnded(){return this.privIsReadEnded}get id(){return this.privId}close(){this.privIsWriteEnded||(this.writeStreamChunk({buffer:null,isEnd:!0,timeReceived:Date.now()}),this.privIsWriteEnded=!0)}writeStreamChunk(e){if(this.throwIfClosed(),!this.privReaderQueue.isDisposed())try{this.privReaderQueue.enqueue(e)}catch(t){}}read(){if(this.privIsReadEnded)throw new e.InvalidOperationError("Stream read has already finished");return this.privReaderQueue.dequeue().then((async e=>((void 0===e||e.isEnd)&&await this.privReaderQueue.dispose("End of stream reached"),e)))}readEnded(){this.privIsReadEnded||(this.privIsReadEnded=!0,this.privReaderQueue=new r.Queue)}throwIfClosed(){if(this.privIsWriteEnded)throw new e.InvalidOperationError("Stream closed")}},Et}var Ot,Mt={};function Dt(){return Ot||(Ot=1,e=Mt,Object.defineProperty(e,"__esModule",{value:!0}),e.TranslationStatus=void 0,(t=e.TranslationStatus||(e.TranslationStatus={}))[t.Success=0]="Success",t[t.Error=1]="Error"),Mt;var e,t}var kt,zt={};function Nt(){if(kt)return zt;kt=1,Object.defineProperty(zt,"__esModule",{value:!0}),zt.ChunkedArrayBufferStream=void 0;const e=Gt();let t=class extends e.Stream{constructor(e,t){super(t),this.privTargetChunkSize=Math.round(e),this.privNextBufferReadyBytes=0}writeStreamChunk(e){if(e.isEnd||0===this.privNextBufferReadyBytes&&e.buffer.byteLength===this.privTargetChunkSize)return void super.writeStreamChunk(e);let t=0;for(;t<e.buffer.byteLength;){void 0===this.privNextBufferToWrite&&(this.privNextBufferToWrite=new ArrayBuffer(this.privTargetChunkSize),this.privNextBufferStartTime=e.timeReceived);const r=Math.min(e.buffer.byteLength-t,this.privTargetChunkSize-this.privNextBufferReadyBytes),i=new Uint8Array(this.privNextBufferToWrite),n=new Uint8Array(e.buffer.slice(t,r+t));i.set(n,this.privNextBufferReadyBytes),this.privNextBufferReadyBytes+=r,t+=r,this.privNextBufferReadyBytes===this.privTargetChunkSize&&(super.writeStreamChunk({buffer:this.privNextBufferToWrite,isEnd:!1,timeReceived:this.privNextBufferStartTime}),this.privNextBufferReadyBytes=0,this.privNextBufferToWrite=void 0)}}close(){0===this.privNextBufferReadyBytes||this.isClosed||super.writeStreamChunk({buffer:this.privNextBufferToWrite.slice(0,this.privNextBufferReadyBytes),isEnd:!1,timeReceived:this.privNextBufferStartTime}),super.close()}};return zt.ChunkedArrayBufferStream=t,zt}var _t,Lt={};function xt(){return _t||(_t=1,Object.defineProperty(Lt,"__esModule",{value:!0})),Lt}var Ft,Bt={};function jt(){if(Ft)return Bt;Ft=1,Object.defineProperty(Bt,"__esModule",{value:!0}),Bt.Timeout=void 0;let e=class e{static load(){const t=new Map([[0,()=>{}]]),r=new Map,i="data:text/javascript;base64,"+btoa('!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=14)}([function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return d}));const r=new Map,o=new Map,i=e=>{const t=r.get(e);if(void 0===t)throw new Error(\'There is no interval scheduled with the given id "\'.concat(e,\'".\'));clearTimeout(t),r.delete(e)},u=e=>{const t=o.get(e);if(void 0===t)throw new Error(\'There is no timeout scheduled with the given id "\'.concat(e,\'".\'));clearTimeout(t),o.delete(e)},f=(e,t)=>{let n,r;if("performance"in self){const o=performance.now();n=o,r=e-Math.max(0,o-t)}else n=Date.now(),r=e;return{expected:n+r,remainingDelay:r}},c=(e,t,n,r)=>{const o="performance"in self?performance.now():Date.now();o>n?postMessage({id:null,method:"call",params:{timerId:t}}):e.set(t,setTimeout(c,n-o,e,t,n))},a=(e,t,n)=>{const{expected:o,remainingDelay:i}=f(e,n);r.set(t,setTimeout(c,i,r,t,o))},d=(e,t,n)=>{const{expected:r,remainingDelay:i}=f(e,n);o.set(t,setTimeout(c,i,o,t,r))}},function(e,t,n){"use strict";n.r(t);var r=n(2);for(var o in r)"default"!==o&&function(e){n.d(t,e,(function(){return r[e]}))}(o);var i=n(3);for(var o in i)"default"!==o&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var u=n(4);for(var o in u)"default"!==o&&function(e){n.d(t,e,(function(){return u[e]}))}(o);var f=n(5);for(var o in f)"default"!==o&&function(e){n.d(t,e,(function(){return f[e]}))}(o);var c=n(6);for(var o in c)"default"!==o&&function(e){n.d(t,e,(function(){return c[e]}))}(o);var a=n(7);for(var o in a)"default"!==o&&function(e){n.d(t,e,(function(){return a[e]}))}(o);var d=n(8);for(var o in d)"default"!==o&&function(e){n.d(t,e,(function(){return d[e]}))}(o);var s=n(9);for(var o in s)"default"!==o&&function(e){n.d(t,e,(function(){return s[e]}))}(o)},function(e,t){},function(e,t){},function(e,t){},function(e,t){},function(e,t){},function(e,t){},function(e,t){},function(e,t){},function(e,t,n){"use strict";n.r(t);var r=n(11);for(var o in r)"default"!==o&&function(e){n.d(t,e,(function(){return r[e]}))}(o);var i=n(12);for(var o in i)"default"!==o&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var u=n(13);for(var o in u)"default"!==o&&function(e){n.d(t,e,(function(){return u[e]}))}(o)},function(e,t){},function(e,t){},function(e,t){},function(e,t,n){"use strict";n.r(t);var r=n(0),o=n(1);for(var i in o)"default"!==i&&function(e){n.d(t,e,(function(){return o[e]}))}(i);var u=n(10);for(var i in u)"default"!==i&&function(e){n.d(t,e,(function(){return u[e]}))}(i);addEventListener("message",({data:e})=>{try{if("clear"===e.method){const{id:t,params:{timerId:n}}=e;Object(r.b)(n),postMessage({error:null,id:t})}else{if("set"!==e.method)throw new Error(\'The given method "\'.concat(e.method,\'" is not supported\'));{const{params:{delay:t,now:n,timerId:o}}=e;Object(r.d)(t,o,n)}}}catch(t){postMessage({error:{message:t.message},id:e.id,result:null})}})}]);'),n=new Worker(i);n.addEventListener("message",(({data:i})=>{if(e.isCallNotification(i)){const{params:{timerId:e}}=i,n=t.get(e);if("number"==typeof n){const t=r.get(n);if(void 0===t||t!==e)throw new Error("The timer is in an undefined state.")}else{if(void 0===n)throw new Error("The timer is in an undefined state.");n(),t.delete(e)}}else{if(!e.isClearResponse(i)){const{error:{message:e}}=i;throw new Error(e)}{const{id:e}=i,n=r.get(e);if(void 0===n)throw new Error("The timer is in an undefined state.");r.delete(e),t.delete(n)}}}));return{clearTimeout:e=>{const i=Math.random();r.set(i,e),t.set(e,i),n.postMessage({id:i,method:"clear",params:{timerId:e}})},setTimeout:(e,r)=>{const i=Math.random();return t.set(i,e),n.postMessage({id:null,method:"set",params:{delay:r,now:performance.now(),timerId:i}}),i}}}static loadWorkerTimers(){return()=>(null!==e.workerTimers||(e.workerTimers=e.load()),e.workerTimers)}static isCallNotification(e){return void 0!==e.method&&"call"===e.method}static isClearResponse(e){return null===e.error&&"number"==typeof e.id}};return Bt.Timeout=e,e.workerTimers=null,e.clearTimeout=t=>e.timers().clearTimeout(t),e.setTimeout=(t,r)=>e.timers().setTimeout(t,r),e.timers=e.loadWorkerTimers(),Bt}var Ut,qt={};function Wt(){if(Ut)return qt;Ut=1,Object.defineProperty(qt,"__esModule",{value:!0}),qt.OCSPCacheUpdateErrorEvent=qt.OCSPResponseRetrievedEvent=qt.OCSPCacheFetchErrorEvent=qt.OCSPVerificationFailedEvent=qt.OCSPCacheHitEvent=qt.OCSPCacheEntryNeedsRefreshEvent=qt.OCSPCacheEntryExpiredEvent=qt.OCSPWSUpgradeStartedEvent=qt.OCSPStapleReceivedEvent=qt.OCSPCacheUpdateCompleteEvent=qt.OCSPDiskCacheStoreEvent=qt.OCSPMemoryCacheStoreEvent=qt.OCSPCacheUpdateNeededEvent=qt.OCSPDiskCacheHitEvent=qt.OCSPCacheMissEvent=qt.OCSPMemoryCacheHitEvent=qt.OCSPEvent=void 0;const e=pe();class t extends e.PlatformEvent{constructor(e,t,r){super(e,t),this.privSignature=r}}qt.OCSPEvent=t;qt.OCSPMemoryCacheHitEvent=class extends t{constructor(t){super("OCSPMemoryCacheHitEvent",e.EventType.Debug,t)}};qt.OCSPCacheMissEvent=class extends t{constructor(t){super("OCSPCacheMissEvent",e.EventType.Debug,t)}};qt.OCSPDiskCacheHitEvent=class extends t{constructor(t){super("OCSPDiskCacheHitEvent",e.EventType.Debug,t)}};qt.OCSPCacheUpdateNeededEvent=class extends t{constructor(t){super("OCSPCacheUpdateNeededEvent",e.EventType.Debug,t)}};qt.OCSPMemoryCacheStoreEvent=class extends t{constructor(t){super("OCSPMemoryCacheStoreEvent",e.EventType.Debug,t)}};qt.OCSPDiskCacheStoreEvent=class extends t{constructor(t){super("OCSPDiskCacheStoreEvent",e.EventType.Debug,t)}};qt.OCSPCacheUpdateCompleteEvent=class extends t{constructor(t){super("OCSPCacheUpdateCompleteEvent",e.EventType.Debug,t)}};qt.OCSPStapleReceivedEvent=class extends t{constructor(){super("OCSPStapleReceivedEvent",e.EventType.Debug,"")}};qt.OCSPWSUpgradeStartedEvent=class extends t{constructor(t){super("OCSPWSUpgradeStartedEvent",e.EventType.Debug,t)}};qt.OCSPCacheEntryExpiredEvent=class extends t{constructor(t,r){super("OCSPCacheEntryExpiredEvent",e.EventType.Debug,t),this.privExpireTime=r}};qt.OCSPCacheEntryNeedsRefreshEvent=class extends t{constructor(t,r,i){super("OCSPCacheEntryNeedsRefreshEvent",e.EventType.Debug,t),this.privExpireTime=i,this.privStartTime=r}};qt.OCSPCacheHitEvent=class extends t{constructor(t,r,i){super("OCSPCacheHitEvent",e.EventType.Debug,t),this.privExpireTime=i,this.privExpireTimeString=new Date(i).toLocaleDateString(),this.privStartTime=r,this.privStartTimeString=new Date(r).toLocaleTimeString()}};qt.OCSPVerificationFailedEvent=class extends t{constructor(t,r){super("OCSPVerificationFailedEvent",e.EventType.Debug,t),this.privError=r}};qt.OCSPCacheFetchErrorEvent=class extends t{constructor(t,r){super("OCSPCacheFetchErrorEvent",e.EventType.Debug,t),this.privError=r}};qt.OCSPResponseRetrievedEvent=class extends t{constructor(t){super("OCSPResponseRetrievedEvent",e.EventType.Debug,t)}};return qt.OCSPCacheUpdateErrorEvent=class extends t{constructor(t,r){super("OCSPCacheUpdateErrorEvent",e.EventType.Debug,t),this.privError=r}},qt}var Ht,Vt,Kt={};function Jt(){if(Ht)return Kt;Ht=1,Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.BackgroundEvent=void 0;const e=Gt();class t extends e.PlatformEvent{constructor(t){super("BackgroundEvent",e.EventType.Error),this.privError=t}get error(){return this.privError}}return Kt.BackgroundEvent=t,Kt}function Gt(){return Vt||(Vt=1,function(e){var t=c&&c.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=c&&c.__exportStar||function(e,r){for(var i in e)"default"===i||r.hasOwnProperty(i)||t(r,e,i)};Object.defineProperty(e,"__esModule",{value:!0}),r(ue(),e),r(ve(),e),r(ye(),e),r(Re(),e),r(Te(),e),r(be(),e),r(Se(),e),r(Ne(),e),r(ze(),e),r(ce(),e),r(xe(),e),r(je(),e),r(We(),e),r(Ke(),e),r($e(),e),r(Ze(),e),r(tt(),e),r(nt(),e),r(at(),e),r(ut(),e),r(vt(),e),r(pe(),e),r(ft(),e),r(yt(),e),r(Rt(),e),r(Tt(),e),r(bt(),e);var i=Dt();Object.defineProperty(e,"TranslationStatus",{enumerable:!0,get:function(){return i.TranslationStatus}}),r(Nt(),e),r(xt(),e),r(jt(),e),r(Wt(),e),r(Jt(),e)}(c)),c}var $t,Qt={};function Xt(){if($t)return Qt;$t=1,Object.defineProperty(Qt,"__esModule",{value:!0}),Qt.HeaderNames=void 0;let e=class{};return Qt.HeaderNames=e,e.AuthKey="Ocp-Apim-Subscription-Key",e.Authorization="Authorization",e.SpIDAuthKey="Apim-Subscription-Id",e.ConnectionId="X-ConnectionId",e.ContentType="Content-Type",e.CustomCommandsAppId="X-CommandsAppId",e.Path="Path",e.RequestId="X-RequestId",e.RequestStreamId="X-StreamId",e.RequestTimestamp="X-Timestamp",Qt}var Zt,Yt,er={};function tr(){if(Zt)return er;Zt=1,Object.defineProperty(er,"__esModule",{value:!0}),er.AuthInfo=void 0;return er.AuthInfo=class{constructor(e,t){this.privHeaderName=e,this.privToken=t}get headerName(){return this.privHeaderName}get token(){return this.privToken}},er}function rr(){if(Yt)return a;Yt=1,Object.defineProperty(a,"__esModule",{value:!0}),a.CognitiveSubscriptionKeyAuthentication=void 0;const e=Gt(),t=Xt(),r=tr();return a.CognitiveSubscriptionKeyAuthentication=class{constructor(i){if(!i)throw new e.ArgumentNullError("subscriptionKey");this.privAuthInfo=new r.AuthInfo(t.HeaderNames.AuthKey,i)}fetch(e){return Promise.resolve(this.privAuthInfo)}fetchOnExpiry(e){return Promise.resolve(this.privAuthInfo)}},a}var ir,nr={};function or(){if(ir)return nr;ir=1,Object.defineProperty(nr,"__esModule",{value:!0}),nr.CognitiveTokenAuthentication=void 0;const e=Gt(),t=tr(),r=Xt();let i=class i{constructor(t,r){if(!t)throw new e.ArgumentNullError("fetchCallback");if(!r)throw new e.ArgumentNullError("fetchOnExpiryCallback");this.privFetchCallback=t,this.privFetchOnExpiryCallback=r}fetch(e){return this.privFetchCallback(e).then((e=>new t.AuthInfo(r.HeaderNames.Authorization,void 0===e?void 0:i.privTokenPrefix+e)))}fetchOnExpiry(e){return this.privFetchOnExpiryCallback(e).then((e=>new t.AuthInfo(r.HeaderNames.Authorization,void 0===e?void 0:i.privTokenPrefix+e)))}};return nr.CognitiveTokenAuthentication=i,i.privTokenPrefix="Bearer ",nr}var sr,ar={};function cr(){return sr||(sr=1,Object.defineProperty(ar,"__esModule",{value:!0})),ar}var pr,ur={};function hr(){return pr||(pr=1,Object.defineProperty(ur,"__esModule",{value:!0})),ur}var dr={},vr={},lr={};const gr=i(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var fr,mr={};function Sr(){return fr||(fr=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.LogLevel=void 0;const t=Gt();Object.defineProperty(e,"LogLevel",{enumerable:!0,get:function(){return t.EventType}})}(mr)),mr}var yr,Cr,Pr={};function Rr(){if(yr)return Pr;yr=1,Object.defineProperty(Pr,"__esModule",{value:!0}),Pr.Contracts=void 0;return Pr.Contracts=class e{static throwIfNullOrUndefined(e,t){if(null==e)throw new Error("throwIfNullOrUndefined:"+t)}static throwIfNull(e,t){if(null===e)throw new Error("throwIfNull:"+t)}static throwIfNullOrWhitespace(t,r){if(e.throwIfNullOrUndefined(t,r),(""+t).trim().length<1)throw new Error("throwIfNullOrWhitespace:"+r)}static throwIfNullOrTooLong(t,r,i){if(e.throwIfNullOrUndefined(t,r),(""+t).length>i)throw new Error("throwIfNullOrTooLong:"+r+" (more than "+i.toString()+" characters)")}static throwIfNullOrTooShort(t,r,i){if(e.throwIfNullOrUndefined(t,r),(""+t).length<i)throw new Error("throwIfNullOrTooShort:"+r+" (less than "+i.toString()+" characters)")}static throwIfDisposed(e){if(e)throw new Error("the object is already disposed")}static throwIfArrayEmptyOrWhitespace(t,r){if(e.throwIfNullOrUndefined(t,r),0===t.length)throw new Error("throwIfArrayEmptyOrWhitespace:"+r);for(const i of t)e.throwIfNullOrWhitespace(i,r)}static throwIfFileDoesNotExist(t,r){e.throwIfNullOrWhitespace(t,r)}static throwIfNotUndefined(e,t){if(void 0!==e)throw new Error("throwIfNotUndefined:"+t)}},Pr}function Ir(){if(Cr)return lr;Cr=1;var e=lr&&lr.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),t=lr&&lr.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=lr&&lr.__importStar||function(r){if(r&&r.__esModule)return r;var i={};if(null!=r)for(var n in r)"default"!==n&&Object.hasOwnProperty.call(r,n)&&e(i,r,n);return t(i,r),i};Object.defineProperty(lr,"__esModule",{value:!0}),lr.ConsoleLoggingListener=void 0;const i=r(gr),n=Sr(),o=Rr();return lr.ConsoleLoggingListener=class{constructor(e=n.LogLevel.None){this.privLogPath=void 0,this.privEnableConsoleOutput=!0,this.privLogLevelFilter=e}set logPath(e){o.Contracts.throwIfNullOrUndefined(i.openSync,"\nFile System access not available"),this.privLogPath=e}set enableConsoleOutput(e){this.privEnableConsoleOutput=e}onEvent(e){if(e.eventType>=this.privLogLevelFilter){const t=this.toString(e);if(this.logCallback&&this.logCallback(t),this.privLogPath&&i.writeFileSync(this.privLogPath,t+"\n",{flag:"a+"}),this.privEnableConsoleOutput)switch(e.eventType){case n.LogLevel.Debug:break;case n.LogLevel.Info:console.info(t);break;case n.LogLevel.Warning:console.warn(t);break;case n.LogLevel.Error:console.error(t)}}}toString(e){const t=[`${e.eventTime}`,`${e.name}`],r=e;for(const i in r)if(i&&e.hasOwnProperty(i)&&"eventTime"!==i&&"eventType"!==i&&"eventId"!==i&&"name"!==i&&"constructor"!==i){const e=r[i];let n="<NULL>";null!=e&&(n="number"==typeof e||"string"==typeof e?e.toString():JSON.stringify(e)),t.push(`${i}: ${n}`)}return t.join(" | ")}},lr}var wr,Tr={};function Ar(){return wr||(wr=1,Object.defineProperty(Tr,"__esModule",{value:!0})),Tr}var Er,br,Or={},Mr={};function Dr(){return Er||(Er=1,function(e){var t,r;Object.defineProperty(e,"__esModule",{value:!0}),e.AudioStreamFormatImpl=e.AudioStreamFormat=e.AudioFormatTag=void 0,(r=t=e.AudioFormatTag||(e.AudioFormatTag={}))[r.PCM=1]="PCM",r[r.MuLaw=2]="MuLaw",r[r.Siren=3]="Siren",r[r.MP3=4]="MP3",r[r.SILKSkype=5]="SILKSkype",r[r.OGG_OPUS=6]="OGG_OPUS",r[r.WEBM_OPUS=7]="WEBM_OPUS",r[r.ALaw=8]="ALaw",r[r.FLAC=9]="FLAC",r[r.OPUS=10]="OPUS",r[r.AMR_WB=11]="AMR_WB",r[r.G722=12]="G722";class i{static getDefaultInputFormat(){return n.getDefaultInputFormat()}static getWaveFormat(e,t,r,i){return new n(e,t,r,i)}static getWaveFormatPCM(e,t,r){return new n(e,t,r)}}e.AudioStreamFormat=i;class n extends i{constructor(e=16e3,r=16,i=1,n=t.PCM){super();let o=!0;switch(n){case t.PCM:this.formatTag=1;break;case t.ALaw:this.formatTag=6;break;case t.MuLaw:this.formatTag=7;break;default:o=!1}if(this.bitsPerSample=r,this.samplesPerSec=e,this.channels=i,this.avgBytesPerSec=this.samplesPerSec*this.channels*(this.bitsPerSample/8),this.blockAlign=this.channels*Math.max(this.bitsPerSample,8),o){this.privHeader=new ArrayBuffer(44);const e=new DataView(this.privHeader);this.setString(e,0,"RIFF"),e.setUint32(4,0,!0),this.setString(e,8,"WAVEfmt "),e.setUint32(16,16,!0),e.setUint16(20,this.formatTag,!0),e.setUint16(22,this.channels,!0),e.setUint32(24,this.samplesPerSec,!0),e.setUint32(28,this.avgBytesPerSec,!0),e.setUint16(32,this.channels*(this.bitsPerSample/8),!0),e.setUint16(34,this.bitsPerSample,!0),this.setString(e,36,"data"),e.setUint32(40,0,!0)}}static getDefaultInputFormat(){return new n}static getAudioContext(e){const t=window.AudioContext||window.webkitAudioContext||!1;if(t)return void 0!==e&&navigator.mediaDevices.getSupportedConstraints().sampleRate?new t({sampleRate:e}):new t;throw new Error("Browser does not support Web Audio API (AudioContext is not available).")}close(){}get header(){return this.privHeader}setString(e,t,r){for(let i=0;i<r.length;i++)e.setUint8(t+i,r.charCodeAt(i))}}e.AudioStreamFormatImpl=n}(Mr)),Mr}function kr(){return br||(br=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.MicAudioSource=e.AudioWorkletSourceURLPropertyName=void 0;const t=jv(),r=Gt(),i=Dr();e.AudioWorkletSourceURLPropertyName="MICROPHONE-WorkletSourceUrl";class n{constructor(e,t,i,o){this.privRecorder=e,this.deviceId=t,this.privStreams={},this.privOutputChunkSize=n.AUDIOFORMAT.avgBytesPerSec/10,this.privId=i||r.createNoDashGuid(),this.privEvents=new r.EventSource,this.privMediaStream=o||null,this.privIsClosing=!1}get format(){return Promise.resolve(n.AUDIOFORMAT)}turnOn(){if(this.privInitializeDeferral)return this.privInitializeDeferral.promise;this.privInitializeDeferral=new r.Deferred;try{this.createAudioContext()}catch(i){if(i instanceof Error){const e=i;this.privInitializeDeferral.reject(e.name+": "+e.message)}else this.privInitializeDeferral.reject(i);return this.privInitializeDeferral.promise}const e=window.navigator;let t=e.getUserMedia||e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia;if(e.mediaDevices&&(t=(t,r,i)=>{e.mediaDevices.getUserMedia(t).then(r).catch(i)}),t){const e=()=>{this.onEvent(new r.AudioSourceInitializingEvent(this.privId)),this.privMediaStream&&this.privMediaStream.active?(this.onEvent(new r.AudioSourceReadyEvent(this.privId)),this.privInitializeDeferral.resolve()):t({audio:!this.deviceId||{deviceId:this.deviceId},video:!1},(e=>{this.privMediaStream=e,this.onEvent(new r.AudioSourceReadyEvent(this.privId)),this.privInitializeDeferral.resolve()}),(e=>{const t=`Error occurred during microphone initialization: ${e}`;this.privInitializeDeferral.reject(t),this.onEvent(new r.AudioSourceErrorEvent(this.privId,t))}))};"suspended"===this.privContext.state?this.privContext.resume().then(e).catch((e=>{this.privInitializeDeferral.reject(`Failed to initialize audio context: ${e}`)})):e()}else{const e="Browser does not support getUserMedia.";this.privInitializeDeferral.reject(e),this.onEvent(new r.AudioSourceErrorEvent(e,""))}return this.privInitializeDeferral.promise}id(){return this.privId}attach(e){return this.onEvent(new r.AudioStreamNodeAttachingEvent(this.privId,e)),this.listen(e).then((t=>(this.onEvent(new r.AudioStreamNodeAttachedEvent(this.privId,e)),{detach:async()=>(t.readEnded(),delete this.privStreams[e],this.onEvent(new r.AudioStreamNodeDetachedEvent(this.privId,e)),this.turnOff()),id:()=>e,read:()=>t.read()})))}detach(e){e&&this.privStreams[e]&&(this.privStreams[e].close(),delete this.privStreams[e],this.onEvent(new r.AudioStreamNodeDetachedEvent(this.privId,e)))}async turnOff(){for(const e in this.privStreams)if(e){const t=this.privStreams[e];t&&t.close()}this.onEvent(new r.AudioSourceOffEvent(this.privId)),this.privInitializeDeferral&&(await this.privInitializeDeferral,this.privInitializeDeferral=null),await this.destroyAudioContext()}get events(){return this.privEvents}get deviceInfo(){return this.getMicrophoneLabel().then((e=>({bitspersample:n.AUDIOFORMAT.bitsPerSample,channelcount:n.AUDIOFORMAT.channels,connectivity:t.connectivity.Unknown,manufacturer:"Speech SDK",model:e,samplerate:n.AUDIOFORMAT.samplesPerSec,type:t.type.Microphones})))}setProperty(t,r){if(t!==e.AudioWorkletSourceURLPropertyName)throw new Error("Property '"+t+"' is not supported on Microphone.");this.privRecorder.setWorkletUrl(r)}getMicrophoneLabel(){const e="microphone";if(void 0!==this.privMicrophoneLabel)return Promise.resolve(this.privMicrophoneLabel);if(void 0===this.privMediaStream||!this.privMediaStream.active)return Promise.resolve(e);this.privMicrophoneLabel=e;const t=this.privMediaStream.getTracks()[0].getSettings().deviceId;if(void 0===t)return Promise.resolve(this.privMicrophoneLabel);const i=new r.Deferred;return navigator.mediaDevices.enumerateDevices().then((e=>{for(const r of e)if(r.deviceId===t){this.privMicrophoneLabel=r.label;break}i.resolve(this.privMicrophoneLabel)}),(()=>i.resolve(this.privMicrophoneLabel))),i.promise}async listen(e){await this.turnOn();const t=new r.ChunkedArrayBufferStream(this.privOutputChunkSize,e);this.privStreams[e]=t;try{this.privRecorder.record(this.privContext,this.privMediaStream,t)}catch(i){throw this.onEvent(new r.AudioStreamNodeErrorEvent(this.privId,e,i)),i}return t}onEvent(e){this.privEvents.onEvent(e),r.Events.instance.onEvent(e)}createAudioContext(){this.privContext||(this.privContext=i.AudioStreamFormatImpl.getAudioContext(n.AUDIOFORMAT.samplesPerSec))}async destroyAudioContext(){if(!this.privContext)return;this.privRecorder.releaseMediaResources(this.privContext);let e=!1;"close"in this.privContext&&(e=!0),e?this.privIsClosing||(this.privIsClosing=!0,await this.privContext.close(),this.privContext=null,this.privIsClosing=!1):null!==this.privContext&&"running"===this.privContext.state&&await this.privContext.suspend()}}e.MicAudioSource=n,n.AUDIOFORMAT=i.AudioStreamFormat.getDefaultInputFormat()}(Or)),Or}var zr,Nr={};function _r(){if(zr)return Nr;zr=1,Object.defineProperty(Nr,"__esModule",{value:!0}),Nr.FileAudioSource=void 0;const e=jv(),t=Gt(),r=Dr();return Nr.FileAudioSource=class{constructor(e,r,i){this.privStreams={},this.privHeaderEnd=44,this.privId=i||t.createNoDashGuid(),this.privEvents=new t.EventSource,this.privSource=e,"undefined"!=typeof window&&"undefined"!=typeof Blob&&this.privSource instanceof Blob?this.privFilename=e.name:this.privFilename=r||"unknown.wav",this.privAudioFormatPromise=this.readHeader()}get format(){return this.privAudioFormatPromise}turnOn(){if(this.privFilename.lastIndexOf(".wav")!==this.privFilename.length-4){const e=this.privFilename+" is not supported. Only WAVE files are allowed at the moment.";return this.onEvent(new t.AudioSourceErrorEvent(e,"")),Promise.reject(e)}this.onEvent(new t.AudioSourceInitializingEvent(this.privId)),this.onEvent(new t.AudioSourceReadyEvent(this.privId))}id(){return this.privId}async attach(e){this.onEvent(new t.AudioStreamNodeAttachingEvent(this.privId,e));const r=await this.upload(e);return this.onEvent(new t.AudioStreamNodeAttachedEvent(this.privId,e)),Promise.resolve({detach:async()=>{r.readEnded(),delete this.privStreams[e],this.onEvent(new t.AudioStreamNodeDetachedEvent(this.privId,e)),await this.turnOff()},id:()=>e,read:()=>r.read()})}detach(e){e&&this.privStreams[e]&&(this.privStreams[e].close(),delete this.privStreams[e],this.onEvent(new t.AudioStreamNodeDetachedEvent(this.privId,e)))}turnOff(){for(const e in this.privStreams)if(e){const t=this.privStreams[e];t&&!t.isClosed&&t.close()}return this.onEvent(new t.AudioSourceOffEvent(this.privId)),Promise.resolve()}get events(){return this.privEvents}get deviceInfo(){return this.privAudioFormatPromise.then((t=>Promise.resolve({bitspersample:t.bitsPerSample,channelcount:t.channels,connectivity:e.connectivity.Unknown,manufacturer:"Speech SDK",model:"File",samplerate:t.samplesPerSec,type:e.type.File})))}readHeader(){const e=this.privSource.slice(0,4296),i=new t.Deferred,n=e=>{const t=new DataView(e),n=e=>String.fromCharCode(t.getUint8(e),t.getUint8(e+1),t.getUint8(e+2),t.getUint8(e+3));if("RIFF"!==n(0))return void i.reject("Invalid WAV header in file, RIFF was not found");if("WAVE"!==n(8)||"fmt "!==n(12))return void i.reject("Invalid WAV header in file, WAVEfmt was not found");const o=t.getInt32(16,!0),s=t.getUint16(22,!0),a=t.getUint32(24,!0),c=t.getUint16(34,!0);let p=36+Math.max(o-16,0);for(;"data"!==n(p);p+=2)if(p>4288)return void i.reject("Invalid WAV header in file, data block was not found");this.privHeaderEnd=p+8,i.resolve(r.AudioStreamFormat.getWaveFormatPCM(a,c,s))};if("undefined"!=typeof window&&"undefined"!=typeof Blob&&e instanceof Blob){const t=new FileReader;t.onload=e=>{const t=e.target.result;n(t)},t.readAsArrayBuffer(e)}else{const t=e;n(t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength))}return i.promise}async upload(e){const r=r=>{const i=`Error occurred while processing '${this.privFilename}'. ${r}`;throw this.onEvent(new t.AudioStreamNodeErrorEvent(this.privId,e,i)),new Error(i)};try{await this.turnOn();const i=await this.privAudioFormatPromise,n=new t.ChunkedArrayBufferStream(i.avgBytesPerSec/10,e);this.privStreams[e]=n;const o=this.privSource.slice(this.privHeaderEnd),s=e=>{n.isClosed||(n.writeStreamChunk({buffer:e,isEnd:!1,timeReceived:Date.now()}),n.close())};if("undefined"!=typeof window&&"undefined"!=typeof Blob&&o instanceof Blob){const e=new FileReader;e.onerror=e=>r(e.toString()),e.onload=e=>{const t=e.target.result;s(t)},e.readAsArrayBuffer(o)}else{const e=o;s(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength))}return n}catch(i){r(i)}}onEvent(e){this.privEvents.onEvent(e),t.Events.instance.onEvent(e)}},Nr}var Lr,xr={};function Fr(){if(Lr)return xr;Lr=1,Object.defineProperty(xr,"__esModule",{value:!0}),xr.PcmRecorder=void 0;const e=Gt();return xr.PcmRecorder=class{constructor(e){this.privStopInputOnRelease=e}record(t,r,i){const n=new e.RiffPcmEncoder(t.sampleRate,16e3),o=t.createMediaStreamSource(r),s=()=>{const e=(()=>{let e=0;try{return t.createScriptProcessor(e,1,1)}catch(r){e=2048;let i=t.sampleRate;for(;e<16384&&i>=32e3;)e<<=1,i>>=1;return t.createScriptProcessor(e,1,1)}})();e.onaudioprocess=e=>{const t=e.inputBuffer.getChannelData(0);if(i&&!i.isClosed){const e=n.encode(t);e&&i.writeStreamChunk({buffer:e,isEnd:!1,timeReceived:Date.now()})}},o.connect(e),e.connect(t.destination),this.privMediaResources={scriptProcessorNode:e,source:o,stream:r}},a=!!this.privSpeechProcessorScript&&"ignore"===this.privSpeechProcessorScript.toLowerCase();if(t.audioWorklet&&!a){if(!this.privSpeechProcessorScript){const e=new Blob(["class SP extends AudioWorkletProcessor {\n                    constructor(options) {\n                      super(options);\n                    }\n                    process(inputs, outputs) {\n                      const input = inputs[0];\n                      const output = [];\n                      for (let channel = 0; channel < input.length; channel += 1) {\n                        output[channel] = input[channel];\n                      }\n                      this.port.postMessage(output[0]);\n                      return true;\n                    }\n                  }\n                  registerProcessor('speech-processor', SP);"],{type:"application/javascript; charset=utf-8"});this.privSpeechProcessorScript=URL.createObjectURL(e)}t.audioWorklet.addModule(this.privSpeechProcessorScript).then((()=>{const e=new AudioWorkletNode(t,"speech-processor");e.port.onmessage=e=>{const t=e.data;if(i&&!i.isClosed){const e=n.encode(t);e&&i.writeStreamChunk({buffer:e,isEnd:!1,timeReceived:Date.now()})}},o.connect(e),e.connect(t.destination),this.privMediaResources={scriptProcessorNode:e,source:o,stream:r}})).catch((()=>{s()}))}else try{s()}catch(c){throw new Error(`Unable to start audio worklet node for PCMRecorder: ${c}`)}}releaseMediaResources(e){this.privMediaResources&&(this.privMediaResources.scriptProcessorNode&&(this.privMediaResources.scriptProcessorNode.disconnect(e.destination),this.privMediaResources.scriptProcessorNode=null),this.privMediaResources.source&&(this.privMediaResources.source.disconnect(),this.privStopInputOnRelease&&this.privMediaResources.stream.getTracks().forEach((e=>e.stop())),this.privMediaResources.source=null))}setWorkletUrl(e){this.privSpeechProcessorScript=e}},xr}var Br,jr,Ur={},qr={};function Wr(){if(Br)return qr;Br=1;var e=qr&&qr.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),t=qr&&qr.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=qr&&qr.__importStar||function(r){if(r&&r.__esModule)return r;var i={};if(null!=r)for(var n in r)"default"!==n&&Object.hasOwnProperty.call(r,n)&&e(i,r,n);return t(i,r),i},i=qr&&qr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(qr,"__esModule",{value:!0}),qr.WebsocketMessageAdapter=void 0;const n=r(gr),o=r(gr),s=i(gr),a=i(gr),c=i(gr),p=Xt(),u=Gt();let h=class e{constructor(e,t,r,i,n,o){if(!e)throw new u.ArgumentNullError("uri");if(!r)throw new u.ArgumentNullError("messageFormatter");this.proxyInfo=i,this.privConnectionEvents=new u.EventSource,this.privConnectionId=t,this.privMessageFormatter=r,this.privConnectionState=u.ConnectionState.None,this.privUri=e,this.privHeaders=n,this.privEnableCompression=o,this.privHeaders[p.HeaderNames.ConnectionId]=this.privConnectionId,this.privLastErrorReceived=""}get state(){return this.privConnectionState}open(){if(this.privConnectionState===u.ConnectionState.Disconnected)return Promise.reject(`Cannot open a connection that is in ${this.privConnectionState} state`);if(this.privConnectionEstablishDeferral)return this.privConnectionEstablishDeferral.promise;this.privConnectionEstablishDeferral=new u.Deferred,this.privCertificateValidatedDeferral=new u.Deferred,this.privConnectionState=u.ConnectionState.Connecting;try{if("undefined"==typeof WebSocket||e.forceNpmWebSocket){const e={headers:this.privHeaders,perMessageDeflate:this.privEnableCompression};this.privCertificateValidatedDeferral.resolve(),e.agent=this.getAgent();let t=new URL(this.privUri).protocol;"wss:"===(null==t?void 0:t.toLocaleLowerCase())?t="https:":"ws:"===(null==t?void 0:t.toLocaleLowerCase())&&(t="http:"),e.agent.protocol=t,this.privWebsocketClient=new c.default(this.privUri,e)}else this.privCertificateValidatedDeferral.resolve(),this.privWebsocketClient=new WebSocket(this.privUri);this.privWebsocketClient.binaryType="arraybuffer",this.privReceivingMessageQueue=new u.Queue,this.privDisconnectDeferral=new u.Deferred,this.privSendMessageQueue=new u.Queue,this.processSendQueue().catch((e=>{u.Events.instance.onEvent(new u.BackgroundEvent(e))}))}catch(t){return this.privConnectionEstablishDeferral.resolve(new u.ConnectionOpenResponse(500,t)),this.privConnectionEstablishDeferral.promise}return this.onEvent(new u.ConnectionStartEvent(this.privConnectionId,this.privUri)),this.privWebsocketClient.onopen=()=>{this.privCertificateValidatedDeferral.promise.then((()=>{this.privConnectionState=u.ConnectionState.Connected,this.onEvent(new u.ConnectionEstablishedEvent(this.privConnectionId)),this.privConnectionEstablishDeferral.resolve(new u.ConnectionOpenResponse(200,""))}),(e=>{this.privConnectionEstablishDeferral.reject(e)}))},this.privWebsocketClient.onerror=e=>{this.onEvent(new u.ConnectionErrorEvent(this.privConnectionId,e.message,e.type)),this.privLastErrorReceived=e.message},this.privWebsocketClient.onclose=e=>{this.privConnectionState===u.ConnectionState.Connecting?(this.privConnectionState=u.ConnectionState.Disconnected,this.privConnectionEstablishDeferral.resolve(new u.ConnectionOpenResponse(e.code,e.reason+" "+this.privLastErrorReceived))):(this.privConnectionState=u.ConnectionState.Disconnected,this.privWebsocketClient=null,this.onEvent(new u.ConnectionClosedEvent(this.privConnectionId,e.code,e.reason))),this.onClose(e.code,e.reason).catch((e=>{u.Events.instance.onEvent(new u.BackgroundEvent(e))}))},this.privWebsocketClient.onmessage=e=>{const t=(new Date).toISOString();if(this.privConnectionState===u.ConnectionState.Connected){const r=new u.Deferred;if(this.privReceivingMessageQueue.enqueueFromPromise(r.promise),e.data instanceof ArrayBuffer){const i=new u.RawWebsocketMessage(u.MessageType.Binary,e.data);this.privMessageFormatter.toConnectionMessage(i).then((e=>{this.onEvent(new u.ConnectionMessageReceivedEvent(this.privConnectionId,t,e)),r.resolve(e)}),(e=>{r.reject(`Invalid binary message format. Error: ${e}`)}))}else{const i=new u.RawWebsocketMessage(u.MessageType.Text,e.data);this.privMessageFormatter.toConnectionMessage(i).then((e=>{this.onEvent(new u.ConnectionMessageReceivedEvent(this.privConnectionId,t,e)),r.resolve(e)}),(e=>{r.reject(`Invalid text message format. Error: ${e}`)}))}}},this.privConnectionEstablishDeferral.promise}send(e){if(this.privConnectionState!==u.ConnectionState.Connected)return Promise.reject(`Cannot send on connection that is in ${u.ConnectionState[this.privConnectionState]} state`);const t=new u.Deferred,r=new u.Deferred;return this.privSendMessageQueue.enqueueFromPromise(r.promise),this.privMessageFormatter.fromConnectionMessage(e).then((i=>{r.resolve({Message:e,RawWebsocketMessage:i,sendStatusDeferral:t})}),(e=>{r.reject(`Error formatting the message. ${e}`)})),t.promise}read(){return this.privConnectionState!==u.ConnectionState.Connected?Promise.reject(`Cannot read on connection that is in ${this.privConnectionState} state`):this.privReceivingMessageQueue.dequeue()}close(e){return this.privWebsocketClient?(this.privConnectionState!==u.ConnectionState.Disconnected&&this.privWebsocketClient.close(1e3,e||"Normal closure by client"),this.privDisconnectDeferral.promise):Promise.resolve()}get events(){return this.privConnectionEvents}sendRawMessage(e){try{return e?(this.onEvent(new u.ConnectionMessageSentEvent(this.privConnectionId,(new Date).toISOString(),e.Message)),this.isWebsocketOpen?(this.privWebsocketClient.send(e.RawWebsocketMessage.payload),Promise.resolve()):Promise.reject("websocket send error: Websocket not ready "+this.privConnectionId+" "+e.Message.id+" "+(new Error).stack)):Promise.resolve()}catch(t){return Promise.reject(`websocket send error: ${t}`)}}async onClose(e,t){const r=`Connection closed. ${e}: ${t}`;this.privConnectionState=u.ConnectionState.Disconnected,this.privDisconnectDeferral.resolve(),await this.privReceivingMessageQueue.drainAndDispose((()=>{}),r),await this.privSendMessageQueue.drainAndDispose((e=>{e.sendStatusDeferral.reject(r)}),r)}async processSendQueue(){for(;;){const t=this.privSendMessageQueue.dequeue(),r=await t;if(!r)return;try{await this.sendRawMessage(r),r.sendStatusDeferral.resolve()}catch(e){r.sendStatusDeferral.reject(e)}}}onEvent(e){this.privConnectionEvents.onEvent(e),u.Events.instance.onEvent(e)}getAgent(){const e=new s.default.Agent(this.createConnection);return void 0!==this.proxyInfo&&void 0!==this.proxyInfo.HostName&&this.proxyInfo.Port>0&&(e.proxyInfo=this.proxyInfo),e}static GetProxyAgent(e){const t={host:e.HostName,port:e.Port};e.UserName?t.headers={"Proxy-Authentication":"Basic "+new Buffer(`${e.UserName}:${void 0===e.Password?"":e.Password}`).toString("base64")}:t.headers={},t.headers.requestOCSP="true";return new a.default(t)}createConnection(t,r){let i;if(r={...r,requestOCSP:!0,servername:r.host},this.proxyInfo){const n=e.GetProxyAgent(this.proxyInfo);i=new Promise(((e,i)=>{n.callback(t,r,((t,r)=>{t?i(t):e(r)}))}))}else i=r.secureEndpoint?Promise.resolve(o.connect(r)):Promise.resolve(n.connect(r));return i}get isWebsocketOpen(){return this.privWebsocketClient&&this.privWebsocketClient.readyState===this.privWebsocketClient.OPEN}};return qr.WebsocketMessageAdapter=h,h.forceNpmWebSocket=!1,qr}function Hr(){if(jr)return Ur;jr=1,Object.defineProperty(Ur,"__esModule",{value:!0}),Ur.WebsocketConnection=void 0;const e=Gt(),t=Wr();return Ur.WebsocketConnection=class{constructor(r,i,n,o,s,a=!1,c){if(this.privIsDisposed=!1,!r)throw new e.ArgumentNullError("uri");if(!o)throw new e.ArgumentNullError("messageFormatter");this.privMessageFormatter=o;let p="",u=0;if(i)for(const e in i)if(e){p+=0===u&&-1===r.indexOf("?")?"?":"&";p+=encodeURIComponent(e);let t=i[e];t&&(t=encodeURIComponent(t),p+=`=${t}`),u++}if(n)for(const e in n)if(e){p+=0===u&&-1===r.indexOf("?")?"?":"&";p+=`${e}=${encodeURIComponent(n[e])}`,u++}this.privUri=r+p,this.privId=c||e.createNoDashGuid(),this.privConnectionMessageAdapter=new t.WebsocketMessageAdapter(this.privUri,this.id,this.privMessageFormatter,s,n,a)}async dispose(){this.privIsDisposed=!0,this.privConnectionMessageAdapter&&await this.privConnectionMessageAdapter.close()}isDisposed(){return this.privIsDisposed}get id(){return this.privId}get uri(){return this.privUri}state(){return this.privConnectionMessageAdapter.state}open(){return this.privConnectionMessageAdapter.open()}send(e){return this.privConnectionMessageAdapter.send(e)}read(){return this.privConnectionMessageAdapter.read()}get events(){return this.privConnectionMessageAdapter.events}},Ur}var Vr,Kr={};function Jr(){if(Vr)return Kr;Vr=1,Object.defineProperty(Kr,"__esModule",{value:!0}),Kr.ReplayableAudioNode=void 0;Kr.ReplayableAudioNode=class{constructor(e,t){this.privBuffers=[],this.privReplayOffset=0,this.privLastShrinkOffset=0,this.privBufferStartOffset=0,this.privBufferSerial=0,this.privBufferedBytes=0,this.privReplay=!1,this.privLastChunkAcquiredTime=0,this.privAudioNode=e,this.privBytesPerSecond=t}id(){return this.privAudioNode.id()}read(){if(this.privReplay&&0!==this.privBuffers.length){const e=this.privReplayOffset-this.privBufferStartOffset;let t=Math.round(e*this.privBytesPerSecond*1e-7);0!=t%2&&t++;let r=0;for(;r<this.privBuffers.length&&t>=this.privBuffers[r].chunk.buffer.byteLength;)t-=this.privBuffers[r++].chunk.buffer.byteLength;if(r<this.privBuffers.length){const e=this.privBuffers[r].chunk.buffer.slice(t);return this.privReplayOffset+=e.byteLength/this.privBytesPerSecond*1e7,r===this.privBuffers.length-1&&(this.privReplay=!1),Promise.resolve({buffer:e,isEnd:!1,timeReceived:this.privBuffers[r].chunk.timeReceived})}}return this.privAudioNode.read().then((t=>(t&&t.buffer&&(this.privBuffers.push(new e(t,this.privBufferSerial++,this.privBufferedBytes)),this.privBufferedBytes+=t.buffer.byteLength),t)))}detach(){return this.privBuffers=void 0,this.privAudioNode.detach()}replay(){this.privBuffers&&0!==this.privBuffers.length&&(this.privReplay=!0,this.privReplayOffset=this.privLastShrinkOffset)}shrinkBuffers(e){if(void 0===this.privBuffers||0===this.privBuffers.length)return;this.privLastShrinkOffset=e;const t=e-this.privBufferStartOffset;let r=Math.round(t*this.privBytesPerSecond*1e-7),i=0;for(;i<this.privBuffers.length&&r>=this.privBuffers[i].chunk.buffer.byteLength;)r-=this.privBuffers[i++].chunk.buffer.byteLength;this.privBufferStartOffset=Math.round(e-r/this.privBytesPerSecond*1e7),this.privBuffers=this.privBuffers.slice(i)}findTimeAtOffset(e){if(e<this.privBufferStartOffset||void 0===this.privBuffers)return 0;for(const t of this.privBuffers){const r=t.byteOffset/this.privBytesPerSecond*1e7,i=r+t.chunk.buffer.byteLength/this.privBytesPerSecond*1e7;if(e>=r&&e<=i)return t.chunk.timeReceived}return 0}};class e{constructor(e,t,r){this.chunk=e,this.serial=t,this.byteOffset=r}}return Kr}var Gr,$r={},Qr={},Xr={},Zr={};function Yr(){if(Gr)return Zr;Gr=1;var e=Zr&&Zr.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),t=Zr&&Zr.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=Zr&&Zr.__importStar||function(r){if(r&&r.__esModule)return r;var i={};if(null!=r)for(var n in r)"default"!==n&&Object.hasOwnProperty.call(r,n)&&e(i,r,n);return t(i,r),i};Object.defineProperty(Zr,"__esModule",{value:!0}),Zr.AudioFileWriter=void 0;const i=r(gr),n=Rr();return Zr.AudioFileWriter=class{constructor(e){n.Contracts.throwIfNullOrUndefined(i.openSync,"\nFile System access not available, please use Push or PullAudioOutputStream"),this.privFd=i.openSync(e,"w")}set format(e){n.Contracts.throwIfNotUndefined(this.privAudioFormat,"format is already set"),this.privAudioFormat=e;let t=0;this.privAudioFormat.hasHeader&&(t=this.privAudioFormat.header.byteLength),void 0!==this.privFd&&(this.privWriteStream=i.createWriteStream("",{fd:this.privFd,start:t,autoClose:!1}))}write(e){n.Contracts.throwIfNullOrUndefined(this.privAudioFormat,"must set format before writing."),void 0!==this.privWriteStream&&this.privWriteStream.write(new Uint8Array(e.slice(0)))}close(){void 0!==this.privFd&&(this.privWriteStream.on("finish",(()=>{this.privAudioFormat.hasHeader&&(this.privAudioFormat.updateHeader(this.privWriteStream.bytesWritten),i.writeSync(this.privFd,new Int8Array(this.privAudioFormat.header),0,this.privAudioFormat.header.byteLength,0)),i.closeSync(this.privFd),this.privFd=void 0})),this.privWriteStream.end())}id(){return this.privId}},Zr}var ei,ti={};function ri(){if(ei)return ti;ei=1,Object.defineProperty(ti,"__esModule",{value:!0}),ti.PullAudioInputStreamImpl=ti.PullAudioInputStream=ti.PushAudioInputStreamImpl=ti.PushAudioInputStream=ti.AudioInputStream=void 0;const e=jv(),t=Gt(),r=ce(),i=Mp(),n=Dr();let o=class{constructor(){}static createPushStream(e){return s.create(e)}static createPullStream(e,t){return c.create(e,t)}};ti.AudioInputStream=o;class s extends o{static create(e){return new a(e)}}ti.PushAudioInputStream=s;class a extends s{constructor(e){super(),this.privFormat=void 0===e?n.AudioStreamFormatImpl.getDefaultInputFormat():e,this.privEvents=new t.EventSource,this.privId=r.createNoDashGuid(),this.privStream=new t.ChunkedArrayBufferStream(this.privFormat.avgBytesPerSec/10)}get format(){return Promise.resolve(this.privFormat)}write(e){this.privStream.writeStreamChunk({buffer:e,isEnd:!1,timeReceived:Date.now()})}close(){this.privStream.close()}id(){return this.privId}turnOn(){this.onEvent(new t.AudioSourceInitializingEvent(this.privId)),this.onEvent(new t.AudioSourceReadyEvent(this.privId))}async attach(e){this.onEvent(new t.AudioStreamNodeAttachingEvent(this.privId,e)),await this.turnOn();const r=this.privStream;return this.onEvent(new t.AudioStreamNodeAttachedEvent(this.privId,e)),{detach:async()=>(this.onEvent(new t.AudioStreamNodeDetachedEvent(this.privId,e)),this.turnOff()),id:()=>e,read:()=>r.read()}}detach(e){this.onEvent(new t.AudioStreamNodeDetachedEvent(this.privId,e))}turnOff(){}get events(){return this.privEvents}get deviceInfo(){return Promise.resolve({bitspersample:this.privFormat.bitsPerSample,channelcount:this.privFormat.channels,connectivity:e.connectivity.Unknown,manufacturer:"Speech SDK",model:"PushStream",samplerate:this.privFormat.samplesPerSec,type:e.type.Stream})}onEvent(e){this.privEvents.onEvent(e),t.Events.instance.onEvent(e)}toBuffer(e){const t=Buffer.alloc(e.byteLength),r=new Uint8Array(e);for(let i=0;i<t.length;++i)t[i]=r[i];return t}}ti.PushAudioInputStreamImpl=a;class c extends o{constructor(){super()}static create(e,t){return new p(e,t)}}ti.PullAudioInputStream=c;class p extends c{constructor(e,n){super(),this.privFormat=void 0===n?i.AudioStreamFormat.getDefaultInputFormat():n,this.privEvents=new t.EventSource,this.privId=r.createNoDashGuid(),this.privCallback=e,this.privIsClosed=!1,this.privBufferSize=this.privFormat.avgBytesPerSec/10}get format(){return Promise.resolve(this.privFormat)}close(){this.privIsClosed=!0,this.privCallback.close()}id(){return this.privId}turnOn(){this.onEvent(new t.AudioSourceInitializingEvent(this.privId)),this.onEvent(new t.AudioSourceReadyEvent(this.privId))}async attach(e){return this.onEvent(new t.AudioStreamNodeAttachingEvent(this.privId,e)),await this.turnOn(),this.onEvent(new t.AudioStreamNodeAttachedEvent(this.privId,e)),{detach:()=>(this.privCallback.close(),this.onEvent(new t.AudioStreamNodeDetachedEvent(this.privId,e)),this.turnOff()),id:()=>e,read:()=>{let e,t=0;for(;t<this.privBufferSize;){const r=new ArrayBuffer(this.privBufferSize-t),i=this.privCallback.read(r);if(void 0===e)e=r;else{new Int8Array(e).set(new Int8Array(r),t)}if(0===i)break;t+=i}return Promise.resolve({buffer:e.slice(0,t),isEnd:this.privIsClosed||0===t,timeReceived:Date.now()})}}}detach(e){this.onEvent(new t.AudioStreamNodeDetachedEvent(this.privId,e))}turnOff(){}get events(){return this.privEvents}get deviceInfo(){return Promise.resolve({bitspersample:this.privFormat.bitsPerSample,channelcount:this.privFormat.channels,connectivity:e.connectivity.Unknown,manufacturer:"Speech SDK",model:"PullStream",samplerate:this.privFormat.samplesPerSec,type:e.type.Stream})}onEvent(e){this.privEvents.onEvent(e),t.Events.instance.onEvent(e)}}return ti.PullAudioInputStreamImpl=p,ti}var ii,ni,oi,si,ai={},ci={},pi={};function ui(){return ii||(ii=1,e=pi,Object.defineProperty(e,"__esModule",{value:!0}),e.SpeechSynthesisOutputFormat=void 0,(t=e.SpeechSynthesisOutputFormat||(e.SpeechSynthesisOutputFormat={}))[t.Raw8Khz8BitMonoMULaw=0]="Raw8Khz8BitMonoMULaw",t[t.Riff16Khz16KbpsMonoSiren=1]="Riff16Khz16KbpsMonoSiren",t[t.Audio16Khz16KbpsMonoSiren=2]="Audio16Khz16KbpsMonoSiren",t[t.Audio16Khz32KBitRateMonoMp3=3]="Audio16Khz32KBitRateMonoMp3",t[t.Audio16Khz128KBitRateMonoMp3=4]="Audio16Khz128KBitRateMonoMp3",t[t.Audio16Khz64KBitRateMonoMp3=5]="Audio16Khz64KBitRateMonoMp3",t[t.Audio24Khz48KBitRateMonoMp3=6]="Audio24Khz48KBitRateMonoMp3",t[t.Audio24Khz96KBitRateMonoMp3=7]="Audio24Khz96KBitRateMonoMp3",t[t.Audio24Khz160KBitRateMonoMp3=8]="Audio24Khz160KBitRateMonoMp3",t[t.Raw16Khz16BitMonoTrueSilk=9]="Raw16Khz16BitMonoTrueSilk",t[t.Riff16Khz16BitMonoPcm=10]="Riff16Khz16BitMonoPcm",t[t.Riff8Khz16BitMonoPcm=11]="Riff8Khz16BitMonoPcm",t[t.Riff24Khz16BitMonoPcm=12]="Riff24Khz16BitMonoPcm",t[t.Riff8Khz8BitMonoMULaw=13]="Riff8Khz8BitMonoMULaw",t[t.Raw16Khz16BitMonoPcm=14]="Raw16Khz16BitMonoPcm",t[t.Raw24Khz16BitMonoPcm=15]="Raw24Khz16BitMonoPcm",t[t.Raw8Khz16BitMonoPcm=16]="Raw8Khz16BitMonoPcm",t[t.Ogg16Khz16BitMonoOpus=17]="Ogg16Khz16BitMonoOpus",t[t.Ogg24Khz16BitMonoOpus=18]="Ogg24Khz16BitMonoOpus",t[t.Raw48Khz16BitMonoPcm=19]="Raw48Khz16BitMonoPcm",t[t.Riff48Khz16BitMonoPcm=20]="Riff48Khz16BitMonoPcm",t[t.Audio48Khz96KBitRateMonoMp3=21]="Audio48Khz96KBitRateMonoMp3",t[t.Audio48Khz192KBitRateMonoMp3=22]="Audio48Khz192KBitRateMonoMp3",t[t.Ogg48Khz16BitMonoOpus=23]="Ogg48Khz16BitMonoOpus",t[t.Webm16Khz16BitMonoOpus=24]="Webm16Khz16BitMonoOpus",t[t.Webm24Khz16BitMonoOpus=25]="Webm24Khz16BitMonoOpus",t[t.Raw24Khz16BitMonoTrueSilk=26]="Raw24Khz16BitMonoTrueSilk",t[t.Raw8Khz8BitMonoALaw=27]="Raw8Khz8BitMonoALaw",t[t.Riff8Khz8BitMonoALaw=28]="Riff8Khz8BitMonoALaw",t[t.Webm24Khz16Bit24KbpsMonoOpus=29]="Webm24Khz16Bit24KbpsMonoOpus",t[t.Audio16Khz16Bit32KbpsMonoOpus=30]="Audio16Khz16Bit32KbpsMonoOpus",t[t.Audio24Khz16Bit48KbpsMonoOpus=31]="Audio24Khz16Bit48KbpsMonoOpus",t[t.Audio24Khz16Bit24KbpsMonoOpus=32]="Audio24Khz16Bit24KbpsMonoOpus",t[t.Raw22050Hz16BitMonoPcm=33]="Raw22050Hz16BitMonoPcm",t[t.Riff22050Hz16BitMonoPcm=34]="Riff22050Hz16BitMonoPcm",t[t.Raw44100Hz16BitMonoPcm=35]="Raw44100Hz16BitMonoPcm",t[t.Riff44100Hz16BitMonoPcm=36]="Riff44100Hz16BitMonoPcm",t[t.AmrWb16000Hz=37]="AmrWb16000Hz",t[t.G72216Khz64Kbps=38]="G72216Khz64Kbps"),pi;var e,t}function hi(){if(ni)return ci;ni=1,Object.defineProperty(ci,"__esModule",{value:!0}),ci.AudioOutputFormatImpl=void 0;const e=ui(),t=Dr();class r extends t.AudioStreamFormatImpl{constructor(e,t,r,i,n,o,s,a,c){super(r,o,t,e),this.formatTag=e,this.avgBytesPerSec=i,this.blockAlign=n,this.priAudioFormatString=s,this.priRequestAudioFormatString=a,this.priHasHeader=c}static fromSpeechSynthesisOutputFormat(e){return void 0===e?r.getDefaultOutputFormat():r.fromSpeechSynthesisOutputFormatString(r.SpeechSynthesisOutputFormatToString[e])}static fromSpeechSynthesisOutputFormatString(e){switch(e){case"raw-8khz-8bit-mono-mulaw":return new r(t.AudioFormatTag.MuLaw,1,8e3,8e3,1,8,e,e,!1);case"riff-16khz-16kbps-mono-siren":return new r(t.AudioFormatTag.Siren,1,16e3,2e3,40,0,e,"audio-16khz-16kbps-mono-siren",!0);case"audio-16khz-16kbps-mono-siren":return new r(t.AudioFormatTag.Siren,1,16e3,2e3,40,0,e,e,!1);case"audio-16khz-32kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,16e3,4096,2,16,e,e,!1);case"audio-16khz-128kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,16e3,16384,2,16,e,e,!1);case"audio-16khz-64kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,16e3,8192,2,16,e,e,!1);case"audio-24khz-48kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,24e3,6144,2,16,e,e,!1);case"audio-24khz-96kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,24e3,12288,2,16,e,e,!1);case"audio-24khz-160kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,24e3,20480,2,16,e,e,!1);case"raw-16khz-16bit-mono-truesilk":return new r(t.AudioFormatTag.SILKSkype,1,16e3,32e3,2,16,e,e,!1);case"riff-8khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,8e3,16e3,2,16,e,"raw-8khz-16bit-mono-pcm",!0);case"riff-24khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,24e3,48e3,2,16,e,"raw-24khz-16bit-mono-pcm",!0);case"riff-8khz-8bit-mono-mulaw":return new r(t.AudioFormatTag.MuLaw,1,8e3,8e3,1,8,e,"raw-8khz-8bit-mono-mulaw",!0);case"raw-16khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,16e3,32e3,2,16,e,"raw-16khz-16bit-mono-pcm",!1);case"raw-24khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,24e3,48e3,2,16,e,"raw-24khz-16bit-mono-pcm",!1);case"raw-8khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,8e3,16e3,2,16,e,"raw-8khz-16bit-mono-pcm",!1);case"ogg-16khz-16bit-mono-opus":return new r(t.AudioFormatTag.OGG_OPUS,1,16e3,8192,2,16,e,e,!1);case"ogg-24khz-16bit-mono-opus":return new r(t.AudioFormatTag.OGG_OPUS,1,24e3,8192,2,16,e,e,!1);case"raw-48khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,48e3,96e3,2,16,e,"raw-48khz-16bit-mono-pcm",!1);case"riff-48khz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,48e3,96e3,2,16,e,"raw-48khz-16bit-mono-pcm",!0);case"audio-48khz-96kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,48e3,12288,2,16,e,e,!1);case"audio-48khz-192kbitrate-mono-mp3":return new r(t.AudioFormatTag.MP3,1,48e3,24576,2,16,e,e,!1);case"ogg-48khz-16bit-mono-opus":return new r(t.AudioFormatTag.OGG_OPUS,1,48e3,12e3,2,16,e,e,!1);case"webm-16khz-16bit-mono-opus":return new r(t.AudioFormatTag.WEBM_OPUS,1,16e3,4e3,2,16,e,e,!1);case"webm-24khz-16bit-mono-opus":return new r(t.AudioFormatTag.WEBM_OPUS,1,24e3,6e3,2,16,e,e,!1);case"webm-24khz-16bit-24kbps-mono-opus":return new r(t.AudioFormatTag.WEBM_OPUS,1,24e3,3e3,2,16,e,e,!1);case"audio-16khz-16bit-32kbps-mono-opus":return new r(t.AudioFormatTag.OPUS,1,16e3,4e3,2,16,e,e,!1);case"audio-24khz-16bit-48kbps-mono-opus":return new r(t.AudioFormatTag.OPUS,1,24e3,6e3,2,16,e,e,!1);case"audio-24khz-16bit-24kbps-mono-opus":return new r(t.AudioFormatTag.OPUS,1,24e3,3e3,2,16,e,e,!1);case"audio-24khz-16bit-mono-flac":return new r(t.AudioFormatTag.FLAC,1,24e3,24e3,2,16,e,e,!1);case"audio-48khz-16bit-mono-flac":return new r(t.AudioFormatTag.FLAC,1,48e3,3e4,2,16,e,e,!1);case"raw-24khz-16bit-mono-truesilk":return new r(t.AudioFormatTag.SILKSkype,1,24e3,48e3,2,16,e,e,!1);case"raw-8khz-8bit-mono-alaw":return new r(t.AudioFormatTag.ALaw,1,8e3,8e3,1,8,e,e,!1);case"riff-8khz-8bit-mono-alaw":return new r(t.AudioFormatTag.ALaw,1,8e3,8e3,1,8,e,"raw-8khz-8bit-mono-alaw",!0);case"raw-22050hz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,22050,44100,2,16,e,e,!1);case"riff-22050hz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,22050,44100,2,16,e,"raw-22050hz-16bit-mono-pcm",!0);case"raw-44100hz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,44100,88200,2,16,e,e,!1);case"riff-44100hz-16bit-mono-pcm":return new r(t.AudioFormatTag.PCM,1,44100,88200,2,16,e,"raw-44100hz-16bit-mono-pcm",!0);case"amr-wb-16000h":return new r(t.AudioFormatTag.AMR_WB,1,16e3,3052,2,16,e,e,!1);case"g722-16khz-64kbps":return new r(t.AudioFormatTag.G722,1,16e3,8e3,2,16,e,e,!1);default:return new r(t.AudioFormatTag.PCM,1,16e3,32e3,2,16,"riff-16khz-16bit-mono-pcm","raw-16khz-16bit-mono-pcm",!0)}}static getDefaultOutputFormat(){return r.fromSpeechSynthesisOutputFormatString("undefined"!=typeof window?"audio-24khz-48kbitrate-mono-mp3":"riff-16khz-16bit-mono-pcm")}get hasHeader(){return this.priHasHeader}get header(){if(this.hasHeader)return this.privHeader}updateHeader(e){if(this.priHasHeader){const t=new DataView(this.privHeader);t.setUint32(4,e+this.privHeader.byteLength-8,!0),t.setUint32(40,e,!0)}}get requestAudioFormatString(){return this.priRequestAudioFormatString}addHeader(e){if(!this.hasHeader)return e;this.updateHeader(e.byteLength);const t=new Uint8Array(e.byteLength+this.header.byteLength);return t.set(new Uint8Array(this.header),0),t.set(new Uint8Array(e),this.header.byteLength),t.buffer}}return ci.AudioOutputFormatImpl=r,r.SpeechSynthesisOutputFormatToString={[e.SpeechSynthesisOutputFormat.Raw8Khz8BitMonoMULaw]:"raw-8khz-8bit-mono-mulaw",[e.SpeechSynthesisOutputFormat.Riff16Khz16KbpsMonoSiren]:"riff-16khz-16kbps-mono-siren",[e.SpeechSynthesisOutputFormat.Audio16Khz16KbpsMonoSiren]:"audio-16khz-16kbps-mono-siren",[e.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3]:"audio-16khz-32kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Audio16Khz128KBitRateMonoMp3]:"audio-16khz-128kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Audio16Khz64KBitRateMonoMp3]:"audio-16khz-64kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3]:"audio-24khz-48kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Audio24Khz96KBitRateMonoMp3]:"audio-24khz-96kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Audio24Khz160KBitRateMonoMp3]:"audio-24khz-160kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Raw16Khz16BitMonoTrueSilk]:"raw-16khz-16bit-mono-truesilk",[e.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm]:"riff-16khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Riff8Khz16BitMonoPcm]:"riff-8khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm]:"riff-24khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Riff8Khz8BitMonoMULaw]:"riff-8khz-8bit-mono-mulaw",[e.SpeechSynthesisOutputFormat.Raw16Khz16BitMonoPcm]:"raw-16khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Raw24Khz16BitMonoPcm]:"raw-24khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Raw8Khz16BitMonoPcm]:"raw-8khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Ogg16Khz16BitMonoOpus]:"ogg-16khz-16bit-mono-opus",[e.SpeechSynthesisOutputFormat.Ogg24Khz16BitMonoOpus]:"ogg-24khz-16bit-mono-opus",[e.SpeechSynthesisOutputFormat.Raw48Khz16BitMonoPcm]:"raw-48khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Riff48Khz16BitMonoPcm]:"riff-48khz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Audio48Khz96KBitRateMonoMp3]:"audio-48khz-96kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Audio48Khz192KBitRateMonoMp3]:"audio-48khz-192kbitrate-mono-mp3",[e.SpeechSynthesisOutputFormat.Ogg48Khz16BitMonoOpus]:"ogg-48khz-16bit-mono-opus",[e.SpeechSynthesisOutputFormat.Webm16Khz16BitMonoOpus]:"webm-16khz-16bit-mono-opus",[e.SpeechSynthesisOutputFormat.Webm24Khz16BitMonoOpus]:"webm-24khz-16bit-mono-opus",[e.SpeechSynthesisOutputFormat.Webm24Khz16Bit24KbpsMonoOpus]:"webm-24khz-16bit-24kbps-mono-opus",[e.SpeechSynthesisOutputFormat.Raw24Khz16BitMonoTrueSilk]:"raw-24khz-16bit-mono-truesilk",[e.SpeechSynthesisOutputFormat.Raw8Khz8BitMonoALaw]:"raw-8khz-8bit-mono-alaw",[e.SpeechSynthesisOutputFormat.Riff8Khz8BitMonoALaw]:"riff-8khz-8bit-mono-alaw",[e.SpeechSynthesisOutputFormat.Audio16Khz16Bit32KbpsMonoOpus]:"audio-16khz-16bit-32kbps-mono-opus",[e.SpeechSynthesisOutputFormat.Audio24Khz16Bit48KbpsMonoOpus]:"audio-24khz-16bit-48kbps-mono-opus",[e.SpeechSynthesisOutputFormat.Audio24Khz16Bit24KbpsMonoOpus]:"audio-24khz-16bit-24kbps-mono-opus",[e.SpeechSynthesisOutputFormat.Raw22050Hz16BitMonoPcm]:"raw-22050hz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Riff22050Hz16BitMonoPcm]:"riff-22050hz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Raw44100Hz16BitMonoPcm]:"raw-44100hz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.Riff44100Hz16BitMonoPcm]:"riff-44100hz-16bit-mono-pcm",[e.SpeechSynthesisOutputFormat.AmrWb16000Hz]:"amr-wb-16000hz",[e.SpeechSynthesisOutputFormat.G72216Khz64Kbps]:"g722-16khz-64kbps"},ci}function di(){if(oi)return ai;oi=1,Object.defineProperty(ai,"__esModule",{value:!0}),ai.PushAudioOutputStreamImpl=ai.PushAudioOutputStream=ai.PullAudioOutputStreamImpl=ai.PullAudioOutputStream=ai.AudioOutputStream=void 0;const e=Gt(),t=Rr(),r=hi();let i=class{constructor(){}static createPullStream(){return n.create()}};ai.AudioOutputStream=i;class n extends i{static create(){return new o}}ai.PullAudioOutputStream=n;class o extends n{constructor(){super(),this.privId=e.createNoDashGuid(),this.privStream=new e.Stream}set format(e){null==e&&(this.privFormat=r.AudioOutputFormatImpl.getDefaultOutputFormat()),this.privFormat=e}get format(){return this.privFormat}get isClosed(){return this.privStream.isClosed}id(){return this.privId}async read(e){const t=new Int8Array(e);let r=0;if(void 0!==this.privLastChunkView){if(this.privLastChunkView.length>e.byteLength)return t.set(this.privLastChunkView.slice(0,e.byteLength)),this.privLastChunkView=this.privLastChunkView.slice(e.byteLength),Promise.resolve(e.byteLength);t.set(this.privLastChunkView),r=this.privLastChunkView.length,this.privLastChunkView=void 0}for(;r<e.byteLength&&!this.privStream.isReadEnded;){const i=await this.privStream.read();if(void 0===i||i.isEnd)this.privStream.readEnded();else{let n;i.buffer.byteLength>e.byteLength-r?(n=i.buffer.slice(0,e.byteLength-r),this.privLastChunkView=new Int8Array(i.buffer.slice(e.byteLength-r))):n=i.buffer,t.set(new Int8Array(n),r),r+=n.byteLength}}return r}write(e){t.Contracts.throwIfNullOrUndefined(this.privStream,"must set format before writing"),this.privStream.writeStreamChunk({buffer:e,isEnd:!1,timeReceived:Date.now()})}close(){this.privStream.close()}}ai.PullAudioOutputStreamImpl=o;class s extends i{constructor(){super()}static create(e){return new a(e)}}ai.PushAudioOutputStream=s;class a extends s{constructor(t){super(),this.privId=e.createNoDashGuid(),this.privCallback=t}set format(e){}write(e){this.privCallback.write&&this.privCallback.write(e)}close(){this.privCallback.close&&this.privCallback.close()}id(){return this.privId}}return ai.PushAudioOutputStreamImpl=a,ai}function vi(){if(si)return Xr;si=1,Object.defineProperty(Xr,"__esModule",{value:!0}),Xr.AudioOutputConfigImpl=Xr.AudioConfigImpl=Xr.AudioConfig=void 0;const e=Vp(),t=Rr(),r=Mp(),i=Yr(),n=ri(),o=di();let s=class t{static fromDefaultMicrophoneInput(){const t=new e.PcmRecorder(!0);return new a(new e.MicAudioSource(t))}static fromMicrophoneInput(t){const r=new e.PcmRecorder(!0);return new a(new e.MicAudioSource(r,t))}static fromWavFileInput(t,r="unnamedBuffer.wav"){return new a(new e.FileAudioSource(t,r))}static fromStreamInput(t){if(t instanceof r.PullAudioInputStreamCallback)return new a(new n.PullAudioInputStreamImpl(t));if(t instanceof r.AudioInputStream)return new a(t);if("undefined"!=typeof MediaStream&&t instanceof MediaStream){const r=new e.PcmRecorder(!1);return new a(new e.MicAudioSource(r,null,null,t))}throw new Error("Not Supported Type")}static fromDefaultSpeakerOutput(){return new c(new r.SpeakerAudioDestination)}static fromSpeakerOutput(e){if(void 0===e)return t.fromDefaultSpeakerOutput();if(e instanceof r.SpeakerAudioDestination)return new c(e);throw new Error("Not Supported Type")}static fromAudioFileOutput(e){return new c(new i.AudioFileWriter(e))}static fromStreamOutput(e){if(e instanceof r.PushAudioOutputStreamCallback)return new c(new o.PushAudioOutputStreamImpl(e));if(e instanceof r.PushAudioOutputStream)return new c(e);if(e instanceof r.PullAudioOutputStream)return new c(e);throw new Error("Not Supported Type")}};Xr.AudioConfig=s;class a extends s{constructor(e){super(),this.privSource=e}get format(){return this.privSource.format}close(e,t){this.privSource.turnOff().then((()=>{e&&e()}),(e=>{t&&t(e)}))}id(){return this.privSource.id()}turnOn(){return this.privSource.turnOn()}attach(e){return this.privSource.attach(e)}detach(e){return this.privSource.detach(e)}turnOff(){return this.privSource.turnOff()}get events(){return this.privSource.events}setProperty(e,r){if(t.Contracts.throwIfNull(r,"value"),void 0===this.privSource.setProperty)throw new Error("This AudioConfig instance does not support setting properties.");this.privSource.setProperty(e,r)}getProperty(e,t){if(void 0!==this.privSource.getProperty)return this.privSource.getProperty(e,t);throw new Error("This AudioConfig instance does not support getting properties.")}get deviceInfo(){return this.privSource.deviceInfo}}Xr.AudioConfigImpl=a;class c extends s{constructor(e){super(),this.privDestination=e}set format(e){this.privDestination.format=e}write(e){this.privDestination.write(e)}close(){this.privDestination.close()}id(){return this.privDestination.id()}setProperty(){throw new Error("This AudioConfig instance does not support setting properties.")}getProperty(){throw new Error("This AudioConfig instance does not support getting properties.")}}return Xr.AudioOutputConfigImpl=c,Xr}var li,gi={};function fi(){return li||(li=1,e=gi,Object.defineProperty(e,"__esModule",{value:!0}),e.CancellationReason=void 0,(t=e.CancellationReason||(e.CancellationReason={}))[t.Error=0]="Error",t[t.EndOfStream=1]="EndOfStream"),gi;var e,t}var mi,Si={};function yi(){if(mi)return Si;mi=1,Object.defineProperty(Si,"__esModule",{value:!0}),Si.PullAudioInputStreamCallback=void 0;return Si.PullAudioInputStreamCallback=class{},Si}var Ci,Pi={};function Ri(){if(Ci)return Pi;Ci=1,Object.defineProperty(Pi,"__esModule",{value:!0}),Pi.PushAudioOutputStreamCallback=void 0;return Pi.PushAudioOutputStreamCallback=class{},Pi}var Ii,wi={};function Ti(){if(Ii)return wi;Ii=1,Object.defineProperty(wi,"__esModule",{value:!0}),wi.KeywordRecognitionModel=void 0;const e=Rr();return wi.KeywordRecognitionModel=class{constructor(){this.privDisposed=!1}static fromFile(t){throw e.Contracts.throwIfFileDoesNotExist(t,"fileName"),new Error("Not yet implemented.")}static fromStream(t){throw e.Contracts.throwIfNull(t,"file"),new Error("Not yet implemented.")}close(){this.privDisposed||(this.privDisposed=!0)}},wi}var Ai,Ei={};function bi(){if(Ai)return Ei;Ai=1,Object.defineProperty(Ei,"__esModule",{value:!0}),Ei.SessionEventArgs=void 0;return Ei.SessionEventArgs=class{constructor(e){this.privSessionId=e}get sessionId(){return this.privSessionId}},Ei}var Oi,Mi={};function Di(){if(Oi)return Mi;Oi=1,Object.defineProperty(Mi,"__esModule",{value:!0}),Mi.RecognitionEventArgs=void 0;const e=Mp();let t=class extends e.SessionEventArgs{constructor(e,t){super(t),this.privOffset=e}get offset(){return this.privOffset}};return Mi.RecognitionEventArgs=t,Mi}var ki,zi={};function Ni(){return ki||(ki=1,e=zi,Object.defineProperty(e,"__esModule",{value:!0}),e.OutputFormat=void 0,(t=e.OutputFormat||(e.OutputFormat={}))[t.Simple=0]="Simple",t[t.Detailed=1]="Detailed"),zi;var e,t}var _i,Li={};function xi(){if(_i)return Li;_i=1,Object.defineProperty(Li,"__esModule",{value:!0}),Li.IntentRecognitionEventArgs=void 0;const e=Mp();let t=class extends e.RecognitionEventArgs{constructor(e,t,r){super(t,r),this.privResult=e}get result(){return this.privResult}};return Li.IntentRecognitionEventArgs=t,Li}var Fi,Bi={};function ji(){if(Fi)return Bi;Fi=1,Object.defineProperty(Bi,"__esModule",{value:!0}),Bi.RecognitionResult=void 0;return Bi.RecognitionResult=class{constructor(e,t,r,i,n,o,s,a,c,p){this.privResultId=e,this.privReason=t,this.privText=r,this.privDuration=i,this.privOffset=n,this.privLanguage=o,this.privLanguageDetectionConfidence=s,this.privErrorDetails=a,this.privJson=c,this.privProperties=p}get resultId(){return this.privResultId}get reason(){return this.privReason}get text(){return this.privText}get duration(){return this.privDuration}get offset(){return this.privOffset}get language(){return this.privLanguage}get languageDetectionConfidence(){return this.privLanguageDetectionConfidence}get errorDetails(){return this.privErrorDetails}get json(){return this.privJson}get properties(){return this.privProperties}},Bi}var Ui,qi={};function Wi(){if(Ui)return qi;Ui=1,Object.defineProperty(qi,"__esModule",{value:!0}),qi.SpeechRecognitionResult=void 0;const e=Mp();let t=class extends e.RecognitionResult{constructor(e,t,r,i,n,o,s,a,c,p,u){super(e,t,r,i,n,o,s,c,p,u),this.privSpeakerId=a}get speakerId(){return this.privSpeakerId}};return qi.SpeechRecognitionResult=t,qi}var Hi,Vi={};function Ki(){if(Hi)return Vi;Hi=1,Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.IntentRecognitionResult=void 0;const e=Mp();let t=class extends e.SpeechRecognitionResult{constructor(e,t,r,i,n,o,s,a,c,p,u){super(t,r,i,n,o,s,a,void 0,c,p,u),this.privIntentId=e}get intentId(){return this.privIntentId}};return Vi.IntentRecognitionResult=t,Vi}var Ji,Gi={};function $i(){if(Ji)return Gi;Ji=1,Object.defineProperty(Gi,"__esModule",{value:!0}),Gi.LanguageUnderstandingModelImpl=Gi.LanguageUnderstandingModel=void 0;const e=Rr();let t=class{constructor(){}static fromEndpoint(t){e.Contracts.throwIfNull(t,"uri"),e.Contracts.throwIfNullOrWhitespace(t.hostname,"uri");const i=new r,n=t.host.indexOf(".");if(-1===n)throw new Error("Could not determine region from endpoint");i.region=t.host.substr(0,n);const o=t.pathname.lastIndexOf("/")+1;if(-1===o)throw new Error("Could not determine appId from endpoint");if(i.appId=t.pathname.substr(o),i.subscriptionKey=t.searchParams.get("subscription-key"),void 0===i.subscriptionKey)throw new Error("Could not determine subscription key from endpoint");return i}static fromAppId(t){e.Contracts.throwIfNullOrWhitespace(t,"appId");const i=new r;return i.appId=t,i}static fromSubscription(t,i,n){e.Contracts.throwIfNullOrWhitespace(t,"subscriptionKey"),e.Contracts.throwIfNullOrWhitespace(i,"appId"),e.Contracts.throwIfNullOrWhitespace(n,"region");const o=new r;return o.appId=i,o.region=n,o.subscriptionKey=t,o}};Gi.LanguageUnderstandingModel=t;class r extends t{}return Gi.LanguageUnderstandingModelImpl=r,Gi}var Qi,Xi={};function Zi(){if(Qi)return Xi;Qi=1,Object.defineProperty(Xi,"__esModule",{value:!0}),Xi.MeetingTranscriptionEventArgs=Xi.ConversationTranscriptionEventArgs=Xi.SpeechRecognitionEventArgs=void 0;const e=Mp();let t=class extends e.RecognitionEventArgs{constructor(e,t,r){super(t,r),this.privResult=e}get result(){return this.privResult}};Xi.SpeechRecognitionEventArgs=t;class r extends e.RecognitionEventArgs{constructor(e,t,r){super(t,r),this.privResult=e}get result(){return this.privResult}}Xi.ConversationTranscriptionEventArgs=r;return Xi.MeetingTranscriptionEventArgs=class extends t{},Xi}var Yi,en,tn={},rn={};function nn(){if(Yi)return rn;Yi=1,Object.defineProperty(rn,"__esModule",{value:!0}),rn.CancellationEventArgsBase=void 0;const e=Mp();let t=class extends e.RecognitionEventArgs{constructor(e,t,r,i,n){super(i,n),this.privReason=e,this.privErrorDetails=t,this.privErrorCode=r}get reason(){return this.privReason}get errorCode(){return this.privErrorCode}get errorDetails(){return this.privErrorDetails}};return rn.CancellationEventArgsBase=t,rn}function on(){if(en)return tn;en=1,Object.defineProperty(tn,"__esModule",{value:!0}),tn.SpeechRecognitionCanceledEventArgs=void 0;const e=nn();let t=class extends e.CancellationEventArgsBase{};return tn.SpeechRecognitionCanceledEventArgs=t,tn}var sn,an={};function cn(){if(sn)return an;sn=1,Object.defineProperty(an,"__esModule",{value:!0}),an.TranslationRecognitionEventArgs=void 0;const e=Mp();let t=class extends e.RecognitionEventArgs{constructor(e,t,r){super(t,r),this.privResult=e}get result(){return this.privResult}};return an.TranslationRecognitionEventArgs=t,an}var pn,un={};function hn(){if(pn)return un;pn=1,Object.defineProperty(un,"__esModule",{value:!0}),un.TranslationSynthesisEventArgs=void 0;const e=Mp();let t=class extends e.SessionEventArgs{constructor(e,t){super(t),this.privResult=e}get result(){return this.privResult}};return un.TranslationSynthesisEventArgs=t,un}var dn,vn={};function ln(){if(dn)return vn;dn=1,Object.defineProperty(vn,"__esModule",{value:!0}),vn.TranslationRecognitionResult=void 0;const e=Mp();let t=class t extends e.SpeechRecognitionResult{constructor(e,t,r,i,n,o,s,a,c,p,u){super(t,r,i,n,o,s,a,void 0,c,p,u),this.privTranslations=e}static fromSpeechRecognitionResult(e){return new t(void 0,e.resultId,e.reason,e.text,e.duration,e.offset,e.language,e.languageDetectionConfidence,e.errorDetails,e.json,e.properties)}get translations(){return this.privTranslations}};return vn.TranslationRecognitionResult=t,vn}var gn,fn={};function mn(){if(gn)return fn;gn=1,Object.defineProperty(fn,"__esModule",{value:!0}),fn.TranslationSynthesisResult=void 0;return fn.TranslationSynthesisResult=class{constructor(e,t){this.privReason=e,this.privAudio=t}get audio(){return this.privAudio}get reason(){return this.privReason}},fn}var Sn,yn={};function Cn(){return Sn||(Sn=1,e=yn,Object.defineProperty(e,"__esModule",{value:!0}),e.ResultReason=void 0,(t=e.ResultReason||(e.ResultReason={}))[t.NoMatch=0]="NoMatch",t[t.Canceled=1]="Canceled",t[t.RecognizingSpeech=2]="RecognizingSpeech",t[t.RecognizedSpeech=3]="RecognizedSpeech",t[t.RecognizedKeyword=4]="RecognizedKeyword",t[t.RecognizingIntent=5]="RecognizingIntent",t[t.RecognizedIntent=6]="RecognizedIntent",t[t.TranslatingSpeech=7]="TranslatingSpeech",t[t.TranslatedSpeech=8]="TranslatedSpeech",t[t.SynthesizingAudio=9]="SynthesizingAudio",t[t.SynthesizingAudioCompleted=10]="SynthesizingAudioCompleted",t[t.SynthesizingAudioStarted=11]="SynthesizingAudioStarted",t[t.EnrollingVoiceProfile=12]="EnrollingVoiceProfile",t[t.EnrolledVoiceProfile=13]="EnrolledVoiceProfile",t[t.RecognizedSpeakers=14]="RecognizedSpeakers",t[t.RecognizedSpeaker=15]="RecognizedSpeaker",t[t.ResetVoiceProfile=16]="ResetVoiceProfile",t[t.DeletedVoiceProfile=17]="DeletedVoiceProfile",t[t.VoicesListRetrieved=18]="VoicesListRetrieved",t[t.TranslatingParticipantSpeech=19]="TranslatingParticipantSpeech",t[t.TranslatedParticipantSpeech=20]="TranslatedParticipantSpeech",t[t.TranslatedInstantMessage=21]="TranslatedInstantMessage",t[t.TranslatedParticipantInstantMessage=22]="TranslatedParticipantInstantMessage"),yn;var e,t}var Pn,Rn={};function In(){if(Pn)return Rn;Pn=1,Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.SpeechConfigImpl=Rn.SpeechConfig=void 0;const e=jv(),t=Rr(),r=Mp();let i=class{constructor(){}static fromSubscription(e,i){t.Contracts.throwIfNullOrWhitespace(e,"subscriptionKey"),t.Contracts.throwIfNullOrWhitespace(i,"region");const o=new n;return o.setProperty(r.PropertyId.SpeechServiceConnection_Region,i),o.setProperty(r.PropertyId.SpeechServiceConnection_IntentRegion,i),o.setProperty(r.PropertyId.SpeechServiceConnection_Key,e),o}static fromEndpoint(e,i){t.Contracts.throwIfNull(e,"endpoint");const o=new n;return o.setProperty(r.PropertyId.SpeechServiceConnection_Endpoint,e.href),void 0!==i&&o.setProperty(r.PropertyId.SpeechServiceConnection_Key,i),o}static fromHost(e,i){t.Contracts.throwIfNull(e,"hostName");const o=new n;return o.setProperty(r.PropertyId.SpeechServiceConnection_Host,e.protocol+"//"+e.hostname+(""===e.port?"":":"+e.port)),void 0!==i&&o.setProperty(r.PropertyId.SpeechServiceConnection_Key,i),o}static fromAuthorizationToken(e,i){t.Contracts.throwIfNull(e,"authorizationToken"),t.Contracts.throwIfNullOrWhitespace(i,"region");const o=new n;return o.setProperty(r.PropertyId.SpeechServiceConnection_Region,i),o.setProperty(r.PropertyId.SpeechServiceConnection_IntentRegion,i),o.authorizationToken=e,o}close(){}};Rn.SpeechConfig=i;class n extends i{constructor(){super(),this.privProperties=new r.PropertyCollection,this.speechRecognitionLanguage="en-US",this.outputFormat=r.OutputFormat.Simple}get properties(){return this.privProperties}get endPoint(){return new URL(this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_Endpoint))}get subscriptionKey(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_Key)}get region(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_Region)}get authorizationToken(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,e)}get speechRecognitionLanguage(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage)}set speechRecognitionLanguage(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage,e)}get autoDetectSourceLanguages(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages)}set autoDetectSourceLanguages(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,e)}get outputFormat(){return r.OutputFormat[this.privProperties.getProperty(e.OutputFormatPropertyName,void 0)]}set outputFormat(t){this.privProperties.setProperty(e.OutputFormatPropertyName,r.OutputFormat[t])}get endpointId(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_EndpointId)}set endpointId(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_EndpointId,e)}setProperty(e,r){t.Contracts.throwIfNull(r,"value"),this.privProperties.setProperty(e,r)}getProperty(e,t){return this.privProperties.getProperty(e,t)}setProxy(e,t,i,n){this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyHostName],e),this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyPort],t),this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyUserName],i),this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyPassword],n)}setServiceProperty(t,r){const i=JSON.parse(this.privProperties.getProperty(e.ServicePropertiesPropertyName,"{}"));i[t]=r,this.privProperties.setProperty(e.ServicePropertiesPropertyName,JSON.stringify(i))}setProfanity(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceResponse_ProfanityOption,r.ProfanityOption[e])}enableAudioLogging(){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_EnableAudioLogging,"true")}requestWordLevelTimestamps(){this.privProperties.setProperty(r.PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps,"true"),this.privProperties.setProperty(e.OutputFormatPropertyName,r.OutputFormat[r.OutputFormat.Detailed])}enableDictation(){this.privProperties.setProperty(e.ForceDictationPropertyName,"true")}clone(){const e=new n;return e.privProperties=this.privProperties.clone(),e}get speechSynthesisLanguage(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_SynthLanguage)}set speechSynthesisLanguage(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_SynthLanguage,e)}get speechSynthesisVoiceName(){return this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_SynthVoice)}set speechSynthesisVoiceName(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_SynthVoice,e)}get speechSynthesisOutputFormat(){return r.SpeechSynthesisOutputFormat[this.privProperties.getProperty(r.PropertyId.SpeechServiceConnection_SynthOutputFormat,void 0)]}set speechSynthesisOutputFormat(e){this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_SynthOutputFormat,r.SpeechSynthesisOutputFormat[e])}}return Rn.SpeechConfigImpl=n,Rn}var wn,Tn={};function An(){if(wn)return Tn;wn=1,Object.defineProperty(Tn,"__esModule",{value:!0}),Tn.SpeechTranslationConfigImpl=Tn.SpeechTranslationConfig=void 0;const e=jv(),t=Rr(),r=Mp();let i=class extends r.SpeechConfig{constructor(){super()}static fromSubscription(e,i){t.Contracts.throwIfNullOrWhitespace(e,"subscriptionKey"),t.Contracts.throwIfNullOrWhitespace(i,"region");const o=new n;return o.properties.setProperty(r.PropertyId.SpeechServiceConnection_Key,e),o.properties.setProperty(r.PropertyId.SpeechServiceConnection_Region,i),o}static fromAuthorizationToken(e,i){t.Contracts.throwIfNullOrWhitespace(e,"authorizationToken"),t.Contracts.throwIfNullOrWhitespace(i,"region");const o=new n;return o.properties.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,e),o.properties.setProperty(r.PropertyId.SpeechServiceConnection_Region,i),o}static fromHost(e,i){t.Contracts.throwIfNull(e,"hostName");const o=new n;return o.setProperty(r.PropertyId.SpeechServiceConnection_Host,e.protocol+"//"+e.hostname+(""===e.port?"":":"+e.port)),void 0!==i&&o.setProperty(r.PropertyId.SpeechServiceConnection_Key,i),o}static fromEndpoint(e,i){t.Contracts.throwIfNull(e,"endpoint"),t.Contracts.throwIfNull(i,"subscriptionKey");const o=new n;return o.properties.setProperty(r.PropertyId.SpeechServiceConnection_Endpoint,e.href),o.properties.setProperty(r.PropertyId.SpeechServiceConnection_Key,i),o}};Tn.SpeechTranslationConfig=i;class n extends i{constructor(){super(),this.privSpeechProperties=new r.PropertyCollection,this.outputFormat=r.OutputFormat.Simple}set authorizationToken(e){t.Contracts.throwIfNullOrWhitespace(e,"value"),this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,e)}set speechRecognitionLanguage(e){t.Contracts.throwIfNullOrWhitespace(e,"value"),this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage,e)}get speechRecognitionLanguage(){return this.privSpeechProperties.getProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_RecoLanguage])}get subscriptionKey(){return this.privSpeechProperties.getProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_Key])}get outputFormat(){return r.OutputFormat[this.privSpeechProperties.getProperty(e.OutputFormatPropertyName,void 0)]}set outputFormat(t){this.privSpeechProperties.setProperty(e.OutputFormatPropertyName,r.OutputFormat[t])}get endpointId(){return this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_EndpointId)}set endpointId(e){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_EndpointId,e)}addTargetLanguage(e){t.Contracts.throwIfNullOrWhitespace(e,"value");const i=this.targetLanguages;i.includes(e)||(i.push(e),this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_TranslationToLanguages,i.join(",")))}get targetLanguages(){return void 0!==this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_TranslationToLanguages,void 0)?this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_TranslationToLanguages).split(","):[]}get voiceName(){return this.getProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_TranslationVoice])}set voiceName(e){t.Contracts.throwIfNullOrWhitespace(e,"value"),this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_TranslationVoice,e)}get region(){return this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_Region)}setProxy(e,t,i,n){this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyHostName],e),this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyPort],t),this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyUserName],i),this.setProperty(r.PropertyId[r.PropertyId.SpeechServiceConnection_ProxyPassword],n)}getProperty(e,t){return this.privSpeechProperties.getProperty(e,t)}setProperty(e,t){this.privSpeechProperties.setProperty(e,t)}get properties(){return this.privSpeechProperties}close(){}setServiceProperty(t,r){const i=JSON.parse(this.privSpeechProperties.getProperty(e.ServicePropertiesPropertyName,"{}"));i[t]=r,this.privSpeechProperties.setProperty(e.ServicePropertiesPropertyName,JSON.stringify(i))}setProfanity(e){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceResponse_ProfanityOption,r.ProfanityOption[e])}enableAudioLogging(){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_EnableAudioLogging,"true")}requestWordLevelTimestamps(){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps,"true")}enableDictation(){this.privSpeechProperties.setProperty(e.ForceDictationPropertyName,"true")}get speechSynthesisLanguage(){return this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_SynthLanguage)}set speechSynthesisLanguage(e){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_SynthLanguage,e)}get speechSynthesisVoiceName(){return this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_SynthVoice)}set speechSynthesisVoiceName(e){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_SynthVoice,e)}get speechSynthesisOutputFormat(){return r.SpeechSynthesisOutputFormat[this.privSpeechProperties.getProperty(r.PropertyId.SpeechServiceConnection_SynthOutputFormat,void 0)]}set speechSynthesisOutputFormat(e){this.privSpeechProperties.setProperty(r.PropertyId.SpeechServiceConnection_SynthOutputFormat,r.SpeechSynthesisOutputFormat[e])}}return Tn.SpeechTranslationConfigImpl=n,Tn}var En,bn={};function On(){if(En)return bn;En=1,Object.defineProperty(bn,"__esModule",{value:!0}),bn.PropertyCollection=void 0;const e=Mp();return bn.PropertyCollection=class t{constructor(){this.privKeys=[],this.privValues=[]}getProperty(t,r){let i;i="string"==typeof t?t:e.PropertyId[t];for(let e=0;e<this.privKeys.length;e++)if(this.privKeys[e]===i)return this.privValues[e];if(void 0!==r)return String(r)}setProperty(t,r){let i;i="string"==typeof t?t:e.PropertyId[t];for(let e=0;e<this.privKeys.length;e++)if(this.privKeys[e]===i)return void(this.privValues[e]=r);this.privKeys.push(i),this.privValues.push(r)}clone(){const e=new t;for(let t=0;t<this.privKeys.length;t++)e.privKeys.push(this.privKeys[t]),e.privValues.push(this.privValues[t]);return e}mergeTo(e){this.privKeys.forEach((t=>{if(void 0===e.getProperty(t,void 0)){const r=this.getProperty(t);e.setProperty(t,r)}}))}get keys(){return this.privKeys}},bn}var Mn,Dn={};function kn(){return Mn||(Mn=1,e=Dn,Object.defineProperty(e,"__esModule",{value:!0}),e.PropertyId=void 0,(t=e.PropertyId||(e.PropertyId={}))[t.SpeechServiceConnection_Key=0]="SpeechServiceConnection_Key",t[t.SpeechServiceConnection_Endpoint=1]="SpeechServiceConnection_Endpoint",t[t.SpeechServiceConnection_Region=2]="SpeechServiceConnection_Region",t[t.SpeechServiceAuthorization_Token=3]="SpeechServiceAuthorization_Token",t[t.SpeechServiceAuthorization_Type=4]="SpeechServiceAuthorization_Type",t[t.SpeechServiceConnection_EndpointId=5]="SpeechServiceConnection_EndpointId",t[t.SpeechServiceConnection_TranslationToLanguages=6]="SpeechServiceConnection_TranslationToLanguages",t[t.SpeechServiceConnection_TranslationVoice=7]="SpeechServiceConnection_TranslationVoice",t[t.SpeechServiceConnection_TranslationFeatures=8]="SpeechServiceConnection_TranslationFeatures",t[t.SpeechServiceConnection_IntentRegion=9]="SpeechServiceConnection_IntentRegion",t[t.SpeechServiceConnection_ProxyHostName=10]="SpeechServiceConnection_ProxyHostName",t[t.SpeechServiceConnection_ProxyPort=11]="SpeechServiceConnection_ProxyPort",t[t.SpeechServiceConnection_ProxyUserName=12]="SpeechServiceConnection_ProxyUserName",t[t.SpeechServiceConnection_ProxyPassword=13]="SpeechServiceConnection_ProxyPassword",t[t.SpeechServiceConnection_RecoMode=14]="SpeechServiceConnection_RecoMode",t[t.SpeechServiceConnection_RecoLanguage=15]="SpeechServiceConnection_RecoLanguage",t[t.Speech_SessionId=16]="Speech_SessionId",t[t.SpeechServiceConnection_SynthLanguage=17]="SpeechServiceConnection_SynthLanguage",t[t.SpeechServiceConnection_SynthVoice=18]="SpeechServiceConnection_SynthVoice",t[t.SpeechServiceConnection_SynthOutputFormat=19]="SpeechServiceConnection_SynthOutputFormat",t[t.SpeechServiceConnection_AutoDetectSourceLanguages=20]="SpeechServiceConnection_AutoDetectSourceLanguages",t[t.SpeechServiceResponse_RequestDetailedResultTrueFalse=21]="SpeechServiceResponse_RequestDetailedResultTrueFalse",t[t.SpeechServiceResponse_RequestProfanityFilterTrueFalse=22]="SpeechServiceResponse_RequestProfanityFilterTrueFalse",t[t.SpeechServiceResponse_JsonResult=23]="SpeechServiceResponse_JsonResult",t[t.SpeechServiceResponse_JsonErrorDetails=24]="SpeechServiceResponse_JsonErrorDetails",t[t.CancellationDetails_Reason=25]="CancellationDetails_Reason",t[t.CancellationDetails_ReasonText=26]="CancellationDetails_ReasonText",t[t.CancellationDetails_ReasonDetailedText=27]="CancellationDetails_ReasonDetailedText",t[t.LanguageUnderstandingServiceResponse_JsonResult=28]="LanguageUnderstandingServiceResponse_JsonResult",t[t.SpeechServiceConnection_Url=29]="SpeechServiceConnection_Url",t[t.SpeechServiceConnection_InitialSilenceTimeoutMs=30]="SpeechServiceConnection_InitialSilenceTimeoutMs",t[t.SpeechServiceConnection_EndSilenceTimeoutMs=31]="SpeechServiceConnection_EndSilenceTimeoutMs",t[t.Speech_SegmentationSilenceTimeoutMs=32]="Speech_SegmentationSilenceTimeoutMs",t[t.Speech_SegmentationMaximumTimeMs=33]="Speech_SegmentationMaximumTimeMs",t[t.Speech_SegmentationStrategy=34]="Speech_SegmentationStrategy",t[t.SpeechServiceConnection_EnableAudioLogging=35]="SpeechServiceConnection_EnableAudioLogging",t[t.SpeechServiceConnection_LanguageIdMode=36]="SpeechServiceConnection_LanguageIdMode",t[t.SpeechServiceConnection_RecognitionEndpointVersion=37]="SpeechServiceConnection_RecognitionEndpointVersion",t[t.SpeechServiceConnection_SpeakerIdMode=38]="SpeechServiceConnection_SpeakerIdMode",t[t.SpeechServiceResponse_ProfanityOption=39]="SpeechServiceResponse_ProfanityOption",t[t.SpeechServiceResponse_PostProcessingOption=40]="SpeechServiceResponse_PostProcessingOption",t[t.SpeechServiceResponse_RequestWordLevelTimestamps=41]="SpeechServiceResponse_RequestWordLevelTimestamps",t[t.SpeechServiceResponse_StablePartialResultThreshold=42]="SpeechServiceResponse_StablePartialResultThreshold",t[t.SpeechServiceResponse_OutputFormatOption=43]="SpeechServiceResponse_OutputFormatOption",t[t.SpeechServiceResponse_TranslationRequestStablePartialResult=44]="SpeechServiceResponse_TranslationRequestStablePartialResult",t[t.SpeechServiceResponse_RequestWordBoundary=45]="SpeechServiceResponse_RequestWordBoundary",t[t.SpeechServiceResponse_RequestPunctuationBoundary=46]="SpeechServiceResponse_RequestPunctuationBoundary",t[t.SpeechServiceResponse_RequestSentenceBoundary=47]="SpeechServiceResponse_RequestSentenceBoundary",t[t.SpeechServiceResponse_DiarizeIntermediateResults=48]="SpeechServiceResponse_DiarizeIntermediateResults",t[t.Conversation_ApplicationId=49]="Conversation_ApplicationId",t[t.Conversation_DialogType=50]="Conversation_DialogType",t[t.Conversation_Initial_Silence_Timeout=51]="Conversation_Initial_Silence_Timeout",t[t.Conversation_From_Id=52]="Conversation_From_Id",t[t.Conversation_Conversation_Id=53]="Conversation_Conversation_Id",t[t.Conversation_Custom_Voice_Deployment_Ids=54]="Conversation_Custom_Voice_Deployment_Ids",t[t.Conversation_Speech_Activity_Template=55]="Conversation_Speech_Activity_Template",t[t.Conversation_Request_Bot_Status_Messages=56]="Conversation_Request_Bot_Status_Messages",t[t.Conversation_Agent_Connection_Id=57]="Conversation_Agent_Connection_Id",t[t.SpeechServiceConnection_Host=58]="SpeechServiceConnection_Host",t[t.ConversationTranslator_Host=59]="ConversationTranslator_Host",t[t.ConversationTranslator_Name=60]="ConversationTranslator_Name",t[t.ConversationTranslator_CorrelationId=61]="ConversationTranslator_CorrelationId",t[t.ConversationTranslator_Token=62]="ConversationTranslator_Token",t[t.PronunciationAssessment_ReferenceText=63]="PronunciationAssessment_ReferenceText",t[t.PronunciationAssessment_GradingSystem=64]="PronunciationAssessment_GradingSystem",t[t.PronunciationAssessment_Granularity=65]="PronunciationAssessment_Granularity",t[t.PronunciationAssessment_EnableMiscue=66]="PronunciationAssessment_EnableMiscue",t[t.PronunciationAssessment_Json=67]="PronunciationAssessment_Json",t[t.PronunciationAssessment_Params=68]="PronunciationAssessment_Params",t[t.SpeakerRecognition_Api_Version=69]="SpeakerRecognition_Api_Version",t[t.WebWorkerLoadType=70]="WebWorkerLoadType",t[t.TalkingAvatarService_WebRTC_SDP=71]="TalkingAvatarService_WebRTC_SDP"),Dn;var e,t}var zn,Nn={};function _n(){if(zn)return Nn;zn=1,Object.defineProperty(Nn,"__esModule",{value:!0}),Nn.Recognizer=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();return Nn.Recognizer=class n{constructor(e,t,r){this.audioConfig=void 0!==e?e:i.AudioConfig.fromDefaultMicrophoneInput(),this.privDisposed=!1,this.privProperties=t.clone(),this.privConnectionFactory=r,this.implCommonRecognizerSetup()}close(e,i){r.Contracts.throwIfDisposed(this.privDisposed),t.marshalPromiseToCallbacks(this.dispose(!0),e,i)}get internalData(){return this.privReco}async dispose(e){this.privDisposed||(this.privDisposed=!0,e&&this.privReco&&(await this.privReco.audioSource.turnOff(),await this.privReco.dispose()))}static get telemetryEnabled(){return e.ServiceRecognizerBase.telemetryDataEnabled}static enableTelemetry(t){e.ServiceRecognizerBase.telemetryDataEnabled=t}implCommonRecognizerSetup(){let t="undefined"!=typeof window?"Browser":"Node",r="unknown",i="unknown";"undefined"!=typeof navigator&&(t=t+"/"+navigator.platform,r=navigator.userAgent,i=navigator.appVersion);const o=this.createRecognizerConfig(new e.SpeechServiceConfig(new e.Context(new e.OS(t,r,i))));this.privReco=this.createServiceRecognizer(n.getAuthFromProperties(this.privProperties),this.privConnectionFactory,this.audioConfig,o)}async recognizeOnceAsyncImpl(e){r.Contracts.throwIfDisposed(this.privDisposed);const i=new t.Deferred;await this.implRecognizerStop(),await this.privReco.recognize(e,i.resolve,i.reject);const n=await i.promise;return await this.implRecognizerStop(),n}async startContinuousRecognitionAsyncImpl(e){r.Contracts.throwIfDisposed(this.privDisposed),await this.implRecognizerStop(),await this.privReco.recognize(e,void 0,void 0)}async stopContinuousRecognitionAsyncImpl(){r.Contracts.throwIfDisposed(this.privDisposed),await this.implRecognizerStop()}async implRecognizerStop(){this.privReco&&await this.privReco.stopRecognizing()}static getAuthFromProperties(t){const r=t.getProperty(i.PropertyId.SpeechServiceConnection_Key,void 0);return r&&""!==r?new e.CognitiveSubscriptionKeyAuthentication(r):new e.CognitiveTokenAuthentication((()=>{const e=t.getProperty(i.PropertyId.SpeechServiceAuthorization_Token,void 0);return Promise.resolve(e)}),(()=>{const e=t.getProperty(i.PropertyId.SpeechServiceAuthorization_Token,void 0);return Promise.resolve(e)}))}},Nn}var Ln,xn={};function Fn(){if(Ln)return xn;Ln=1,Object.defineProperty(xn,"__esModule",{value:!0}),xn.SpeechRecognizer=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();let n=class n extends i.Recognizer{constructor(t,n){const o=t;r.Contracts.throwIfNull(o,"speechConfig"),r.Contracts.throwIfNullOrWhitespace(o.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage),i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage]),super(n,o.properties,new e.SpeechConnectionFactory),this.privDisposedRecognizer=!1}static FromConfig(e,t,r){const i=e;t.properties.mergeTo(i.properties);return new n(e,r)}get endpointId(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(i.PropertyId.SpeechServiceConnection_EndpointId,"00000000-0000-0000-0000-000000000000")}get authorizationToken(){return this.properties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(i.PropertyId.SpeechServiceAuthorization_Token,e)}get speechRecognitionLanguage(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage)}get outputFormat(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(e.OutputFormatPropertyName,i.OutputFormat[i.OutputFormat.Simple])===i.OutputFormat[i.OutputFormat.Simple]?i.OutputFormat.Simple:i.OutputFormat.Detailed}get properties(){return this.privProperties}recognizeOnceAsync(r,i){t.marshalPromiseToCallbacks(this.recognizeOnceAsyncImpl(e.RecognitionMode.Interactive),r,i)}startContinuousRecognitionAsync(r,i){t.marshalPromiseToCallbacks(this.startContinuousRecognitionAsyncImpl(e.RecognitionMode.Conversation),r,i)}stopContinuousRecognitionAsync(e,r){t.marshalPromiseToCallbacks(this.stopContinuousRecognitionAsyncImpl(),e,r)}startKeywordRecognitionAsync(e,t,i){r.Contracts.throwIfNull(e,"model"),i&&i("Not yet implemented.")}stopKeywordRecognitionAsync(e){e&&e()}close(e,i){r.Contracts.throwIfDisposed(this.privDisposedRecognizer),t.marshalPromiseToCallbacks(this.dispose(!0),e,i)}async dispose(e){this.privDisposedRecognizer||(e&&(this.privDisposedRecognizer=!0,await this.implRecognizerStop()),await super.dispose(e))}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.privProperties)}createServiceRecognizer(t,r,i,n){const o=i;return new e.SpeechServiceRecognizer(t,r,o,n,this)}};return xn.SpeechRecognizer=n,xn}var Bn,jn={};function Un(){if(Bn)return jn;Bn=1,Object.defineProperty(jn,"__esModule",{value:!0}),jn.IntentRecognizer=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();let n=class extends i.Recognizer{constructor(t,n){r.Contracts.throwIfNullOrUndefined(t,"speechConfig");const o=t;r.Contracts.throwIfNullOrUndefined(o,"speechConfig"),super(n,o.properties,new e.IntentConnectionFactory),this.privAddedIntents=[],this.privAddedLmIntents={},this.privDisposedIntentRecognizer=!1,this.privProperties=o.properties,r.Contracts.throwIfNullOrWhitespace(this.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage),i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage])}get speechRecognitionLanguage(){return r.Contracts.throwIfDisposed(this.privDisposedIntentRecognizer),this.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage)}get authorizationToken(){return this.properties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){this.properties.setProperty(i.PropertyId.SpeechServiceAuthorization_Token,e)}get properties(){return this.privProperties}recognizeOnceAsync(i,n){if(r.Contracts.throwIfDisposed(this.privDisposedIntentRecognizer),0!==Object.keys(this.privAddedLmIntents).length||void 0!==this.privUmbrellaIntent){const e=this.buildSpeechContext();this.privReco.speechContext.setSection("intent",e.Intent),this.privReco.dynamicGrammar.addReferenceGrammar(e.ReferenceGrammars);this.privReco.setIntents(this.privAddedLmIntents,this.privUmbrellaIntent)}t.marshalPromiseToCallbacks(this.recognizeOnceAsyncImpl(e.RecognitionMode.Interactive),i,n)}startContinuousRecognitionAsync(r,i){if(0!==Object.keys(this.privAddedLmIntents).length||void 0!==this.privUmbrellaIntent){const e=this.buildSpeechContext();this.privReco.speechContext.setSection("intent",e.Intent),this.privReco.dynamicGrammar.addReferenceGrammar(e.ReferenceGrammars);this.privReco.setIntents(this.privAddedLmIntents,this.privUmbrellaIntent)}t.marshalPromiseToCallbacks(this.startContinuousRecognitionAsyncImpl(e.RecognitionMode.Conversation),r,i)}stopContinuousRecognitionAsync(e,r){t.marshalPromiseToCallbacks(this.stopContinuousRecognitionAsyncImpl(),e,r)}startKeywordRecognitionAsync(e,t,i){r.Contracts.throwIfNull(e,"model"),i&&i("Not yet implemented.")}stopKeywordRecognitionAsync(e,t){if(e)try{e()}catch(r){t&&t(r)}}addIntent(e,t){r.Contracts.throwIfDisposed(this.privDisposedIntentRecognizer),r.Contracts.throwIfNullOrWhitespace(t,"intentId"),r.Contracts.throwIfNullOrWhitespace(e,"simplePhrase"),this.privAddedIntents.push([t,e])}addIntentWithLanguageModel(t,i,n){r.Contracts.throwIfDisposed(this.privDisposedIntentRecognizer),r.Contracts.throwIfNullOrWhitespace(t,"intentId"),r.Contracts.throwIfNull(i,"model");const o=i;r.Contracts.throwIfNullOrWhitespace(o.appId,"model.appId"),this.privAddedLmIntents[t]=new e.AddedLmIntent(o,n)}addAllIntents(t,i){r.Contracts.throwIfNull(t,"model");const n=t;r.Contracts.throwIfNullOrWhitespace(n.appId,"model.appId"),this.privUmbrellaIntent=new e.AddedLmIntent(n,i)}close(e,i){r.Contracts.throwIfDisposed(this.privDisposedIntentRecognizer),t.marshalPromiseToCallbacks(this.dispose(!0),e,i)}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.privProperties)}createServiceRecognizer(t,r,i,n){const o=i;return new e.IntentServiceRecognizer(t,r,o,n,this)}async dispose(e){this.privDisposedIntentRecognizer||e&&(this.privDisposedIntentRecognizer=!0,await super.dispose(e))}buildSpeechContext(){let e,t,r;const n=[];void 0!==this.privUmbrellaIntent&&(e=this.privUmbrellaIntent.modelImpl.appId,t=this.privUmbrellaIntent.modelImpl.region,r=this.privUmbrellaIntent.modelImpl.subscriptionKey);for(const i of Object.keys(this.privAddedLmIntents)){const o=this.privAddedLmIntents[i];if(void 0===e)e=o.modelImpl.appId;else if(e!==o.modelImpl.appId)throw new Error("Intents must all be from the same LUIS model");if(void 0===t)t=o.modelImpl.region;else if(t!==o.modelImpl.region)throw new Error("Intents must all be from the same LUIS model in a single region");if(void 0===r)r=o.modelImpl.subscriptionKey;else if(r!==o.modelImpl.subscriptionKey)throw new Error("Intents must all use the same subscription key");const s="luis/"+e+"-PRODUCTION#"+i;n.push(s)}return{Intent:{id:e,key:void 0===r?this.privProperties.getProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_Key]):r,provider:"LUIS"},ReferenceGrammars:void 0===this.privUmbrellaIntent?n:["luis/"+e+"-PRODUCTION"]}}};return jn.IntentRecognizer=n,jn}var qn,Wn={};function Hn(){return qn||(qn=1,e=Wn,Object.defineProperty(e,"__esModule",{value:!0}),e.VoiceProfileType=void 0,(t=e.VoiceProfileType||(e.VoiceProfileType={}))[t.TextIndependentIdentification=0]="TextIndependentIdentification",t[t.TextDependentVerification=1]="TextDependentVerification",t[t.TextIndependentVerification=2]="TextIndependentVerification"),Wn;var e,t}var Vn,Kn,Jn,Gn={},$n={},Qn={};function Xn(){if(Vn)return Qn;Vn=1,Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.ConnectionMessageImpl=Qn.ConnectionMessage=void 0;const e=Xt(),t=Gt(),r=On(),i=kn();Qn.ConnectionMessage=class{};return Qn.ConnectionMessageImpl=class{constructor(t){this.privConnectionMessage=t,this.privProperties=new r.PropertyCollection,this.privConnectionMessage.headers[e.HeaderNames.ConnectionId]&&this.privProperties.setProperty(i.PropertyId.Speech_SessionId,this.privConnectionMessage.headers[e.HeaderNames.ConnectionId]),Object.keys(this.privConnectionMessage.headers).forEach((e=>{this.privProperties.setProperty(e,this.privConnectionMessage.headers[e])}))}get path(){return this.privConnectionMessage.headers[Object.keys(this.privConnectionMessage.headers).find((e=>e.toLowerCase()==="path".toLowerCase()))]}get isTextMessage(){return this.privConnectionMessage.messageType===t.MessageType.Text}get isBinaryMessage(){return this.privConnectionMessage.messageType===t.MessageType.Binary}get TextMessage(){return this.privConnectionMessage.textBody}get binaryMessage(){return this.privConnectionMessage.binaryBody}get properties(){return this.privProperties}toString(){return""}},Qn}function Zn(){if(Kn)return $n;Kn=1,Object.defineProperty($n,"__esModule",{value:!0}),$n.Connection=void 0;const e=jv(),t=Gt(),r=Xn(),i=Rr(),n=Mp();return $n.Connection=class o{static fromRecognizer(e){const t=e.internalData,r=new o;return r.privInternalData=t,r.setupEvents(),r}static fromSynthesizer(e){const t=e.internalData,r=new o;return r.privInternalData=t,r.setupEvents(),r}openConnection(e,r){t.marshalPromiseToCallbacks(this.privInternalData.connect(),e,r)}closeConnection(r,i){if(this.privInternalData instanceof e.SynthesisAdapterBase)throw new Error("Disconnecting a synthesizer's connection is currently not supported");t.marshalPromiseToCallbacks(this.privInternalData.disconnect(),r,i)}setMessageProperty(t,r,n){if(i.Contracts.throwIfNullOrWhitespace(r,"propertyName"),this.privInternalData instanceof e.ServiceRecognizerBase){if("speech.context"!==t.toLowerCase())throw new Error("Only speech.context message property sets are currently supported for recognizer");this.privInternalData.speechContext.setSection(r,n)}else if(this.privInternalData instanceof e.SynthesisAdapterBase){if("synthesis.context"!==t.toLowerCase())throw new Error("Only synthesis.context message property sets are currently supported for synthesizer");this.privInternalData.synthesisContext.setSection(r,n)}}sendMessageAsync(e,r,i,n){t.marshalPromiseToCallbacks(this.privInternalData.sendNetworkMessage(e,r),i,n)}close(){}setupEvents(){this.privEventListener=this.privInternalData.connectionEvents.attach((e=>{"ConnectionEstablishedEvent"===e.name?this.connected&&this.connected(new n.ConnectionEventArgs(e.connectionId)):"ConnectionClosedEvent"===e.name?this.disconnected&&this.disconnected(new n.ConnectionEventArgs(e.connectionId)):"ConnectionMessageSentEvent"===e.name?this.messageSent&&this.messageSent(new n.ConnectionMessageEventArgs(new r.ConnectionMessageImpl(e.message))):"ConnectionMessageReceivedEvent"===e.name&&this.messageReceived&&this.messageReceived(new n.ConnectionMessageEventArgs(new r.ConnectionMessageImpl(e.message)))})),this.privServiceEventListener=this.privInternalData.serviceEvents.attach((e=>{this.receivedServiceMessage&&this.receivedServiceMessage(new n.ServiceEventArgs(e.jsonString,e.name))}))}},$n}function Yn(){if(Jn)return Gn;Jn=1,Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.TranslationRecognizer=void 0;const e=jv(),t=Gt(),r=Zn(),i=Rr(),n=Mp();let o=class o extends n.Recognizer{constructor(t,r,o){const s=t;i.Contracts.throwIfNull(s,"speechConfig"),super(r,s.properties,o||new e.TranslationConnectionFactory),this.privDisposedTranslationRecognizer=!1,void 0!==this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationVoice,void 0)&&i.Contracts.throwIfNullOrWhitespace(this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationVoice),n.PropertyId[n.PropertyId.SpeechServiceConnection_TranslationVoice]),i.Contracts.throwIfNullOrWhitespace(this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages),n.PropertyId[n.PropertyId.SpeechServiceConnection_TranslationToLanguages]),i.Contracts.throwIfNullOrWhitespace(this.properties.getProperty(n.PropertyId.SpeechServiceConnection_RecoLanguage),n.PropertyId[n.PropertyId.SpeechServiceConnection_RecoLanguage])}static FromConfig(t,r,i){const s=t;return r.properties.mergeTo(s.properties),r.properties.getProperty(n.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,void 0)===e.AutoDetectSourceLanguagesOpenRangeOptionName&&s.properties.setProperty(n.PropertyId.SpeechServiceConnection_RecoLanguage,"en-US"),new o(t,i)}get speechRecognitionLanguage(){return i.Contracts.throwIfDisposed(this.privDisposedTranslationRecognizer),this.properties.getProperty(n.PropertyId.SpeechServiceConnection_RecoLanguage)}get targetLanguages(){return i.Contracts.throwIfDisposed(this.privDisposedTranslationRecognizer),this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages).split(",")}get voiceName(){return i.Contracts.throwIfDisposed(this.privDisposedTranslationRecognizer),this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationVoice,void 0)}get properties(){return this.privProperties}get authorizationToken(){return this.properties.getProperty(n.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){this.properties.setProperty(n.PropertyId.SpeechServiceAuthorization_Token,e)}recognizeOnceAsync(r,n){i.Contracts.throwIfDisposed(this.privDisposedTranslationRecognizer),t.marshalPromiseToCallbacks(this.recognizeOnceAsyncImpl(e.RecognitionMode.Interactive),r,n)}startContinuousRecognitionAsync(r,i){t.marshalPromiseToCallbacks(this.startContinuousRecognitionAsyncImpl(e.RecognitionMode.Conversation),r,i)}stopContinuousRecognitionAsync(e,r){t.marshalPromiseToCallbacks(this.stopContinuousRecognitionAsyncImpl(),e,r)}removeTargetLanguage(e){if(i.Contracts.throwIfNullOrUndefined(e,"language to be removed"),void 0!==this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages,void 0)){const t=this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages).split(","),r=t.indexOf(e);r>-1&&(t.splice(r,1),this.properties.setProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages,t.join(",")),this.updateLanguages(t))}}addTargetLanguage(e){i.Contracts.throwIfNullOrUndefined(e,"language to be added");let t=[];void 0!==this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages,void 0)?(t=this.properties.getProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages).split(","),t.includes(e)||(t.push(e),this.properties.setProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages,t.join(",")))):(this.properties.setProperty(n.PropertyId.SpeechServiceConnection_TranslationToLanguages,e),t=[e]),this.updateLanguages(t)}close(e,r){i.Contracts.throwIfDisposed(this.privDisposedTranslationRecognizer),t.marshalPromiseToCallbacks(this.dispose(!0),e,r)}onConnection(){}async dispose(e){this.privDisposedTranslationRecognizer||(this.privDisposedTranslationRecognizer=!0,e&&(await this.implRecognizerStop(),await super.dispose(e)))}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.privProperties)}createServiceRecognizer(t,r,i,n){const o=i;return new e.TranslationServiceRecognizer(t,r,o,n,this)}updateLanguages(e){const t=r.Connection.fromRecognizer(this);t&&(t.setMessageProperty("speech.context","translationcontext",{to:e}),t.sendMessageAsync("event",JSON.stringify({id:"translation",name:"updateLanguage",to:e})))}};return Gn.TranslationRecognizer=o,Gn}var eo,to={};function ro(){if(eo)return to;eo=1,Object.defineProperty(to,"__esModule",{value:!0}),to.Translations=void 0;const e=Mp();return to.Translations=class{constructor(){this.privMap=new e.PropertyCollection}get languages(){return this.privMap.keys}get(e,t){return this.privMap.getProperty(e,t)}set(e,t){this.privMap.setProperty(e,t)}},to}var io,no={};function oo(){return io||(io=1,e=no,Object.defineProperty(e,"__esModule",{value:!0}),e.NoMatchReason=void 0,(t=e.NoMatchReason||(e.NoMatchReason={}))[t.NotRecognized=0]="NotRecognized",t[t.InitialSilenceTimeout=1]="InitialSilenceTimeout",t[t.InitialBabbleTimeout=2]="InitialBabbleTimeout"),no;var e,t}var so,ao={};function co(){if(so)return ao;so=1,Object.defineProperty(ao,"__esModule",{value:!0}),ao.NoMatchDetails=void 0;const e=jv(),t=Mp();return ao.NoMatchDetails=class r{constructor(e){this.privReason=e}static fromResult(i){const n=e.SimpleSpeechPhrase.fromJSON(i.json,0);let o=t.NoMatchReason.NotRecognized;switch(n.RecognitionStatus){case e.RecognitionStatus.BabbleTimeout:o=t.NoMatchReason.InitialBabbleTimeout;break;case e.RecognitionStatus.InitialSilenceTimeout:o=t.NoMatchReason.InitialSilenceTimeout;break;default:o=t.NoMatchReason.NotRecognized}return new r(o)}get reason(){return this.privReason}},ao}var po,uo={};function ho(){if(po)return uo;po=1,Object.defineProperty(uo,"__esModule",{value:!0}),uo.TranslationRecognitionCanceledEventArgs=void 0;return uo.TranslationRecognitionCanceledEventArgs=class{constructor(e,t,r,i,n){this.privCancelReason=t,this.privErrorDetails=r,this.privResult=n,this.privSessionId=e,this.privErrorCode=i}get result(){return this.privResult}get sessionId(){return this.privSessionId}get reason(){return this.privCancelReason}get errorCode(){return this.privErrorCode}get errorDetails(){return this.privErrorDetails}},uo}var vo,lo={};function go(){if(vo)return lo;vo=1,Object.defineProperty(lo,"__esModule",{value:!0}),lo.IntentRecognitionCanceledEventArgs=void 0;const e=Mp();let t=class extends e.IntentRecognitionEventArgs{constructor(e,t,r,i,n,o){super(i,n,o),this.privReason=e,this.privErrorDetails=t,this.privErrorCode=r}get reason(){return this.privReason}get errorCode(){return this.privErrorCode}get errorDetails(){return this.privErrorDetails}};return lo.IntentRecognitionCanceledEventArgs=t,lo}var fo,mo={};function So(){if(fo)return mo;fo=1,Object.defineProperty(mo,"__esModule",{value:!0}),mo.CancellationDetailsBase=void 0;return mo.CancellationDetailsBase=class{constructor(e,t,r){this.privReason=e,this.privErrorDetails=t,this.privErrorCode=r}get reason(){return this.privReason}get errorDetails(){return this.privErrorDetails}get ErrorCode(){return this.privErrorCode}},mo}var yo,Co={};function Po(){if(yo)return Co;yo=1,Object.defineProperty(Co,"__esModule",{value:!0}),Co.CancellationDetails=void 0;const e=jv(),t=So(),r=Mp();let i=class i extends t.CancellationDetailsBase{constructor(e,t,r){super(e,t,r)}static fromResult(t){let n=r.CancellationReason.Error,o=r.CancellationErrorCode.NoError;if(t instanceof r.RecognitionResult&&t.json){const r=e.SimpleSpeechPhrase.fromJSON(t.json,0);n=e.EnumTranslation.implTranslateCancelResult(r.RecognitionStatus)}return t.properties&&(o=r.CancellationErrorCode[t.properties.getProperty(e.CancellationErrorCodePropertyName,r.CancellationErrorCode[r.CancellationErrorCode.NoError])]),new i(n,t.errorDetails||e.EnumTranslation.implTranslateErrorDetails(o),o)}};return Co.CancellationDetails=i,Co}var Ro,Io={};function wo(){return Ro||(Ro=1,e=Io,Object.defineProperty(e,"__esModule",{value:!0}),e.CancellationErrorCode=void 0,(t=e.CancellationErrorCode||(e.CancellationErrorCode={}))[t.NoError=0]="NoError",t[t.AuthenticationFailure=1]="AuthenticationFailure",t[t.BadRequestParameters=2]="BadRequestParameters",t[t.TooManyRequests=3]="TooManyRequests",t[t.ConnectionFailure=4]="ConnectionFailure",t[t.ServiceTimeout=5]="ServiceTimeout",t[t.ServiceError=6]="ServiceError",t[t.RuntimeError=7]="RuntimeError",t[t.Forbidden=8]="Forbidden"),Io;var e,t}var To,Ao={};function Eo(){if(To)return Ao;To=1,Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.ConnectionEventArgs=void 0;const e=Mp();let t=class extends e.SessionEventArgs{};return Ao.ConnectionEventArgs=t,Ao}var bo,Oo={};function Mo(){if(bo)return Oo;bo=1,Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.ServiceEventArgs=void 0;const e=Mp();let t=class extends e.SessionEventArgs{constructor(e,t,r){super(r),this.privJsonResult=e,this.privEventName=t}get jsonString(){return this.privJsonResult}get eventName(){return this.privEventName}};return Oo.ServiceEventArgs=t,Oo}var Do,ko={};function zo(){if(Do)return ko;Do=1,Object.defineProperty(ko,"__esModule",{value:!0}),ko.PhraseListGrammar=void 0;return ko.PhraseListGrammar=class e{constructor(e){this.privGrammerBuilder=e.dynamicGrammar}static fromRecognizer(t){const r=t.internalData;return new e(r)}addPhrase(e){this.privGrammerBuilder.addPhrase(e)}addPhrases(e){this.privGrammerBuilder.addPhrase(e)}clear(){this.privGrammerBuilder.clearPhrases()}},ko}var No,_o={};function Lo(){if(No)return _o;No=1,Object.defineProperty(_o,"__esModule",{value:!0}),_o.DialogServiceConfigImpl=_o.DialogServiceConfig=void 0;const e=Rr(),t=Mp();let r=class{constructor(){}set applicationId(e){}static get DialogTypes(){return{BotFramework:"bot_framework",CustomCommands:"custom_commands"}}};_o.DialogServiceConfig=r;return _o.DialogServiceConfigImpl=class extends r{constructor(){super(),this.privSpeechConfig=new t.SpeechConfigImpl}get properties(){return this.privSpeechConfig.properties}get speechRecognitionLanguage(){return this.privSpeechConfig.speechRecognitionLanguage}set speechRecognitionLanguage(t){e.Contracts.throwIfNullOrWhitespace(t,"value"),this.privSpeechConfig.speechRecognitionLanguage=t}get outputFormat(){return this.privSpeechConfig.outputFormat}set outputFormat(e){this.privSpeechConfig.outputFormat=e}setProperty(e,t){this.privSpeechConfig.setProperty(e,t)}getProperty(e,t){return this.privSpeechConfig.getProperty(e)}setProxy(e,r,i,n){this.setProperty(t.PropertyId.SpeechServiceConnection_ProxyHostName,e),this.setProperty(t.PropertyId.SpeechServiceConnection_ProxyPort,`${r}`),i&&this.setProperty(t.PropertyId.SpeechServiceConnection_ProxyUserName,i),n&&this.setProperty(t.PropertyId.SpeechServiceConnection_ProxyPassword,n)}setServiceProperty(e,t,r){this.privSpeechConfig.setServiceProperty(e,t)}close(){}},_o}var xo,Fo={};function Bo(){if(xo)return Fo;xo=1,Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.BotFrameworkConfig=void 0;const e=Rr(),t=Lo(),r=Mp();let i=class extends t.DialogServiceConfigImpl{constructor(){super()}static fromSubscription(i,n,o){e.Contracts.throwIfNullOrWhitespace(i,"subscription"),e.Contracts.throwIfNullOrWhitespace(n,"region");const s=new t.DialogServiceConfigImpl;return s.setProperty(r.PropertyId.Conversation_DialogType,t.DialogServiceConfig.DialogTypes.BotFramework),s.setProperty(r.PropertyId.SpeechServiceConnection_Key,i),s.setProperty(r.PropertyId.SpeechServiceConnection_Region,n),o&&s.setProperty(r.PropertyId.Conversation_ApplicationId,o),s}static fromAuthorizationToken(i,n,o){e.Contracts.throwIfNullOrWhitespace(i,"authorizationToken"),e.Contracts.throwIfNullOrWhitespace(n,"region");const s=new t.DialogServiceConfigImpl;return s.setProperty(r.PropertyId.Conversation_DialogType,t.DialogServiceConfig.DialogTypes.BotFramework),s.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,i),s.setProperty(r.PropertyId.SpeechServiceConnection_Region,n),o&&s.setProperty(r.PropertyId.Conversation_ApplicationId,o),s}static fromHost(i,n,o){e.Contracts.throwIfNullOrUndefined(i,"host");const s=i instanceof URL?i:new URL(`wss://${i}.convai.speech.azure.us`);e.Contracts.throwIfNullOrUndefined(s,"resolvedHost");const a=new t.DialogServiceConfigImpl;return a.setProperty(r.PropertyId.Conversation_DialogType,t.DialogServiceConfig.DialogTypes.BotFramework),a.setProperty(r.PropertyId.SpeechServiceConnection_Host,s.toString()),void 0!==n&&a.setProperty(r.PropertyId.SpeechServiceConnection_Key,n),a}static fromEndpoint(i,n){e.Contracts.throwIfNull(i,"endpoint");const o=new t.DialogServiceConfigImpl;return o.setProperty(r.PropertyId.Conversation_DialogType,t.DialogServiceConfig.DialogTypes.BotFramework),o.setProperty(r.PropertyId.SpeechServiceConnection_Endpoint,i.toString()),void 0!==n&&o.setProperty(r.PropertyId.SpeechServiceConnection_Key,n),o}};return Fo.BotFrameworkConfig=i,Fo}var jo,Uo={};function qo(){if(jo)return Uo;jo=1,Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.CustomCommandsConfig=void 0;const e=Rr(),t=Lo(),r=Mp();let i=class extends t.DialogServiceConfigImpl{constructor(){super()}static fromSubscription(i,n,o){e.Contracts.throwIfNullOrWhitespace(i,"applicationId"),e.Contracts.throwIfNullOrWhitespace(n,"subscription"),e.Contracts.throwIfNullOrWhitespace(o,"region");const s=new t.DialogServiceConfigImpl;return s.setProperty(r.PropertyId.Conversation_DialogType,t.DialogServiceConfig.DialogTypes.CustomCommands),s.setProperty(r.PropertyId.Conversation_ApplicationId,i),s.setProperty(r.PropertyId.SpeechServiceConnection_Key,n),s.setProperty(r.PropertyId.SpeechServiceConnection_Region,o),s}static fromAuthorizationToken(i,n,o){e.Contracts.throwIfNullOrWhitespace(i,"applicationId"),e.Contracts.throwIfNullOrWhitespace(n,"authorizationToken"),e.Contracts.throwIfNullOrWhitespace(o,"region");const s=new t.DialogServiceConfigImpl;return s.setProperty(r.PropertyId.Conversation_DialogType,t.DialogServiceConfig.DialogTypes.CustomCommands),s.setProperty(r.PropertyId.Conversation_ApplicationId,i),s.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,n),s.setProperty(r.PropertyId.SpeechServiceConnection_Region,o),s}set applicationId(t){e.Contracts.throwIfNullOrWhitespace(t,"value"),this.setProperty(r.PropertyId.Conversation_ApplicationId,t)}get applicationId(){return this.getProperty(r.PropertyId.Conversation_ApplicationId)}};return Uo.CustomCommandsConfig=i,Uo}var Wo,Ho,Vo,Ko,Jo={},Go={},$o={},Qo={};function Xo(){if(Wo)return Qo;Wo=1,Object.defineProperty(Qo,"__esModule",{value:!0}),Qo.QueryParameterNames=void 0;let e=class{};return Qo.QueryParameterNames=e,e.BotId="botid",e.CustomSpeechDeploymentId="cid",e.CustomVoiceDeploymentId="deploymentId",e.EnableAudioLogging="storeAudio",e.EnableLanguageId="lidEnabled",e.EnableWordLevelTimestamps="wordLevelTimestamps",e.EndSilenceTimeoutMs="endSilenceTimeoutMs",e.SegmentationSilenceTimeoutMs="segmentationSilenceTimeoutMs",e.SegmentationMaximumTimeMs="segmentationMaximumTimeMs",e.SegmentationStrategy="segmentationStrategy",e.Format="format",e.InitialSilenceTimeoutMs="initialSilenceTimeoutMs",e.Language="language",e.Profanity="profanity",e.RequestBotStatusMessages="enableBotMessageStatus",e.StableIntermediateThreshold="stableIntermediateThreshold",e.StableTranslation="stableTranslation",e.TestHooks="testhooks",e.Postprocessing="postprocessing",e.CtsMeetingId="meetingId",e.CtsDeviceId="deviceId",e.CtsIsParticipant="isParticipant",e.EnableAvatar="enableTalkingAvatar",Qo}function Zo(){if(Ho)return $o;Ho=1,Object.defineProperty($o,"__esModule",{value:!0}),$o.ConnectionFactoryBase=void 0;const e=jv(),t=Mp(),r=Xo();return $o.ConnectionFactoryBase=class{static getHostSuffix(e){if(e){if(e.toLowerCase().startsWith("china"))return".azure.cn";if(e.toLowerCase().startsWith("usgov"))return".azure.us"}return".microsoft.com"}setCommonUrlParams(i,n,o){new Map([[t.PropertyId.Speech_SegmentationSilenceTimeoutMs,r.QueryParameterNames.SegmentationSilenceTimeoutMs],[t.PropertyId.SpeechServiceConnection_EnableAudioLogging,r.QueryParameterNames.EnableAudioLogging],[t.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,r.QueryParameterNames.EndSilenceTimeoutMs],[t.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,r.QueryParameterNames.InitialSilenceTimeoutMs],[t.PropertyId.SpeechServiceResponse_PostProcessingOption,r.QueryParameterNames.Postprocessing],[t.PropertyId.SpeechServiceResponse_ProfanityOption,r.QueryParameterNames.Profanity],[t.PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps,r.QueryParameterNames.EnableWordLevelTimestamps],[t.PropertyId.SpeechServiceResponse_StablePartialResultThreshold,r.QueryParameterNames.StableIntermediateThreshold]]).forEach(((e,t)=>{this.setUrlParameter(t,e,i,n,o)}));const s=JSON.parse(i.parameters.getProperty(e.ServicePropertiesPropertyName,"{}"));Object.keys(s).forEach((e=>{n[e]=s[e]}))}setUrlParameter(e,t,r,i,n){const o=r.parameters.getProperty(e,void 0);!o||n&&-1!==n.search(t)||(i[t]=o.toLocaleLowerCase())}},$o}function Yo(){if(Vo)return Go;Vo=1,Object.defineProperty(Go,"__esModule",{value:!0}),Go.DialogConnectionFactory=void 0;const e=Vp(),t=jv(),r=Mp(),i=Zo(),n=jv(),o=Xt(),s=Xo();class a extends i.ConnectionFactoryBase{create(c,p,u){const h=c.parameters.getProperty(r.PropertyId.Conversation_ApplicationId,""),d=c.parameters.getProperty(r.PropertyId.Conversation_DialogType),v=c.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Region),l=c.parameters.getProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage,"en-US"),g=c.parameters.getProperty(r.PropertyId.Conversation_Request_Bot_Status_Messages,"true"),f={};f[o.HeaderNames.ConnectionId]=u,f[s.QueryParameterNames.Format]=c.parameters.getProperty(t.OutputFormatPropertyName,r.OutputFormat[r.OutputFormat.Simple]).toLowerCase(),f[s.QueryParameterNames.Language]=l,f[s.QueryParameterNames.RequestBotStatusMessages]=g,h&&(f[s.QueryParameterNames.BotId]=h,d===r.DialogServiceConfig.DialogTypes.CustomCommands&&(f[o.HeaderNames.CustomCommandsAppId]=h));const m=d===r.DialogServiceConfig.DialogTypes.CustomCommands?"commands/":"",S=d===r.DialogServiceConfig.DialogTypes.CustomCommands?"v1":d===r.DialogServiceConfig.DialogTypes.BotFramework?"v3":"v0",y={};null!=p.token&&""!==p.token&&(y[p.headerName]=p.token);let C=c.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Endpoint,"");if(!C){const e=i.ConnectionFactoryBase.getHostSuffix(v),t=c.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Host,`wss://${v}.${a.BaseUrl}${e}`);C=`${t.endsWith("/")?t:t+"/"}${m}${a.ApiKey}/${S}`}this.setCommonUrlParams(c,f,C);const P="true"===c.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(C,f,y,new n.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(c),P,u)}}return Go.DialogConnectionFactory=a,a.ApiKey="api",a.BaseUrl="convai.speech",Go}function es(){if(Ko)return Jo;Ko=1,Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.DialogServiceConnector=void 0;const e=Yo(),t=jv(),r=Gt(),i=Rr(),n=Mp(),o=kn();let s=class extends n.Recognizer{constructor(t,r){const n=t;i.Contracts.throwIfNull(t,"dialogConfig"),super(r,n.properties,new e.DialogConnectionFactory),this.isTurnComplete=!0,this.privIsDisposed=!1,this.privProperties=n.properties.clone();const o=this.buildAgentConfig();this.privReco.agentConfig.set(o)}connect(e,t){r.marshalPromiseToCallbacks(this.privReco.connect(),e,t)}disconnect(e,t){r.marshalPromiseToCallbacks(this.privReco.disconnect(),e,t)}get authorizationToken(){return this.properties.getProperty(o.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){i.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(o.PropertyId.SpeechServiceAuthorization_Token,e)}get properties(){return this.privProperties}get speechActivityTemplate(){return this.properties.getProperty(o.PropertyId.Conversation_Speech_Activity_Template)}set speechActivityTemplate(e){this.properties.setProperty(o.PropertyId.Conversation_Speech_Activity_Template,e)}listenOnceAsync(e,n){if(this.isTurnComplete){i.Contracts.throwIfDisposed(this.privIsDisposed);const o=(async()=>{await this.privReco.connect(),await this.implRecognizerStop(),this.isTurnComplete=!1;const e=new r.Deferred;await this.privReco.recognize(t.RecognitionMode.Conversation,e.resolve,e.reject);const i=await e.promise;return await this.implRecognizerStop(),i})();o.catch((()=>{this.dispose(!0).catch((()=>{}))})),r.marshalPromiseToCallbacks(o.finally((()=>{this.isTurnComplete=!0})),e,n)}}sendActivityAsync(e,t,i){r.marshalPromiseToCallbacks(this.privReco.sendMessage(e),t,i)}close(e,t){i.Contracts.throwIfDisposed(this.privIsDisposed),r.marshalPromiseToCallbacks(this.dispose(!0),e,t)}async dispose(e){this.privIsDisposed||e&&(this.privIsDisposed=!0,await this.implRecognizerStop(),await super.dispose(e))}createRecognizerConfig(e){return new t.RecognizerConfig(e,this.privProperties)}createServiceRecognizer(e,r,i,n){const o=i;return new t.DialogServiceAdapter(e,r,o,n,this)}buildAgentConfig(){return{botInfo:{commType:this.properties.getProperty("Conversation_Communication_Type","Default"),commandsCulture:void 0,connectionId:this.properties.getProperty(o.PropertyId.Conversation_Agent_Connection_Id),conversationId:this.properties.getProperty(o.PropertyId.Conversation_Conversation_Id,void 0),fromId:this.properties.getProperty(o.PropertyId.Conversation_From_Id,void 0),ttsAudioFormat:this.properties.getProperty(o.PropertyId.SpeechServiceConnection_SynthOutputFormat,void 0)},version:.2}}};return Jo.DialogServiceConnector=s,Jo}var ts,rs={};function is(){if(ts)return rs;ts=1,Object.defineProperty(rs,"__esModule",{value:!0}),rs.ActivityReceivedEventArgs=void 0;return rs.ActivityReceivedEventArgs=class{constructor(e,t){this.privActivity=e,this.privAudioStream=t}get activity(){return this.privActivity}get audioStream(){return this.privAudioStream}},rs}var ns,os,ss={},as={};function cs(){if(ns)return as;ns=1,Object.defineProperty(as,"__esModule",{value:!0}),as.TurnStatusResponsePayload=void 0;class e{constructor(e){this.privMessageStatusResponse=JSON.parse(e)}static fromJSON(t){return new e(t)}get interactionId(){return this.privMessageStatusResponse.interactionId}get conversationId(){return this.privMessageStatusResponse.conversationId}get statusCode(){switch(this.privMessageStatusResponse.statusCode){case"Success":return 200;case"Failed":return 400;case"TimedOut":return 429;default:return this.privMessageStatusResponse.statusCode}}}return as.TurnStatusResponsePayload=e,as}function ps(){if(os)return ss;os=1,Object.defineProperty(ss,"__esModule",{value:!0}),ss.TurnStatusReceivedEventArgs=void 0;const e=cs();return ss.TurnStatusReceivedEventArgs=class{constructor(t){this.privTurnStatus=e.TurnStatusResponsePayload.fromJSON(t)}get interactionId(){return this.privTurnStatus.interactionId}get conversationId(){return this.privTurnStatus.conversationId}get statusCode(){return this.privTurnStatus.statusCode}},ss}var us,hs={};function ds(){return us||(us=1,e=hs,Object.defineProperty(e,"__esModule",{value:!0}),e.ServicePropertyChannel=void 0,(t=e.ServicePropertyChannel||(e.ServicePropertyChannel={}))[t.UriQueryParameter=0]="UriQueryParameter"),hs;var e,t}var vs,ls={};function gs(){return vs||(vs=1,e=ls,Object.defineProperty(e,"__esModule",{value:!0}),e.ProfanityOption=void 0,(t=e.ProfanityOption||(e.ProfanityOption={}))[t.Masked=0]="Masked",t[t.Removed=1]="Removed",t[t.Raw=2]="Raw"),ls;var e,t}var fs,ms={};function Ss(){if(fs)return ms;fs=1,Object.defineProperty(ms,"__esModule",{value:!0}),ms.BaseAudioPlayer=void 0;const e=Se(),t=Mp(),r=Dr();return ms.BaseAudioPlayer=class{constructor(e){this.audioContext=null,this.gainNode=null,this.autoUpdateBufferTimer=0,void 0===e&&(e=t.AudioStreamFormat.getDefaultInputFormat()),this.init(e)}playAudioSample(e,t,r){try{this.ensureInitializedContext();const r=this.formatAudioData(e),i=new Float32Array(this.samples.length+r.length);i.set(this.samples,0),i.set(r,this.samples.length),this.samples=i,t&&t()}catch(i){r&&r(i)}}stopAudio(e,t){null!==this.audioContext&&(this.samples=new Float32Array,clearInterval(this.autoUpdateBufferTimer),this.audioContext.close().then((()=>{e&&e()}),(e=>{t&&t(e)})),this.audioContext=null)}init(e){this.audioFormat=e,this.samples=new Float32Array}ensureInitializedContext(){if(null===this.audioContext){this.createAudioContext();const e=200;this.autoUpdateBufferTimer=setInterval((()=>{this.updateAudioBuffer()}),e)}}createAudioContext(){this.audioContext=r.AudioStreamFormatImpl.getAudioContext(),this.gainNode=this.audioContext.createGain(),this.gainNode.gain.value=1,this.gainNode.connect(this.audioContext.destination),this.startTime=this.audioContext.currentTime}formatAudioData(t){switch(this.audioFormat.bitsPerSample){case 8:return this.formatArrayBuffer(new Int8Array(t),128);case 16:return this.formatArrayBuffer(new Int16Array(t),32768);case 32:return this.formatArrayBuffer(new Int32Array(t),2147483648);default:throw new e.InvalidOperationError("Only WAVE_FORMAT_PCM (8/16/32 bps) format supported at this time")}}formatArrayBuffer(e,t){const r=new Float32Array(e.length);for(let i=0;i<e.length;i++)r[i]=e[i]/t;return r}updateAudioBuffer(){if(0===this.samples.length)return;const e=this.audioFormat.channels,t=this.audioContext.createBufferSource(),r=this.samples.length/e,i=this.audioContext.createBuffer(e,r,this.audioFormat.samplesPerSec);for(let n=0;n<e;n++){let t=n;const r=i.getChannelData(n);for(let i=0;i<this.samples.length;i++,t+=e)r[i]=this.samples[t]}this.startTime<this.audioContext.currentTime&&(this.startTime=this.audioContext.currentTime),t.buffer=i,t.connect(this.gainNode),t.start(this.startTime),this.startTime+=i.duration,this.samples=new Float32Array}async playAudio(e){null===this.audioContext&&this.createAudioContext();const t=this.audioContext.createBufferSource(),r=this.audioContext.destination;await this.audioContext.decodeAudioData(e,(e=>{t.buffer=e,t.connect(r),t.start(0)}))}},ms}var ys,Cs={};function Ps(){if(ys)return Cs;ys=1,Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.ConnectionMessageEventArgs=void 0;return Cs.ConnectionMessageEventArgs=class{constructor(e){this.privConnectionMessage=e}get message(){return this.privConnectionMessage}toString(){return"Message: "+this.privConnectionMessage.toString()}},Cs}var Rs,Is={};function ws(){if(Rs)return Is;Rs=1,Object.defineProperty(Is,"__esModule",{value:!0}),Is.VoiceProfile=void 0;return Is.VoiceProfile=class{constructor(e,t){this.privId=e,this.privProfileType=t}get profileId(){return this.privId}get profileType(){return this.privProfileType}},Is}var Ts,As={};function Es(){if(Ts)return As;Ts=1,Object.defineProperty(As,"__esModule",{value:!0}),As.VoiceProfileEnrollmentCancellationDetails=As.VoiceProfileEnrollmentResult=void 0;const e=jv(),t=Mp();As.VoiceProfileEnrollmentResult=class r{constructor(r,i,n){this.privReason=r,this.privProperties=new t.PropertyCollection,this.privReason!==t.ResultReason.Canceled?i&&(this.privDetails=JSON.parse(i),"enrolling"===this.privDetails.enrollmentStatus.toLowerCase()&&(this.privReason=t.ResultReason.EnrollingVoiceProfile)):(this.privErrorDetails=n,this.privProperties.setProperty(e.CancellationErrorCodePropertyName,t.CancellationErrorCode[t.CancellationErrorCode.ServiceError]))}get reason(){return this.privReason}get enrollmentsCount(){return this.privDetails.enrollmentsCount}get enrollmentsLength(){return this.privDetails.enrollmentsLength}get properties(){return this.privProperties}get enrollmentResultDetails(){return this.privDetails}get errorDetails(){return this.privErrorDetails}static FromIdentificationProfileList(e){const i=[];for(const n of e.value){const e="enrolling"===n.enrollmentStatus.toLowerCase()?t.ResultReason.EnrollingVoiceProfile:"enrolled"===n.enrollmentStatus.toLowerCase()?t.ResultReason.EnrolledVoiceProfile:t.ResultReason.Canceled,o=new r(e,null,null);o.privDetails=this.getIdentificationDetails(n),i.push(o)}return i}static FromVerificationProfileList(e){const i=[];for(const n of e.value){const e="enrolling"===n.enrollmentStatus.toLowerCase()?t.ResultReason.EnrollingVoiceProfile:"enrolled"===n.enrollmentStatus.toLowerCase()?t.ResultReason.EnrolledVoiceProfile:t.ResultReason.Canceled,o=new r(e,null,null);o.privDetails=this.getVerificationDetails(n),i.push(o)}return i}static getIdentificationDetails(e){return{audioLength:e.audioLength?parseFloat(e.audioLength):0,audioSpeechLength:e.audioSpeechLength?parseFloat(e.audioSpeechLength):0,enrollmentStatus:e.enrollmentStatus,enrollmentsCount:e.enrollmentsCount||0,enrollmentsLength:e.enrollmentsLength?parseFloat(e.enrollmentsLength):0,enrollmentsSpeechLength:e.enrollmentsSpeechLength?parseFloat(e.enrollmentsSpeechLength):0,profileId:e.profileId||e.identificationProfileId,remainingEnrollmentsSpeechLength:e.remainingEnrollmentsSpeechLength?parseFloat(e.remainingEnrollmentsSpeechLength):0}}static getVerificationDetails(e){return{audioLength:e.audioLength?parseFloat(e.audioLength):0,audioSpeechLength:e.audioSpeechLength?parseFloat(e.audioSpeechLength):0,enrollmentStatus:e.enrollmentStatus,enrollmentsCount:e.enrollmentsCount,enrollmentsLength:e.enrollmentsLength?parseFloat(e.enrollmentsLength):0,enrollmentsSpeechLength:e.enrollmentsSpeechLength?parseFloat(e.enrollmentsSpeechLength):0,profileId:e.profileId||e.verificationProfileId,remainingEnrollmentsCount:e.remainingEnrollments||e.remainingEnrollmentsCount,remainingEnrollmentsSpeechLength:e.remainingEnrollmentsSpeechLength?parseFloat(e.remainingEnrollmentsSpeechLength):0}}};class r extends t.CancellationDetailsBase{constructor(e,t,r){super(e,t,r)}static fromResult(i){const n=t.CancellationReason.Error;let o=t.CancellationErrorCode.NoError;return i.properties&&(o=t.CancellationErrorCode[i.properties.getProperty(e.CancellationErrorCodePropertyName,t.CancellationErrorCode[t.CancellationErrorCode.NoError])]),new r(n,i.errorDetails,o)}}return As.VoiceProfileEnrollmentCancellationDetails=r,As}var bs,Os={};function Ms(){if(bs)return Os;bs=1,Object.defineProperty(Os,"__esModule",{value:!0}),Os.VoiceProfileCancellationDetails=Os.VoiceProfileResult=void 0;const e=jv(),t=Rr(),r=Mp();Os.VoiceProfileResult=class{constructor(i,n){this.privReason=i,this.privProperties=new r.PropertyCollection,i===r.ResultReason.Canceled&&(t.Contracts.throwIfNullOrUndefined(n,"statusText"),this.privErrorDetails=n,this.privProperties.setProperty(e.CancellationErrorCodePropertyName,r.CancellationErrorCode[r.CancellationErrorCode.ServiceError]))}get reason(){return this.privReason}get properties(){return this.privProperties}get errorDetails(){return this.privErrorDetails}};class i extends r.CancellationDetailsBase{constructor(e,t,r){super(e,t,r)}static fromResult(t){const n=r.CancellationReason.Error;let o=r.CancellationErrorCode.NoError;return t.properties&&(o=r.CancellationErrorCode[t.properties.getProperty(e.CancellationErrorCodePropertyName,r.CancellationErrorCode[r.CancellationErrorCode.NoError])]),new i(n,t.errorDetails,o)}}return Os.VoiceProfileCancellationDetails=i,Os}var Ds,ks={};function zs(){if(Ds)return ks;Ds=1,Object.defineProperty(ks,"__esModule",{value:!0}),ks.VoiceProfilePhraseResult=void 0;const e=Rr(),t=Mp();let r=class extends t.VoiceProfileResult{constructor(t,r,i,n){super(t,r),this.privPhrases=[],e.Contracts.throwIfNullOrUndefined(n,"phrase array"),this.privType=i,n&&n[0]&&(this.privPhrases=n)}get phrases(){return this.privPhrases}get type(){return this.privType}};return ks.VoiceProfilePhraseResult=r,ks}var Ns,_s={};function Ls(){if(Ns)return _s;Ns=1,Object.defineProperty(_s,"__esModule",{value:!0}),_s.VoiceProfileClient=void 0;const e=jv(),t=vi(),r=Rr(),i=Mp();let n=class extends i.Recognizer{constructor(n){r.Contracts.throwIfNullOrUndefined(n,"speechConfig");const o=n;r.Contracts.throwIfNull(o,"speechConfig"),super(t.AudioConfig.fromStreamInput(i.AudioInputStream.createPushStream()),o.properties,new e.VoiceProfileConnectionFactory),this.privProperties=o.properties.clone(),this.privVoiceAdapter=this.privReco,this.privDisposedVoiceAdapter=!1}get properties(){return this.privProperties}get authorizationToken(){return this.properties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(i.PropertyId.SpeechServiceAuthorization_Token,e)}async createProfileAsync(e,t){const r=await this.privVoiceAdapter.createProfile(e,t);return new i.VoiceProfile(r[0],e)}async retrieveEnrollmentResultAsync(e){return this.privVoiceAdapter.retrieveEnrollmentResult(e)}async getAllProfilesAsync(e){return this.privVoiceAdapter.getAllProfiles(e)}async getActivationPhrasesAsync(e,t){return this.privVoiceAdapter.getActivationPhrases(e,t)}async enrollProfileAsync(e,t){const i=t;return r.Contracts.throwIfNullOrUndefined(i,"audioConfig"),this.audioConfig=t,this.privVoiceAdapter.SpeakerAudioSource=i,this.privVoiceAdapter.enrollProfile(e)}async deleteProfileAsync(e){return this.privVoiceAdapter.deleteProfile(e)}async resetProfileAsync(e){return this.privVoiceAdapter.resetProfile(e)}async close(){await this.dispose(!0)}createServiceRecognizer(t,r,i,n){const o=i;return new e.VoiceServiceRecognizer(t,r,o,n,this)}async dispose(e){this.privDisposedVoiceAdapter||(this.privDisposedVoiceAdapter=!0,e&&await super.dispose(e))}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.properties)}getResult(e,t){return new i.VoiceProfileResult(e.ok?t:i.ResultReason.Canceled,e.statusText)}};return _s.VoiceProfileClient=n,_s}var xs,Fs={};function Bs(){if(xs)return Fs;xs=1,Object.defineProperty(Fs,"__esModule",{value:!0}),Fs.SpeakerRecognizer=void 0;const e=jv(),t=Rr(),r=Mp();let i=class extends r.Recognizer{constructor(r,i){t.Contracts.throwIfNullOrUndefined(r,"speechConfig");const n=r;t.Contracts.throwIfNullOrUndefined(n,"speechConfig"),super(i,n.properties,new e.SpeakerRecognitionConnectionFactory),this.privAudioConfigImpl=i,t.Contracts.throwIfNull(this.privAudioConfigImpl,"audioConfig"),this.privDisposedSpeakerRecognizer=!1,this.privProperties=n.properties}get authorizationToken(){return this.properties.getProperty(r.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){t.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,e)}get properties(){return this.privProperties}async recognizeOnceAsync(e){return t.Contracts.throwIfDisposed(this.privDisposedSpeakerRecognizer),this.recognizeSpeakerOnceAsyncImpl(e)}async close(){t.Contracts.throwIfDisposed(this.privDisposedSpeakerRecognizer),await this.dispose(!0)}async recognizeSpeakerOnceAsyncImpl(e){t.Contracts.throwIfDisposed(this.privDisposedSpeakerRecognizer),await this.implRecognizerStop();const r=await this.privReco.recognizeSpeaker(e);return await this.implRecognizerStop(),r}async implRecognizerStop(){this.privReco&&await this.privReco.stopRecognizing()}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.privProperties)}createServiceRecognizer(t,r,i,n){const o=i;return new e.SpeakerServiceRecognizer(t,r,o,n,this)}async dispose(e){this.privDisposedSpeakerRecognizer||e&&(this.privDisposedSpeakerRecognizer=!0,await super.dispose(e))}};return Fs.SpeakerRecognizer=i,Fs}var js,Us={};function qs(){if(js)return Us;js=1,Object.defineProperty(Us,"__esModule",{value:!0}),Us.SpeakerIdentificationModel=void 0;const e=Rr(),t=Mp();return Us.SpeakerIdentificationModel=class r{constructor(r){if(this.privVoiceProfiles=[],this.privProfileIds=[],e.Contracts.throwIfNullOrUndefined(r,"VoiceProfiles"),0===r.length)throw new Error("Empty Voice Profiles array");for(const e of r){if(e.profileType!==t.VoiceProfileType.TextIndependentIdentification)throw new Error("Identification model can only be created from Identification profile: "+e.profileId);this.privVoiceProfiles.push(e),this.privProfileIds.push(e.profileId)}}static fromProfiles(e){return new r(e)}get voiceProfileIds(){return this.privProfileIds.join(",")}get profileIds(){return this.privProfileIds}get scenario(){return"TextIndependentIdentification"}},Us}var Ws,Hs={};function Vs(){if(Ws)return Hs;Ws=1,Object.defineProperty(Hs,"__esModule",{value:!0}),Hs.SpeakerVerificationModel=void 0;const e=Rr(),t=Mp();return Hs.SpeakerVerificationModel=class r{constructor(r){if(e.Contracts.throwIfNullOrUndefined(r,"VoiceProfile"),r.profileType===t.VoiceProfileType.TextIndependentIdentification)throw new Error("Verification model cannot be created from Identification profile");this.privVoiceProfile=r}static fromProfile(e){return new r(e)}get voiceProfile(){return this.privVoiceProfile}get profileIds(){return[this.voiceProfile.profileId]}get scenario(){return this.voiceProfile.profileType===t.VoiceProfileType.TextDependentVerification?"TextDependentVerification":"TextIndependentVerification"}},Hs}var Ks,Js,Gs={},$s={};function Qs(){return Ks||(Ks=1,e=$s,Object.defineProperty(e,"__esModule",{value:!0}),e.LanguageIdMode=void 0,(t=e.LanguageIdMode||(e.LanguageIdMode={}))[t.AtStart=0]="AtStart",t[t.Continuous=1]="Continuous"),$s;var e,t}function Xs(){if(Js)return Gs;Js=1,Object.defineProperty(Gs,"__esModule",{value:!0}),Gs.AutoDetectSourceLanguageConfig=void 0;const e=jv(),t=Rr(),r=Mp(),i=Qs();return Gs.AutoDetectSourceLanguageConfig=class n{constructor(){this.privProperties=new r.PropertyCollection,this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_LanguageIdMode,"AtStart"),this.privLanguageIdMode=i.LanguageIdMode.AtStart}static fromOpenRange(){const t=new n;return t.properties.setProperty(r.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,e.AutoDetectSourceLanguagesOpenRangeOptionName),t}static fromLanguages(e){t.Contracts.throwIfArrayEmptyOrWhitespace(e,"languages");const i=new n;return i.properties.setProperty(r.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,e.join()),i}static fromSourceLanguageConfigs(e){if(e.length<1)throw new Error("Expected non-empty SourceLanguageConfig array.");const t=new n,i=[];return e.forEach((e=>{if(i.push(e.language),void 0!==e.endpointId&&""!==e.endpointId){const i=e.language+r.PropertyId.SpeechServiceConnection_EndpointId.toString();t.properties.setProperty(i,e.endpointId)}})),t.properties.setProperty(r.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,i.join()),t}get properties(){return this.privProperties}set mode(e){e===i.LanguageIdMode.Continuous?(this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_RecognitionEndpointVersion,"2"),this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_LanguageIdMode,"Continuous")):(this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_RecognitionEndpointVersion,"1"),this.privProperties.setProperty(r.PropertyId.SpeechServiceConnection_LanguageIdMode,"AtStart")),this.privLanguageIdMode=e}},Gs}var Zs,Ys={};function ea(){if(Zs)return Ys;Zs=1,Object.defineProperty(Ys,"__esModule",{value:!0}),Ys.AutoDetectSourceLanguageResult=void 0;const e=Rr();return Ys.AutoDetectSourceLanguageResult=class t{constructor(t,r){e.Contracts.throwIfNullOrUndefined(t,"language"),e.Contracts.throwIfNullOrUndefined(r,"languageDetectionConfidence"),this.privLanguage=t,this.privLanguageDetectionConfidence=r}static fromResult(e){return new t(e.language,e.languageDetectionConfidence)}static fromConversationTranscriptionResult(e){return new t(e.language,e.languageDetectionConfidence)}get language(){return this.privLanguage}get languageDetectionConfidence(){return this.privLanguageDetectionConfidence}},Ys}var ta,ra={};function ia(){if(ta)return ra;ta=1,Object.defineProperty(ra,"__esModule",{value:!0}),ra.SourceLanguageConfig=void 0;const e=Rr();return ra.SourceLanguageConfig=class t{constructor(t,r){e.Contracts.throwIfNullOrUndefined(t,"language"),this.privLanguage=t,this.privEndpointId=r}static fromLanguage(e,r){return new t(e,r)}get language(){return this.privLanguage}get endpointId(){return this.privEndpointId}},ra}var na,oa={};function sa(){return na||(na=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.SpeakerRecognitionCancellationDetails=e.SpeakerRecognitionResult=e.SpeakerRecognitionResultType=void 0;const t=jv(),r=Mp();var i,n;(n=i=e.SpeakerRecognitionResultType||(e.SpeakerRecognitionResultType={}))[n.Verify=0]="Verify",n[n.Identify=1]="Identify";e.SpeakerRecognitionResult=class{constructor(e,n=r.ResultReason.RecognizedSpeaker,o=r.CancellationErrorCode.NoError,s=""){this.privProperties=new r.PropertyCollection;const a="TextIndependentIdentification"===e.scenario?i.Identify:i.Verify;this.privReason=n,this.privReason!==r.ResultReason.Canceled?a===i.Identify?(this.privProfileId=e.identificationResult.identifiedProfile.profileId,this.privScore=e.identificationResult.identifiedProfile.score,this.privReason=r.ResultReason.RecognizedSpeakers):(this.privScore=e.verificationResult.score,"accept"!==e.verificationResult.recognitionResult.toLowerCase()&&(this.privReason=r.ResultReason.NoMatch),void 0!==e.verificationResult.profileId&&""!==e.verificationResult.profileId&&(this.privProfileId=e.verificationResult.profileId)):(this.privErrorDetails=s,this.privProperties.setProperty(t.CancellationErrorCodePropertyName,r.CancellationErrorCode[o])),this.privProperties.setProperty(r.PropertyId.SpeechServiceResponse_JsonResult,JSON.stringify(e))}get properties(){return this.privProperties}get reason(){return this.privReason}get profileId(){return this.privProfileId}get errorDetails(){return this.privErrorDetails}get score(){return this.privScore}};class o extends r.CancellationDetailsBase{constructor(e,t,r){super(e,t,r)}static fromResult(e){const i=r.CancellationReason.Error;let n=r.CancellationErrorCode.NoError;return e.properties&&(n=r.CancellationErrorCode[e.properties.getProperty(t.CancellationErrorCodePropertyName,r.CancellationErrorCode[r.CancellationErrorCode.NoError])]),new o(i,e.errorDetails,n)}}e.SpeakerRecognitionCancellationDetails=o}(oa)),oa}var aa,ca={},pa={};function ua(){if(aa)return pa;aa=1,Object.defineProperty(pa,"__esModule",{value:!0}),pa.ConversationImpl=pa.Conversation=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();let n=class{constructor(){}static createConversationAsync(n,s,a,c){let p,u,h;return r.Contracts.throwIfNullOrUndefined(n,e.ConversationConnectionConfig.restErrors.invalidArgs.replace("{arg}","config")),r.Contracts.throwIfNullOrUndefined(n.region,e.ConversationConnectionConfig.restErrors.invalidArgs.replace("{arg}","SpeechServiceConnection_Region")),n.subscriptionKey||n.getProperty(i.PropertyId[i.PropertyId.SpeechServiceAuthorization_Token])||r.Contracts.throwIfNullOrUndefined(n.subscriptionKey,e.ConversationConnectionConfig.restErrors.invalidArgs.replace("{arg}","SpeechServiceConnection_Key")),"string"==typeof s?(p=new o(n,s),t.marshalPromiseToCallbacks((async()=>{})(),a,c)):(p=new o(n),u=s,h=a,p.createConversationAsync((()=>{u&&u()}),(e=>{h&&h(e)}))),p}};pa.Conversation=n;class o extends n{constructor(t,n){super(),this.privErrors=e.ConversationConnectionConfig.restErrors,this.onConnected=e=>{var t;this.privIsConnected=!0;try{(null==(t=this.privConversationTranslator)?void 0:t.sessionStarted)&&this.privConversationTranslator.sessionStarted(this.privConversationTranslator,e)}catch(r){}},this.onDisconnected=e=>{var t;try{(null==(t=this.privConversationTranslator)?void 0:t.sessionStopped)&&this.privConversationTranslator.sessionStopped(this.privConversationTranslator,e)}catch(r){}finally{this.close(!1)}},this.onCanceled=(e,t)=>{var r;try{(null==(r=this.privConversationTranslator)?void 0:r.canceled)&&this.privConversationTranslator.canceled(this.privConversationTranslator,t)}catch(i){}},this.onParticipantUpdateCommandReceived=(t,r)=>{try{const t=this.privParticipants.getParticipant(r.id);if(void 0!==t){switch(r.key){case e.ConversationTranslatorCommandTypes.changeNickname:t.displayName=r.value;break;case e.ConversationTranslatorCommandTypes.setUseTTS:t.isUsingTts=r.value;break;case e.ConversationTranslatorCommandTypes.setProfanityFiltering:t.profanity=r.value;break;case e.ConversationTranslatorCommandTypes.setMute:t.isMuted=r.value;break;case e.ConversationTranslatorCommandTypes.setTranslateToLanguages:t.translateToLanguages=r.value}this.privParticipants.addOrUpdateParticipant(t),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.Updated,[this.toParticipant(t)],r.sessionId))}}catch(n){}},this.onLockRoomCommandReceived=()=>{},this.onMuteAllCommandReceived=(e,t)=>{try{this.privParticipants.participants.forEach((e=>e.isMuted=!e.isHost&&t.isMuted)),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.Updated,this.toParticipants(!1),t.sessionId))}catch(r){}},this.onParticipantJoinCommandReceived=(e,t)=>{try{const e=this.privParticipants.addOrUpdateParticipant(t.participant);void 0!==e&&this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.JoinedConversation,[this.toParticipant(e)],t.sessionId))}catch(r){}},this.onParticipantLeaveCommandReceived=(e,t)=>{try{const e=this.privParticipants.getParticipant(t.participant.id);void 0!==e&&(this.privParticipants.deleteParticipant(t.participant.id),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.LeftConversation,[this.toParticipant(e)],t.sessionId)))}catch(r){}},this.onTranslationReceived=(t,r)=>{try{switch(r.command){case e.ConversationTranslatorMessageTypes.final:this.privConversationTranslator&&this.privConversationTranslator.transcribed(this.privConversationTranslator,new i.ConversationTranslationEventArgs(r.payload,void 0,r.sessionId));break;case e.ConversationTranslatorMessageTypes.partial:this.privConversationTranslator&&this.privConversationTranslator.transcribing(this.privConversationTranslator,new i.ConversationTranslationEventArgs(r.payload,void 0,r.sessionId));break;case e.ConversationTranslatorMessageTypes.instantMessage:this.privConversationTranslator&&this.privConversationTranslator.textMessageReceived(this.privConversationTranslator,new i.ConversationTranslationEventArgs(r.payload,void 0,r.sessionId))}}catch(n){}},this.onParticipantsListReceived=(e,t)=>{var r;try{if(void 0!==t.sessionToken&&null!==t.sessionToken&&(this.privRoom.token=t.sessionToken),this.privParticipants.participants=[...t.participants],void 0!==this.privParticipants.me&&(this.privIsReady=!0),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.JoinedConversation,this.toParticipants(!0),t.sessionId)),this.me.isHost){const e=null==(r=this.privConversationTranslator)?void 0:r.properties.getProperty(i.PropertyId.ConversationTranslator_Name);void 0!==e&&e.length>0&&e!==this.me.displayName&&this.changeNicknameAsync(e)}}catch(n){}},this.onConversationExpiration=(e,t)=>{try{this.privConversationTranslator&&this.privConversationTranslator.conversationExpiration(this.privConversationTranslator,t)}catch(r){}},this.privIsConnected=!1,this.privIsDisposed=!1,this.privConversationId="",this.privProperties=new i.PropertyCollection,this.privManager=new e.ConversationManager;if(t.getProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage])||t.setProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage],e.ConversationConnectionConfig.defaultLanguageCode),this.privLanguage=t.getProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage]),n)this.privConversationId=n;else{0===t.targetLanguages.length&&t.addTargetLanguage(this.privLanguage);t.getProperty(i.PropertyId[i.PropertyId.SpeechServiceResponse_ProfanityOption])||t.setProfanity(i.ProfanityOption.Masked);let e=t.getProperty(i.PropertyId[i.PropertyId.ConversationTranslator_Name]);null==e&&(e="Host"),r.Contracts.throwIfNullOrTooLong(e,"nickname",50),r.Contracts.throwIfNullOrTooShort(e,"nickname",2),t.setProperty(i.PropertyId[i.PropertyId.ConversationTranslator_Name],e)}this.privConfig=t;const o=t;r.Contracts.throwIfNull(o,"speechConfig"),this.privProperties=o.properties.clone(),this.privIsConnected=!1,this.privParticipants=new e.InternalParticipants,this.privIsReady=!1,this.privTextMessageMaxLength=1e3}get room(){return this.privRoom}get connection(){return this.privConversationRecognizer}get config(){return this.privConfig}get conversationId(){return this.privRoom?this.privRoom.roomId:this.privConversationId}get properties(){return this.privProperties}get speechRecognitionLanguage(){return this.privLanguage}get isMutedByHost(){var e,t;return!(null==(e=this.privParticipants.me)?void 0:e.isHost)&&(null==(t=this.privParticipants.me)?void 0:t.isMuted)}get isConnected(){return this.privIsConnected&&this.privIsReady}get participants(){return this.toParticipants(!0)}get me(){return this.toParticipant(this.privParticipants.me)}get host(){return this.toParticipant(this.privParticipants.host)}get transcriberRecognizer(){return this.privTranscriberRecognizer}get conversationInfo(){const t=this.conversationId,r=this.participants.map((e=>({id:e.id,preferredLanguage:e.preferredLanguage,voice:e.voice}))),i={};for(const n of e.ConversationConnectionConfig.transcriptionEventKeys){const e=this.properties.getProperty(n,"");""!==e&&(i[n]=e)}return{id:t,participants:r,conversationProperties:i}}get canSend(){var e;return this.privIsConnected&&!(null==(e=this.privParticipants.me)?void 0:e.isMuted)}get canSendAsHost(){var e;return this.privIsConnected&&(null==(e=this.privParticipants.me)?void 0:e.isHost)}get authorizationToken(){return this.privToken}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"authorizationToken"),this.privToken=e}set conversationTranslator(e){this.privConversationTranslator=e}onToken(e){this.privConversationTranslator.onToken(e)}createConversationAsync(e,t){try{this.privConversationRecognizer&&this.handleError(new Error(this.privErrors.permissionDeniedStart),t),this.privManager.createOrJoin(this.privProperties,void 0,(r=>{r||this.handleError(new Error(this.privErrors.permissionDeniedConnect),t),this.privRoom=r,this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(r){this.handleError(r,t)}}startConversationAsync(t,i){try{this.privConversationRecognizer&&this.handleError(new Error(this.privErrors.permissionDeniedStart),i),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedConnect),this.privParticipants.meId=this.privRoom.participantId,this.privConversationRecognizer=e.ConversationRecognizerFactory.fromConfig(this,this.privConfig),this.privConversationRecognizer.connected=this.onConnected,this.privConversationRecognizer.disconnected=this.onDisconnected,this.privConversationRecognizer.canceled=this.onCanceled,this.privConversationRecognizer.participantUpdateCommandReceived=this.onParticipantUpdateCommandReceived,this.privConversationRecognizer.lockRoomCommandReceived=this.onLockRoomCommandReceived,this.privConversationRecognizer.muteAllCommandReceived=this.onMuteAllCommandReceived,this.privConversationRecognizer.participantJoinCommandReceived=this.onParticipantJoinCommandReceived,this.privConversationRecognizer.participantLeaveCommandReceived=this.onParticipantLeaveCommandReceived,this.privConversationRecognizer.translationReceived=this.onTranslationReceived,this.privConversationRecognizer.participantsListReceived=this.onParticipantsListReceived,this.privConversationRecognizer.conversationExpiration=this.onConversationExpiration,this.privConversationRecognizer.connect(this.privRoom.token,(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}addParticipantAsync(e,i,n){r.Contracts.throwIfNullOrUndefined(e,"Participant"),t.marshalPromiseToCallbacks(this.addParticipantImplAsync(e),i,n)}joinConversationAsync(e,t,i,n,o){try{r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","conversationId")),r.Contracts.throwIfNullOrWhitespace(t,this.privErrors.invalidArgs.replace("{arg}","nickname")),r.Contracts.throwIfNullOrWhitespace(i,this.privErrors.invalidArgs.replace("{arg}","language")),this.privManager.createOrJoin(this.privProperties,e,(e=>{r.Contracts.throwIfNullOrUndefined(e,this.privErrors.permissionDeniedConnect),this.privRoom=e,this.privConfig.authorizationToken=e.cognitiveSpeechAuthToken,n&&n(e.cognitiveSpeechAuthToken)}),(e=>{this.handleError(e,o)}))}catch(s){this.handleError(s,o)}}deleteConversationAsync(e,r){t.marshalPromiseToCallbacks(this.deleteConversationImplAsync(),e,r)}async deleteConversationImplAsync(){r.Contracts.throwIfNullOrUndefined(this.privProperties,this.privErrors.permissionDeniedConnect),r.Contracts.throwIfNullOrWhitespace(this.privRoom.token,this.privErrors.permissionDeniedConnect),await this.privManager.leave(this.privProperties,this.privRoom.token),this.dispose()}endConversationAsync(e,r){t.marshalPromiseToCallbacks(this.endConversationImplAsync(),e,r)}endConversationImplAsync(){return this.close(!0)}lockConversationAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","lock")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getLockCommand(!0),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}muteAllParticipantsAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privConversationRecognizer,this.privErrors.permissionDeniedSend),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","mute")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteAllCommand(!0),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}muteParticipantAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","userId")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.me.isHost||this.me.id===e||this.handleError(new Error(this.privErrors.permissionDeniedParticipant.replace("{command}","mute")),i);-1===this.privParticipants.getParticipantIndex(e)&&this.handleError(new Error(this.privErrors.invalidParticipantRequest),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteCommand(e,!0),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}removeParticipantAsync(e,i,n){try{if(r.Contracts.throwIfDisposed(this.privIsDisposed),this.privTranscriberRecognizer&&e.hasOwnProperty("id"))t.marshalPromiseToCallbacks(this.removeParticipantImplAsync(e),i,n);else{r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedParticipant.replace("{command}","remove")),n);let t="";if("string"==typeof e)t=e;else if(e.hasOwnProperty("id")){t=e.id}else if(e.hasOwnProperty("userId")){t=e.userId}r.Contracts.throwIfNullOrWhitespace(t,this.privErrors.invalidArgs.replace("{arg}","userId"));-1===this.participants.findIndex((e=>e.id===t))&&this.handleError(new Error(this.privErrors.invalidParticipantRequest),n),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getEjectCommand(t),(()=>{this.handleCallback(i,n)}),(e=>{this.handleError(e,n)}))}}catch(o){this.handleError(o,n)}}unlockConversationAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","unlock")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getLockCommand(!1),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}unmuteAllParticipantsAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","unmute all")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteAllCommand(!1),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}unmuteParticipantAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","userId")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.me.isHost||this.me.id===e||this.handleError(new Error(this.privErrors.permissionDeniedParticipant.replace("{command}","mute")),i);-1===this.privParticipants.getParticipantIndex(e)&&this.handleError(new Error(this.privErrors.invalidParticipantRequest),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteCommand(e,!1),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}sendTextMessageAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","message")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),e.length>this.privTextMessageMaxLength&&this.handleError(new Error(this.privErrors.invalidArgs.replace("{arg}","message length")),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMessageCommand(e),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}setTranslatedLanguagesAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfArrayEmptyOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","languages")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getSetTranslateToLanguagesCommand(e),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}changeNicknameAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","nickname")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getChangeNicknameCommand(e),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}isDisposed(){return this.privIsDisposed}dispose(){this.isDisposed||(this.privIsDisposed=!0,this.config&&this.config.close(),this.privConfig=void 0,this.privLanguage=void 0,this.privProperties=void 0,this.privRoom=void 0,this.privToken=void 0,this.privManager=void 0,this.privIsConnected=!1,this.privIsReady=!1,this.privParticipants=void 0)}async connectTranscriberRecognizer(e){this.privTranscriberRecognizer&&await this.privTranscriberRecognizer.close(),await e.enforceAudioGating(),this.privTranscriberRecognizer=e,this.privTranscriberRecognizer.conversation=this}getKeepAlive(){const t=this.me?this.me.displayName:"default_nickname";return JSON.stringify({id:"0",nickname:t,participantId:this.privRoom.participantId,roomId:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.keepAlive})}addParticipantImplAsync(e){if(void 0!==this.privParticipants.addOrUpdateParticipant(e)&&this.privTranscriberRecognizer){const t=this.conversationInfo;return t.participants=[e],this.privTranscriberRecognizer.pushConversationEvent(t,"join")}}removeParticipantImplAsync(e){this.privParticipants.deleteParticipant(e.id);const t=this.conversationInfo;return t.participants=[e],this.privTranscriberRecognizer.pushConversationEvent(t,"leave")}async close(e){var t;try{this.privIsConnected=!1,await(null==(t=this.privConversationRecognizer)?void 0:t.close()),this.privConversationRecognizer=void 0,this.privConversationTranslator&&this.privConversationTranslator.dispose()}catch(r){throw r}e&&this.dispose()}handleCallback(e,t){if(e){try{e()}catch(r){t&&t(r)}e=void 0}}handleError(e,t){if(t)if(e instanceof Error){const r=e;t(r.name+": "+r.message)}else t(e)}toParticipants(e){const t=this.privParticipants.participants.map((e=>this.toParticipant(e)));return e?t:t.filter((e=>!1===e.isHost))}toParticipant(e){return new i.Participant(e.id,e.avatar,e.displayName,e.isHost,e.isMuted,e.isUsingTts,e.preferredLanguage,e.voice)}getMuteAllCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setMuteAll,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getMuteCommand(t,i){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(t,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setMute,participantId:t,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:i})}getLockCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setLockState,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getEjectCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(t,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.ejectParticipant,participantId:t,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand})}getSetTranslateToLanguagesCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setTranslateToLanguages,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getChangeNicknameCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(t,"nickname"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.changeNickname,nickname:t,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getMessageCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),r.Contracts.throwIfNullOrWhitespace(t,"message"),JSON.stringify({participantId:this.privRoom.participantId,roomId:this.privRoom.roomId,text:t,type:e.ConversationTranslatorMessageTypes.instantMessage})}}return pa.ConversationImpl=o,pa}var ha,da={};function va(){if(ha)return da;ha=1,Object.defineProperty(da,"__esModule",{value:!0}),da.ConversationCommon=void 0;return da.ConversationCommon=class{constructor(e){this.privAudioConfig=e}handleCallback(e,t){if(e){try{e()}catch(r){t&&t(r)}e=void 0}}handleError(e,t){if(t)if(e instanceof Error){const r=e;t(r.name+": "+r.message)}else t(e)}},da}var la,ga={};function fa(){if(la)return ga;la=1,Object.defineProperty(ga,"__esModule",{value:!0}),ga.ConversationExpirationEventArgs=void 0;const e=Mp();let t=class extends e.SessionEventArgs{constructor(e,t){super(t),this.privExpirationTime=e}get expirationTime(){return this.privExpirationTime}};return ga.ConversationExpirationEventArgs=t,ga}var ma,Sa={};function ya(){if(ma)return Sa;ma=1,Object.defineProperty(Sa,"__esModule",{value:!0}),Sa.ConversationParticipantsChangedEventArgs=void 0;const e=Mp();let t=class extends e.SessionEventArgs{constructor(e,t,r){super(r),this.privReason=e,this.privParticipant=t}get reason(){return this.privReason}get participants(){return this.privParticipant}};return Sa.ConversationParticipantsChangedEventArgs=t,Sa}var Ca,Pa={};function Ra(){if(Ca)return Pa;Ca=1,Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.ConversationTranslationCanceledEventArgs=void 0;const e=nn();let t=class extends e.CancellationEventArgsBase{};return Pa.ConversationTranslationCanceledEventArgs=t,Pa}var Ia,wa={};function Ta(){if(Ia)return wa;Ia=1,Object.defineProperty(wa,"__esModule",{value:!0}),wa.ConversationTranslationEventArgs=void 0;const e=Mp();let t=class extends e.RecognitionEventArgs{constructor(e,t,r){super(t,r),this.privResult=e}get result(){return this.privResult}};return wa.ConversationTranslationEventArgs=t,wa}var Aa,Ea={};function ba(){if(Aa)return Ea;Aa=1,Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.ConversationTranslationResult=void 0;const e=ln();let t=class extends e.TranslationRecognitionResult{constructor(e,t,r,i,n,o,s,a,c,p,u){super(t,i,n,o,s,a,void 0,void 0,c,p,u),this.privId=e,this.privOrigLang=r}get participantId(){return this.privId}get originalLang(){return this.privOrigLang}};return Ea.ConversationTranslationResult=t,Ea}var Oa,Ma,Da,ka={},za={},Na={};function _a(){if(Oa)return Na;Oa=1,Object.defineProperty(Na,"__esModule",{value:!0}),Na.StringUtils=void 0;return Na.StringUtils=class{static formatString(e,t){if(!e)return"";if(!t)return e;let r="",i="";const n=e=>{r+=e},o=e=>{i+=e};let s=n;for(let a=0;a<e.length;a++){const c=e[a],p=a+1<e.length?e[a+1]:"";switch(c){case"{":"{"===p?(s("{"),a++):s=o;break;case"}":"}"===p?(s("}"),a++):(t.hasOwnProperty(i)&&(r+=t[i]),s=n,i="");break;default:s(c)}}return r}},Na}function La(){if(Ma)return za;Ma=1,Object.defineProperty(za,"__esModule",{value:!0}),za.ConversationTranslatorConnectionFactory=void 0;const e=Vp(),t=_a(),r=Rr(),i=Mp(),n=Xt(),o=Xo(),s=Zo(),a=jv();let c=class c extends s.ConnectionFactoryBase{constructor(e){super(),r.Contracts.throwIfNullOrUndefined(e,"convGetter"),this.privConvGetter=e}create(r,p,u){const h="TRUE"===r.parameters.getProperty("ConversationTranslator_MultiChannelAudio","").toUpperCase(),d=this.privConvGetter().room,v=d.cognitiveSpeechRegion||r.parameters.getProperty(i.PropertyId.SpeechServiceConnection_Region,""),l={hostSuffix:s.ConnectionFactoryBase.getHostSuffix(v),path:c.CTS_VIRT_MIC_PATH,region:encodeURIComponent(v)};l[o.QueryParameterNames.Language]=encodeURIComponent(r.parameters.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage,"")),l[o.QueryParameterNames.CtsMeetingId]=encodeURIComponent(d.roomId),l[o.QueryParameterNames.CtsDeviceId]=encodeURIComponent(d.participantId),l[o.QueryParameterNames.CtsIsParticipant]=d.isHost?"":"&"+o.QueryParameterNames.CtsIsParticipant;let g="";const f={},m={};if(h){if(g=r.parameters.getProperty(i.PropertyId.SpeechServiceConnection_Endpoint),!g){g="wss://"+r.parameters.getProperty(i.PropertyId.SpeechServiceConnection_Host,"transcribe.{region}.cts.speech{hostSuffix}")+"{path}"}g=t.StringUtils.formatString(g,l);const e=new URL(g);e.searchParams.forEach(((e,t)=>{f[t]=e}));(new a.TranscriberConnectionFactory).setQueryParams(f,r,g),f[o.QueryParameterNames.CtsMeetingId]=l[o.QueryParameterNames.CtsMeetingId],f[o.QueryParameterNames.CtsDeviceId]=l[o.QueryParameterNames.CtsDeviceId],d.isHost||(f[o.QueryParameterNames.CtsIsParticipant]=""),o.QueryParameterNames.Format in f||(f[o.QueryParameterNames.Format]="simple"),e.searchParams.forEach(((t,r)=>{e.searchParams.set(r,f[r]),delete f[r]})),g=e.toString()}else{const e=new a.TranslationConnectionFactory;g=e.getEndpointUrl(r,!0),g=t.StringUtils.formatString(g,l),e.setQueryParams(f,r,g)}m[n.HeaderNames.ConnectionId]=u,m[e.RestConfigBase.configParams.token]=d.token,p.token&&(m[p.headerName]=p.token);const S="TRUE"===r.parameters.getProperty("SPEECH-EnableWebsocketCompression","").toUpperCase();return new e.WebsocketConnection(g,f,m,new a.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(r),S,u)}};return za.ConversationTranslatorConnectionFactory=c,c.CTS_VIRT_MIC_PATH="/speech/recognition/dynamicaudio",za}function xa(){return Da||(Da=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ConversationTranslator=e.SpeechState=void 0;const t=jv(),r=La(),i=Gt(),n=Rr(),o=Mp(),s=ua(),a=sc();var c,p;(p=c=e.SpeechState||(e.SpeechState={}))[p.Inactive=0]="Inactive",p[p.Connecting=1]="Connecting",p[p.Connected=2]="Connected";class u extends o.TranslationRecognizer{constructor(e,t,i,n){super(e,t,new r.ConversationTranslatorConnectionFactory(n)),this.privSpeechState=c.Inactive,i&&(this.privTranslator=i,this.sessionStarted=()=>{this.privSpeechState=c.Connected},this.sessionStopped=()=>{this.privSpeechState=c.Inactive},this.recognizing=(e,t)=>{this.privTranslator.recognizing&&this.privTranslator.recognizing(this.privTranslator,t)},this.recognized=async(e,t)=>{var r;(null==(r=t.result)?void 0:r.errorDetails)?(await this.cancelSpeech(),this.fireCancelEvent(t.result.errorDetails)):this.privTranslator.recognized&&this.privTranslator.recognized(this.privTranslator,t)},this.canceled=async()=>{if(this.privSpeechState!==c.Inactive)try{await this.cancelSpeech()}catch(e){this.privSpeechState=c.Inactive}})}get state(){return this.privSpeechState}set state(e){this.privSpeechState=e}set authentication(e){this.privReco.authentication=e}onConnection(){this.privSpeechState=c.Connected}async onCancelSpeech(){this.privSpeechState=c.Inactive,await this.cancelSpeech()}fireCancelEvent(e){try{if(this.privTranslator.canceled){const t=new a.ConversationTranslationCanceledEventArgs(o.CancellationReason.Error,e,o.CancellationErrorCode.RuntimeError);this.privTranslator.canceled(this.privTranslator,t)}}catch(t){}}async cancelSpeech(){var e;try{this.stopContinuousRecognitionAsync(),await(null==(e=this.privReco)?void 0:e.disconnect()),this.privSpeechState=c.Inactive}catch(t){}}}class h extends a.ConversationCommon{constructor(e){super(e),this.privErrors=t.ConversationConnectionConfig.restErrors,this.privIsDisposed=!1,this.privIsSpeaking=!1,this.privPlaceholderKey="abcdefghijklmnopqrstuvwxyz012345",this.privPlaceholderRegion="westus",this.privProperties=new o.PropertyCollection}get properties(){return this.privProperties}get speechRecognitionLanguage(){return this.privSpeechRecognitionLanguage}get participants(){var e;return null==(e=this.privConversation)?void 0:e.participants}get canSpeak(){return!(!this.privConversation.isConnected||!this.privCTRecognizer)&&(!this.privIsSpeaking&&this.privCTRecognizer.state!==c.Connected&&this.privCTRecognizer.state!==c.Connecting&&!this.privConversation.isMutedByHost)}onToken(e){this.privCTRecognizer.authentication=e}setServiceProperty(e,r){const i=JSON.parse(this.privProperties.getProperty(t.ServicePropertiesPropertyName,"{}"));i[e]=r,this.privProperties.setProperty(t.ServicePropertiesPropertyName,JSON.stringify(i))}joinConversationAsync(e,r,i,a,c){try{if("string"==typeof e){n.Contracts.throwIfNullOrUndefined(e,this.privErrors.invalidArgs.replace("{arg}","conversation id")),n.Contracts.throwIfNullOrWhitespace(r,this.privErrors.invalidArgs.replace("{arg}","nickname")),this.privConversation&&this.handleError(new Error(this.privErrors.permissionDeniedStart),c);let p=i;null!=p&&""!==p||(p=t.ConversationConnectionConfig.defaultLanguageCode),this.privSpeechTranslationConfig=o.SpeechTranslationConfig.fromSubscription(this.privPlaceholderKey,this.privPlaceholderRegion),this.privSpeechTranslationConfig.setProfanity(o.ProfanityOption.Masked),this.privSpeechTranslationConfig.addTargetLanguage(p),this.privSpeechTranslationConfig.setProperty(o.PropertyId[o.PropertyId.SpeechServiceConnection_RecoLanguage],p),this.privSpeechTranslationConfig.setProperty(o.PropertyId[o.PropertyId.ConversationTranslator_Name],r);const u=[o.PropertyId.SpeechServiceConnection_Host,o.PropertyId.ConversationTranslator_Host,o.PropertyId.SpeechServiceConnection_Endpoint,o.PropertyId.SpeechServiceConnection_ProxyHostName,o.PropertyId.SpeechServiceConnection_ProxyPassword,o.PropertyId.SpeechServiceConnection_ProxyPort,o.PropertyId.SpeechServiceConnection_ProxyUserName,"ConversationTranslator_MultiChannelAudio","ConversationTranslator_Region"];for(const e of u){const t=this.privProperties.getProperty(e);if(t){const r="string"==typeof e?e:o.PropertyId[e];this.privSpeechTranslationConfig.setProperty(r,t)}}const h=JSON.parse(this.privProperties.getProperty(t.ServicePropertiesPropertyName,"{}"));for(const e of Object.keys(h))this.privSpeechTranslationConfig.setServiceProperty(e,h[e],o.ServicePropertyChannel.UriQueryParameter);this.privConversation=new s.ConversationImpl(this.privSpeechTranslationConfig),this.privConversation.conversationTranslator=this,this.privConversation.joinConversationAsync(e,r,p,(e=>{e||this.handleError(new Error(this.privErrors.permissionDeniedConnect),c),this.privSpeechTranslationConfig.authorizationToken=e,this.privConversation.room.isHost=!1,this.privConversation.startConversationAsync((()=>{this.handleCallback(a,c)}),(e=>{this.handleError(e,c)}))}),(e=>{this.handleError(e,c)}))}else"object"==typeof e?(n.Contracts.throwIfNullOrUndefined(e,this.privErrors.invalidArgs.replace("{arg}","conversation id")),n.Contracts.throwIfNullOrWhitespace(r,this.privErrors.invalidArgs.replace("{arg}","nickname")),this.privProperties.setProperty(o.PropertyId.ConversationTranslator_Name,r),this.privConversation=e,this.privConversation.conversationTranslator=this,this.privConversation.room.isHost=!0,n.Contracts.throwIfNullOrUndefined(this.privConversation,this.privErrors.permissionDeniedConnect),n.Contracts.throwIfNullOrUndefined(this.privConversation.room.token,this.privErrors.permissionDeniedConnect),this.privSpeechTranslationConfig=e.config,this.handleCallback(i,a)):this.handleError(new Error(this.privErrors.invalidArgs.replace("{arg}","invalid conversation type")),a)}catch(p){this.handleError(p,"string"==typeof i?c:a)}}leaveConversationAsync(e,t){i.marshalPromiseToCallbacks((async()=>{await this.cancelSpeech(),await this.privConversation.endConversationImplAsync(),await this.privConversation.deleteConversationImplAsync(),this.dispose()})(),e,t)}sendTextMessageAsync(e,t,r){try{n.Contracts.throwIfNullOrUndefined(this.privConversation,this.privErrors.permissionDeniedSend),n.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}",e)),this.privConversation.sendTextMessageAsync(e,t,r)}catch(i){this.handleError(i,r)}}startTranscribingAsync(e,t){i.marshalPromiseToCallbacks((async()=>{try{n.Contracts.throwIfNullOrUndefined(this.privConversation,this.privErrors.permissionDeniedSend),n.Contracts.throwIfNullOrUndefined(this.privConversation.room.token,this.privErrors.permissionDeniedConnect),void 0===this.privCTRecognizer&&await this.connectTranslatorRecognizer(),n.Contracts.throwIfNullOrUndefined(this.privCTRecognizer,this.privErrors.permissionDeniedSend),this.canSpeak||this.handleError(new Error(this.privErrors.permissionDeniedSend),t),await this.startContinuousRecognition(),this.privIsSpeaking=!0}catch(e){throw this.privIsSpeaking=!1,await this.cancelSpeech(),e}})(),e,t)}stopTranscribingAsync(e,t){i.marshalPromiseToCallbacks((async()=>{try{if(!this.privIsSpeaking)return void(await this.cancelSpeech());this.privIsSpeaking=!1,await new Promise(((e,t)=>{this.privCTRecognizer.stopContinuousRecognitionAsync(e,t)}))}catch(e){await this.cancelSpeech()}})(),e,t)}isDisposed(){return this.privIsDisposed}dispose(e,t,r){i.marshalPromiseToCallbacks((async()=>{this.isDisposed&&!this.privIsSpeaking||(await this.cancelSpeech(),this.privIsDisposed=!0,this.privSpeechTranslationConfig.close(),this.privSpeechRecognitionLanguage=void 0,this.privProperties=void 0,this.privAudioConfig=void 0,this.privSpeechTranslationConfig=void 0,this.privConversation.dispose(),this.privConversation=void 0)})(),t,r)}async cancelSpeech(){var e;try{this.privIsSpeaking=!1,await(null==(e=this.privCTRecognizer)?void 0:e.onCancelSpeech()),this.privCTRecognizer=void 0}catch(t){}}async connectTranslatorRecognizer(){try{void 0===this.privAudioConfig&&(this.privAudioConfig=o.AudioConfig.fromDefaultMicrophoneInput()),this.privSpeechTranslationConfig.getProperty(o.PropertyId[o.PropertyId.SpeechServiceConnection_Key])===this.privPlaceholderKey&&this.privSpeechTranslationConfig.setProperty(o.PropertyId[o.PropertyId.SpeechServiceConnection_Key],"");const e=()=>this.privConversation;this.privCTRecognizer=new u(this.privSpeechTranslationConfig,this.privAudioConfig,this,e)}catch(e){throw await this.cancelSpeech(),e}}startContinuousRecognition(){return new Promise(((e,t)=>{this.privCTRecognizer.startContinuousRecognitionAsync(e,t)}))}}e.ConversationTranslator=h}(ka)),ka}var Fa,Ba={};function ja(){if(Fa)return Ba;Fa=1,Object.defineProperty(Ba,"__esModule",{value:!0}),Ba.ConversationTranscriber=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();let n=class n extends i.Recognizer{constructor(t,n){const o=t;r.Contracts.throwIfNull(o,"speechConfig"),r.Contracts.throwIfNullOrWhitespace(o.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage),i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage]),super(n,o.properties,new e.ConversationTranscriberConnectionFactory),this.privProperties.setProperty(i.PropertyId.SpeechServiceConnection_RecognitionEndpointVersion,"2"),this.privDisposedRecognizer=!1}static FromConfig(e,t,r){const i=e;t.properties.mergeTo(i.properties);return new n(e,r)}get endpointId(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(i.PropertyId.SpeechServiceConnection_EndpointId,"00000000-0000-0000-0000-000000000000")}get authorizationToken(){return this.properties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(i.PropertyId.SpeechServiceAuthorization_Token,e)}get speechRecognitionLanguage(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage)}get outputFormat(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(e.OutputFormatPropertyName,i.OutputFormat[i.OutputFormat.Simple])===i.OutputFormat[i.OutputFormat.Simple]?i.OutputFormat.Simple:i.OutputFormat.Detailed}get properties(){return this.privProperties}startTranscribingAsync(r,i){t.marshalPromiseToCallbacks(this.startContinuousRecognitionAsyncImpl(e.RecognitionMode.Conversation),r,i)}stopTranscribingAsync(e,r){t.marshalPromiseToCallbacks(this.stopContinuousRecognitionAsyncImpl(),e,r)}close(e,i){r.Contracts.throwIfDisposed(this.privDisposedRecognizer),t.marshalPromiseToCallbacks(this.dispose(!0),e,i)}async dispose(e){this.privDisposedRecognizer||(e&&(this.privDisposedRecognizer=!0,await this.implRecognizerStop()),await super.dispose(e))}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.privProperties)}createServiceRecognizer(t,r,i,n){const o=i;return n.isSpeakerDiarizationEnabled=!0,new e.ConversationTranscriptionServiceRecognizer(t,r,o,n,this)}};return Ba.ConversationTranscriber=n,Ba}var Ua,qa={};function Wa(){if(Ua)return qa;Ua=1,Object.defineProperty(qa,"__esModule",{value:!0}),qa.Participant=qa.User=void 0;const e=Mp();qa.User=class{constructor(e){this.privUserId=e}get userId(){return this.privUserId}};class t{constructor(t,r,i,n,o,s,a,c){this.privId=t,this.privAvatar=r,this.privDisplayName=i,this.privIsHost=n,this.privIsMuted=o,this.privIsUsingTts=s,this.privPreferredLanguage=a,this.privVoice=c,this.privProperties=new e.PropertyCollection}get avatar(){return this.privAvatar}get displayName(){return this.privDisplayName}get id(){return this.privId}get preferredLanguage(){return this.privPreferredLanguage}get isHost(){return this.privIsHost}get isMuted(){return this.privIsMuted}get isUsingTts(){return this.privIsUsingTts}get voice(){return this.privVoice}get properties(){return this.privProperties}static From(e,r,i){return new t(e,"",e,!1,!1,!1,r,i)}}return qa.Participant=t,qa}var Ha,Va={};function Ka(){return Ha||(Ha=1,e=Va,Object.defineProperty(e,"__esModule",{value:!0}),e.ParticipantChangedReason=void 0,(t=e.ParticipantChangedReason||(e.ParticipantChangedReason={}))[t.JoinedConversation=0]="JoinedConversation",t[t.LeftConversation=1]="LeftConversation",t[t.Updated=2]="Updated"),Va;var e,t}var Ja,Ga={};function $a(){if(Ja)return Ga;Ja=1,Object.defineProperty(Ga,"__esModule",{value:!0}),Ga.MeetingImpl=Ga.Meeting=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();let n=class{constructor(){}static createMeetingAsync(n,s,a,c){if(r.Contracts.throwIfNullOrUndefined(n,e.ConversationConnectionConfig.restErrors.invalidArgs.replace("{arg}","config")),r.Contracts.throwIfNullOrUndefined(n.region,e.ConversationConnectionConfig.restErrors.invalidArgs.replace("{arg}","SpeechServiceConnection_Region")),r.Contracts.throwIfNull(s,"meetingId"),0===s.length)throw new Error("meetingId cannot be empty");n.subscriptionKey||n.getProperty(i.PropertyId[i.PropertyId.SpeechServiceAuthorization_Token])||r.Contracts.throwIfNullOrUndefined(n.subscriptionKey,e.ConversationConnectionConfig.restErrors.invalidArgs.replace("{arg}","SpeechServiceConnection_Key"));const p=new o(n,s);return t.marshalPromiseToCallbacks((async()=>{})(),a,c),p}};Ga.Meeting=n;class o extends n{constructor(t,n){super(),this.privErrors=e.ConversationConnectionConfig.restErrors,this.onConnected=e=>{var t;this.privIsConnected=!0;try{(null==(t=this.privConversationTranslator)?void 0:t.sessionStarted)&&this.privConversationTranslator.sessionStarted(this.privConversationTranslator,e)}catch(r){}},this.onDisconnected=e=>{var t;try{(null==(t=this.privConversationTranslator)?void 0:t.sessionStopped)&&this.privConversationTranslator.sessionStopped(this.privConversationTranslator,e)}catch(r){}finally{this.close(!1)}},this.onCanceled=(e,t)=>{var r;try{(null==(r=this.privConversationTranslator)?void 0:r.canceled)&&this.privConversationTranslator.canceled(this.privConversationTranslator,t)}catch(i){}},this.onParticipantUpdateCommandReceived=(t,r)=>{try{const t=this.privParticipants.getParticipant(r.id);if(void 0!==t){switch(r.key){case e.ConversationTranslatorCommandTypes.changeNickname:t.displayName=r.value;break;case e.ConversationTranslatorCommandTypes.setUseTTS:t.isUsingTts=r.value;break;case e.ConversationTranslatorCommandTypes.setProfanityFiltering:t.profanity=r.value;break;case e.ConversationTranslatorCommandTypes.setMute:t.isMuted=r.value;break;case e.ConversationTranslatorCommandTypes.setTranslateToLanguages:t.translateToLanguages=r.value}this.privParticipants.addOrUpdateParticipant(t),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.Updated,[this.toParticipant(t)],r.sessionId))}}catch(n){}},this.onLockRoomCommandReceived=()=>{},this.onMuteAllCommandReceived=(e,t)=>{try{this.privParticipants.participants.forEach((e=>e.isMuted=!e.isHost&&t.isMuted)),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.Updated,this.toParticipants(!1),t.sessionId))}catch(r){}},this.onParticipantJoinCommandReceived=(e,t)=>{try{const e=this.privParticipants.addOrUpdateParticipant(t.participant);void 0!==e&&this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.JoinedConversation,[this.toParticipant(e)],t.sessionId))}catch(r){}},this.onParticipantLeaveCommandReceived=(e,t)=>{try{const e=this.privParticipants.getParticipant(t.participant.id);void 0!==e&&(this.privParticipants.deleteParticipant(t.participant.id),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.LeftConversation,[this.toParticipant(e)],t.sessionId)))}catch(r){}},this.onTranslationReceived=(t,r)=>{try{switch(r.command){case e.ConversationTranslatorMessageTypes.final:this.privConversationTranslator&&this.privConversationTranslator.transcribed(this.privConversationTranslator,new i.ConversationTranslationEventArgs(r.payload,void 0,r.sessionId));break;case e.ConversationTranslatorMessageTypes.partial:this.privConversationTranslator&&this.privConversationTranslator.transcribing(this.privConversationTranslator,new i.ConversationTranslationEventArgs(r.payload,void 0,r.sessionId));break;case e.ConversationTranslatorMessageTypes.instantMessage:this.privConversationTranslator&&this.privConversationTranslator.textMessageReceived(this.privConversationTranslator,new i.ConversationTranslationEventArgs(r.payload,void 0,r.sessionId))}}catch(n){}},this.onParticipantsListReceived=(e,t)=>{var r;try{if(void 0!==t.sessionToken&&null!==t.sessionToken&&(this.privRoom.token=t.sessionToken),this.privParticipants.participants=[...t.participants],void 0!==this.privParticipants.me&&(this.privIsReady=!0),this.privConversationTranslator&&this.privConversationTranslator.participantsChanged(this.privConversationTranslator,new i.ConversationParticipantsChangedEventArgs(i.ParticipantChangedReason.JoinedConversation,this.toParticipants(!0),t.sessionId)),this.me.isHost){const e=null==(r=this.privConversationTranslator)?void 0:r.properties.getProperty(i.PropertyId.ConversationTranslator_Name);void 0!==e&&e.length>0&&e!==this.me.displayName&&this.changeNicknameAsync(e)}}catch(n){}},this.onConversationExpiration=(e,t)=>{try{this.privConversationTranslator&&this.privConversationTranslator.conversationExpiration(this.privConversationTranslator,t)}catch(r){}},this.privIsConnected=!1,this.privIsDisposed=!1,this.privConversationId="",this.privProperties=new i.PropertyCollection,this.privManager=new e.ConversationManager;t.getProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage])||t.setProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage],e.ConversationConnectionConfig.defaultLanguageCode),this.privLanguage=t.getProperty(i.PropertyId[i.PropertyId.SpeechServiceConnection_RecoLanguage]),this.privConversationId=n,this.privConfig=t;const o=t;r.Contracts.throwIfNull(o,"speechConfig"),this.privProperties=o.properties.clone(),this.privIsConnected=!1,this.privParticipants=new e.InternalParticipants,this.privIsReady=!1,this.privTextMessageMaxLength=1e3}get room(){return this.privRoom}get connection(){return this.privConversationRecognizer}get config(){return this.privConfig}get meetingId(){return this.privRoom?this.privRoom.roomId:this.privConversationId}get properties(){return this.privProperties}get speechRecognitionLanguage(){return this.privLanguage}get isMutedByHost(){var e,t;return!(null==(e=this.privParticipants.me)?void 0:e.isHost)&&(null==(t=this.privParticipants.me)?void 0:t.isMuted)}get isConnected(){return this.privIsConnected&&this.privIsReady}get participants(){return this.toParticipants(!0)}get me(){return this.toParticipant(this.privParticipants.me)}get host(){return this.toParticipant(this.privParticipants.host)}get transcriberRecognizer(){return this.privTranscriberRecognizer}get meetingInfo(){const t=this.meetingId,r=this.participants.map((e=>({id:e.id,preferredLanguage:e.preferredLanguage,voice:e.voice}))),i={};for(const n of e.ConversationConnectionConfig.transcriptionEventKeys){const e=this.properties.getProperty(n,"");""!==e&&(i[n]=e)}return{id:t,participants:r,meetingProperties:i}}get canSend(){var e;return this.privIsConnected&&!(null==(e=this.privParticipants.me)?void 0:e.isMuted)}get canSendAsHost(){var e;return this.privIsConnected&&(null==(e=this.privParticipants.me)?void 0:e.isHost)}get authorizationToken(){return this.privToken}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"authorizationToken"),this.privToken=e}createMeetingAsync(e,t){try{this.privConversationRecognizer&&this.handleError(new Error(this.privErrors.permissionDeniedStart),t),this.privManager.createOrJoin(this.privProperties,void 0,(r=>{r||this.handleError(new Error(this.privErrors.permissionDeniedConnect),t),this.privRoom=r,this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(r){this.handleError(r,t)}}startMeetingAsync(e,t){try{this.privConversationRecognizer&&this.handleError(new Error(this.privErrors.permissionDeniedStart),t),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedConnect),this.privParticipants.meId=this.privRoom.participantId,this.privConversationRecognizer.connected=this.onConnected,this.privConversationRecognizer.disconnected=this.onDisconnected,this.privConversationRecognizer.canceled=this.onCanceled,this.privConversationRecognizer.participantUpdateCommandReceived=this.onParticipantUpdateCommandReceived,this.privConversationRecognizer.lockRoomCommandReceived=this.onLockRoomCommandReceived,this.privConversationRecognizer.muteAllCommandReceived=this.onMuteAllCommandReceived,this.privConversationRecognizer.participantJoinCommandReceived=this.onParticipantJoinCommandReceived,this.privConversationRecognizer.participantLeaveCommandReceived=this.onParticipantLeaveCommandReceived,this.privConversationRecognizer.translationReceived=this.onTranslationReceived,this.privConversationRecognizer.participantsListReceived=this.onParticipantsListReceived,this.privConversationRecognizer.conversationExpiration=this.onConversationExpiration,this.privConversationRecognizer.connect(this.privRoom.token,(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}addParticipantAsync(e,i,n){r.Contracts.throwIfNullOrUndefined(e,"Participant"),t.marshalPromiseToCallbacks(this.addParticipantImplAsync(e),i,n)}joinMeetingAsync(e,t,i,n,o){try{r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","conversationId")),r.Contracts.throwIfNullOrWhitespace(t,this.privErrors.invalidArgs.replace("{arg}","nickname")),r.Contracts.throwIfNullOrWhitespace(i,this.privErrors.invalidArgs.replace("{arg}","language")),this.privManager.createOrJoin(this.privProperties,e,(e=>{r.Contracts.throwIfNullOrUndefined(e,this.privErrors.permissionDeniedConnect),this.privRoom=e,this.privConfig.authorizationToken=e.cognitiveSpeechAuthToken,n&&n(e.cognitiveSpeechAuthToken)}),(e=>{this.handleError(e,o)}))}catch(s){this.handleError(s,o)}}deleteMeetingAsync(e,r){t.marshalPromiseToCallbacks(this.deleteMeetingImplAsync(),e,r)}async deleteMeetingImplAsync(){r.Contracts.throwIfNullOrUndefined(this.privProperties,this.privErrors.permissionDeniedConnect),r.Contracts.throwIfNullOrWhitespace(this.privRoom.token,this.privErrors.permissionDeniedConnect),await this.privManager.leave(this.privProperties,this.privRoom.token),this.dispose()}endMeetingAsync(e,r){t.marshalPromiseToCallbacks(this.endMeetingImplAsync(),e,r)}endMeetingImplAsync(){return this.close(!0)}lockMeetingAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","lock")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getLockCommand(!0),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}muteAllParticipantsAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privConversationRecognizer,this.privErrors.permissionDeniedSend),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","mute")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteAllCommand(!0),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}muteParticipantAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","userId")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.me.isHost||this.me.id===e||this.handleError(new Error(this.privErrors.permissionDeniedParticipant.replace("{command}","mute")),i);-1===this.privParticipants.getParticipantIndex(e)&&this.handleError(new Error(this.privErrors.invalidParticipantRequest),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteCommand(e,!0),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}removeParticipantAsync(e,i,n){try{if(r.Contracts.throwIfDisposed(this.privIsDisposed),this.privTranscriberRecognizer&&e.hasOwnProperty("id"))t.marshalPromiseToCallbacks(this.removeParticipantImplAsync(e),i,n);else{r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedParticipant.replace("{command}","remove")),n);let t="";if("string"==typeof e)t=e;else if(e.hasOwnProperty("id")){t=e.id}else if(e.hasOwnProperty("userId")){t=e.userId}r.Contracts.throwIfNullOrWhitespace(t,this.privErrors.invalidArgs.replace("{arg}","userId"));-1===this.participants.findIndex((e=>e.id===t))&&this.handleError(new Error(this.privErrors.invalidParticipantRequest),n),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getEjectCommand(t),(()=>{this.handleCallback(i,n)}),(e=>{this.handleError(e,n)}))}}catch(o){this.handleError(o,n)}}unlockMeetingAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","unlock")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getLockCommand(!1),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}unmuteAllParticipantsAsync(e,t){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSendAsHost||this.handleError(new Error(this.privErrors.permissionDeniedConversation.replace("{command}","unmute all")),t),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteAllCommand(!1),(()=>{this.handleCallback(e,t)}),(e=>{this.handleError(e,t)}))}catch(i){this.handleError(i,t)}}unmuteParticipantAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","userId")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.me.isHost||this.me.id===e||this.handleError(new Error(this.privErrors.permissionDeniedParticipant.replace("{command}","mute")),i);-1===this.privParticipants.getParticipantIndex(e)&&this.handleError(new Error(this.privErrors.invalidParticipantRequest),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMuteCommand(e,!1),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}sendTextMessageAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","message")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),e.length>this.privTextMessageMaxLength&&this.handleError(new Error(this.privErrors.invalidArgs.replace("{arg}","message length")),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getMessageCommand(e),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}setTranslatedLanguagesAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfArrayEmptyOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","languages")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getSetTranslateToLanguagesCommand(e),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}changeNicknameAsync(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfDisposed(this.privConversationRecognizer.isDisposed()),r.Contracts.throwIfNullOrWhitespace(e,this.privErrors.invalidArgs.replace("{arg}","nickname")),r.Contracts.throwIfNullOrUndefined(this.privRoom,this.privErrors.permissionDeniedSend),this.canSend||this.handleError(new Error(this.privErrors.permissionDeniedSend),i),this.privConversationRecognizer&&this.privConversationRecognizer.sendRequest(this.getChangeNicknameCommand(e),(()=>{this.handleCallback(t,i)}),(e=>{this.handleError(e,i)}))}catch(n){this.handleError(n,i)}}isDisposed(){return this.privIsDisposed}dispose(){this.isDisposed||(this.privIsDisposed=!0,this.config&&this.config.close(),this.privConfig=void 0,this.privLanguage=void 0,this.privProperties=void 0,this.privRoom=void 0,this.privToken=void 0,this.privManager=void 0,this.privIsConnected=!1,this.privIsReady=!1,this.privParticipants=void 0)}async connectTranscriberRecognizer(e){this.privTranscriberRecognizer&&await this.privTranscriberRecognizer.close(),await e.enforceAudioGating(),this.privTranscriberRecognizer=e,this.privTranscriberRecognizer.meeting=this}getKeepAlive(){const t=this.me?this.me.displayName:"default_nickname";return JSON.stringify({id:"0",nickname:t,participantId:this.privRoom.participantId,roomId:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.keepAlive})}addParticipantImplAsync(e){if(void 0!==this.privParticipants.addOrUpdateParticipant(e)&&this.privTranscriberRecognizer){const t=this.meetingInfo;return t.participants=[e],this.privTranscriberRecognizer.pushMeetingEvent(t,"join")}}removeParticipantImplAsync(e){this.privParticipants.deleteParticipant(e.id);const t=this.meetingInfo;return t.participants=[e],this.privTranscriberRecognizer.pushMeetingEvent(t,"leave")}async close(e){var t;try{this.privIsConnected=!1,await(null==(t=this.privConversationRecognizer)?void 0:t.close()),this.privConversationRecognizer=void 0,this.privConversationTranslator&&this.privConversationTranslator.dispose()}catch(r){throw r}e&&this.dispose()}handleCallback(e,t){if(e){try{e()}catch(r){t&&t(r)}e=void 0}}handleError(e,t){if(t)if(e instanceof Error){const r=e;t(r.name+": "+r.message)}else t(e)}toParticipants(e){const t=this.privParticipants.participants.map((e=>this.toParticipant(e)));return e?t:t.filter((e=>!1===e.isHost))}toParticipant(e){return new i.Participant(e.id,e.avatar,e.displayName,e.isHost,e.isMuted,e.isUsingTts,e.preferredLanguage,e.voice)}getMuteAllCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"meetingd"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setMuteAll,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getMuteCommand(t,i){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"conversationId"),r.Contracts.throwIfNullOrWhitespace(t,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setMute,participantId:t,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:i})}getLockCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"meetingId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setLockState,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getEjectCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"meetingId"),r.Contracts.throwIfNullOrWhitespace(t,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.ejectParticipant,participantId:t,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand})}getSetTranslateToLanguagesCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"meetingId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.setTranslateToLanguages,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getChangeNicknameCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"meetingId"),r.Contracts.throwIfNullOrWhitespace(t,"nickname"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),JSON.stringify({command:e.ConversationTranslatorCommandTypes.changeNickname,nickname:t,participantId:this.privRoom.participantId,roomid:this.privRoom.roomId,type:e.ConversationTranslatorMessageTypes.participantCommand,value:t})}getMessageCommand(t){return r.Contracts.throwIfNullOrWhitespace(this.privRoom.roomId,"meetingId"),r.Contracts.throwIfNullOrWhitespace(this.privRoom.participantId,"participantId"),r.Contracts.throwIfNullOrWhitespace(t,"message"),JSON.stringify({participantId:this.privRoom.participantId,roomId:this.privRoom.roomId,text:t,type:e.ConversationTranslatorMessageTypes.instantMessage})}}return Ga.MeetingImpl=o,Ga}var Qa,Xa={};function Za(){if(Qa)return Xa;Qa=1,Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.MeetingTranscriptionCanceledEventArgs=void 0;const e=nn();class t extends e.CancellationEventArgsBase{}return Xa.MeetingTranscriptionCanceledEventArgs=t,Xa}var Ya,ec={};function tc(){if(Ya)return ec;Ya=1,Object.defineProperty(ec,"__esModule",{value:!0}),ec.MeetingTranscriber=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp(),n=sc();return ec.MeetingTranscriber=class{constructor(e){this.privAudioConfig=e,this.privProperties=new i.PropertyCollection,this.privRecognizer=void 0,this.privDisposedRecognizer=!1}get speechRecognitionLanguage(){return r.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(i.PropertyId.SpeechServiceConnection_RecoLanguage)}get properties(){return this.privProperties}get internalData(){return this.privRecognizer.internalData}get connection(){return i.Connection.fromRecognizer(this.privRecognizer)}get authorizationToken(){return this.properties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(i.PropertyId.SpeechServiceAuthorization_Token,e)}joinMeetingAsync(i,o,s){const a=i;r.Contracts.throwIfNullOrUndefined(n.MeetingImpl,"Meeting"),this.privRecognizer=new e.TranscriberRecognizer(i.config,this.privAudioConfig),r.Contracts.throwIfNullOrUndefined(this.privRecognizer,"Recognizer"),this.privRecognizer.connectMeetingCallbacks(this),t.marshalPromiseToCallbacks(a.connectTranscriberRecognizer(this.privRecognizer),o,s)}startTranscribingAsync(e,t){this.privRecognizer.startContinuousRecognitionAsync(e,t)}stopTranscribingAsync(e,t){this.privRecognizer.stopContinuousRecognitionAsync(e,t)}leaveMeetingAsync(e,r){this.privRecognizer.disconnectCallbacks(),t.marshalPromiseToCallbacks((async()=>{})(),e,r)}close(e,i){r.Contracts.throwIfDisposed(this.privDisposedRecognizer),t.marshalPromiseToCallbacks(this.dispose(!0),e,i)}async dispose(e){this.privDisposedRecognizer||(this.privRecognizer&&(await this.privRecognizer.close(),this.privRecognizer=void 0),e&&(this.privDisposedRecognizer=!0))}},ec}var rc,ic,nc={};function oc(){if(rc)return nc;rc=1,Object.defineProperty(nc,"__esModule",{value:!0}),nc.ConversationTranscriptionResult=void 0;const e=Mp();let t=class extends e.RecognitionResult{constructor(e,t,r,i,n,o,s,a,c,p,u){super(e,t,r,i,n,o,s,c,p,u),this.privSpeakerId=a}get speakerId(){return this.privSpeakerId}};return nc.ConversationTranscriptionResult=t,nc}function sc(){return ic||(ic=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=ua();Object.defineProperty(e,"Conversation",{enumerable:!0,get:function(){return t.Conversation}}),Object.defineProperty(e,"ConversationImpl",{enumerable:!0,get:function(){return t.ConversationImpl}});var r=va();Object.defineProperty(e,"ConversationCommon",{enumerable:!0,get:function(){return r.ConversationCommon}});var i=fa();Object.defineProperty(e,"ConversationExpirationEventArgs",{enumerable:!0,get:function(){return i.ConversationExpirationEventArgs}});var n=ya();Object.defineProperty(e,"ConversationParticipantsChangedEventArgs",{enumerable:!0,get:function(){return n.ConversationParticipantsChangedEventArgs}});var o=Ra();Object.defineProperty(e,"ConversationTranslationCanceledEventArgs",{enumerable:!0,get:function(){return o.ConversationTranslationCanceledEventArgs}});var s=Ta();Object.defineProperty(e,"ConversationTranslationEventArgs",{enumerable:!0,get:function(){return s.ConversationTranslationEventArgs}});var a=ba();Object.defineProperty(e,"ConversationTranslationResult",{enumerable:!0,get:function(){return a.ConversationTranslationResult}});var c=xa();Object.defineProperty(e,"ConversationTranslator",{enumerable:!0,get:function(){return c.ConversationTranslator}});var p=ja();Object.defineProperty(e,"ConversationTranscriber",{enumerable:!0,get:function(){return p.ConversationTranscriber}});var u=Wa();Object.defineProperty(e,"Participant",{enumerable:!0,get:function(){return u.Participant}}),Object.defineProperty(e,"User",{enumerable:!0,get:function(){return u.User}});var h=Ka();Object.defineProperty(e,"ParticipantChangedReason",{enumerable:!0,get:function(){return h.ParticipantChangedReason}});var d=$a();Object.defineProperty(e,"Meeting",{enumerable:!0,get:function(){return d.Meeting}}),Object.defineProperty(e,"MeetingImpl",{enumerable:!0,get:function(){return d.MeetingImpl}});var v=Za();Object.defineProperty(e,"MeetingTranscriptionCanceledEventArgs",{enumerable:!0,get:function(){return v.MeetingTranscriptionCanceledEventArgs}});var l=tc();Object.defineProperty(e,"MeetingTranscriber",{enumerable:!0,get:function(){return l.MeetingTranscriber}});var g=oc();Object.defineProperty(e,"ConversationTranscriptionResult",{enumerable:!0,get:function(){return g.ConversationTranscriptionResult}})}(ca)),ca}var ac,cc={};function pc(){if(ac)return cc;ac=1,Object.defineProperty(cc,"__esModule",{value:!0}),cc.SynthesisRequest=cc.Synthesizer=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp();cc.Synthesizer=class n{constructor(e){const i=e;r.Contracts.throwIfNull(i,"speechConfig"),this.privProperties=i.properties.clone(),this.privDisposed=!1,this.privSynthesizing=!1,this.synthesisRequestQueue=new t.Queue}get authorizationToken(){return this.properties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){r.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(i.PropertyId.SpeechServiceAuthorization_Token,e)}get properties(){return this.privProperties}get autoDetectSourceLanguage(){return this.properties.getProperty(i.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages)===e.AutoDetectSourceLanguagesOpenRangeOptionName}buildSsml(e){const t={"af-ZA":"af-ZA-AdriNeural","am-ET":"am-ET-AmehaNeural","ar-AE":"ar-AE-FatimaNeural","ar-BH":"ar-BH-AliNeural","ar-DZ":"ar-DZ-AminaNeural","ar-EG":"ar-EG-SalmaNeural","ar-IQ":"ar-IQ-BasselNeural","ar-JO":"ar-JO-SanaNeural","ar-KW":"ar-KW-FahedNeural","ar-LY":"ar-LY-ImanNeural","ar-MA":"ar-MA-JamalNeural","ar-QA":"ar-QA-AmalNeural","ar-SA":"ar-SA-HamedNeural","ar-SY":"ar-SY-AmanyNeural","ar-TN":"ar-TN-HediNeural","ar-YE":"ar-YE-MaryamNeural","bg-BG":"bg-BG-BorislavNeural","bn-BD":"bn-BD-NabanitaNeural","bn-IN":"bn-IN-BashkarNeural","ca-ES":"ca-ES-JoanaNeural","cs-CZ":"cs-CZ-AntoninNeural","cy-GB":"cy-GB-AledNeural","da-DK":"da-DK-ChristelNeural","de-AT":"de-AT-IngridNeural","de-CH":"de-CH-JanNeural","de-DE":"de-DE-KatjaNeural","el-GR":"el-GR-AthinaNeural","en-AU":"en-AU-NatashaNeural","en-CA":"en-CA-ClaraNeural","en-GB":"en-GB-LibbyNeural","en-HK":"en-HK-SamNeural","en-IE":"en-IE-ConnorNeural","en-IN":"en-IN-NeerjaNeural","en-KE":"en-KE-AsiliaNeural","en-NG":"en-NG-AbeoNeural","en-NZ":"en-NZ-MitchellNeural","en-PH":"en-PH-JamesNeural","en-SG":"en-SG-LunaNeural","en-TZ":"en-TZ-ElimuNeural","en-US":"en-US-AvaMultilingualNeural","en-ZA":"en-ZA-LeahNeural","es-AR":"es-AR-ElenaNeural","es-BO":"es-BO-MarceloNeural","es-CL":"es-CL-CatalinaNeural","es-CO":"es-CO-GonzaloNeural","es-CR":"es-CR-JuanNeural","es-CU":"es-CU-BelkysNeural","es-DO":"es-DO-EmilioNeural","es-EC":"es-EC-AndreaNeural","es-ES":"es-ES-AlvaroNeural","es-GQ":"es-GQ-JavierNeural","es-GT":"es-GT-AndresNeural","es-HN":"es-HN-CarlosNeural","es-MX":"es-MX-DaliaNeural","es-NI":"es-NI-FedericoNeural","es-PA":"es-PA-MargaritaNeural","es-PE":"es-PE-AlexNeural","es-PR":"es-PR-KarinaNeural","es-PY":"es-PY-MarioNeural","es-SV":"es-SV-LorenaNeural","es-US":"es-US-AlonsoNeural","es-UY":"es-UY-MateoNeural","es-VE":"es-VE-PaolaNeural","et-EE":"et-EE-AnuNeural","fa-IR":"fa-IR-DilaraNeural","fi-FI":"fi-FI-SelmaNeural","fil-PH":"fil-PH-AngeloNeural","fr-BE":"fr-BE-CharlineNeural","fr-CA":"fr-CA-SylvieNeural","fr-CH":"fr-CH-ArianeNeural","fr-FR":"fr-FR-DeniseNeural","ga-IE":"ga-IE-ColmNeural","gl-ES":"gl-ES-RoiNeural","gu-IN":"gu-IN-DhwaniNeural","he-IL":"he-IL-AvriNeural","hi-IN":"hi-IN-MadhurNeural","hr-HR":"hr-HR-GabrijelaNeural","hu-HU":"hu-HU-NoemiNeural","id-ID":"id-ID-ArdiNeural","is-IS":"is-IS-GudrunNeural","it-IT":"it-IT-IsabellaNeural","ja-JP":"ja-JP-NanamiNeural","jv-ID":"jv-ID-DimasNeural","kk-KZ":"kk-KZ-AigulNeural","km-KH":"km-KH-PisethNeural","kn-IN":"kn-IN-GaganNeural","ko-KR":"ko-KR-SunHiNeural","lo-LA":"lo-LA-ChanthavongNeural","lt-LT":"lt-LT-LeonasNeural","lv-LV":"lv-LV-EveritaNeural","mk-MK":"mk-MK-AleksandarNeural","ml-IN":"ml-IN-MidhunNeural","mr-IN":"mr-IN-AarohiNeural","ms-MY":"ms-MY-OsmanNeural","mt-MT":"mt-MT-GraceNeural","my-MM":"my-MM-NilarNeural","nb-NO":"nb-NO-PernilleNeural","nl-BE":"nl-BE-ArnaudNeural","nl-NL":"nl-NL-ColetteNeural","pl-PL":"pl-PL-AgnieszkaNeural","ps-AF":"ps-AF-GulNawazNeural","pt-BR":"pt-BR-FranciscaNeural","pt-PT":"pt-PT-DuarteNeural","ro-RO":"ro-RO-AlinaNeural","ru-RU":"ru-RU-SvetlanaNeural","si-LK":"si-LK-SameeraNeural","sk-SK":"sk-SK-LukasNeural","sl-SI":"sl-SI-PetraNeural","so-SO":"so-SO-MuuseNeural","sr-RS":"sr-RS-NicholasNeural","su-ID":"su-ID-JajangNeural","sv-SE":"sv-SE-SofieNeural","sw-KE":"sw-KE-RafikiNeural","sw-TZ":"sw-TZ-DaudiNeural","ta-IN":"ta-IN-PallaviNeural","ta-LK":"ta-LK-KumarNeural","ta-SG":"ta-SG-AnbuNeural","te-IN":"te-IN-MohanNeural","th-TH":"th-TH-PremwadeeNeural","tr-TR":"tr-TR-AhmetNeural","uk-UA":"uk-UA-OstapNeural","ur-IN":"ur-IN-GulNeural","ur-PK":"ur-PK-AsadNeural","uz-UZ":"uz-UZ-MadinaNeural","vi-VN":"vi-VN-HoaiMyNeural","zh-CN":"zh-CN-XiaoxiaoNeural","zh-HK":"zh-HK-HiuMaanNeural","zh-TW":"zh-TW-HsiaoChenNeural","zu-ZA":"zu-ZA-ThandoNeural"};let r=this.properties.getProperty(i.PropertyId.SpeechServiceConnection_SynthLanguage,"en-US"),o=this.properties.getProperty(i.PropertyId.SpeechServiceConnection_SynthVoice,""),s=n.XMLEncode(e);return this.autoDetectSourceLanguage?r="en-US":o=o||t[r],o&&(s=`<voice name='${o}'>${s}</voice>`),s=`<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xmlns:mstts='http://www.w3.org/2001/mstts' xmlns:emo='http://www.w3.org/2009/10/emotionml' xml:lang='${r}'>${s}</speak>`,s}async dispose(e){this.privDisposed||(e&&this.privAdapter&&await this.privAdapter.dispose(),this.privDisposed=!0)}async adapterSpeak(){if(!this.privDisposed&&!this.privSynthesizing){this.privSynthesizing=!0;const e=await this.synthesisRequestQueue.dequeue();return this.privAdapter.Speak(e.text,e.isSSML,e.requestId,e.cb,e.err,e.dataStream)}}createSynthesizerConfig(t){return new e.SynthesizerConfig(t,this.privProperties)}implCommonSynthesizeSetup(){let t="undefined"!=typeof window?"Browser":"Node",r="unknown",n="unknown";"undefined"!=typeof navigator&&(t=t+"/"+navigator.platform,r=navigator.userAgent,n=navigator.appVersion);const o=this.createSynthesizerConfig(new e.SpeechServiceConfig(new e.Context(new e.OS(t,r,n)))),s=this.privProperties.getProperty(i.PropertyId.SpeechServiceConnection_Key,void 0),a=s&&""!==s?new e.CognitiveSubscriptionKeyAuthentication(s):new e.CognitiveTokenAuthentication((()=>{const e=this.privProperties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token,void 0);return Promise.resolve(e)}),(()=>{const e=this.privProperties.getProperty(i.PropertyId.SpeechServiceAuthorization_Token,void 0);return Promise.resolve(e)}));this.privAdapter=this.createSynthesisAdapter(a,this.privConnectionFactory,o),this.privRestAdapter=this.createRestSynthesisAdapter(a,o)}static XMLEncode(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}};return cc.SynthesisRequest=class{constructor(e,t,r,i,n,o){this.requestId=e,this.text=t,this.isSSML=r,this.cb=i,this.err=n,this.dataStream=o}},cc}var uc,hc={};function dc(){if(uc)return hc;uc=1,Object.defineProperty(hc,"__esModule",{value:!0}),hc.SpeechSynthesizer=void 0;const e=jv(),t=Gt(),r=Yr(),i=hi(),n=di(),o=Rr(),s=Mp(),a=pc();let c=class c extends s.Synthesizer{constructor(t,r){super(t),null!==r&&(this.audioConfig=void 0===r?"undefined"==typeof window?void 0:s.AudioConfig.fromDefaultSpeakerOutput():r),this.privConnectionFactory=new e.SpeechSynthesisConnectionFactory,this.implCommonSynthesizeSetup()}static FromConfig(e,t,r){const i=e;return t.properties.mergeTo(i.properties),new c(e,r)}speakTextAsync(e,t,r,i){this.speakImpl(e,!1,t,r,i)}speakSsmlAsync(e,t,r,i){this.speakImpl(e,!0,t,r,i)}async getVoicesAsync(e=""){return this.getVoices(e)}close(e,r){o.Contracts.throwIfDisposed(this.privDisposed),t.marshalPromiseToCallbacks(this.dispose(!0),e,r)}get internalData(){return this.privAdapter}createSynthesisAdapter(t,r,i){return new e.SpeechSynthesisAdapter(t,r,i,this,this.audioConfig)}createRestSynthesisAdapter(t,r){return new e.SynthesisRestAdapter(r,t)}implCommonSynthesizeSetup(){super.implCommonSynthesizeSetup(),this.privAdapter.audioOutputFormat=i.AudioOutputFormatImpl.fromSpeechSynthesisOutputFormat(s.SpeechSynthesisOutputFormat[this.properties.getProperty(s.PropertyId.SpeechServiceConnection_SynthOutputFormat,void 0)])}speakImpl(e,i,c,p,u){try{o.Contracts.throwIfDisposed(this.privDisposed);const h=t.createNoDashGuid();let d;d=u instanceof s.PushAudioOutputStreamCallback?new n.PushAudioOutputStreamImpl(u):u instanceof s.PullAudioOutputStream?u:void 0!==u?new r.AudioFileWriter(u):void 0,this.synthesisRequestQueue.enqueue(new a.SynthesisRequest(h,e,i,(e=>{if(this.privSynthesizing=!1,c)try{c(e)}catch(t){p&&p(t)}c=void 0,this.adapterSpeak().catch((()=>{}))}),(e=>{p&&p(e)}),d)),this.adapterSpeak().catch((()=>{}))}catch(h){if(p)if(h instanceof Error){const e=h;p(e.name+": "+e.message)}else p(h);this.dispose(!0).catch((()=>{}))}}async getVoices(e){const r=t.createNoDashGuid(),i=await this.privRestAdapter.getVoicesList(r);if(i.ok&&Array.isArray(i.json)){let t=i.json;return e&&e.length>0&&(t=t.filter((t=>!!t.Locale&&t.Locale.toLowerCase()===e.toLowerCase()))),new s.SynthesisVoicesResult(r,t,void 0)}return new s.SynthesisVoicesResult(r,void 0,`Error: ${i.status}: ${i.statusText}`)}};return hc.SpeechSynthesizer=c,hc}var vc,lc={};function gc(){if(vc)return lc;vc=1,Object.defineProperty(lc,"__esModule",{value:!0}),lc.SynthesisResult=void 0;return lc.SynthesisResult=class{constructor(e,t,r,i){this.privResultId=e,this.privReason=t,this.privErrorDetails=r,this.privProperties=i}get resultId(){return this.privResultId}get reason(){return this.privReason}get errorDetails(){return this.privErrorDetails}get properties(){return this.privProperties}},lc}var fc,mc={};function Sc(){if(fc)return mc;fc=1,Object.defineProperty(mc,"__esModule",{value:!0}),mc.SpeechSynthesisResult=void 0;const e=Mp();let t=class extends e.SynthesisResult{constructor(e,t,r,i,n,o){super(e,t,i,n),this.privAudioData=r,this.privAudioDuration=o}get audioData(){return this.privAudioData}get audioDuration(){return this.privAudioDuration}};return mc.SpeechSynthesisResult=t,mc}var yc,Cc={};function Pc(){if(yc)return Cc;yc=1,Object.defineProperty(Cc,"__esModule",{value:!0}),Cc.SpeechSynthesisEventArgs=void 0;return Cc.SpeechSynthesisEventArgs=class{constructor(e){this.privResult=e}get result(){return this.privResult}},Cc}var Rc,Ic={};function wc(){if(Rc)return Ic;Rc=1,Object.defineProperty(Ic,"__esModule",{value:!0}),Ic.SpeechSynthesisWordBoundaryEventArgs=void 0;return Ic.SpeechSynthesisWordBoundaryEventArgs=class{constructor(e,t,r,i,n,o){this.privAudioOffset=e,this.privDuration=t,this.privText=r,this.privWordLength=i,this.privTextOffset=n,this.privBoundaryType=o}get audioOffset(){return this.privAudioOffset}get duration(){return this.privDuration}get text(){return this.privText}get wordLength(){return this.privWordLength}get textOffset(){return this.privTextOffset}get boundaryType(){return this.privBoundaryType}},Ic}var Tc,Ac={};function Ec(){if(Tc)return Ac;Tc=1,Object.defineProperty(Ac,"__esModule",{value:!0}),Ac.SpeechSynthesisBookmarkEventArgs=void 0;return Ac.SpeechSynthesisBookmarkEventArgs=class{constructor(e,t){this.privAudioOffset=e,this.privText=t}get audioOffset(){return this.privAudioOffset}get text(){return this.privText}},Ac}var bc,Oc={};function Mc(){if(bc)return Oc;bc=1,Object.defineProperty(Oc,"__esModule",{value:!0}),Oc.SpeechSynthesisVisemeEventArgs=void 0;return Oc.SpeechSynthesisVisemeEventArgs=class{constructor(e,t,r){this.privAudioOffset=e,this.privVisemeId=t,this.privAnimation=r}get audioOffset(){return this.privAudioOffset}get visemeId(){return this.privVisemeId}get animation(){return this.privAnimation}},Oc}var Dc,kc={};function zc(){return Dc||(Dc=1,e=kc,Object.defineProperty(e,"__esModule",{value:!0}),e.SpeechSynthesisBoundaryType=void 0,(t=e.SpeechSynthesisBoundaryType||(e.SpeechSynthesisBoundaryType={})).Word="WordBoundary",t.Punctuation="PunctuationBoundary",t.Sentence="SentenceBoundary"),kc;var e,t}var Nc,_c={};function Lc(){if(Nc)return _c;Nc=1,Object.defineProperty(_c,"__esModule",{value:!0}),_c.SynthesisVoicesResult=void 0;const e=Mp();let t=class extends e.SynthesisResult{constructor(t,r,i){if(Array.isArray(r)){super(t,e.ResultReason.VoicesListRetrieved,void 0,new e.PropertyCollection),this.privVoices=[];for(const t of r)this.privVoices.push(new e.VoiceInfo(t))}else super(t,e.ResultReason.Canceled,i||"Error information unavailable",new e.PropertyCollection)}get voices(){return this.privVoices}};return _c.SynthesisVoicesResult=t,_c}var xc,Fc={};function Bc(){return xc||(xc=1,function(e){var t,r,i,n;Object.defineProperty(e,"__esModule",{value:!0}),e.VoiceInfo=e.SynthesisVoiceType=e.SynthesisVoiceGender=void 0,(r=t=e.SynthesisVoiceGender||(e.SynthesisVoiceGender={}))[r.Unknown=0]="Unknown",r[r.Female=1]="Female",r[r.Male=2]="Male",r[r.Neutral=3]="Neutral",(n=i=e.SynthesisVoiceType||(e.SynthesisVoiceType={}))[n.Unknown=0]="Unknown",n[n.OnlineNeural=1]="OnlineNeural",n[n.OnlineStandard=2]="OnlineStandard",n[n.OfflineNeural=3]="OfflineNeural",n[n.OfflineStandard=4]="OfflineStandard",n[n.OnlineNeuralHD=5]="OnlineNeuralHD";const o={[t[t.Neutral]]:t.Neutral,[t[t.Male]]:t.Male,[t[t.Female]]:t.Female},s={Neural:i.OnlineNeural,NeuralHD:i.OnlineNeuralHD};e.VoiceInfo=class{constructor(e){if(this.privStyleList=[],e){if(this.privName=e.Name,this.privLocale=e.Locale,this.privShortName=e.ShortName,this.privLocaleName=e.LocaleName,this.privDisplayName=e.DisplayName,this.privLocalName=e.LocalName,this.privVoiceType=s[e.VoiceType]||i.Unknown,this.privGender=o[e.Gender]||t.Unknown,e.StyleList&&Array.isArray(e.StyleList))for(const t of e.StyleList)this.privStyleList.push(t);this.privSampleRateHertz=e.SampleRateHertz,this.privStatus=e.Status,e.ExtendedPropertyMap&&(this.privExtendedPropertyMap=e.ExtendedPropertyMap),this.privWordsPerMinute=e.WordsPerMinute,Array.isArray(e.SecondaryLocaleList)&&(this.privSecondaryLocaleList=[...e.SecondaryLocaleList]),Array.isArray(e.RolePlayList)&&(this.privRolePlayList=[...e.RolePlayList]),e.VoiceTag&&(this.privVoiceTag=e.VoiceTag)}}get name(){return this.privName}get locale(){return this.privLocale}get shortName(){return this.privShortName}get displayName(){return this.privDisplayName}get localName(){return this.privLocalName}get localeName(){return this.privLocaleName}get gender(){return this.privGender}get voiceType(){return this.privVoiceType}get styleList(){return this.privStyleList}get sampleRateHertz(){return this.privSampleRateHertz}get status(){return this.privStatus}get extendedPropertyMap(){return this.privExtendedPropertyMap}get wordsPerMinute(){return this.privWordsPerMinute}get secondaryLocaleList(){return this.privSecondaryLocaleList}get rolePlayList(){return this.privRolePlayList}get voiceTag(){return this.privVoiceTag}}}(Fc)),Fc}var jc,Uc={};function qc(){if(jc)return Uc;jc=1,Object.defineProperty(Uc,"__esModule",{value:!0}),Uc.SpeakerAudioDestination=void 0;const e=Gt(),t=di(),r=Dr(),i={[r.AudioFormatTag.PCM]:"audio/wav",[r.AudioFormatTag.MuLaw]:"audio/x-wav",[r.AudioFormatTag.MP3]:"audio/mpeg",[r.AudioFormatTag.OGG_OPUS]:"audio/ogg",[r.AudioFormatTag.WEBM_OPUS]:"audio/webm; codecs=opus",[r.AudioFormatTag.ALaw]:"audio/x-wav",[r.AudioFormatTag.FLAC]:"audio/flac",[r.AudioFormatTag.AMR_WB]:"audio/amr-wb",[r.AudioFormatTag.G722]:"audio/G722"};return Uc.SpeakerAudioDestination=class{constructor(t){this.privPlaybackStarted=!1,this.privAppendingToBuffer=!1,this.privMediaSourceOpened=!1,this.privBytesReceived=0,this.privId=t||e.createNoDashGuid(),this.privIsPaused=!1,this.privIsClosed=!1}id(){return this.privId}write(e,t,r){void 0!==this.privAudioBuffer?(this.privAudioBuffer.push(e),this.updateSourceBuffer().then((()=>{t&&t()}),(e=>{r&&r(e)}))):void 0!==this.privAudioOutputStream&&(this.privAudioOutputStream.write(e),this.privBytesReceived+=e.byteLength)}close(e,t){if(this.privIsClosed=!0,void 0!==this.privSourceBuffer)this.handleSourceBufferUpdateEnd().then((()=>{e&&e()}),(e=>{t&&t(e)}));else if(void 0!==this.privAudioOutputStream&&"undefined"!=typeof window)if(this.privFormat.formatTag!==r.AudioFormatTag.PCM&&this.privFormat.formatTag!==r.AudioFormatTag.MuLaw&&this.privFormat.formatTag!==r.AudioFormatTag.ALaw||!1!==this.privFormat.hasHeader){let r=new ArrayBuffer(this.privBytesReceived);this.privAudioOutputStream.read(r).then((()=>{r=this.privFormat.addHeader(r);const n=new Blob([r],{type:i[this.privFormat.formatTag]});this.privAudio.src=window.URL.createObjectURL(n),this.notifyPlayback().then((()=>{e&&e()}),(e=>{t&&t(e)}))}),(e=>{t&&t(e)}))}else console.warn("Play back is not supported for raw PCM, mulaw or alaw format without header."),this.onAudioEnd&&this.onAudioEnd(this);else this.onAudioEnd&&this.onAudioEnd(this)}set format(n){if("undefined"!=typeof AudioContext||"undefined"!=typeof window&&void 0!==window.webkitAudioContext){this.privFormat=n;const o=i[this.privFormat.formatTag];void 0===o?console.warn(`Unknown mimeType for format ${r.AudioFormatTag[this.privFormat.formatTag]}; playback is not supported.`):"undefined"!=typeof MediaSource&&MediaSource.isTypeSupported(o)?(this.privAudio=new Audio,this.privAudioBuffer=[],this.privMediaSource=new MediaSource,this.privAudio.src=URL.createObjectURL(this.privMediaSource),this.privAudio.load(),this.privMediaSource.onsourceopen=()=>{this.privMediaSourceOpened=!0,this.privMediaSource.duration=1800,this.privSourceBuffer=this.privMediaSource.addSourceBuffer(o),this.privSourceBuffer.onupdate=()=>{this.updateSourceBuffer().catch((t=>{e.Events.instance.onEvent(new e.BackgroundEvent(t))}))},this.privSourceBuffer.onupdateend=()=>{this.handleSourceBufferUpdateEnd().catch((t=>{e.Events.instance.onEvent(new e.BackgroundEvent(t))}))},this.privSourceBuffer.onupdatestart=()=>{this.privAppendingToBuffer=!1}},this.updateSourceBuffer().catch((t=>{e.Events.instance.onEvent(new e.BackgroundEvent(t))}))):(console.warn(`Format ${r.AudioFormatTag[this.privFormat.formatTag]} could not be played by MSE, streaming playback is not enabled.`),this.privAudioOutputStream=new t.PullAudioOutputStreamImpl,this.privAudioOutputStream.format=this.privFormat,this.privAudio=new Audio)}}get volume(){var e;return(null==(e=this.privAudio)?void 0:e.volume)??-1}set volume(e){this.privAudio&&(this.privAudio.volume=e)}mute(){this.privAudio&&(this.privAudio.muted=!0)}unmute(){this.privAudio&&(this.privAudio.muted=!1)}get isClosed(){return this.privIsClosed}get currentTime(){return void 0!==this.privAudio?this.privAudio.currentTime:-1}pause(){this.privIsPaused||void 0===this.privAudio||(this.privAudio.pause(),this.privIsPaused=!0)}resume(e,t){this.privIsPaused&&void 0!==this.privAudio&&(this.privAudio.play().then((()=>{e&&e()}),(e=>{t&&t(e)})),this.privIsPaused=!1)}get internalAudio(){return this.privAudio}async updateSourceBuffer(){if(void 0!==this.privAudioBuffer&&this.privAudioBuffer.length>0&&this.sourceBufferAvailable()){this.privAppendingToBuffer=!0;const t=this.privAudioBuffer.shift();try{this.privSourceBuffer.appendBuffer(t)}catch(e){return void this.privAudioBuffer.unshift(t)}await this.notifyPlayback()}else this.canEndStream()&&await this.handleSourceBufferUpdateEnd()}async handleSourceBufferUpdateEnd(){this.canEndStream()&&this.sourceBufferAvailable()&&(this.privMediaSource.endOfStream(),await this.notifyPlayback())}async notifyPlayback(){this.privPlaybackStarted||void 0===this.privAudio||(this.privPlaybackStarted=!0,this.onAudioStart&&this.onAudioStart(this),this.privAudio.onended=()=>{this.onAudioEnd&&this.onAudioEnd(this)},this.privIsPaused||await this.privAudio.play())}canEndStream(){return this.isClosed&&void 0!==this.privSourceBuffer&&0===this.privAudioBuffer.length&&this.privMediaSourceOpened&&!this.privAppendingToBuffer&&"open"===this.privMediaSource.readyState}sourceBufferAvailable(){return void 0!==this.privSourceBuffer&&!this.privSourceBuffer.updating}},Uc}var Wc,Hc={};function Vc(){if(Wc)return Hc;Wc=1,Object.defineProperty(Hc,"__esModule",{value:!0}),Hc.ConversationTranscriptionCanceledEventArgs=void 0;const e=nn();let t=class extends e.CancellationEventArgsBase{};return Hc.ConversationTranscriptionCanceledEventArgs=t,Hc}var Kc,Jc={};function Gc(){if(Kc)return Jc;Kc=1,Object.defineProperty(Jc,"__esModule",{value:!0}),Jc.MeetingTranscriptionCanceledEventArgs=void 0;const e=nn();let t=class extends e.CancellationEventArgsBase{};return Jc.MeetingTranscriptionCanceledEventArgs=t,Jc}var $c,Qc={};function Xc(){return $c||($c=1,e=Qc,Object.defineProperty(e,"__esModule",{value:!0}),e.PronunciationAssessmentGradingSystem=void 0,(t=e.PronunciationAssessmentGradingSystem||(e.PronunciationAssessmentGradingSystem={}))[t.FivePoint=1]="FivePoint",t[t.HundredMark=2]="HundredMark"),Qc;var e,t}var Zc,Yc={};function ep(){return Zc||(Zc=1,e=Yc,Object.defineProperty(e,"__esModule",{value:!0}),e.PronunciationAssessmentGranularity=void 0,(t=e.PronunciationAssessmentGranularity||(e.PronunciationAssessmentGranularity={}))[t.Phoneme=1]="Phoneme",t[t.Word=2]="Word",t[t.FullText=3]="FullText"),Yc;var e,t}var tp,rp={};function ip(){if(tp)return rp;tp=1,Object.defineProperty(rp,"__esModule",{value:!0}),rp.PronunciationAssessmentConfig=void 0;const e=Rr(),t=Mp();return rp.PronunciationAssessmentConfig=class r{constructor(r,i=t.PronunciationAssessmentGradingSystem.FivePoint,n=t.PronunciationAssessmentGranularity.Phoneme,o=!1){e.Contracts.throwIfNullOrUndefined(r,"referenceText"),this.privProperties=new t.PropertyCollection,this.privProperties.setProperty(t.PropertyId.PronunciationAssessment_ReferenceText,r),this.privProperties.setProperty(t.PropertyId.PronunciationAssessment_GradingSystem,t.PronunciationAssessmentGradingSystem[i]),this.privProperties.setProperty(t.PropertyId.PronunciationAssessment_Granularity,t.PronunciationAssessmentGranularity[n]),this.privProperties.setProperty(t.PropertyId.PronunciationAssessment_EnableMiscue,String(o))}static fromJSON(i){e.Contracts.throwIfNullOrUndefined(i,"json");const n=new r("");return n.privProperties=new t.PropertyCollection,n.properties.setProperty(t.PropertyId.PronunciationAssessment_Json,i),n}toJSON(){return this.updateJson(),this.privProperties.getProperty(t.PropertyId.PronunciationAssessment_Params)}applyTo(e){this.updateJson();const r=e.internalData;r.expectContentAssessmentResponse=!!this.privContentAssessmentTopic,r.speechContext.setPronunciationAssessmentParams(this.properties.getProperty(t.PropertyId.PronunciationAssessment_Params),this.privContentAssessmentTopic,r.isSpeakerDiarizationEnabled)}get referenceText(){return this.properties.getProperty(t.PropertyId.PronunciationAssessment_ReferenceText)}set referenceText(r){e.Contracts.throwIfNullOrWhitespace(r,"referenceText"),this.properties.setProperty(t.PropertyId.PronunciationAssessment_ReferenceText,r)}set phonemeAlphabet(t){e.Contracts.throwIfNullOrWhitespace(t,"phonemeAlphabet"),this.privPhonemeAlphabet=t}set enableMiscue(e){const r=e?"true":"false";this.properties.setProperty(t.PropertyId.PronunciationAssessment_EnableMiscue,r)}get enableMiscue(){return"true"===this.properties.getProperty(t.PropertyId.PronunciationAssessment_EnableMiscue,"false").toLowerCase()}set nbestPhonemeCount(e){this.privNBestPhonemeCount=e}set enableProsodyAssessment(e){this.privEnableProsodyAssessment=e}enableContentAssessmentWithTopic(e){this.privContentAssessmentTopic=e}get properties(){return this.privProperties}updateJson(){const e=this.privProperties.getProperty(t.PropertyId.PronunciationAssessment_Json,"{}"),r=JSON.parse(e),i=this.privProperties.getProperty(t.PropertyId.PronunciationAssessment_ReferenceText);i&&(r.referenceText=i);const n=this.privProperties.getProperty(t.PropertyId.PronunciationAssessment_GradingSystem);n&&(r.gradingSystem=n);const o=this.privProperties.getProperty(t.PropertyId.PronunciationAssessment_Granularity);o&&(r.granularity=o),this.privPhonemeAlphabet&&(r.phonemeAlphabet=this.privPhonemeAlphabet),this.privNBestPhonemeCount&&(r.nbestPhonemeCount=this.privNBestPhonemeCount),r.enableProsodyAssessment=this.privEnableProsodyAssessment,r.dimension="Comprehensive";this.privProperties.getProperty(t.PropertyId.PronunciationAssessment_EnableMiscue)&&(r.enableMiscue=this.enableMiscue),this.privProperties.setProperty(t.PropertyId.PronunciationAssessment_Params,JSON.stringify(r))}},rp}var np,op={};function sp(){if(np)return op;np=1,Object.defineProperty(op,"__esModule",{value:!0}),op.PronunciationAssessmentResult=op.ContentAssessmentResult=void 0;const e=Rr(),t=Mp();class r{constructor(e){this.privPronJson=e}get grammarScore(){return this.privPronJson.ContentAssessment.GrammarScore}get vocabularyScore(){return this.privPronJson.ContentAssessment.VocabularyScore}get topicScore(){return this.privPronJson.ContentAssessment.TopicScore}}op.ContentAssessmentResult=r;return op.PronunciationAssessmentResult=class i{constructor(t){const r=JSON.parse(t);e.Contracts.throwIfNullOrUndefined(r.NBest[0],"NBest"),this.privPronJson=r.NBest[0]}static fromResult(r){e.Contracts.throwIfNullOrUndefined(r,"result");const n=r.properties.getProperty(t.PropertyId.SpeechServiceResponse_JsonResult);return e.Contracts.throwIfNullOrUndefined(n,"json"),new i(n)}get detailResult(){return this.privPronJson}get accuracyScore(){var e;return null==(e=this.detailResult.PronunciationAssessment)?void 0:e.AccuracyScore}get pronunciationScore(){var e;return null==(e=this.detailResult.PronunciationAssessment)?void 0:e.PronScore}get completenessScore(){var e;return null==(e=this.detailResult.PronunciationAssessment)?void 0:e.CompletenessScore}get fluencyScore(){var e;return null==(e=this.detailResult.PronunciationAssessment)?void 0:e.FluencyScore}get prosodyScore(){var e;return null==(e=this.detailResult.PronunciationAssessment)?void 0:e.ProsodyScore}get contentAssessmentResult(){if(void 0!==this.detailResult.ContentAssessment)return new r(this.detailResult)}},op}var ap,cp={};function pp(){if(ap)return cp;ap=1,Object.defineProperty(cp,"__esModule",{value:!0}),cp.AvatarConfig=void 0;const e=Rr(),t=Mp();return cp.AvatarConfig=class{constructor(r,i,n){this.privCustomized=!1,this.privUseBuiltInVoice=!1,e.Contracts.throwIfNullOrWhitespace(r,"character"),this.character=r,this.style=i,void 0===n&&(n=new t.AvatarVideoFormat),this.videoFormat=n}get customized(){return this.privCustomized}set customized(e){this.privCustomized=e}get useBuiltInVoice(){return this.privUseBuiltInVoice}set useBuiltInVoice(e){this.privUseBuiltInVoice=e}get backgroundColor(){return this.privBackgroundColor}set backgroundColor(e){this.privBackgroundColor=e}get backgroundImage(){return this.privBackgroundImage}set backgroundImage(e){this.privBackgroundImage=e}get remoteIceServers(){return this.privRemoteIceServers}set remoteIceServers(e){this.privRemoteIceServers=e}},cp}var up,hp={};function dp(){return up||(up=1,function(e){var t;Object.defineProperty(e,"__esModule",{value:!0}),e.AvatarEventArgs=e.AvatarEventTypes=void 0,(t=e.AvatarEventTypes||(e.AvatarEventTypes={})).SwitchedToSpeaking="SwitchedToSpeaking",t.SwitchedToIdle="SwitchedToIdle",t.SessionClosed="SessionClosed";e.AvatarEventArgs=class{constructor(e,t){this.privOffset=e,this.privDescription=t}get type(){return this.privType}get offset(){return this.privOffset}get description(){return this.privDescription}}}(hp)),hp}var vp,lp,gp={},fp={};function mp(){if(vp)return fp;vp=1,Object.defineProperty(fp,"__esModule",{value:!0}),fp.SpeechSynthesisConnectionFactory=void 0;const e=Vp(),t=Mp(),r=Zo(),i=jv(),n=Xt(),o=Xo();return fp.SpeechSynthesisConnectionFactory=class{constructor(){this.synthesisUri="/cognitiveservices/websocket/v1"}create(s,a,c){let p=s.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Endpoint,void 0);const u=s.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Region,void 0),h=r.ConnectionFactoryBase.getHostSuffix(u),d=s.parameters.getProperty(t.PropertyId.SpeechServiceConnection_EndpointId,void 0),v=void 0===d?"tts":"voice",l=s.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Host,"wss://"+u+"."+v+".speech"+h),g={},f={};void 0!==a.token&&""!==a.token&&(f[a.headerName]=a.token),f[n.HeaderNames.ConnectionId]=c,void 0!==d&&""!==d&&(p&&-1!==p.search(o.QueryParameterNames.CustomVoiceDeploymentId)||(g[o.QueryParameterNames.CustomVoiceDeploymentId]=d)),s.avatarEnabled&&(p&&-1!==p.search(o.QueryParameterNames.EnableAvatar)||(g[o.QueryParameterNames.EnableAvatar]="true")),p||(p=l+this.synthesisUri),s.parameters.setProperty(t.PropertyId.SpeechServiceConnection_Url,p);const m="true"===s.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(p,g,f,new i.WebsocketMessageFormatter,e.ProxyInfo.fromParameters(s.parameters),m,c)}},fp}function Sp(){if(lp)return gp;lp=1,Object.defineProperty(gp,"__esModule",{value:!0}),gp.AvatarSynthesizer=void 0;const e=mp(),t=jv(),r=Gt(),i=hi(),n=Mp(),o=Rr(),s=pc();let a=class extends n.Synthesizer{constructor(t,r){super(t),o.Contracts.throwIfNullOrUndefined(r,"avatarConfig"),this.privConnectionFactory=new e.SpeechSynthesisConnectionFactory,this.privAvatarConfig=r,this.implCommonSynthesizeSetup()}implCommonSynthesizeSetup(){super.implCommonSynthesizeSetup(),this.privAdapter.audioOutputFormat=i.AudioOutputFormatImpl.fromSpeechSynthesisOutputFormat(n.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm)}async startAvatarAsync(e){o.Contracts.throwIfNullOrUndefined(e,"peerConnection"),this.privIceServers=e.getConfiguration().iceServers,o.Contracts.throwIfNullOrUndefined(this.privIceServers,"Ice servers must be set.");const t=new r.Deferred;e.onicegatheringstatechange=()=>{r.Events.instance.onEvent(new r.PlatformEvent("peer connection: ice gathering state: "+e.iceGatheringState,r.EventType.Debug)),"complete"===e.iceGatheringState&&(r.Events.instance.onEvent(new r.PlatformEvent("peer connection: ice gathering complete.",r.EventType.Info)),t.resolve())},e.onicecandidate=e=>{e.candidate?r.Events.instance.onEvent(new r.PlatformEvent("peer connection: ice candidate: "+e.candidate.candidate,r.EventType.Debug)):(r.Events.instance.onEvent(new r.PlatformEvent("peer connection: ice candidate: complete",r.EventType.Debug)),t.resolve())},setTimeout((()=>{"complete"!==e.iceGatheringState&&(r.Events.instance.onEvent(new r.PlatformEvent("peer connection: ice gathering timeout.",r.EventType.Warning)),t.resolve())}),2e3);const i=await e.createOffer();await e.setLocalDescription(i),await t.promise,r.Events.instance.onEvent(new r.PlatformEvent("peer connection: got local SDP.",r.EventType.Info)),this.privProperties.setProperty(n.PropertyId.TalkingAvatarService_WebRTC_SDP,JSON.stringify(e.localDescription));const s=await this.speak("",!1);if(s.reason!==n.ResultReason.SynthesizingAudioCompleted)return new n.SynthesisResult(s.resultId,s.reason,s.errorDetails,s.properties);const a=atob(s.properties.getProperty(n.PropertyId.TalkingAvatarService_WebRTC_SDP)),c=new RTCSessionDescription(JSON.parse(a));return await e.setRemoteDescription(c),new n.SynthesisResult(s.resultId,s.reason,void 0,s.properties)}async speakTextAsync(e){const t=await this.speak(e,!1);return new n.SynthesisResult(t.resultId,t.reason,t.errorDetails,t.properties)}async speakSsmlAsync(e){const t=await this.speak(e,!0);return new n.SynthesisResult(t.resultId,t.reason,t.errorDetails,t.properties)}async stopSpeakingAsync(){for(;this.synthesisRequestQueue.length()>0;){(await this.synthesisRequestQueue.dequeue()).err("Synthesis is canceled by user.")}return this.privAdapter.stopSpeaking()}async stopAvatarAsync(){return o.Contracts.throwIfDisposed(this.privDisposed),this.dispose(!0)}async close(){if(!this.privDisposed)return this.dispose(!0)}get iceServers(){return this.privIceServers}createSynthesisAdapter(e,r,i){return new t.AvatarSynthesisAdapter(e,r,i,this,this.privAvatarConfig)}createRestSynthesisAdapter(e,t){}createSynthesizerConfig(e){const t=super.createSynthesizerConfig(e);return t.avatarEnabled=!0,t}async speak(e,t){const i=r.createNoDashGuid(),n=new r.Deferred;return this.synthesisRequestQueue.enqueue(new s.SynthesisRequest(i,e,t,(e=>{n.resolve(e),this.privSynthesizing=!1,this.adapterSpeak()}),(e=>{n.reject(e),this.privSynthesizing=!1}))),this.adapterSpeak(),n.promise}};return gp.AvatarSynthesizer=a,gp}var yp,Cp={};function Pp(){if(yp)return Cp;yp=1,Object.defineProperty(Cp,"__esModule",{value:!0}),Cp.AvatarVideoFormat=Cp.Coordinate=void 0;Cp.Coordinate=class{constructor(e,t){this.x=e,this.y=t}};return Cp.AvatarVideoFormat=class{constructor(e="H264",t=2e6,r=1920,i=1080){this.codec=e,this.bitrate=t,this.width=r,this.height=i}setCropRange(e,t){this.cropRange={bottomRight:t,topLeft:e}}},Cp}var Rp,Ip={};function wp(){if(Rp)return Ip;Rp=1,Object.defineProperty(Ip,"__esModule",{value:!0}),Ip.AvatarWebRTCConnectionResult=void 0;const e=Mp();let t=class extends e.SynthesisResult{constructor(e,t,r,i,n){super(t,r,i,n),this.privSDPAnswer=e}get SDPAnswer(){return this.privSDPAnswer}};return Ip.AvatarWebRTCConnectionResult=t,Ip}var Tp,Ap,Ep,bp={};function Op(){if(Tp)return bp;Tp=1,Object.defineProperty(bp,"__esModule",{value:!0}),bp.Diagnostics=void 0;const e=Vp(),t=Gt();let r=class{static SetLoggingLevel(r){this.privListener=new e.ConsoleLoggingListener(r),t.Events.instance.attachConsoleListener(this.privListener)}static StartConsoleOutput(){this.privListener&&(this.privListener.enableConsoleOutput=!0)}static StopConsoleOutput(){this.privListener&&(this.privListener.enableConsoleOutput=!1)}static SetLogOutputPath(e){if("undefined"!=typeof window)throw new Error("File system logging not available in browser.");this.privListener&&(this.privListener.logPath=e)}static set onLogOutput(e){this.privListener&&(this.privListener.logCallback=e)}};return bp.Diagnostics=r,r.privListener=void 0,bp}function Mp(){return Ap||(Ap=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=vi();Object.defineProperty(e,"AudioConfig",{enumerable:!0,get:function(){return t.AudioConfig}});var r=Dr();Object.defineProperty(e,"AudioStreamFormat",{enumerable:!0,get:function(){return r.AudioStreamFormat}}),Object.defineProperty(e,"AudioFormatTag",{enumerable:!0,get:function(){return r.AudioFormatTag}});var i=ri();Object.defineProperty(e,"AudioInputStream",{enumerable:!0,get:function(){return i.AudioInputStream}}),Object.defineProperty(e,"PullAudioInputStream",{enumerable:!0,get:function(){return i.PullAudioInputStream}}),Object.defineProperty(e,"PushAudioInputStream",{enumerable:!0,get:function(){return i.PushAudioInputStream}});var n=di();Object.defineProperty(e,"AudioOutputStream",{enumerable:!0,get:function(){return n.AudioOutputStream}}),Object.defineProperty(e,"PullAudioOutputStream",{enumerable:!0,get:function(){return n.PullAudioOutputStream}}),Object.defineProperty(e,"PushAudioOutputStream",{enumerable:!0,get:function(){return n.PushAudioOutputStream}});var o=fi();Object.defineProperty(e,"CancellationReason",{enumerable:!0,get:function(){return o.CancellationReason}});var s=yi();Object.defineProperty(e,"PullAudioInputStreamCallback",{enumerable:!0,get:function(){return s.PullAudioInputStreamCallback}});var a=Ri();Object.defineProperty(e,"PushAudioOutputStreamCallback",{enumerable:!0,get:function(){return a.PushAudioOutputStreamCallback}});var c=Ti();Object.defineProperty(e,"KeywordRecognitionModel",{enumerable:!0,get:function(){return c.KeywordRecognitionModel}});var p=bi();Object.defineProperty(e,"SessionEventArgs",{enumerable:!0,get:function(){return p.SessionEventArgs}});var u=Di();Object.defineProperty(e,"RecognitionEventArgs",{enumerable:!0,get:function(){return u.RecognitionEventArgs}});var h=Ni();Object.defineProperty(e,"OutputFormat",{enumerable:!0,get:function(){return h.OutputFormat}});var d=xi();Object.defineProperty(e,"IntentRecognitionEventArgs",{enumerable:!0,get:function(){return d.IntentRecognitionEventArgs}});var v=ji();Object.defineProperty(e,"RecognitionResult",{enumerable:!0,get:function(){return v.RecognitionResult}});var l=Wi();Object.defineProperty(e,"SpeechRecognitionResult",{enumerable:!0,get:function(){return l.SpeechRecognitionResult}});var g=Ki();Object.defineProperty(e,"IntentRecognitionResult",{enumerable:!0,get:function(){return g.IntentRecognitionResult}});var f=$i();Object.defineProperty(e,"LanguageUnderstandingModel",{enumerable:!0,get:function(){return f.LanguageUnderstandingModel}});var m=Zi();Object.defineProperty(e,"SpeechRecognitionEventArgs",{enumerable:!0,get:function(){return m.SpeechRecognitionEventArgs}}),Object.defineProperty(e,"ConversationTranscriptionEventArgs",{enumerable:!0,get:function(){return m.ConversationTranscriptionEventArgs}}),Object.defineProperty(e,"MeetingTranscriptionEventArgs",{enumerable:!0,get:function(){return m.MeetingTranscriptionEventArgs}});var S=on();Object.defineProperty(e,"SpeechRecognitionCanceledEventArgs",{enumerable:!0,get:function(){return S.SpeechRecognitionCanceledEventArgs}});var y=cn();Object.defineProperty(e,"TranslationRecognitionEventArgs",{enumerable:!0,get:function(){return y.TranslationRecognitionEventArgs}});var C=hn();Object.defineProperty(e,"TranslationSynthesisEventArgs",{enumerable:!0,get:function(){return C.TranslationSynthesisEventArgs}});var P=ln();Object.defineProperty(e,"TranslationRecognitionResult",{enumerable:!0,get:function(){return P.TranslationRecognitionResult}});var R=mn();Object.defineProperty(e,"TranslationSynthesisResult",{enumerable:!0,get:function(){return R.TranslationSynthesisResult}});var I=Cn();Object.defineProperty(e,"ResultReason",{enumerable:!0,get:function(){return I.ResultReason}});var w=In();Object.defineProperty(e,"SpeechConfig",{enumerable:!0,get:function(){return w.SpeechConfig}}),Object.defineProperty(e,"SpeechConfigImpl",{enumerable:!0,get:function(){return w.SpeechConfigImpl}});var T=An();Object.defineProperty(e,"SpeechTranslationConfig",{enumerable:!0,get:function(){return T.SpeechTranslationConfig}}),Object.defineProperty(e,"SpeechTranslationConfigImpl",{enumerable:!0,get:function(){return T.SpeechTranslationConfigImpl}});var A=On();Object.defineProperty(e,"PropertyCollection",{enumerable:!0,get:function(){return A.PropertyCollection}});var E=kn();Object.defineProperty(e,"PropertyId",{enumerable:!0,get:function(){return E.PropertyId}});var b=_n();Object.defineProperty(e,"Recognizer",{enumerable:!0,get:function(){return b.Recognizer}});var O=Fn();Object.defineProperty(e,"SpeechRecognizer",{enumerable:!0,get:function(){return O.SpeechRecognizer}});var M=Un();Object.defineProperty(e,"IntentRecognizer",{enumerable:!0,get:function(){return M.IntentRecognizer}});var D=Hn();Object.defineProperty(e,"VoiceProfileType",{enumerable:!0,get:function(){return D.VoiceProfileType}});var k=Yn();Object.defineProperty(e,"TranslationRecognizer",{enumerable:!0,get:function(){return k.TranslationRecognizer}});var z=ro();Object.defineProperty(e,"Translations",{enumerable:!0,get:function(){return z.Translations}});var N=oo();Object.defineProperty(e,"NoMatchReason",{enumerable:!0,get:function(){return N.NoMatchReason}});var _=co();Object.defineProperty(e,"NoMatchDetails",{enumerable:!0,get:function(){return _.NoMatchDetails}});var L=ho();Object.defineProperty(e,"TranslationRecognitionCanceledEventArgs",{enumerable:!0,get:function(){return L.TranslationRecognitionCanceledEventArgs}});var x=go();Object.defineProperty(e,"IntentRecognitionCanceledEventArgs",{enumerable:!0,get:function(){return x.IntentRecognitionCanceledEventArgs}});var F=So();Object.defineProperty(e,"CancellationDetailsBase",{enumerable:!0,get:function(){return F.CancellationDetailsBase}});var B=Po();Object.defineProperty(e,"CancellationDetails",{enumerable:!0,get:function(){return B.CancellationDetails}});var j=wo();Object.defineProperty(e,"CancellationErrorCode",{enumerable:!0,get:function(){return j.CancellationErrorCode}});var U=Eo();Object.defineProperty(e,"ConnectionEventArgs",{enumerable:!0,get:function(){return U.ConnectionEventArgs}});var q=Mo();Object.defineProperty(e,"ServiceEventArgs",{enumerable:!0,get:function(){return q.ServiceEventArgs}});var W=Zn();Object.defineProperty(e,"Connection",{enumerable:!0,get:function(){return W.Connection}});var H=zo();Object.defineProperty(e,"PhraseListGrammar",{enumerable:!0,get:function(){return H.PhraseListGrammar}});var V=Lo();Object.defineProperty(e,"DialogServiceConfig",{enumerable:!0,get:function(){return V.DialogServiceConfig}});var K=Bo();Object.defineProperty(e,"BotFrameworkConfig",{enumerable:!0,get:function(){return K.BotFrameworkConfig}});var J=qo();Object.defineProperty(e,"CustomCommandsConfig",{enumerable:!0,get:function(){return J.CustomCommandsConfig}});var G=es();Object.defineProperty(e,"DialogServiceConnector",{enumerable:!0,get:function(){return G.DialogServiceConnector}});var $=is();Object.defineProperty(e,"ActivityReceivedEventArgs",{enumerable:!0,get:function(){return $.ActivityReceivedEventArgs}});var Q=ps();Object.defineProperty(e,"TurnStatusReceivedEventArgs",{enumerable:!0,get:function(){return Q.TurnStatusReceivedEventArgs}});var X=ds();Object.defineProperty(e,"ServicePropertyChannel",{enumerable:!0,get:function(){return X.ServicePropertyChannel}});var Z=gs();Object.defineProperty(e,"ProfanityOption",{enumerable:!0,get:function(){return Z.ProfanityOption}});var Y=Ss();Object.defineProperty(e,"BaseAudioPlayer",{enumerable:!0,get:function(){return Y.BaseAudioPlayer}});var ee=Ps();Object.defineProperty(e,"ConnectionMessageEventArgs",{enumerable:!0,get:function(){return ee.ConnectionMessageEventArgs}});var te=Xn();Object.defineProperty(e,"ConnectionMessage",{enumerable:!0,get:function(){return te.ConnectionMessage}});var re=ws();Object.defineProperty(e,"VoiceProfile",{enumerable:!0,get:function(){return re.VoiceProfile}});var ie=Es();Object.defineProperty(e,"VoiceProfileEnrollmentResult",{enumerable:!0,get:function(){return ie.VoiceProfileEnrollmentResult}}),Object.defineProperty(e,"VoiceProfileEnrollmentCancellationDetails",{enumerable:!0,get:function(){return ie.VoiceProfileEnrollmentCancellationDetails}});var ne=Ms();Object.defineProperty(e,"VoiceProfileResult",{enumerable:!0,get:function(){return ne.VoiceProfileResult}}),Object.defineProperty(e,"VoiceProfileCancellationDetails",{enumerable:!0,get:function(){return ne.VoiceProfileCancellationDetails}});var oe=zs();Object.defineProperty(e,"VoiceProfilePhraseResult",{enumerable:!0,get:function(){return oe.VoiceProfilePhraseResult}});var se=Ls();Object.defineProperty(e,"VoiceProfileClient",{enumerable:!0,get:function(){return se.VoiceProfileClient}});var ae=Bs();Object.defineProperty(e,"SpeakerRecognizer",{enumerable:!0,get:function(){return ae.SpeakerRecognizer}});var ce=qs();Object.defineProperty(e,"SpeakerIdentificationModel",{enumerable:!0,get:function(){return ce.SpeakerIdentificationModel}});var pe=Vs();Object.defineProperty(e,"SpeakerVerificationModel",{enumerable:!0,get:function(){return pe.SpeakerVerificationModel}});var ue=Xs();Object.defineProperty(e,"AutoDetectSourceLanguageConfig",{enumerable:!0,get:function(){return ue.AutoDetectSourceLanguageConfig}});var he=ea();Object.defineProperty(e,"AutoDetectSourceLanguageResult",{enumerable:!0,get:function(){return he.AutoDetectSourceLanguageResult}});var de=ia();Object.defineProperty(e,"SourceLanguageConfig",{enumerable:!0,get:function(){return de.SourceLanguageConfig}});var ve=sa();Object.defineProperty(e,"SpeakerRecognitionResult",{enumerable:!0,get:function(){return ve.SpeakerRecognitionResult}}),Object.defineProperty(e,"SpeakerRecognitionResultType",{enumerable:!0,get:function(){return ve.SpeakerRecognitionResultType}}),Object.defineProperty(e,"SpeakerRecognitionCancellationDetails",{enumerable:!0,get:function(){return ve.SpeakerRecognitionCancellationDetails}});var le=sc();Object.defineProperty(e,"Conversation",{enumerable:!0,get:function(){return le.Conversation}}),Object.defineProperty(e,"ConversationExpirationEventArgs",{enumerable:!0,get:function(){return le.ConversationExpirationEventArgs}}),Object.defineProperty(e,"ConversationParticipantsChangedEventArgs",{enumerable:!0,get:function(){return le.ConversationParticipantsChangedEventArgs}}),Object.defineProperty(e,"ConversationTranslationCanceledEventArgs",{enumerable:!0,get:function(){return le.ConversationTranslationCanceledEventArgs}}),Object.defineProperty(e,"ConversationTranslationEventArgs",{enumerable:!0,get:function(){return le.ConversationTranslationEventArgs}}),Object.defineProperty(e,"ConversationTranslationResult",{enumerable:!0,get:function(){return le.ConversationTranslationResult}}),Object.defineProperty(e,"ConversationTranslator",{enumerable:!0,get:function(){return le.ConversationTranslator}}),Object.defineProperty(e,"ConversationTranscriber",{enumerable:!0,get:function(){return le.ConversationTranscriber}}),Object.defineProperty(e,"ConversationTranscriptionResult",{enumerable:!0,get:function(){return le.ConversationTranscriptionResult}}),Object.defineProperty(e,"Meeting",{enumerable:!0,get:function(){return le.Meeting}}),Object.defineProperty(e,"MeetingTranscriber",{enumerable:!0,get:function(){return le.MeetingTranscriber}}),Object.defineProperty(e,"Participant",{enumerable:!0,get:function(){return le.Participant}}),Object.defineProperty(e,"ParticipantChangedReason",{enumerable:!0,get:function(){return le.ParticipantChangedReason}}),Object.defineProperty(e,"User",{enumerable:!0,get:function(){return le.User}});var ge=pc();Object.defineProperty(e,"Synthesizer",{enumerable:!0,get:function(){return ge.Synthesizer}});var fe=ui();Object.defineProperty(e,"SpeechSynthesisOutputFormat",{enumerable:!0,get:function(){return fe.SpeechSynthesisOutputFormat}});var me=dc();Object.defineProperty(e,"SpeechSynthesizer",{enumerable:!0,get:function(){return me.SpeechSynthesizer}});var Se=gc();Object.defineProperty(e,"SynthesisResult",{enumerable:!0,get:function(){return Se.SynthesisResult}});var ye=Sc();Object.defineProperty(e,"SpeechSynthesisResult",{enumerable:!0,get:function(){return ye.SpeechSynthesisResult}});var Ce=Pc();Object.defineProperty(e,"SpeechSynthesisEventArgs",{enumerable:!0,get:function(){return Ce.SpeechSynthesisEventArgs}});var Pe=wc();Object.defineProperty(e,"SpeechSynthesisWordBoundaryEventArgs",{enumerable:!0,get:function(){return Pe.SpeechSynthesisWordBoundaryEventArgs}});var Re=Ec();Object.defineProperty(e,"SpeechSynthesisBookmarkEventArgs",{enumerable:!0,get:function(){return Re.SpeechSynthesisBookmarkEventArgs}});var Ie=Mc();Object.defineProperty(e,"SpeechSynthesisVisemeEventArgs",{enumerable:!0,get:function(){return Ie.SpeechSynthesisVisemeEventArgs}});var we=zc();Object.defineProperty(e,"SpeechSynthesisBoundaryType",{enumerable:!0,get:function(){return we.SpeechSynthesisBoundaryType}});var Te=Lc();Object.defineProperty(e,"SynthesisVoicesResult",{enumerable:!0,get:function(){return Te.SynthesisVoicesResult}});var Ae=Bc();Object.defineProperty(e,"SynthesisVoiceGender",{enumerable:!0,get:function(){return Ae.SynthesisVoiceGender}}),Object.defineProperty(e,"SynthesisVoiceType",{enumerable:!0,get:function(){return Ae.SynthesisVoiceType}}),Object.defineProperty(e,"VoiceInfo",{enumerable:!0,get:function(){return Ae.VoiceInfo}});var Ee=qc();Object.defineProperty(e,"SpeakerAudioDestination",{enumerable:!0,get:function(){return Ee.SpeakerAudioDestination}});var be=Vc();Object.defineProperty(e,"ConversationTranscriptionCanceledEventArgs",{enumerable:!0,get:function(){return be.ConversationTranscriptionCanceledEventArgs}});var Oe=Gc();Object.defineProperty(e,"MeetingTranscriptionCanceledEventArgs",{enumerable:!0,get:function(){return Oe.MeetingTranscriptionCanceledEventArgs}});var Me=Xc();Object.defineProperty(e,"PronunciationAssessmentGradingSystem",{enumerable:!0,get:function(){return Me.PronunciationAssessmentGradingSystem}});var De=ep();Object.defineProperty(e,"PronunciationAssessmentGranularity",{enumerable:!0,get:function(){return De.PronunciationAssessmentGranularity}});var ke=ip();Object.defineProperty(e,"PronunciationAssessmentConfig",{enumerable:!0,get:function(){return ke.PronunciationAssessmentConfig}});var ze=sp();Object.defineProperty(e,"PronunciationAssessmentResult",{enumerable:!0,get:function(){return ze.PronunciationAssessmentResult}});var Ne=Qs();Object.defineProperty(e,"LanguageIdMode",{enumerable:!0,get:function(){return Ne.LanguageIdMode}});var _e=pp();Object.defineProperty(e,"AvatarConfig",{enumerable:!0,get:function(){return _e.AvatarConfig}});var Le=dp();Object.defineProperty(e,"AvatarEventArgs",{enumerable:!0,get:function(){return Le.AvatarEventArgs}});var xe=Sp();Object.defineProperty(e,"AvatarSynthesizer",{enumerable:!0,get:function(){return xe.AvatarSynthesizer}});var Fe=Pp();Object.defineProperty(e,"AvatarVideoFormat",{enumerable:!0,get:function(){return Fe.AvatarVideoFormat}}),Object.defineProperty(e,"Coordinate",{enumerable:!0,get:function(){return Fe.Coordinate}});var Be=wp();Object.defineProperty(e,"AvatarWebRTCConnectionResult",{enumerable:!0,get:function(){return Be.AvatarWebRTCConnectionResult}});var je=Op();Object.defineProperty(e,"Diagnostics",{enumerable:!0,get:function(){return je.Diagnostics}});var Ue=Sr();Object.defineProperty(e,"LogLevel",{enumerable:!0,get:function(){return Ue.LogLevel}})}(Qr)),Qr}function Dp(){if(Ep)return $r;Ep=1,Object.defineProperty($r,"__esModule",{value:!0}),$r.ProxyInfo=void 0;const e=Mp();return $r.ProxyInfo=class t{constructor(e,t,r,i){this.privProxyHostName=e,this.privProxyPort=t,this.privProxyUserName=r,this.privProxyPassword=i}static fromParameters(r){return new t(r.getProperty(e.PropertyId.SpeechServiceConnection_ProxyHostName),parseInt(r.getProperty(e.PropertyId.SpeechServiceConnection_ProxyPort),10),r.getProperty(e.PropertyId.SpeechServiceConnection_ProxyUserName),r.getProperty(e.PropertyId.SpeechServiceConnection_ProxyPassword))}static fromRecognizerConfig(e){return this.fromParameters(e.parameters)}get HostName(){return this.privProxyHostName}get Port(){return this.privProxyPort}get UserName(){return this.privProxyUserName}get Password(){return this.privProxyPassword}},$r}var kp,zp,Np,_p,Lp,xp={};function Fp(){if(_p)return Np;_p=1;const e=function(){if(zp)return kp;zp=1;const e=new Set(["json","buffer","string"]);return kp=t=>(...r)=>{const i=new Set;let n,o,s,a="";return r.forEach((t=>{if("string"==typeof t)if(t.toUpperCase()===t){if(n)throw new Error(`Can't set method to ${t}, already set to ${n}.`);n=t}else if(t.startsWith("http:")||t.startsWith("https:"))a=t;else{if(!e.has(t))throw new Error(`Unknown encoding, ${t}`);o=t}else if("number"==typeof t)i.add(t);else{if("object"!=typeof t)throw new Error("Unknown type: "+typeof t);if(Array.isArray(t)||t instanceof Set)t.forEach((e=>i.add(e)));else{if(s)throw new Error("Cannot set headers twice.");s=t}}})),n||(n="GET"),0===i.size&&i.add(200),t(i,n,o,s,a)}}();class t extends Error{constructor(e,...r){let i;super(...r),Error.captureStackTrace&&Error.captureStackTrace(this,t),this.name="StatusError",this.message=e.statusMessage,this.statusCode=e.status,this.res=e,this.json=e.json.bind(e),this.text=e.text.bind(e),this.arrayBuffer=e.arrayBuffer.bind(e);Object.defineProperty(this,"responseBody",{get:()=>(i||(i=this.arrayBuffer()),i)}),this.headers={};for(const[t,n]of e.headers.entries())this.headers[t.toLowerCase()]=n}}return Np=e(((e,r,i,n,o)=>async(s,a,c={})=>{s=o+(s||"");let p=new URL(s);if(n||(n={}),p.username&&(n.Authorization="Basic "+btoa(p.username+":"+p.password),p=new URL(p.protocol+"//"+p.host+p.pathname+p.search)),"https:"!==p.protocol&&"http:"!==p.protocol)throw new Error(`Unknown protocol, ${p.protocol}`);if(a)if(a instanceof ArrayBuffer||ArrayBuffer.isView(a)||"string"==typeof a);else{if("object"!=typeof a)throw new Error("Unknown body type.");a=JSON.stringify(a),n["Content-Type"]="application/json"}c=new Headers({...n||{},...c});const u=await fetch(p,{method:r,headers:c,body:a});if(u.statusCode=u.status,!e.has(u.status))throw new t(u);return"json"===i?u.json():"buffer"===i?u.arrayBuffer():"string"===i?u.text():u}))}function Bp(){return Lp||(Lp=1,function(e){var t=xp&&xp.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.RestMessageAdapter=e.RestRequestType=void 0;const r=t(Fp()),i=Gt();var n,o;(o=n=e.RestRequestType||(e.RestRequestType={})).Get="GET",o.Post="POST",o.Delete="DELETE",o.File="file";e.RestMessageAdapter=class{constructor(e){if(!e)throw new i.ArgumentNullError("configParams");this.privHeaders=e.headers,this.privIgnoreCache=e.ignoreCache}static extractHeaderValue(e,t){let r="";try{const i=t.trim().split(/[\r\n]+/),n={};i.forEach((e=>{const t=e.split(": "),r=t.shift().toLowerCase(),i=t.join(": ");n[r]=i})),r=n[e.toLowerCase()]}catch(i){}return r}set options(e){this.privHeaders=e.headers,this.privIgnoreCache=e.ignoreCache}setHeaders(e,t){this.privHeaders[e]=t}request(e,t,o={},s=null){const a=new i.Deferred,c=e===n.File?"POST":e,p=(e,t={})=>{const r=e;return{data:JSON.stringify(t),headers:JSON.stringify(e.headers),json:t,ok:e.statusCode>=200&&e.statusCode<300,status:e.statusCode,statusText:t.error?t.error.message:r.statusText?r.statusText:r.statusMessage}};return this.privIgnoreCache&&(this.privHeaders["Cache-Control"]="no-cache"),e===n.Post&&s&&(this.privHeaders["content-type"]="application/json",this.privHeaders["Content-Type"]="application/json"),(i=>{r.default(t,c,this.privHeaders,200,201,202,204,400,401,402,403,404)(""===this.queryParams(o)?"":`?${this.queryParams(o)}`,i).then((async t=>{if(e===n.Delete||204===t.statusCode)a.resolve(p(t));else try{const e=await t.json();a.resolve(p(t,e))}catch{a.resolve(p(t))}})).catch((e=>{a.reject(e)}))})(s),a.promise}queryParams(e={}){return Object.keys(e).map((t=>encodeURIComponent(t)+"="+encodeURIComponent(e[t]))).join("&")}}}(xp)),xp}var jp,Up,qp,Wp={};function Hp(){if(jp)return Wp;jp=1,Object.defineProperty(Wp,"__esModule",{value:!0}),Wp.RestConfigBase=void 0;let e=class e{static get requestOptions(){return e.privDefaultRequestOptions}static get configParams(){return e.privDefaultParams}static get restErrors(){return e.privRestErrors}};return Wp.RestConfigBase=e,e.privDefaultRequestOptions={headers:{Accept:"application/json"},ignoreCache:!1,timeout:1e4},e.privRestErrors={authInvalidSubscriptionKey:"You must specify either an authentication token to use, or a Cognitive Speech subscription key.",authInvalidSubscriptionRegion:"You must specify the Cognitive Speech region to use.",invalidArgs:"Required input not found: {arg}.",invalidCreateJoinConversationResponse:"Creating/Joining conversation failed with HTTP {status}.",invalidParticipantRequest:"The requested participant was not found.",permissionDeniedConnect:"Required credentials not found.",permissionDeniedConversation:"Invalid operation: only the host can {command} the conversation.",permissionDeniedParticipant:"Invalid operation: only the host can {command} a participant.",permissionDeniedSend:"Invalid operation: the conversation is not in a connected state.",permissionDeniedStart:"Invalid operation: there is already an active conversation."},e.privDefaultParams={apiVersion:"api-version",authorization:"Authorization",clientAppId:"X-ClientAppId",contentTypeKey:"Content-Type",correlationId:"X-CorrelationId",languageCode:"language",nickname:"nickname",profanity:"profanity",requestId:"X-RequestId",roomId:"roomid",sessionToken:"token",subscriptionKey:"Ocp-Apim-Subscription-Key",subscriptionRegion:"Ocp-Apim-Subscription-Region",token:"X-CapitoToken"},Wp}function Vp(){return Up||(Up=1,e=vr,t=vr&&vr.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=vr&&vr.__exportStar||function(e,r){for(var i in e)"default"===i||r.hasOwnProperty(i)||t(r,e,i)},Object.defineProperty(e,"__esModule",{value:!0}),r(Ir(),e),r(Ar(),e),r(kr(),e),r(_r(),e),r(Fr(),e),r(Hr(),e),r(Wr(),e),r(Jr(),e),r(Dp(),e),r(Bp(),e),r(Hp(),e)),vr;var e,t,r}function Kp(){if(qp)return dr;qp=1,Object.defineProperty(dr,"__esModule",{value:!0}),dr.IntentConnectionFactory=void 0;const e=Vp(),t=Mp(),r=Zo(),i=jv(),n=Xt();let o=class extends r.ConnectionFactoryBase{create(o,s,a){let c=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Endpoint);if(!c){const e=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_IntentRegion),i=r.ConnectionFactoryBase.getHostSuffix(e);c=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Host,"wss://"+e+".sr.speech"+i)+"/speech/recognition/interactive/cognitiveservices/v1"}const p={format:"simple",language:o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_RecoLanguage)};this.setCommonUrlParams(o,p,c);const u={};void 0!==s.token&&""!==s.token&&(u[s.headerName]=s.token),u[n.HeaderNames.ConnectionId]=a,o.parameters.setProperty(t.PropertyId.SpeechServiceConnection_Url,c);const h="true"===o.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(c,p,u,new i.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(o),h,a)}getSpeechRegionFromIntentRegion(e){switch(e){case"West US":case"US West":case"westus":return"uswest";case"West US 2":case"US West 2":case"westus2":return"uswest2";case"South Central US":case"US South Central":case"southcentralus":return"ussouthcentral";case"West Central US":case"US West Central":case"westcentralus":return"uswestcentral";case"East US":case"US East":case"eastus":return"useast";case"East US 2":case"US East 2":case"eastus2":return"useast2";case"West Europe":case"Europe West":case"westeurope":return"europewest";case"North Europe":case"Europe North":case"northeurope":return"europenorth";case"Brazil South":case"South Brazil":case"southbrazil":return"brazilsouth";case"Australia East":case"East Australia":case"eastaustralia":return"australiaeast";case"Southeast Asia":case"Asia Southeast":case"southeastasia":return"asiasoutheast";case"East Asia":case"Asia East":case"eastasia":return"asiaeast";default:return e}}};return dr.IntentConnectionFactory=o,dr}var Jp,Gp={};function $p(){if(Jp)return Gp;Jp=1,Object.defineProperty(Gp,"__esModule",{value:!0}),Gp.VoiceProfileConnectionFactory=Gp.SpeakerRecognitionConnectionFactory=void 0;const e=Vp(),t=Mp(),r=Zo(),i=jv(),n=Xt();class o extends r.ConnectionFactoryBase{create(o,s,a,c){let p=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Endpoint);if(!p){const e=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Region),i=r.ConnectionFactoryBase.getHostSuffix(e),n=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Host,`wss://${e}.spr-frontend.speech${i}`),s=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_SpeakerIdMode,"TextIndependentIdentification");p=`${n}/speaker/ws/${this.scenarioToPath(s)}/${a}`}const u={format:"simple",language:o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_RecoLanguage)};this.setCommonUrlParams(o,u,p);const h={};void 0!==s.token&&""!==s.token&&(h[s.headerName]=s.token),h[n.HeaderNames.ConnectionId]=c,h[n.HeaderNames.SpIDAuthKey]=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Key),o.parameters.setProperty(t.PropertyId.SpeechServiceConnection_Url,p);const d="true"===o.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(p,u,h,new i.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(o),d,c)}scenarioToPath(e){switch(e){case"TextIndependentVerification":case"2":return"verification/text-independent";case"TextDependentVerification":case"1":return"verification/text-dependent";default:return"identification/text-independent"}}}Gp.SpeakerRecognitionConnectionFactory=class extends o{create(e,t,r){return super.create(e,t,"recognition",r)}};return Gp.VoiceProfileConnectionFactory=class extends o{create(e,t,r){return super.create(e,t,"profile",r)}},Gp}var Qp,Xp={};function Zp(){return Qp||(Qp=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.RecognitionEndedEvent=e.RecognitionCompletionStatus=e.RecognitionStartedEvent=e.ConnectingToServiceEvent=e.ListeningStartedEvent=e.RecognitionTriggeredEvent=e.SpeechRecognitionEvent=void 0;const t=Gt();class r extends t.PlatformEvent{constructor(e,r,i,n=t.EventType.Info){super(e,n),this.privRequestId=r,this.privSessionId=i}get requestId(){return this.privRequestId}get sessionId(){return this.privSessionId}}e.SpeechRecognitionEvent=r;e.RecognitionTriggeredEvent=class extends r{constructor(e,t,r,i){super("RecognitionTriggeredEvent",e,t),this.privAudioSourceId=r,this.privAudioNodeId=i}get audioSourceId(){return this.privAudioSourceId}get audioNodeId(){return this.privAudioNodeId}};e.ListeningStartedEvent=class extends r{constructor(e,t,r,i){super("ListeningStartedEvent",e,t),this.privAudioSourceId=r,this.privAudioNodeId=i}get audioSourceId(){return this.privAudioSourceId}get audioNodeId(){return this.privAudioNodeId}};e.ConnectingToServiceEvent=class extends r{constructor(e,t,r){super("ConnectingToServiceEvent",e,r),this.privAuthFetchEventid=t}get authFetchEventid(){return this.privAuthFetchEventid}};var i,n;e.RecognitionStartedEvent=class extends r{constructor(e,t,r,i,n){super("RecognitionStartedEvent",e,n),this.privAudioSourceId=t,this.privAudioNodeId=r,this.privAuthFetchEventId=i}get audioSourceId(){return this.privAudioSourceId}get audioNodeId(){return this.privAudioNodeId}get authFetchEventId(){return this.privAuthFetchEventId}},(n=i=e.RecognitionCompletionStatus||(e.RecognitionCompletionStatus={}))[n.Success=0]="Success",n[n.AudioSourceError=1]="AudioSourceError",n[n.AudioSourceTimeout=2]="AudioSourceTimeout",n[n.AuthTokenFetchError=3]="AuthTokenFetchError",n[n.AuthTokenFetchTimeout=4]="AuthTokenFetchTimeout",n[n.UnAuthorized=5]="UnAuthorized",n[n.ConnectTimeout=6]="ConnectTimeout",n[n.ConnectError=7]="ConnectError",n[n.ClientRecognitionActivityTimeout=8]="ClientRecognitionActivityTimeout",n[n.UnknownError=9]="UnknownError";e.RecognitionEndedEvent=class extends r{constructor(e,r,n,o,s,a,c,p){super("RecognitionEndedEvent",e,s,c===i.Success?t.EventType.Info:t.EventType.Error),this.privAudioSourceId=r,this.privAudioNodeId=n,this.privAuthFetchEventId=o,this.privStatus=c,this.privError=p,this.privServiceTag=a}get audioSourceId(){return this.privAudioSourceId}get audioNodeId(){return this.privAudioNodeId}get authFetchEventId(){return this.privAuthFetchEventId}get serviceTag(){return this.privServiceTag}get status(){return this.privStatus}get error(){return this.privError}}}(Xp)),Xp}var Yp,eu,tu={},ru={};function iu(){if(Yp)return ru;Yp=1,Object.defineProperty(ru,"__esModule",{value:!0}),ru.SpeechConnectionMessage=void 0;const e=Gt(),t=Xt();class r extends e.ConnectionMessage{constructor(r,i,n,o,s,a,c,p){if(!i)throw new e.ArgumentNullError("path");if(!n)throw new e.ArgumentNullError("requestId");const u={};if(u[t.HeaderNames.Path]=i,u[t.HeaderNames.RequestId]=n,u[t.HeaderNames.RequestTimestamp]=(new Date).toISOString(),o&&(u[t.HeaderNames.ContentType]=o),a&&(u[t.HeaderNames.RequestStreamId]=a),c)for(const e in c)e&&(u[e]=c[e]);p?super(r,s,u,p):super(r,s,u),this.privPath=i,this.privRequestId=n,this.privContentType=o,this.privStreamId=a,this.privAdditionalHeaders=c}get path(){return this.privPath}get requestId(){return this.privRequestId}get contentType(){return this.privContentType}get streamId(){return this.privStreamId}get additionalHeaders(){return this.privAdditionalHeaders}static fromConnectionMessage(e){let i=null,n=null,o=null,s=null;const a={};if(e.headers)for(const r in e.headers)r&&(r.toLowerCase()===t.HeaderNames.Path.toLowerCase()?i=e.headers[r]:r.toLowerCase()===t.HeaderNames.RequestId.toLowerCase()?n=e.headers[r]:r.toLowerCase()===t.HeaderNames.ContentType.toLowerCase()?o=e.headers[r]:r.toLowerCase()===t.HeaderNames.RequestStreamId.toLowerCase()?s=e.headers[r]:a[r]=e.headers[r]);return new r(e.messageType,i,n,o,e.body,s,a,e.id)}}return ru.SpeechConnectionMessage=r,ru}function nu(){if(eu)return tu;eu=1,Object.defineProperty(tu,"__esModule",{value:!0}),tu.ServiceRecognizerBase=void 0;const e=Vp(),t=Gt(),r=Mp(),i=jv(),n=iu();let o=class o{constructor(e,n,o,s,a){if(this.privConnectionConfigurationPromise=void 0,this.privConnectionPromise=void 0,this.privSetTimeout=setTimeout,this.privIsLiveAudio=!1,this.privAverageBytesPerMs=0,this.privEnableSpeakerId=!1,this.privExpectContentAssessmentResponse=!1,this.recognizeOverride=void 0,this.recognizeSpeaker=void 0,this.disconnectOverride=void 0,this.receiveMessageOverride=void 0,this.sendPrePayloadJSONOverride=void 0,this.postConnectImplOverride=void 0,this.configConnectionOverride=void 0,this.handleSpeechPhraseMessage=void 0,this.handleSpeechHypothesisMessage=void 0,!e)throw new t.ArgumentNullError("authentication");if(!n)throw new t.ArgumentNullError("connectionFactory");if(!o)throw new t.ArgumentNullError("audioSource");if(!s)throw new t.ArgumentNullError("recognizerConfig");this.privEnableSpeakerId=s.isSpeakerDiarizationEnabled,this.privMustReportEndOfStream=!1,this.privAuthentication=e,this.privConnectionFactory=n,this.privAudioSource=o,this.privRecognizerConfig=s,this.privIsDisposed=!1,this.privRecognizer=a,this.privRequestSession=new i.RequestSession(this.privAudioSource.id()),this.privConnectionEvents=new t.EventSource,this.privServiceEvents=new t.EventSource,this.privDynamicGrammar=new i.DynamicGrammarBuilder,this.privSpeechContext=new i.SpeechContext(this.privDynamicGrammar),this.privAgentConfig=new i.AgentConfig;"on"===this.privRecognizerConfig.parameters.getProperty(r.PropertyId.WebWorkerLoadType,"on").toLowerCase()&&"undefined"!=typeof Blob&&"undefined"!=typeof Worker?this.privSetTimeout=t.Timeout.setTimeout:("undefined"!=typeof window&&(this.privSetTimeout=window.setTimeout.bind(window)),"undefined"!=typeof globalThis&&(this.privSetTimeout=globalThis.setTimeout.bind(globalThis))),this.connectionEvents.attach((e=>{if("ConnectionClosedEvent"===e.name){const t=e;(1003===t.statusCode||1007===t.statusCode||1002===t.statusCode||4e3===t.statusCode||this.privRequestSession.numConnectionAttempts>this.privRecognizerConfig.maxRetryCount)&&this.cancelRecognitionLocal(r.CancellationReason.Error,1007===t.statusCode?r.CancellationErrorCode.BadRequestParameters:r.CancellationErrorCode.ConnectionFailure,`${t.reason} websocket error code: ${t.statusCode}`)}})),this.privEnableSpeakerId&&(this.privDiarizationSessionId=t.createNoDashGuid()),this.setLanguageIdJson(),this.setOutputDetailLevelJson()}setTranslationJson(){const e=this.privRecognizerConfig.parameters.getProperty(r.PropertyId.SpeechServiceConnection_TranslationToLanguages,void 0);if(void 0!==e){const t=e.split(","),i=this.privRecognizerConfig.parameters.getProperty(r.PropertyId.SpeechServiceConnection_TranslationVoice,void 0),n=void 0!==i?"Synthesize":"None";if(this.privSpeechContext.setSection("translation",{onSuccess:{action:n},output:{interimResults:{mode:"Always"}},targetLanguages:t}),void 0!==i){const e={};for(const r of t)e[r]=i;this.privSpeechContext.setSection("synthesis",{defaultVoices:e})}}}setSpeechSegmentationTimeoutJson(){const e=this.privRecognizerConfig.parameters.getProperty(r.PropertyId.Speech_SegmentationSilenceTimeoutMs,void 0),t=this.privRecognizerConfig.parameters.getProperty(r.PropertyId.Speech_SegmentationMaximumTimeMs,void 0),n=this.privRecognizerConfig.parameters.getProperty(r.PropertyId.Speech_SegmentationStrategy,void 0),o={segmentation:{mode:""}};let s=!1;if(void 0!==n){s=!0;let e="";switch(n.toLowerCase()){case"default":break;case"time":e="Custom";break;case"semantic":e="Semantic"}o.segmentation.mode=e}if(void 0!==e){s=!0;const t=parseInt(e,10);o.segmentation.mode="Custom",o.segmentation.segmentationSilenceTimeoutMs=t}if(void 0!==t){s=!0;const e=parseInt(t,10);o.segmentation.mode="Custom",o.segmentation.segmentationForcedTimeoutMs=e}if(s){const e=this.recognitionMode===i.RecognitionMode.Conversation?"CONVERSATION":this.recognitionMode===i.RecognitionMode.Dictation?"DICTATION":"INTERACTIVE",t=this.privSpeechContext.getSection("phraseDetection");t.mode=e,t[e]=o,this.privSpeechContext.setSection("phraseDetection",t)}}setLanguageIdJson(){const e=this.privSpeechContext.getSection("phraseDetection");if(void 0!==this.privRecognizerConfig.autoDetectSourceLanguages){const t=this.privRecognizerConfig.autoDetectSourceLanguages.split(",");let r;r="Continuous"===this.privRecognizerConfig.languageIdMode?"DetectContinuous":"DetectAtAudioStart",this.privSpeechContext.setSection("languageId",{Priority:"PrioritizeLatency",languages:t,mode:r,onSuccess:{action:"Recognize"},onUnknown:{action:"None"}}),this.privSpeechContext.setSection("phraseOutput",{interimResults:{resultType:"Auto"},phraseResults:{resultType:"Always"}});const i=this.privRecognizerConfig.sourceLanguageModels;void 0!==i&&(e.customModels=i,e.onInterim={action:"None"},e.onSuccess={action:"None"})}void 0!==this.privRecognizerConfig.parameters.getProperty(r.PropertyId.SpeechServiceConnection_TranslationToLanguages,void 0)&&(e.onInterim={action:"Translate"},e.onSuccess={action:"Translate"},this.privSpeechContext.setSection("phraseOutput",{interimResults:{resultType:"None"},phraseResults:{resultType:"None"}})),this.privSpeechContext.setSection("phraseDetection",e)}setOutputDetailLevelJson(){if(this.privEnableSpeakerId){if("true"===this.privRecognizerConfig.parameters.getProperty(r.PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps,"false").toLowerCase())this.privSpeechContext.setWordLevelTimings();else{this.privRecognizerConfig.parameters.getProperty(i.OutputFormatPropertyName,r.OutputFormat[r.OutputFormat.Simple]).toLowerCase()===r.OutputFormat[r.OutputFormat.Detailed].toLocaleLowerCase()&&this.privSpeechContext.setDetailedOutputFormat()}}}get isSpeakerDiarizationEnabled(){return this.privEnableSpeakerId}get audioSource(){return this.privAudioSource}get speechContext(){return this.privSpeechContext}get dynamicGrammar(){return this.privDynamicGrammar}get agentConfig(){return this.privAgentConfig}set conversationTranslatorToken(e){this.privRecognizerConfig.parameters.setProperty(r.PropertyId.ConversationTranslator_Token,e)}set voiceProfileType(e){this.privRecognizerConfig.parameters.setProperty(r.PropertyId.SpeechServiceConnection_SpeakerIdMode,e)}set authentication(e){this.privAuthentication=e}isDisposed(){return this.privIsDisposed}async dispose(e){if(this.privIsDisposed=!0,void 0!==this.privConnectionConfigurationPromise)try{const t=await this.privConnectionConfigurationPromise;await t.dispose(e)}catch(t){return}}get connectionEvents(){return this.privConnectionEvents}get serviceEvents(){return this.privServiceEvents}get recognitionMode(){return this.privRecognizerConfig.recognitionMode}async recognize(t,n,o){if(void 0!==this.recognizeOverride)return void(await this.recognizeOverride(t,n,o));this.privConnectionConfigurationPromise=void 0,this.privRecognizerConfig.recognitionMode=t,this.setSpeechSegmentationTimeoutJson(),this.setTranslationJson(),this.privSuccessCallback=n,this.privErrorCallback=o,this.privRequestSession.startNewRecognition(),this.privRequestSession.listenForServiceTelemetry(this.privAudioSource.events);const s=this.connectImpl();let a;try{const t=await this.audioSource.attach(this.privRequestSession.audioNodeId),r=await this.audioSource.format,n=await this.audioSource.deviceInfo;this.privIsLiveAudio=n.type&&n.type===i.type.Microphones,a=new e.ReplayableAudioNode(t,r.avgBytesPerSec),await this.privRequestSession.onAudioSourceAttachCompleted(a,!1),this.privRecognizerConfig.SpeechServiceConfig.Context.audio={source:n}}catch(p){throw await this.privRequestSession.onStopRecognizing(),p}try{await s}catch(p){return void(await this.cancelRecognitionLocal(r.CancellationReason.Error,r.CancellationErrorCode.ConnectionFailure,p))}const c=new r.SessionEventArgs(this.privRequestSession.sessionId);this.privRecognizer.sessionStarted&&this.privRecognizer.sessionStarted(this.privRecognizer,c),this.receiveMessage();this.sendAudio(a).catch((async e=>{await this.cancelRecognitionLocal(r.CancellationReason.Error,r.CancellationErrorCode.RuntimeError,e)}))}async stopRecognizing(){if(this.privRequestSession.isRecognizing)try{await this.audioSource.turnOff(),await this.sendFinalAudio(),await this.privRequestSession.onStopRecognizing(),await this.privRequestSession.turnCompletionPromise}finally{await this.privRequestSession.dispose()}}async connect(){return await this.connectImpl(),Promise.resolve()}connectAsync(e,t){this.connectImpl().then((()=>{try{e&&e()}catch(r){t&&t(r)}}),(e=>{try{t&&t(e)}catch(r){}}))}async disconnect(){if(await this.cancelRecognitionLocal(r.CancellationReason.Error,r.CancellationErrorCode.NoError,"Disconnecting"),void 0!==this.disconnectOverride&&await this.disconnectOverride(),void 0!==this.privConnectionPromise)try{await(await this.privConnectionPromise).dispose()}catch(e){}this.privConnectionPromise=void 0}sendMessage(e){}async sendNetworkMessage(e,r){const i="string"==typeof r?t.MessageType.Text:t.MessageType.Binary,o="string"==typeof r?"application/json":"";return(await this.fetchConnection()).send(new n.SpeechConnectionMessage(i,e,this.privRequestSession.requestId,o,r))}set activityTemplate(e){this.privActivityTemplate=e}get activityTemplate(){return this.privActivityTemplate}set expectContentAssessmentResponse(e){this.privExpectContentAssessmentResponse=e}async sendTelemetryData(){const e=this.privRequestSession.getTelemetry();if(!0!==o.telemetryDataEnabled||this.privIsDisposed||null===e)return;if(o.telemetryData)try{o.telemetryData(e)}catch{}const r=await this.fetchConnection();await r.send(new n.SpeechConnectionMessage(t.MessageType.Text,"telemetry",this.privRequestSession.requestId,"application/json",e))}async cancelRecognitionLocal(e,t,r){this.privRequestSession.isRecognizing&&(await this.privRequestSession.onStopRecognizing(),this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,e,t,r))}async receiveMessage(){try{if(this.privIsDisposed)return;let e=await this.fetchConnection();const o=await e.read();if(void 0!==this.receiveMessageOverride)return this.receiveMessageOverride();if(!o)return this.receiveMessage();this.privServiceHasSentMessage=!0;const s=n.SpeechConnectionMessage.fromConnectionMessage(o);if(s.requestId.toLowerCase()===this.privRequestSession.requestId.toLowerCase())switch(s.path.toLowerCase()){case"turn.start":this.privMustReportEndOfStream=!0,this.privRequestSession.onServiceTurnStartResponse();break;case"speech.startdetected":const n=i.SpeechDetected.fromJSON(s.textBody,this.privRequestSession.currentTurnAudioOffset),o=new r.RecognitionEventArgs(n.Offset,this.privRequestSession.sessionId);this.privRecognizer.speechStartDetected&&this.privRecognizer.speechStartDetected(this.privRecognizer,o);break;case"speech.enddetected":let a;a=s.textBody.length>0?s.textBody:"{ Offset: 0 }";const c=i.SpeechDetected.fromJSON(a,this.privRequestSession.currentTurnAudioOffset),p=new r.RecognitionEventArgs(c.Offset+this.privRequestSession.currentTurnAudioOffset,this.privRequestSession.sessionId);this.privRecognizer.speechEndDetected&&this.privRecognizer.speechEndDetected(this.privRecognizer,p);break;case"turn.end":await this.sendTelemetryData(),this.privRequestSession.isSpeechEnded&&this.privMustReportEndOfStream&&(this.privMustReportEndOfStream=!1,await this.cancelRecognitionLocal(r.CancellationReason.EndOfStream,r.CancellationErrorCode.NoError,void 0));const u=new r.SessionEventArgs(this.privRequestSession.sessionId);if(await this.privRequestSession.onServiceTurnEndResponse(this.privRecognizerConfig.isContinuousRecognition),!this.privRecognizerConfig.isContinuousRecognition||this.privRequestSession.isSpeechEnded||!this.privRequestSession.isRecognizing)return void(this.privRecognizer.sessionStopped&&this.privRecognizer.sessionStopped(this.privRecognizer,u));e=await this.fetchConnection(),await this.sendPrePayloadJSON(e);break;default:await this.processTypeSpecificMessages(s)||this.privServiceEvents&&this.serviceEvents.onEvent(new t.ServiceEvent(s.path.toLowerCase(),s.textBody))}return this.receiveMessage()}catch(e){return null}}updateSpeakerDiarizationAudioOffset(){const e=this.privRequestSession.recognitionBytesSent,t=0!==this.privAverageBytesPerMs?e/this.privAverageBytesPerMs:0;this.privSpeechContext.setSpeakerDiarizationAudioOffsetMs(t)}sendSpeechContext(e,r){this.privEnableSpeakerId&&this.updateSpeakerDiarizationAudioOffset();const i=this.speechContext.toJSON();if(r&&this.privRequestSession.onSpeechContext(),i)return e.send(new n.SpeechConnectionMessage(t.MessageType.Text,"speech.context",this.privRequestSession.requestId,"application/json",i))}noOp(){}async sendPrePayloadJSON(e,t=!0){if(void 0!==this.sendPrePayloadJSONOverride)return this.sendPrePayloadJSONOverride(e);await this.sendSpeechContext(e,t),await this.sendWaveHeader(e)}async sendWaveHeader(e){const r=await this.audioSource.format;return e.send(new n.SpeechConnectionMessage(t.MessageType.Binary,"audio",this.privRequestSession.requestId,"audio/x-wav",r.header))}connectImpl(){return void 0!==this.privConnectionPromise?this.privConnectionPromise.then((e=>e.state()===t.ConnectionState.Disconnected?(this.privConnectionId=null,this.privConnectionPromise=void 0,this.privServiceHasSentMessage=!1,this.connectImpl()):this.privConnectionPromise),(()=>(this.privConnectionId=null,this.privConnectionPromise=void 0,this.privServiceHasSentMessage=!1,this.connectImpl()))):(this.privConnectionPromise=this.retryableConnect(),this.privConnectionPromise.catch((()=>{})),void 0!==this.postConnectImplOverride?this.postConnectImplOverride(this.privConnectionPromise):this.privConnectionPromise)}sendSpeechServiceConfig(e,r,i){if(r.onSpeechContext(),!0!==o.telemetryDataEnabled){const e={context:{system:JSON.parse(i).context.system}};i=JSON.stringify(e)}if("true"===this.privRecognizerConfig.parameters.getProperty("f0f5debc-f8c9-4892-ac4b-90a7ab359fd2","false").toLowerCase()){const e=JSON.parse(i);e.context.DisableReferenceChannel="True",e.context.MicSpec="1_0_0",i=JSON.stringify(e)}if(i)return e.send(new n.SpeechConnectionMessage(t.MessageType.Text,"speech.config",r.requestId,"application/json",i))}async fetchConnection(){return void 0!==this.privConnectionConfigurationPromise?this.privConnectionConfigurationPromise.then((e=>e.state()===t.ConnectionState.Disconnected?(this.privConnectionId=null,this.privConnectionConfigurationPromise=void 0,this.privServiceHasSentMessage=!1,this.fetchConnection()):this.privConnectionConfigurationPromise),(()=>(this.privConnectionId=null,this.privConnectionConfigurationPromise=void 0,this.privServiceHasSentMessage=!1,this.fetchConnection()))):(this.privConnectionConfigurationPromise=this.configureConnection(),await this.privConnectionConfigurationPromise)}async sendAudio(e){const r=await this.audioSource.format;this.privAverageBytesPerMs=r.avgBytesPerSec/1e3;let i=Date.now();const o=this.privRecognizerConfig.parameters.getProperty("SPEECH-TransmitLengthBeforThrottleMs","5000"),s=r.avgBytesPerSec/1e3*parseInt(o,10),a=this.privRequestSession.recogNumber,c=async()=>{if(!this.privIsDisposed&&!this.privRequestSession.isSpeechEnded&&this.privRequestSession.isRecognizing&&this.privRequestSession.recogNumber===a){const o=await this.fetchConnection(),p=await e.read();if(this.privRequestSession.isSpeechEnded)return;let u,h;if(!p||p.isEnd?(u=null,h=0):(u=p.buffer,this.privRequestSession.onAudioSent(u.byteLength),h=s>=this.privRequestSession.bytesSent?0:Math.max(0,i-Date.now())),0!==h&&await this.delay(h),null!==u&&(i=Date.now()+1e3*u.byteLength/(2*r.avgBytesPerSec)),!this.privIsDisposed&&!this.privRequestSession.isSpeechEnded&&this.privRequestSession.isRecognizing&&this.privRequestSession.recogNumber===a){if(o.send(new n.SpeechConnectionMessage(t.MessageType.Binary,"audio",this.privRequestSession.requestId,null,u)).catch((()=>{this.privRequestSession.onServiceTurnEndResponse(this.privRecognizerConfig.isContinuousRecognition).catch((()=>{}))})),!(null==p?void 0:p.isEnd))return c();this.privIsLiveAudio||this.privRequestSession.onSpeechEnded()}}};return c()}async retryableConnect(){let e=!1;this.privAuthFetchEventId=t.createNoDashGuid();const i=this.privRequestSession.sessionId;this.privConnectionId=void 0!==i?i:t.createNoDashGuid(),this.privRequestSession.onPreConnectionStart(this.privAuthFetchEventId,this.privConnectionId);let n=0,o="";for(;this.privRequestSession.numConnectionAttempts<=this.privRecognizerConfig.maxRetryCount;){const t=e?this.privAuthentication.fetchOnExpiry(this.privAuthFetchEventId):this.privAuthentication.fetch(this.privAuthFetchEventId),r=await t;await this.privRequestSession.onAuthCompleted(!1);const i=this.privConnectionFactory.create(this.privRecognizerConfig,r,this.privConnectionId);this.privRequestSession.listenForServiceTelemetry(i.events),i.events.attach((e=>{this.connectionEvents.onEvent(e)}));const s=await i.open();if(200===s.statusCode)return await this.privRequestSession.onConnectionEstablishCompleted(s.statusCode),Promise.resolve(i);1006===s.statusCode&&(e=!0),n=s.statusCode,o=s.reason,this.privRequestSession.onRetryConnection()}return await this.privRequestSession.onConnectionEstablishCompleted(n,o),Promise.reject(`Unable to contact server. StatusCode: ${n}, ${this.privRecognizerConfig.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Endpoint)} Reason: ${o}`)}delay(e){return new Promise((t=>this.privSetTimeout(t,e)))}writeBufferToConsole(e){let t="Buffer Size: ";if(null===e)t+="null";else{const r=new Uint8Array(e);t+=`${e.byteLength}\r\n`;for(let i=0;i<e.byteLength;i++)t+=r[i].toString(16).padStart(2,"0")+" ",(i+1)%16==0&&(console.info(t),t="")}console.info(t)}async sendFinalAudio(){const e=await this.fetchConnection();await e.send(new n.SpeechConnectionMessage(t.MessageType.Binary,"audio",this.privRequestSession.requestId,null,null))}async configureConnection(){const e=await this.connectImpl();return void 0!==this.configConnectionOverride?this.configConnectionOverride(e):(await this.sendSpeechServiceConfig(e,this.privRequestSession,this.privRecognizerConfig.SpeechServiceConfig.serialize()),await this.sendPrePayloadJSON(e,!1),e)}};return tu.ServiceRecognizerBase=o,o.telemetryDataEnabled=!0,tu}var ou,su={};function au(){if(ou)return su;ou=1,Object.defineProperty(su,"__esModule",{value:!0}),su.ConversationServiceRecognizer=void 0;const e=Mp(),t=jv();let r=class extends t.ServiceRecognizerBase{constructor(e,t,r,i,n){super(e,t,r,i,n),this.handleSpeechPhraseMessage=async e=>this.handleSpeechPhrase(e),this.handleSpeechHypothesisMessage=e=>this.handleSpeechHypothesis(e)}processTypeSpecificMessages(e){}handleRecognizedCallback(e,t,r){}handleRecognizingCallback(e,t,r){}async processSpeechMessages(e){let t=!1;switch(e.path.toLowerCase()){case"speech.hypothesis":case"speech.fragment":this.handleSpeechHypothesisMessage&&this.handleSpeechHypothesisMessage(e.textBody),t=!0;break;case"speech.phrase":this.handleSpeechPhraseMessage&&await this.handleSpeechPhraseMessage(e.textBody),t=!0}return t}cancelRecognition(e,t,r,i,n){}async handleSpeechPhrase(r){const i=t.SimpleSpeechPhrase.fromJSON(r,this.privRequestSession.currentTurnAudioOffset),n=t.EnumTranslation.implTranslateRecognitionResult(i.RecognitionStatus);let o;const s=new e.PropertyCollection;if(s.setProperty(e.PropertyId.SpeechServiceResponse_JsonResult,r),this.privRequestSession.onPhraseRecognized(i.Offset+i.Duration),e.ResultReason.Canceled===n){const e=t.EnumTranslation.implTranslateCancelResult(i.RecognitionStatus),r=t.EnumTranslation.implTranslateCancelErrorCode(i.RecognitionStatus);await this.cancelRecognitionLocal(e,r,t.EnumTranslation.implTranslateErrorDetails(r))}else if(i.RecognitionStatus!==t.RecognitionStatus.EndOfDictation){if(this.privRecognizerConfig.parameters.getProperty(t.OutputFormatPropertyName)===e.OutputFormat[e.OutputFormat.Simple])o=new e.SpeechRecognitionResult(this.privRequestSession.requestId,n,i.DisplayText,i.Duration,i.Offset,i.Language,i.LanguageDetectionConfidence,i.SpeakerId,void 0,i.asJson(),s);else{const i=t.DetailedSpeechPhrase.fromJSON(r,this.privRequestSession.currentTurnAudioOffset);o=new e.SpeechRecognitionResult(this.privRequestSession.requestId,n,i.Text,i.Duration,i.Offset,i.Language,i.LanguageDetectionConfidence,i.SpeakerId,void 0,i.asJson(),s)}this.handleRecognizedCallback(o,o.offset,this.privRequestSession.sessionId)}}handleSpeechHypothesis(r){const i=t.SpeechHypothesis.fromJSON(r,this.privRequestSession.currentTurnAudioOffset),n=new e.PropertyCollection;n.setProperty(e.PropertyId.SpeechServiceResponse_JsonResult,r);const o=new e.SpeechRecognitionResult(this.privRequestSession.requestId,e.ResultReason.RecognizingSpeech,i.Text,i.Duration,i.Offset,i.Language,i.LanguageDetectionConfidence,i.SpeakerId,void 0,i.asJson(),n);this.privRequestSession.onHypothesis(i.Offset),this.handleRecognizingCallback(o,i.Duration,this.privRequestSession.sessionId)}};return su.ConversationServiceRecognizer=r,su}var cu,pu={};function uu(){return cu||(cu=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.RecognizerConfig=e.SpeechResultFormat=e.RecognitionMode=void 0;const t=Mp(),r=jv();var i,n,o;(n=i=e.RecognitionMode||(e.RecognitionMode={}))[n.Interactive=0]="Interactive",n[n.Conversation=1]="Conversation",n[n.Dictation=2]="Dictation",(o=e.SpeechResultFormat||(e.SpeechResultFormat={}))[o.Simple=0]="Simple",o[o.Detailed=1]="Detailed";e.RecognizerConfig=class{constructor(e,i){this.privSpeechServiceConfig=e||new r.SpeechServiceConfig(new r.Context(null)),this.privParameters=i,this.privMaxRetryCount=parseInt(i.getProperty("SPEECH-Error-MaxRetryCount","4"),10),this.privLanguageIdMode=i.getProperty(t.PropertyId.SpeechServiceConnection_LanguageIdMode,void 0),this.privEnableSpeakerId=!1}get parameters(){return this.privParameters}get recognitionMode(){return this.privRecognitionMode}set recognitionMode(e){this.privRecognitionMode=e,this.privRecognitionActivityTimeout=e===i.Interactive?8e3:25e3,this.privSpeechServiceConfig.Recognition=i[e]}get SpeechServiceConfig(){return this.privSpeechServiceConfig}get recognitionActivityTimeout(){return this.privRecognitionActivityTimeout}get isContinuousRecognition(){return this.privRecognitionMode!==i.Interactive}get languageIdMode(){return this.privLanguageIdMode}get autoDetectSourceLanguages(){return this.parameters.getProperty(t.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,void 0)}get recognitionEndpointVersion(){return this.parameters.getProperty(t.PropertyId.SpeechServiceConnection_RecognitionEndpointVersion,void 0)}get sourceLanguageModels(){const e=[];let r=!1;if(void 0!==this.autoDetectSourceLanguages)for(const i of this.autoDetectSourceLanguages.split(",")){const n=i+t.PropertyId.SpeechServiceConnection_EndpointId.toString(),o=this.parameters.getProperty(n,void 0);void 0!==o?(e.push({language:i,endpoint:o}),r=!0):e.push({language:i,endpoint:""})}return r?e:void 0}get maxRetryCount(){return this.privMaxRetryCount}get isSpeakerDiarizationEnabled(){return this.privEnableSpeakerId}set isSpeakerDiarizationEnabled(e){this.privEnableSpeakerId=e}}}(pu)),pu}var hu,du={};function vu(){return hu||(hu=1,Object.defineProperty(du,"__esModule",{value:!0})),du}var lu,gu={};function fu(){if(lu)return gu;lu=1,Object.defineProperty(gu,"__esModule",{value:!0}),gu.WebsocketMessageFormatter=void 0;const e=Gt();return gu.WebsocketMessageFormatter=class{toConnectionMessage(t){const r=new e.Deferred;try{if(t.messageType===e.MessageType.Text){const i=t.textContent;let n={},o=null;if(i){const e=i.split("\r\n\r\n");e&&e.length>0&&(n=this.parseHeaders(e[0]),e.length>1&&(o=e[1]))}r.resolve(new e.ConnectionMessage(t.messageType,o,n,t.id))}else if(t.messageType===e.MessageType.Binary){const i=t.binaryContent;let n={},o=null;if(!i||i.byteLength<2)throw new Error("Invalid binary message format. Header length missing.");const s=new DataView(i),a=s.getInt16(0);if(i.byteLength<a+2)throw new Error("Invalid binary message format. Header content missing.");let c="";for(let e=0;e<a;e++)c+=String.fromCharCode(s.getInt8(e+2));n=this.parseHeaders(c),i.byteLength>a+2&&(o=i.slice(2+a)),r.resolve(new e.ConnectionMessage(t.messageType,o,n,t.id))}}catch(i){r.reject(`Error formatting the message. Error: ${i}`)}return r.promise}fromConnectionMessage(t){const r=new e.Deferred;try{if(t.messageType===e.MessageType.Text){const i=`${this.makeHeaders(t)}\r\n${t.textBody?t.textBody:""}`;r.resolve(new e.RawWebsocketMessage(e.MessageType.Text,i,t.id))}else if(t.messageType===e.MessageType.Binary){const i=this.makeHeaders(t),n=t.binaryBody,o=this.stringToArrayBuffer(i),s=new Int8Array(o),a=s.byteLength,c=new Int8Array(2+a+(n?n.byteLength:0));if(c[0]=a>>8&255,c[1]=255&a,c.set(s,2),n){const e=new Int8Array(n);c.set(e,2+a)}const p=c.buffer;r.resolve(new e.RawWebsocketMessage(e.MessageType.Binary,p,t.id))}}catch(i){r.reject(`Error formatting the message. ${i}`)}return r.promise}makeHeaders(e){let t="";if(e.headers)for(const r in e.headers)r&&(t+=`${r}: ${e.headers[r]}\r\n`);return t}parseHeaders(e){const t={};if(e){const r=e.match(/[^\r\n]+/g);if(t)for(const e of r)if(e){const r=e.indexOf(":"),i=r>0?e.substr(0,r).trim().toLowerCase():e,n=r>0&&e.length>r+1?e.substr(r+1).trim():"";t[i]=n}}return t}stringToArrayBuffer(e){const t=new ArrayBuffer(e.length),r=new DataView(t);for(let i=0;i<e.length;i++)r.setUint8(i,e.charCodeAt(i));return t}},gu}var mu,Su={};function yu(){if(mu)return Su;mu=1,Object.defineProperty(Su,"__esModule",{value:!0}),Su.SpeechConnectionFactory=void 0;const e=Vp(),t=jv(),r=Mp(),i=Zo(),n=jv(),o=Xt(),s=Xo();let a=class extends i.ConnectionFactoryBase{constructor(){super(...arguments),this.interactiveRelativeUri="/speech/recognition/interactive/cognitiveservices/v1",this.conversationRelativeUri="/speech/recognition/conversation/cognitiveservices/v1",this.dictationRelativeUri="/speech/recognition/dictation/cognitiveservices/v1",this.universalUri="/speech/universal/v"}create(a,c,p){let u=a.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Endpoint,void 0);const h=a.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Region,void 0),d=i.ConnectionFactoryBase.getHostSuffix(h),v=a.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Host,"wss://"+h+".stt.speech"+d),l={},g=a.parameters.getProperty(r.PropertyId.SpeechServiceConnection_EndpointId,void 0),f=a.parameters.getProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage,void 0);if(g?u&&-1!==u.search(s.QueryParameterNames.CustomSpeechDeploymentId)||(l[s.QueryParameterNames.CustomSpeechDeploymentId]=g):f&&(u&&-1!==u.search(s.QueryParameterNames.Language)||(l[s.QueryParameterNames.Language]=f)),u&&-1!==u.search(s.QueryParameterNames.Format)||(l[s.QueryParameterNames.Format]=a.parameters.getProperty(t.OutputFormatPropertyName,r.OutputFormat[r.OutputFormat.Simple]).toLowerCase()),void 0!==a.autoDetectSourceLanguages&&(l[s.QueryParameterNames.EnableLanguageId]="true"),this.setCommonUrlParams(a,l,u),!u)switch(a.recognitionMode){case n.RecognitionMode.Conversation:u="true"===a.parameters.getProperty(t.ForceDictationPropertyName,"false")?v+this.dictationRelativeUri:void 0!==a.recognitionEndpointVersion&&parseInt(a.recognitionEndpointVersion,10)>1?`${v}${this.universalUri}${a.recognitionEndpointVersion}`:v+this.conversationRelativeUri;break;case n.RecognitionMode.Dictation:u=v+this.dictationRelativeUri;break;default:u=void 0!==a.recognitionEndpointVersion&&parseInt(a.recognitionEndpointVersion,10)>1?`${v}${this.universalUri}${a.recognitionEndpointVersion}`:v+this.interactiveRelativeUri}const m={};void 0!==c.token&&""!==c.token&&(m[c.headerName]=c.token),m[o.HeaderNames.ConnectionId]=p;const S="true"===a.parameters.getProperty("SPEECH-EnableWebsocketCompression","false"),y=new e.WebsocketConnection(u,l,m,new n.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(a),S,p),C=y.uri;return a.parameters.setProperty(r.PropertyId.SpeechServiceConnection_Url,C),y}};return Su.SpeechConnectionFactory=a,Su}var Cu,Pu={};function Ru(){if(Cu)return Pu;Cu=1,Object.defineProperty(Pu,"__esModule",{value:!0}),Pu.ConversationTranscriberConnectionFactory=void 0;const e=Vp(),t=Mp(),r=jv(),i=Zo(),n=jv(),o=Xt(),s=Xo();let a=class extends i.ConnectionFactoryBase{constructor(){super(...arguments),this.universalUri="/speech/universal/v2"}create(r,a,c){let p=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Endpoint,void 0);const u=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Region,void 0),h=i.ConnectionFactoryBase.getHostSuffix(u),d=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Host,"wss://"+u+".stt.speech"+h),v={},l=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_EndpointId,void 0),g=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_RecoLanguage,void 0);l?p&&-1!==p.search(s.QueryParameterNames.CustomSpeechDeploymentId)||(v[s.QueryParameterNames.CustomSpeechDeploymentId]=l):g&&(p&&-1!==p.search(s.QueryParameterNames.Language)||(v[s.QueryParameterNames.Language]=g)),void 0!==r.autoDetectSourceLanguages&&(v[s.QueryParameterNames.EnableLanguageId]="true"),this.setV2UrlParams(r,v,p),p||(p=`${d}${this.universalUri}`);const f={};void 0!==a.token&&""!==a.token&&(f[a.headerName]=a.token),f[o.HeaderNames.ConnectionId]=c;const m="true"===r.parameters.getProperty("SPEECH-EnableWebsocketCompression","false"),S=new e.WebsocketConnection(p,v,f,new n.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(r),m,c),y=S.uri;return r.parameters.setProperty(t.PropertyId.SpeechServiceConnection_Url,y),S}setV2UrlParams(e,i,n){new Map([[t.PropertyId.Speech_SegmentationSilenceTimeoutMs,s.QueryParameterNames.SegmentationSilenceTimeoutMs],[t.PropertyId.SpeechServiceConnection_EnableAudioLogging,s.QueryParameterNames.EnableAudioLogging],[t.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,s.QueryParameterNames.EndSilenceTimeoutMs],[t.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,s.QueryParameterNames.InitialSilenceTimeoutMs],[t.PropertyId.SpeechServiceResponse_PostProcessingOption,s.QueryParameterNames.Postprocessing],[t.PropertyId.SpeechServiceResponse_ProfanityOption,s.QueryParameterNames.Profanity],[t.PropertyId.SpeechServiceResponse_StablePartialResultThreshold,s.QueryParameterNames.StableIntermediateThreshold]]).forEach(((t,r)=>{this.setUrlParameter(r,t,e,i,n)}));const o=JSON.parse(e.parameters.getProperty(r.ServicePropertiesPropertyName,"{}"));Object.keys(o).forEach((e=>{i[e]=o[e]}))}};return Pu.ConversationTranscriberConnectionFactory=a,Pu}var Iu,wu={};function Tu(){if(Iu)return wu;Iu=1,Object.defineProperty(wu,"__esModule",{value:!0}),wu.TranscriberConnectionFactory=void 0;const e=Vp(),t=Mp(),r=Zo(),i=jv(),n=Xt(),o=Xo();let s=class extends r.ConnectionFactoryBase{constructor(){super(...arguments),this.multiaudioRelativeUri="/speech/recognition/multiaudio"}create(o,s,a){let c=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Endpoint,void 0);const p=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Region,"centralus"),u="wss://transcribe."+p+".cts.speech"+r.ConnectionFactoryBase.getHostSuffix(p)+this.multiaudioRelativeUri,h=o.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Host,u),d={};this.setQueryParams(d,o,c),c||(c=h);const v={};void 0!==s.token&&""!==s.token&&(v[s.headerName]=s.token),v[n.HeaderNames.ConnectionId]=a,o.parameters.setProperty(t.PropertyId.SpeechServiceConnection_Url,c);const l="true"===o.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(c,d,v,new i.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(o),l,a)}setQueryParams(e,r,n){const s=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_EndpointId,void 0),a=r.parameters.getProperty(t.PropertyId.SpeechServiceConnection_RecoLanguage,void 0);s&&!(o.QueryParameterNames.CustomSpeechDeploymentId in e)&&(e[o.QueryParameterNames.CustomSpeechDeploymentId]=s),a&&!(o.QueryParameterNames.Language in e)&&(e[o.QueryParameterNames.Language]=a);const c="true"===r.parameters.getProperty(t.PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps,"false").toLowerCase(),p=r.parameters.getProperty(i.OutputFormatPropertyName,t.OutputFormat[t.OutputFormat.Simple])!==t.OutputFormat[t.OutputFormat.Simple];(c||p)&&(e[o.QueryParameterNames.Format]=t.OutputFormat[t.OutputFormat.Detailed].toLowerCase()),this.setCommonUrlParams(r,e,n)}};return wu.TranscriberConnectionFactory=s,wu}var Au,Eu={};function bu(){if(Au)return Eu;Au=1,Object.defineProperty(Eu,"__esModule",{value:!0}),Eu.TranslationConnectionFactory=void 0;const e=Vp(),t=_a(),r=Mp(),i=Zo(),n=jv(),o=Xt(),s=Xo();let a=class extends i.ConnectionFactoryBase{create(t,i,a){const c=this.getEndpointUrl(t),p={};void 0!==t.autoDetectSourceLanguages&&(p[s.QueryParameterNames.EnableLanguageId]="true"),this.setQueryParams(p,t,c);const u={};void 0!==i.token&&""!==i.token&&(u[i.headerName]=i.token),u[o.HeaderNames.ConnectionId]=a,t.parameters.setProperty(r.PropertyId.SpeechServiceConnection_Url,c);const h="true"===t.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(c,p,u,new n.WebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(t),h,a)}getEndpointUrl(e,n){const o=e.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Region),s=i.ConnectionFactoryBase.getHostSuffix(o);let a=e.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Endpoint,void 0);if(!a)if(void 0!==e.autoDetectSourceLanguages){a=e.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Host,"wss://{region}.stt.speech"+s)+"/speech/universal/v2"}else{a=e.parameters.getProperty(r.PropertyId.SpeechServiceConnection_Host,"wss://{region}.s2s.speech"+s)+"/speech/translation/cognitiveservices/v1"}return!0===n?a:t.StringUtils.formatString(a,{region:o})}setQueryParams(e,t,i){e.from=t.parameters.getProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage),e.to=t.parameters.getProperty(r.PropertyId.SpeechServiceConnection_TranslationToLanguages),e.scenario=t.recognitionMode===n.RecognitionMode.Interactive?"interactive":t.recognitionMode===n.RecognitionMode.Conversation?"conversation":"",this.setCommonUrlParams(t,e,i),this.setUrlParameter(r.PropertyId.SpeechServiceResponse_TranslationRequestStablePartialResult,s.QueryParameterNames.StableTranslation,t,e,i);const o=t.parameters.getProperty(r.PropertyId.SpeechServiceConnection_TranslationVoice,void 0);void 0!==o&&(e.voice=o,e.features="texttospeech")}};return Eu.TranslationConnectionFactory=a,Eu}var Ou,Mu={};function Du(){if(Ou)return Mu;Ou=1,Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.EnumTranslation=void 0;const e=Mp(),t=jv();return Mu.EnumTranslation=class{static implTranslateRecognitionResult(r,i=!1){let n=e.ResultReason.Canceled;switch(r){case t.RecognitionStatus.Success:n=e.ResultReason.RecognizedSpeech;break;case t.RecognitionStatus.EndOfDictation:n=i?e.ResultReason.RecognizedSpeech:e.ResultReason.NoMatch;break;case t.RecognitionStatus.NoMatch:case t.RecognitionStatus.InitialSilenceTimeout:case t.RecognitionStatus.BabbleTimeout:n=e.ResultReason.NoMatch;break;case t.RecognitionStatus.Error:case t.RecognitionStatus.BadRequest:case t.RecognitionStatus.Forbidden:default:n=e.ResultReason.Canceled}return n}static implTranslateCancelResult(r){let i=e.CancellationReason.EndOfStream;switch(r){case t.RecognitionStatus.Success:case t.RecognitionStatus.EndOfDictation:case t.RecognitionStatus.NoMatch:i=e.CancellationReason.EndOfStream;break;case t.RecognitionStatus.InitialSilenceTimeout:case t.RecognitionStatus.BabbleTimeout:case t.RecognitionStatus.Error:case t.RecognitionStatus.BadRequest:case t.RecognitionStatus.Forbidden:default:i=e.CancellationReason.Error}return i}static implTranslateCancelErrorCode(r){let i=e.CancellationErrorCode.NoError;switch(r){case t.RecognitionStatus.Error:i=e.CancellationErrorCode.ServiceError;break;case t.RecognitionStatus.TooManyRequests:i=e.CancellationErrorCode.TooManyRequests;break;case t.RecognitionStatus.BadRequest:i=e.CancellationErrorCode.BadRequestParameters;break;case t.RecognitionStatus.Forbidden:i=e.CancellationErrorCode.Forbidden;break;default:i=e.CancellationErrorCode.NoError}return i}static implTranslateErrorDetails(t){let r="The speech service encountered an internal error and could not continue.";switch(t){case e.CancellationErrorCode.Forbidden:r="The recognizer is using a free subscription that ran out of quota.";break;case e.CancellationErrorCode.BadRequestParameters:r="Invalid parameter or unsupported audio format in the request.";break;case e.CancellationErrorCode.TooManyRequests:r="The number of parallel requests exceeded the number of allowed concurrent transcriptions."}return r}},Mu}var ku,zu={};function Nu(){return ku||(ku=1,e=zu,Object.defineProperty(e,"__esModule",{value:!0}),e.RecognitionStatus=e.SynthesisStatus=void 0,(t=e.SynthesisStatus||(e.SynthesisStatus={}))[t.Success=0]="Success",t[t.SynthesisEnd=1]="SynthesisEnd",t[t.Error=2]="Error",(r=e.RecognitionStatus||(e.RecognitionStatus={}))[r.Success=0]="Success",r[r.NoMatch=1]="NoMatch",r[r.InitialSilenceTimeout=2]="InitialSilenceTimeout",r[r.BabbleTimeout=3]="BabbleTimeout",r[r.Error=4]="Error",r[r.EndOfDictation=5]="EndOfDictation",r[r.TooManyRequests=6]="TooManyRequests",r[r.BadRequest=7]="BadRequest",r[r.Forbidden=8]="Forbidden"),zu;var e,t,r}var _u,Lu={};function xu(){if(_u)return Lu;_u=1,Object.defineProperty(Lu,"__esModule",{value:!0}),Lu.TranslationSynthesisEnd=void 0;const e=jv();return Lu.TranslationSynthesisEnd=class t{constructor(t){this.privSynthesisEnd=JSON.parse(t),this.privSynthesisEnd.SynthesisStatus&&(this.privSynthesisEnd.SynthesisStatus=e.SynthesisStatus[this.privSynthesisEnd.SynthesisStatus]),this.privSynthesisEnd.Status&&(this.privSynthesisEnd.SynthesisStatus=e.SynthesisStatus[this.privSynthesisEnd.Status])}static fromJSON(e){return new t(e)}get SynthesisStatus(){return this.privSynthesisEnd.SynthesisStatus}get FailureReason(){return this.privSynthesisEnd.FailureReason}},Lu}var Fu,Bu={};function ju(){if(Fu)return Bu;Fu=1,Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.TranslationHypothesis=void 0;const e=Rr(),t=Dt();return Bu.TranslationHypothesis=class r{constructor(e,t){this.privTranslationHypothesis=e,this.privTranslationHypothesis.Offset+=t,this.privTranslationHypothesis.Translation.TranslationStatus=this.mapTranslationStatus(this.privTranslationHypothesis.Translation.TranslationStatus)}static fromJSON(e,t){return new r(JSON.parse(e),t)}static fromTranslationResponse(t,i){e.Contracts.throwIfNullOrUndefined(t,"translationHypothesis");const n=t.SpeechHypothesis;return t.SpeechHypothesis=void 0,n.Translation=t,new r(n,i)}get Duration(){return this.privTranslationHypothesis.Duration}get Offset(){return this.privTranslationHypothesis.Offset}get Text(){return this.privTranslationHypothesis.Text}get Translation(){return this.privTranslationHypothesis.Translation}get Language(){var e;return null==(e=this.privTranslationHypothesis.PrimaryLanguage)?void 0:e.Language}asJson(){const e={...this.privTranslationHypothesis};return void 0!==e.Translation?JSON.stringify({...e,TranslationStatus:t.TranslationStatus[e.Translation.TranslationStatus]}):JSON.stringify(e)}mapTranslationStatus(e){return"string"==typeof e?t.TranslationStatus[e]:"number"==typeof e?e:void 0}},Bu}var Uu,qu={};function Wu(){if(Uu)return qu;Uu=1,Object.defineProperty(qu,"__esModule",{value:!0}),qu.TranslationPhrase=void 0;const e=Rr(),t=jv(),r=Dt();return qu.TranslationPhrase=class i{constructor(e,t){this.privTranslationPhrase=e,this.privTranslationPhrase.Offset+=t,this.privTranslationPhrase.RecognitionStatus=this.mapRecognitionStatus(this.privTranslationPhrase.RecognitionStatus),void 0!==this.privTranslationPhrase.Translation&&(this.privTranslationPhrase.Translation.TranslationStatus=this.mapTranslationStatus(this.privTranslationPhrase.Translation.TranslationStatus))}static fromJSON(e,t){return new i(JSON.parse(e),t)}static fromTranslationResponse(t,r){e.Contracts.throwIfNullOrUndefined(t,"translationResponse");const n=t.SpeechPhrase;return t.SpeechPhrase=void 0,n.Translation=t,n.Text=n.DisplayText,new i(n,r)}get RecognitionStatus(){return this.privTranslationPhrase.RecognitionStatus}get Offset(){return this.privTranslationPhrase.Offset}get Duration(){return this.privTranslationPhrase.Duration}get Text(){return this.privTranslationPhrase.Text}get Language(){var e;return null==(e=this.privTranslationPhrase.PrimaryLanguage)?void 0:e.Language}get Confidence(){var e;return null==(e=this.privTranslationPhrase.PrimaryLanguage)?void 0:e.Confidence}get Translation(){return this.privTranslationPhrase.Translation}asJson(){const e={...this.privTranslationPhrase},i={...e,RecognitionStatus:t.RecognitionStatus[e.RecognitionStatus]};return e.Translation&&(i.Translation={...e.Translation,TranslationStatus:r.TranslationStatus[e.Translation.TranslationStatus]}),JSON.stringify(i)}mapRecognitionStatus(e){return"string"==typeof e?t.RecognitionStatus[e]:"number"==typeof e?e:void 0}mapTranslationStatus(e){return"string"==typeof e?r.TranslationStatus[e]:"number"==typeof e?e:void 0}},qu}var Hu,Vu={};function Ku(){if(Hu)return Vu;Hu=1,Object.defineProperty(Vu,"__esModule",{value:!0}),Vu.TranslationServiceRecognizer=void 0;const e=Gt(),t=Mp(),r=jv();let i=class extends r.ConversationServiceRecognizer{constructor(e,t,r,i,n){super(e,t,r,i,n),this.privTranslationRecognizer=n,this.connectionEvents.attach((e=>{"ConnectionEstablishedEvent"===e.name&&this.privTranslationRecognizer.onConnection()}))}async processTypeSpecificMessages(i){const n=new t.PropertyCollection;let o=await this.processSpeechMessages(i);if(o)return!0;const s=async e=>{if(n.setProperty(t.PropertyId.SpeechServiceResponse_JsonResult,e.asJson()),this.privRequestSession.onPhraseRecognized(e.Offset+e.Duration),e.RecognitionStatus===r.RecognitionStatus.Success){const t=this.fireEventForResult(e,n);if(this.privTranslationRecognizer.recognized)try{this.privTranslationRecognizer.recognized(this.privTranslationRecognizer,t)}catch(i){}if(this.privSuccessCallback){try{this.privSuccessCallback(t.result)}catch(s){this.privErrorCallback&&this.privErrorCallback(s)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}}else{const a=r.EnumTranslation.implTranslateRecognitionResult(e.RecognitionStatus),c=new t.TranslationRecognitionResult(void 0,this.privRequestSession.requestId,a,e.Text,e.Duration,e.Offset,e.Language,e.Confidence,void 0,e.asJson(),n);if(a===t.ResultReason.Canceled){const t=r.EnumTranslation.implTranslateCancelResult(e.RecognitionStatus),i=r.EnumTranslation.implTranslateCancelErrorCode(e.RecognitionStatus);await this.cancelRecognitionLocal(t,i,r.EnumTranslation.implTranslateErrorDetails(i))}else if(e.RecognitionStatus!==r.RecognitionStatus.EndOfDictation){const e=new t.TranslationRecognitionEventArgs(c,c.offset,this.privRequestSession.sessionId);if(this.privTranslationRecognizer.recognized)try{this.privTranslationRecognizer.recognized(this.privTranslationRecognizer,e)}catch(i){}if(this.privSuccessCallback){try{this.privSuccessCallback(c)}catch(s){this.privErrorCallback&&this.privErrorCallback(s)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}}o=!0}},a=e=>{n.setProperty(t.PropertyId.SpeechServiceResponse_JsonResult,e.asJson());const r=this.fireEventForResult(e,n);if(this.privRequestSession.onHypothesis(r.offset),this.privTranslationRecognizer.recognizing)try{this.privTranslationRecognizer.recognizing(this.privTranslationRecognizer,r)}catch(i){}o=!0};switch(i.messageType===e.MessageType.Text&&n.setProperty(t.PropertyId.SpeechServiceResponse_JsonResult,i.textBody),i.path.toLowerCase()){case"translation.hypothesis":a(r.TranslationHypothesis.fromJSON(i.textBody,this.privRequestSession.currentTurnAudioOffset));break;case"translation.response":const e=JSON.parse(i.textBody);if(e.SpeechPhrase)await s(r.TranslationPhrase.fromTranslationResponse(e,this.privRequestSession.currentTurnAudioOffset));else{const e=JSON.parse(i.textBody);e.SpeechHypothesis&&a(r.TranslationHypothesis.fromTranslationResponse(e,this.privRequestSession.currentTurnAudioOffset))}break;case"translation.phrase":await s(r.TranslationPhrase.fromJSON(i.textBody,this.privRequestSession.currentTurnAudioOffset));break;case"translation.synthesis":this.sendSynthesisAudio(i.binaryBody,this.privRequestSession.sessionId),o=!0;break;case"audio.end":case"translation.synthesis.end":const n=r.TranslationSynthesisEnd.fromJSON(i.textBody);switch(n.SynthesisStatus){case r.SynthesisStatus.Error:if(this.privTranslationRecognizer.synthesizing){const e=new t.TranslationSynthesisResult(t.ResultReason.Canceled,void 0),r=new t.TranslationSynthesisEventArgs(e,this.privRequestSession.sessionId);try{this.privTranslationRecognizer.synthesizing(this.privTranslationRecognizer,r)}catch(c){}}if(this.privTranslationRecognizer.canceled){const e=new t.TranslationRecognitionCanceledEventArgs(this.privRequestSession.sessionId,t.CancellationReason.Error,n.FailureReason,t.CancellationErrorCode.ServiceError,null);try{this.privTranslationRecognizer.canceled(this.privTranslationRecognizer,e)}catch(c){}}break;case r.SynthesisStatus.Success:this.sendSynthesisAudio(void 0,this.privRequestSession.sessionId)}o=!0}return o}cancelRecognition(e,i,n,o,s){const a=new t.PropertyCollection;if(a.setProperty(r.CancellationErrorCodePropertyName,t.CancellationErrorCode[o]),this.privTranslationRecognizer.canceled){const r=new t.TranslationRecognitionCanceledEventArgs(e,n,s,o,void 0);try{this.privTranslationRecognizer.canceled(this.privTranslationRecognizer,r)}catch{}}if(this.privSuccessCallback){const e=new t.TranslationRecognitionResult(void 0,i,t.ResultReason.Canceled,void 0,void 0,void 0,void 0,void 0,s,void 0,a);try{this.privSuccessCallback(e),this.privSuccessCallback=void 0}catch{}}}handleRecognizingCallback(e,r,i){try{const n=new t.TranslationRecognitionEventArgs(t.TranslationRecognitionResult.fromSpeechRecognitionResult(e),r,i);this.privTranslationRecognizer.recognizing(this.privTranslationRecognizer,n)}catch(n){}}handleRecognizedCallback(e,r,i){try{const n=new t.TranslationRecognitionEventArgs(t.TranslationRecognitionResult.fromSpeechRecognitionResult(e),r,i);this.privTranslationRecognizer.recognized(this.privTranslationRecognizer,n)}catch(n){}}fireEventForResult(i,n){let o,s,a;if(void 0!==i.Translation.Translations){o=new t.Translations;for(const e of i.Translation.Translations)o.set(e.Language,e.Text||e.DisplayText)}i instanceof r.TranslationPhrase?(s=i.Translation&&i.Translation.TranslationStatus===e.TranslationStatus.Success?t.ResultReason.TranslatedSpeech:t.ResultReason.RecognizedSpeech,a=i.Confidence):s=t.ResultReason.TranslatingSpeech;const c=i.Language,p=new t.TranslationRecognitionResult(o,this.privRequestSession.requestId,s,i.Text,i.Duration,i.Offset,c,a,i.Translation.FailureReason,i.asJson(),n);return new t.TranslationRecognitionEventArgs(p,i.Offset,this.privRequestSession.sessionId)}sendSynthesisAudio(e,r){const i=void 0===e?t.ResultReason.SynthesizingAudioCompleted:t.ResultReason.SynthesizingAudio,n=new t.TranslationSynthesisResult(i,e),o=new t.TranslationSynthesisEventArgs(n,r);if(this.privTranslationRecognizer.synthesizing)try{this.privTranslationRecognizer.synthesizing(this.privTranslationRecognizer,o)}catch(s){}}};return Vu.TranslationServiceRecognizer=i,Vu}var Ju,Gu={};function $u(){if(Ju)return Gu;Ju=1,Object.defineProperty(Gu,"__esModule",{value:!0}),Gu.SpeechDetected=void 0;return Gu.SpeechDetected=class e{constructor(e,t){this.privSpeechStartDetected=JSON.parse(e),this.privSpeechStartDetected.Offset+=t}static fromJSON(t,r){return new e(t,r)}get Offset(){return this.privSpeechStartDetected.Offset}},Gu}var Qu,Xu={};function Zu(){if(Qu)return Xu;Qu=1,Object.defineProperty(Xu,"__esModule",{value:!0}),Xu.SpeechHypothesis=void 0;return Xu.SpeechHypothesis=class e{constructor(e,t){this.privSpeechHypothesis=JSON.parse(e),this.updateOffset(t)}static fromJSON(t,r){return new e(t,r)}updateOffset(e){this.privSpeechHypothesis.Offset+=e}asJson(){return JSON.stringify(this.privSpeechHypothesis)}get Text(){return this.privSpeechHypothesis.Text}get Offset(){return this.privSpeechHypothesis.Offset}get Duration(){return this.privSpeechHypothesis.Duration}get Language(){return void 0===this.privSpeechHypothesis.PrimaryLanguage?void 0:this.privSpeechHypothesis.PrimaryLanguage.Language}get LanguageDetectionConfidence(){return void 0===this.privSpeechHypothesis.PrimaryLanguage?void 0:this.privSpeechHypothesis.PrimaryLanguage.Confidence}get SpeakerId(){return this.privSpeechHypothesis.SpeakerId}},Xu}var Yu,eh={};function th(){if(Yu)return eh;Yu=1,Object.defineProperty(eh,"__esModule",{value:!0}),eh.SpeechKeyword=void 0;return eh.SpeechKeyword=class e{constructor(e,t){this.privSpeechKeyword=JSON.parse(e),this.privSpeechKeyword.Offset+=t}static fromJSON(t,r){return new e(t,r)}get Status(){return this.privSpeechKeyword.Status}get Text(){return this.privSpeechKeyword.Text}get Offset(){return this.privSpeechKeyword.Offset}get Duration(){return this.privSpeechKeyword.Duration}asJson(){return JSON.stringify(this.privSpeechKeyword)}},eh}var rh,ih={};function nh(){if(rh)return ih;rh=1,Object.defineProperty(ih,"__esModule",{value:!0}),ih.SpeechServiceRecognizer=void 0;const e=Mp(),t=jv();let r=class extends t.ServiceRecognizerBase{constructor(e,t,r,i,n){super(e,t,r,i,n),this.privSpeechRecognizer=n}async processTypeSpecificMessages(r){let i;const n=new e.PropertyCollection;let o=!1;switch(r.path.toLowerCase()){case"speech.hypothesis":case"speech.fragment":const c=t.SpeechHypothesis.fromJSON(r.textBody,this.privRequestSession.currentTurnAudioOffset);n.setProperty(e.PropertyId.SpeechServiceResponse_JsonResult,c.asJson()),i=new e.SpeechRecognitionResult(this.privRequestSession.requestId,e.ResultReason.RecognizingSpeech,c.Text,c.Duration,c.Offset,c.Language,c.LanguageDetectionConfidence,void 0,void 0,c.asJson(),n),this.privRequestSession.onHypothesis(c.Offset);const p=new e.SpeechRecognitionEventArgs(i,c.Offset,this.privRequestSession.sessionId);if(this.privSpeechRecognizer.recognizing)try{this.privSpeechRecognizer.recognizing(this.privSpeechRecognizer,p)}catch(s){}o=!0;break;case"speech.phrase":const u=t.SimpleSpeechPhrase.fromJSON(r.textBody,this.privRequestSession.currentTurnAudioOffset);n.setProperty(e.PropertyId.SpeechServiceResponse_JsonResult,u.asJson());const h=t.EnumTranslation.implTranslateRecognitionResult(u.RecognitionStatus,this.privExpectContentAssessmentResponse);if(this.privRequestSession.onPhraseRecognized(u.Offset+u.Duration),e.ResultReason.Canceled===h){const e=t.EnumTranslation.implTranslateCancelResult(u.RecognitionStatus),r=t.EnumTranslation.implTranslateCancelErrorCode(u.RecognitionStatus);await this.cancelRecognitionLocal(e,r,t.EnumTranslation.implTranslateErrorDetails(r))}else{if(u.RecognitionStatus===t.RecognitionStatus.EndOfDictation)break;if(this.privRecognizerConfig.parameters.getProperty(t.OutputFormatPropertyName)===e.OutputFormat[e.OutputFormat.Simple])i=new e.SpeechRecognitionResult(this.privRequestSession.requestId,h,u.DisplayText,u.Duration,u.Offset,u.Language,u.LanguageDetectionConfidence,void 0,void 0,u.asJson(),n);else{const o=t.DetailedSpeechPhrase.fromJSON(r.textBody,this.privRequestSession.currentTurnAudioOffset);n.setProperty(e.PropertyId.SpeechServiceResponse_JsonResult,o.asJson()),i=new e.SpeechRecognitionResult(this.privRequestSession.requestId,h,o.RecognitionStatus===t.RecognitionStatus.Success?o.NBest[0].Display:"",o.Duration,o.Offset,o.Language,o.LanguageDetectionConfidence,void 0,void 0,o.asJson(),n)}const o=new e.SpeechRecognitionEventArgs(i,i.offset,this.privRequestSession.sessionId);if(this.privSpeechRecognizer.recognized)try{this.privSpeechRecognizer.recognized(this.privSpeechRecognizer,o)}catch(s){}if(this.privSuccessCallback){try{this.privSuccessCallback(i)}catch(a){this.privErrorCallback&&this.privErrorCallback(a)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}}o=!0}return o}cancelRecognition(r,i,n,o,s){const a=new e.PropertyCollection;if(a.setProperty(t.CancellationErrorCodePropertyName,e.CancellationErrorCode[o]),this.privSpeechRecognizer.canceled){const t=new e.SpeechRecognitionCanceledEventArgs(n,s,o,void 0,r);try{this.privSpeechRecognizer.canceled(this.privSpeechRecognizer,t)}catch{}}if(this.privSuccessCallback){const t=new e.SpeechRecognitionResult(i,e.ResultReason.Canceled,void 0,void 0,void 0,void 0,void 0,void 0,s,void 0,a);try{this.privSuccessCallback(t),this.privSuccessCallback=void 0}catch{}}}};return ih.SpeechServiceRecognizer=r,ih}var oh,sh={};function ah(){if(oh)return sh;oh=1,Object.defineProperty(sh,"__esModule",{value:!0}),sh.ConversationTranscriptionServiceRecognizer=void 0;const e=Mp(),t=jv();let r=class extends t.ServiceRecognizerBase{constructor(e,t,r,i,n){super(e,t,r,i,n),this.privConversationTranscriber=n,this.setSpeakerDiarizationJson()}setSpeakerDiarizationJson(){if(this.privEnableSpeakerId){const t=this.privSpeechContext.getSection("phraseDetection");t.mode="Conversation";const r={mode:"Anonymous"};r.audioSessionId=this.privDiarizationSessionId,r.audioOffsetMs=0,r.diarizeIntermediates="true"===this.privRecognizerConfig.parameters.getProperty(e.PropertyId.SpeechServiceResponse_DiarizeIntermediateResults,"false"),t.speakerDiarization=r,this.privSpeechContext.setSection("phraseDetection",t)}}async processTypeSpecificMessages(r){let i;const n=new e.PropertyCollection;n.setProperty(e.PropertyId.SpeechServiceResponse_JsonResult,r.textBody);let o=!1;switch(r.path.toLowerCase()){case"speech.hypothesis":case"speech.fragment":const a=t.SpeechHypothesis.fromJSON(r.textBody,this.privRequestSession.currentTurnAudioOffset);i=new e.ConversationTranscriptionResult(this.privRequestSession.requestId,e.ResultReason.RecognizingSpeech,a.Text,a.Duration,a.Offset,a.Language,a.LanguageDetectionConfidence,a.SpeakerId,void 0,a.asJson(),n),this.privRequestSession.onHypothesis(a.Offset);const c=new e.ConversationTranscriptionEventArgs(i,a.Duration,this.privRequestSession.sessionId);if(this.privConversationTranscriber.transcribing)try{this.privConversationTranscriber.transcribing(this.privConversationTranscriber,c)}catch(s){}o=!0;break;case"speech.phrase":const p=t.SimpleSpeechPhrase.fromJSON(r.textBody,this.privRequestSession.currentTurnAudioOffset),u=t.EnumTranslation.implTranslateRecognitionResult(p.RecognitionStatus);if(this.privRequestSession.onPhraseRecognized(p.Offset+p.Duration),e.ResultReason.Canceled===u){const e=t.EnumTranslation.implTranslateCancelResult(p.RecognitionStatus),r=t.EnumTranslation.implTranslateCancelErrorCode(p.RecognitionStatus);await this.cancelRecognitionLocal(e,r,t.EnumTranslation.implTranslateErrorDetails(r))}else if(!this.privRequestSession.isSpeechEnded||u!==e.ResultReason.NoMatch||p.RecognitionStatus===t.RecognitionStatus.InitialSilenceTimeout){if(this.privRecognizerConfig.parameters.getProperty(t.OutputFormatPropertyName)===e.OutputFormat[e.OutputFormat.Simple])i=new e.ConversationTranscriptionResult(this.privRequestSession.requestId,u,p.DisplayText,p.Duration,p.Offset,p.Language,p.LanguageDetectionConfidence,p.SpeakerId,void 0,p.asJson(),n);else{const o=t.DetailedSpeechPhrase.fromJSON(r.textBody,this.privRequestSession.currentTurnAudioOffset);i=new e.ConversationTranscriptionResult(this.privRequestSession.requestId,u,o.RecognitionStatus===t.RecognitionStatus.Success?o.NBest[0].Display:void 0,o.Duration,o.Offset,o.Language,o.LanguageDetectionConfidence,p.SpeakerId,void 0,o.asJson(),n)}const o=new e.ConversationTranscriptionEventArgs(i,i.offset,this.privRequestSession.sessionId);if(this.privConversationTranscriber.transcribed)try{this.privConversationTranscriber.transcribed(this.privConversationTranscriber,o)}catch(s){}}o=!0}return o}cancelRecognition(r,i,n,o,s){if((new e.PropertyCollection).setProperty(t.CancellationErrorCodePropertyName,e.CancellationErrorCode[o]),this.privConversationTranscriber.canceled){const t=new e.ConversationTranscriptionCanceledEventArgs(n,s,o,void 0,r);try{this.privConversationTranscriber.canceled(this.privConversationTranscriber,t)}catch{}}}};return sh.ConversationTranscriptionServiceRecognizer=r,sh}var ch,ph={};function uh(){if(ch)return ph;ch=1,Object.defineProperty(ph,"__esModule",{value:!0}),ph.TranscriptionServiceRecognizer=void 0;const e=Gt(),t=Mp(),r=jv(),i=iu();let n=class extends r.ConversationServiceRecognizer{constructor(e,r,i,n,o){super(e,r,i,n,o),this.privTranscriberRecognizer=o,this.sendPrePayloadJSONOverride=e=>this.sendTranscriptionStartJSON(e),"true"===this.privRecognizerConfig.parameters.getProperty(t.PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps)&&this.privSpeechContext.setWordLevelTimings()}async sendSpeechEventAsync(e,t){if(this.privRequestSession.isRecognizing){const r=await this.fetchConnection();await this.sendSpeechEvent(r,this.createSpeechEventPayload(e,t))}}async sendMeetingSpeechEventAsync(e,t){if(this.privRequestSession.isRecognizing){const r=await this.fetchConnection();await this.sendSpeechEvent(r,this.createMeetingSpeechEventPayload(e,t))}}processTypeSpecificMessages(e){return this.processSpeechMessages(e)}handleRecognizedCallback(e,r,i){try{const o=new t.SpeechRecognitionEventArgs(e,r,i);if(this.privTranscriberRecognizer.recognized(this.privTranscriberRecognizer,o),this.privSuccessCallback){try{this.privSuccessCallback(e)}catch(n){this.privErrorCallback&&this.privErrorCallback(n)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}}catch(o){}}handleRecognizingCallback(e,r,i){try{const n=new t.SpeechRecognitionEventArgs(e,r,i);this.privTranscriberRecognizer.recognizing(this.privTranscriberRecognizer,n)}catch(n){}}cancelRecognition(e,i,n,o,s){const a=new t.PropertyCollection;if(a.setProperty(r.CancellationErrorCodePropertyName,t.CancellationErrorCode[o]),this.privTranscriberRecognizer.IsMeetingRecognizer()){if(this.privTranscriberRecognizer.canceled){const r=new t.MeetingTranscriptionCanceledEventArgs(n,s,o,void 0,e);try{this.privTranscriberRecognizer.canceled(this.privTranscriberRecognizer,r)}catch{}}}else if(this.privTranscriberRecognizer.canceled){const r=new t.ConversationTranscriptionCanceledEventArgs(n,s,o,void 0,e);try{this.privTranscriberRecognizer.canceled(this.privTranscriberRecognizer,r)}catch{}}if(this.privSuccessCallback){const e=new t.SpeechRecognitionResult(i,t.ResultReason.Canceled,void 0,void 0,void 0,void 0,void 0,void 0,s,void 0,a);try{this.privSuccessCallback(e),this.privSuccessCallback=void 0}catch{}}}async sendTranscriptionStartJSON(e){if(await this.sendSpeechContext(e,!0),this.privTranscriberRecognizer.IsMeetingRecognizer()){const t=this.privTranscriberRecognizer.getMeetingInfo(),r=this.createMeetingSpeechEventPayload(t,"start");await this.sendSpeechEvent(e,r)}else{const t=this.privTranscriberRecognizer.getConversationInfo(),r=this.createSpeechEventPayload(t,"start");await this.sendSpeechEvent(e,r)}await this.sendWaveHeader(e)}sendSpeechEvent(t,r){const n=JSON.stringify(r);if(n)return t.send(new i.SpeechConnectionMessage(e.MessageType.Text,"speech.event",this.privRequestSession.requestId,"application/json",n))}createSpeechEventPayload(e,t){const r={id:"meeting",name:t,meeting:e.conversationProperties};return r.meeting.id=e.id,r.meeting.attendees=e.participants,r}createMeetingSpeechEventPayload(e,t){const r={id:"meeting",name:t,meeting:e.meetingProperties};return r.meeting.id=e.id,r.meeting.attendees=e.participants,r}};return ph.TranscriptionServiceRecognizer=n,ph}var hh,dh={};function vh(){if(hh)return dh;hh=1,Object.defineProperty(dh,"__esModule",{value:!0}),dh.DetailedSpeechPhrase=void 0;const e=jv();return dh.DetailedSpeechPhrase=class t{constructor(e,t){this.privDetailedSpeechPhrase=JSON.parse(e),this.privDetailedSpeechPhrase.RecognitionStatus=this.mapRecognitionStatus(this.privDetailedSpeechPhrase.RecognitionStatus),this.updateOffsets(t)}static fromJSON(e,r){return new t(e,r)}updateOffsets(e){if(this.privDetailedSpeechPhrase.Offset+=e,this.privDetailedSpeechPhrase.NBest)for(const t of this.privDetailedSpeechPhrase.NBest){if(t.Words)for(const r of t.Words)r.Offset+=e;if(t.DisplayWords)for(const r of t.DisplayWords)r.Offset+=e}}asJson(){const t={...this.privDetailedSpeechPhrase};return JSON.stringify({...t,RecognitionStatus:e.RecognitionStatus[t.RecognitionStatus]})}get RecognitionStatus(){return this.privDetailedSpeechPhrase.RecognitionStatus}get NBest(){return this.privDetailedSpeechPhrase.NBest}get Duration(){return this.privDetailedSpeechPhrase.Duration}get Offset(){return this.privDetailedSpeechPhrase.Offset}get Language(){return void 0===this.privDetailedSpeechPhrase.PrimaryLanguage?void 0:this.privDetailedSpeechPhrase.PrimaryLanguage.Language}get LanguageDetectionConfidence(){return void 0===this.privDetailedSpeechPhrase.PrimaryLanguage?void 0:this.privDetailedSpeechPhrase.PrimaryLanguage.Confidence}get Text(){return this.privDetailedSpeechPhrase.NBest&&this.privDetailedSpeechPhrase.NBest[0]?this.privDetailedSpeechPhrase.NBest[0].Display||this.privDetailedSpeechPhrase.NBest[0].DisplayText:this.privDetailedSpeechPhrase.DisplayText}get SpeakerId(){return this.privDetailedSpeechPhrase.SpeakerId}mapRecognitionStatus(t){return"string"==typeof t?e.RecognitionStatus[t]:"number"==typeof t?t:void 0}},dh}var lh,gh={};function fh(){if(lh)return gh;lh=1,Object.defineProperty(gh,"__esModule",{value:!0}),gh.SimpleSpeechPhrase=void 0;const e=jv();return gh.SimpleSpeechPhrase=class t{constructor(e,t=0){this.privSimpleSpeechPhrase=JSON.parse(e),this.privSimpleSpeechPhrase.RecognitionStatus=this.mapRecognitionStatus(this.privSimpleSpeechPhrase.RecognitionStatus),this.updateOffset(t)}static fromJSON(e,r){return new t(e,r)}updateOffset(e){this.privSimpleSpeechPhrase.Offset+=e}asJson(){const t={...this.privSimpleSpeechPhrase};return JSON.stringify({...t,RecognitionStatus:e.RecognitionStatus[t.RecognitionStatus]})}get RecognitionStatus(){return this.privSimpleSpeechPhrase.RecognitionStatus}get DisplayText(){return this.privSimpleSpeechPhrase.DisplayText}get Offset(){return this.privSimpleSpeechPhrase.Offset}get Duration(){return this.privSimpleSpeechPhrase.Duration}get Language(){return void 0===this.privSimpleSpeechPhrase.PrimaryLanguage?void 0:this.privSimpleSpeechPhrase.PrimaryLanguage.Language}get LanguageDetectionConfidence(){return void 0===this.privSimpleSpeechPhrase.PrimaryLanguage?void 0:this.privSimpleSpeechPhrase.PrimaryLanguage.Confidence}get SpeakerId(){return this.privSimpleSpeechPhrase.SpeakerId}mapRecognitionStatus(t){return"string"==typeof t?e.RecognitionStatus[t]:"number"==typeof t?t:void 0}},gh}var mh,Sh={};function yh(){if(mh)return Sh;mh=1,Object.defineProperty(Sh,"__esModule",{value:!0}),Sh.AddedLmIntent=void 0;return Sh.AddedLmIntent=class{constructor(e,t){this.modelImpl=e,this.intentName=t}},Sh}var Ch,Ph={};function Rh(){if(Ch)return Ph;Ch=1,Object.defineProperty(Ph,"__esModule",{value:!0}),Ph.IntentServiceRecognizer=void 0;const e=Gt(),t=Mp(),r=jv();let i=class extends r.ServiceRecognizerBase{constructor(e,t,r,i,n){super(e,t,r,i,n),this.privIntentRecognizer=n,this.privIntentDataSent=!1}setIntents(e,t){this.privAddedLmIntents=e,this.privUmbrellaIntent=t,this.privIntentDataSent=!0}processTypeSpecificMessages(i){let n,o,s=!1;const a=new t.PropertyCollection;switch(i.messageType===e.MessageType.Text&&a.setProperty(t.PropertyId.SpeechServiceResponse_JsonResult,i.textBody),i.path.toLowerCase()){case"speech.hypothesis":const e=r.SpeechHypothesis.fromJSON(i.textBody,this.privRequestSession.currentTurnAudioOffset);if(n=new t.IntentRecognitionResult(void 0,this.privRequestSession.requestId,t.ResultReason.RecognizingIntent,e.Text,e.Duration,e.Offset,e.Language,e.LanguageDetectionConfidence,void 0,e.asJson(),a),this.privRequestSession.onHypothesis(n.offset),o=new t.IntentRecognitionEventArgs(n,e.Offset,this.privRequestSession.sessionId),this.privIntentRecognizer.recognizing)try{this.privIntentRecognizer.recognizing(this.privIntentRecognizer,o)}catch(p){}s=!0;break;case"speech.phrase":const c=r.SimpleSpeechPhrase.fromJSON(i.textBody,this.privRequestSession.currentTurnAudioOffset);n=new t.IntentRecognitionResult(void 0,this.privRequestSession.requestId,r.EnumTranslation.implTranslateRecognitionResult(c.RecognitionStatus),c.DisplayText,c.Duration,c.Offset,c.Language,c.LanguageDetectionConfidence,void 0,c.asJson(),a),o=new t.IntentRecognitionEventArgs(n,n.offset,this.privRequestSession.sessionId);const h=()=>{if(this.privIntentRecognizer.recognized)try{this.privIntentRecognizer.recognized(this.privIntentRecognizer,o)}catch(p){}if(this.privSuccessCallback){try{this.privSuccessCallback(n)}catch(e){this.privErrorCallback&&this.privErrorCallback(e)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}};!1===this.privIntentDataSent||t.ResultReason.NoMatch===o.result.reason?(this.privRequestSession.onPhraseRecognized(o.offset+o.result.duration),h()):this.privPendingIntentArgs=o,s=!0;break;case"response":if(o=this.privPendingIntentArgs,this.privPendingIntentArgs=void 0,void 0===o){if(""===i.textBody)return;o=new t.IntentRecognitionEventArgs(new t.IntentRecognitionResult,0,this.privRequestSession.sessionId)}const d=r.IntentResponse.fromJSON(i.textBody);if(null!==d&&d.topScoringIntent&&d.topScoringIntent.intent){let e=this.privAddedLmIntents[d.topScoringIntent.intent];if(void 0!==this.privUmbrellaIntent&&(e=this.privUmbrellaIntent),e){const r=void 0===e||void 0===e.intentName?d.topScoringIntent.intent:e.intentName;let n=o.result.reason;void 0!==r&&(n=t.ResultReason.RecognizedIntent);const s=void 0!==o.result.properties?o.result.properties:new t.PropertyCollection;s.setProperty(t.PropertyId.LanguageUnderstandingServiceResponse_JsonResult,i.textBody),o=new t.IntentRecognitionEventArgs(new t.IntentRecognitionResult(r,o.result.resultId,n,o.result.text,o.result.duration,o.result.offset,void 0,void 0,o.result.errorDetails,o.result.json,s),o.offset,o.sessionId)}}if(this.privRequestSession.onPhraseRecognized(o.offset+o.result.duration),this.privIntentRecognizer.recognized)try{this.privIntentRecognizer.recognized(this.privIntentRecognizer,o)}catch(p){}if(this.privSuccessCallback){try{this.privSuccessCallback(o.result)}catch(u){this.privErrorCallback&&this.privErrorCallback(u)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}s=!0}const c=new e.Deferred;return c.resolve(s),c.promise}cancelRecognition(e,i,n,o,s){const a=new t.PropertyCollection;if(a.setProperty(r.CancellationErrorCodePropertyName,t.CancellationErrorCode[o]),this.privIntentRecognizer.canceled){const r=new t.IntentRecognitionCanceledEventArgs(n,s,o,void 0,void 0,e);try{this.privIntentRecognizer.canceled(this.privIntentRecognizer,r)}catch{}}if(this.privSuccessCallback){const e=new t.IntentRecognitionResult(void 0,i,t.ResultReason.Canceled,void 0,void 0,void 0,void 0,void 0,s,void 0,a);try{this.privSuccessCallback(e),this.privSuccessCallback=void 0}catch{}}}};return Ph.IntentServiceRecognizer=i,Ph}var Ih,wh={};function Th(){if(Ih)return wh;Ih=1,Object.defineProperty(wh,"__esModule",{value:!0}),wh.IntentResponse=void 0;return wh.IntentResponse=class e{constructor(e){this.privIntentResponse=""===e?{}:JSON.parse(e)}static fromJSON(t){return new e(t)}get query(){return this.privIntentResponse.query}get topScoringIntent(){return this.privIntentResponse.topScoringIntent}get entities(){return this.privIntentResponse.entities}},wh}var Ah,Eh={};function bh(){return Ah||(Ah=1,Object.defineProperty(Eh,"__esModule",{value:!0})),Eh}var Oh,Mh,Dh={},kh={};function zh(){if(Oh)return kh;Oh=1,Object.defineProperty(kh,"__esModule",{value:!0}),kh.ServiceTelemetryListener=void 0;const e=Gt(),t=Zp();return kh.ServiceTelemetryListener=class{constructor(e,t,r){this.privIsDisposed=!1,this.privListeningTriggerMetric=null,this.privMicMetric=null,this.privConnectionEstablishMetric=null,this.privRequestId=e,this.privAudioSourceId=t,this.privAudioNodeId=r,this.privReceivedMessages={},this.privPhraseLatencies=[],this.privHypothesisLatencies=[]}phraseReceived(e){e>0&&this.privPhraseLatencies.push(Date.now()-e)}hypothesisReceived(e){e>0&&this.privHypothesisLatencies.push(Date.now()-e)}onEvent(r){if(!this.privIsDisposed&&(r instanceof t.RecognitionTriggeredEvent&&r.requestId===this.privRequestId&&(this.privListeningTriggerMetric={End:r.eventTime,Name:"ListeningTrigger",Start:r.eventTime}),r instanceof e.AudioStreamNodeAttachingEvent&&r.audioSourceId===this.privAudioSourceId&&r.audioNodeId===this.privAudioNodeId&&(this.privMicStartTime=r.eventTime),r instanceof e.AudioStreamNodeAttachedEvent&&r.audioSourceId===this.privAudioSourceId&&r.audioNodeId===this.privAudioNodeId&&(this.privMicStartTime=r.eventTime),r instanceof e.AudioSourceErrorEvent&&r.audioSourceId===this.privAudioSourceId&&(this.privMicMetric||(this.privMicMetric={End:r.eventTime,Error:r.error,Name:"Microphone",Start:this.privMicStartTime})),r instanceof e.AudioStreamNodeErrorEvent&&r.audioSourceId===this.privAudioSourceId&&r.audioNodeId===this.privAudioNodeId&&(this.privMicMetric||(this.privMicMetric={End:r.eventTime,Error:r.error,Name:"Microphone",Start:this.privMicStartTime})),r instanceof e.AudioStreamNodeDetachedEvent&&r.audioSourceId===this.privAudioSourceId&&r.audioNodeId===this.privAudioNodeId&&(this.privMicMetric||(this.privMicMetric={End:r.eventTime,Name:"Microphone",Start:this.privMicStartTime})),r instanceof t.ConnectingToServiceEvent&&r.requestId===this.privRequestId&&(this.privConnectionId=r.sessionId),r instanceof e.ConnectionStartEvent&&r.connectionId===this.privConnectionId&&(this.privConnectionStartTime=r.eventTime),r instanceof e.ConnectionEstablishedEvent&&r.connectionId===this.privConnectionId&&(this.privConnectionEstablishMetric||(this.privConnectionEstablishMetric={End:r.eventTime,Id:this.privConnectionId,Name:"Connection",Start:this.privConnectionStartTime})),r instanceof e.ConnectionEstablishErrorEvent&&r.connectionId===this.privConnectionId&&(this.privConnectionEstablishMetric||(this.privConnectionEstablishMetric={End:r.eventTime,Error:this.getConnectionError(r.statusCode),Id:this.privConnectionId,Name:"Connection",Start:this.privConnectionStartTime})),r instanceof e.ConnectionMessageReceivedEvent&&r.connectionId===this.privConnectionId&&r.message&&r.message.headers&&r.message.headers.path)){this.privReceivedMessages[r.message.headers.path]||(this.privReceivedMessages[r.message.headers.path]=new Array);const e=50;this.privReceivedMessages[r.message.headers.path].length<e&&this.privReceivedMessages[r.message.headers.path].push(r.networkReceivedTime)}}getTelemetry(){const e=new Array;this.privListeningTriggerMetric&&e.push(this.privListeningTriggerMetric),this.privMicMetric&&e.push(this.privMicMetric),this.privConnectionEstablishMetric&&e.push(this.privConnectionEstablishMetric),this.privPhraseLatencies.length>0&&e.push({PhraseLatencyMs:this.privPhraseLatencies}),this.privHypothesisLatencies.length>0&&e.push({FirstHypothesisLatencyMs:this.privHypothesisLatencies});const t={Metrics:e,ReceivedMessages:this.privReceivedMessages},r=JSON.stringify(t);return this.privReceivedMessages={},this.privListeningTriggerMetric=null,this.privMicMetric=null,this.privConnectionEstablishMetric=null,this.privPhraseLatencies=[],this.privHypothesisLatencies=[],r}get hasTelemetry(){return 0!==Object.keys(this.privReceivedMessages).length||null!==this.privListeningTriggerMetric||null!==this.privMicMetric||null!==this.privConnectionEstablishMetric||0!==this.privPhraseLatencies.length||0!==this.privHypothesisLatencies.length}dispose(){this.privIsDisposed=!0}getConnectionError(e){switch(e){case 400:case 1002:case 1003:case 1005:case 1007:case 1008:case 1009:return"BadRequest";case 401:return"Unauthorized";case 403:return"Forbidden";case 503:case 1001:return"ServerUnavailable";case 500:case 1011:return"ServerError";case 408:case 504:return"Timeout";default:return"statuscode:"+e.toString()}}},kh}function Nh(){if(Mh)return Dh;Mh=1,Object.defineProperty(Dh,"__esModule",{value:!0}),Dh.RequestSession=void 0;const e=Gt(),t=Zp(),r=zh();return Dh.RequestSession=class{constructor(t){this.privIsDisposed=!1,this.privDetachables=new Array,this.privIsAudioNodeDetached=!1,this.privIsRecognizing=!1,this.privIsSpeechEnded=!1,this.privTurnStartAudioOffset=0,this.privLastRecoOffset=0,this.privHypothesisReceived=!1,this.privBytesSent=0,this.privRecognitionBytesSent=0,this.privRecogNumber=0,this.privInTurn=!1,this.privConnectionAttempts=0,this.privAudioSourceId=t,this.privRequestId=e.createNoDashGuid(),this.privAudioNodeId=e.createNoDashGuid(),this.privTurnDeferral=new e.Deferred,this.privTurnDeferral.resolve()}get sessionId(){return this.privSessionId}get requestId(){return this.privRequestId}get audioNodeId(){return this.privAudioNodeId}get turnCompletionPromise(){return this.privTurnDeferral.promise}get isSpeechEnded(){return this.privIsSpeechEnded}get isRecognizing(){return this.privIsRecognizing}get currentTurnAudioOffset(){return this.privTurnStartAudioOffset}get recogNumber(){return this.privRecogNumber}get numConnectionAttempts(){return this.privConnectionAttempts}get bytesSent(){return this.privBytesSent}get recognitionBytesSent(){return this.privRecognitionBytesSent}listenForServiceTelemetry(e){this.privServiceTelemetryListener&&this.privDetachables.push(e.attachListener(this.privServiceTelemetryListener))}startNewRecognition(){this.privRecognitionBytesSent=0,this.privIsSpeechEnded=!1,this.privIsRecognizing=!0,this.privTurnStartAudioOffset=0,this.privLastRecoOffset=0,this.privRecogNumber++,this.privServiceTelemetryListener=new r.ServiceTelemetryListener(this.privRequestId,this.privAudioSourceId,this.privAudioNodeId),this.onEvent(new t.RecognitionTriggeredEvent(this.requestId,this.privSessionId,this.privAudioSourceId,this.privAudioNodeId))}async onAudioSourceAttachCompleted(e,r){this.privAudioNode=e,this.privIsAudioNodeDetached=!1,r?await this.onComplete():this.onEvent(new t.ListeningStartedEvent(this.privRequestId,this.privSessionId,this.privAudioSourceId,this.privAudioNodeId))}onPreConnectionStart(e,r){this.privAuthFetchEventId=e,this.privSessionId=r,this.onEvent(new t.ConnectingToServiceEvent(this.privRequestId,this.privAuthFetchEventId,this.privSessionId))}async onAuthCompleted(e){e&&await this.onComplete()}async onConnectionEstablishCompleted(e,r){if(200===e)return this.onEvent(new t.RecognitionStartedEvent(this.requestId,this.privAudioSourceId,this.privAudioNodeId,this.privAuthFetchEventId,this.privSessionId)),this.privAudioNode&&this.privAudioNode.replay(),this.privTurnStartAudioOffset=this.privLastRecoOffset,void(this.privBytesSent=0);403===e&&await this.onComplete()}async onServiceTurnEndResponse(e){this.privTurnDeferral.resolve(),!e||this.isSpeechEnded?(await this.onComplete(),this.privInTurn=!1):(this.privTurnStartAudioOffset=this.privLastRecoOffset,this.privAudioNode.replay())}onSpeechContext(){this.privRequestId=e.createNoDashGuid()}onServiceTurnStartResponse(){this.privTurnDeferral&&this.privInTurn&&(this.privTurnDeferral.reject("Another turn started before current completed."),this.privTurnDeferral.promise.then().catch((()=>{}))),this.privInTurn=!0,this.privTurnDeferral=new e.Deferred}onHypothesis(e){this.privHypothesisReceived||(this.privHypothesisReceived=!0,this.privServiceTelemetryListener.hypothesisReceived(this.privAudioNode.findTimeAtOffset(e)))}onPhraseRecognized(e){this.privServiceTelemetryListener.phraseReceived(this.privAudioNode.findTimeAtOffset(e)),this.onServiceRecognized(e)}onServiceRecognized(e){this.privLastRecoOffset=e,this.privHypothesisReceived=!1,this.privAudioNode.shrinkBuffers(e),this.privConnectionAttempts=0}onAudioSent(e){this.privBytesSent+=e,this.privRecognitionBytesSent+=e}onRetryConnection(){this.privConnectionAttempts++}async dispose(){if(!this.privIsDisposed){this.privIsDisposed=!0;for(const e of this.privDetachables)await e.detach();this.privServiceTelemetryListener&&this.privServiceTelemetryListener.dispose(),this.privIsRecognizing=!1}}getTelemetry(){return this.privServiceTelemetryListener.hasTelemetry?this.privServiceTelemetryListener.getTelemetry():null}async onStopRecognizing(){await this.onComplete()}onSpeechEnded(){this.privIsSpeechEnded=!0}onEvent(t){this.privServiceTelemetryListener&&this.privServiceTelemetryListener.onEvent(t),e.Events.instance.onEvent(t)}async onComplete(){this.privIsRecognizing&&(this.privIsRecognizing=!1,await this.detachAudioNode())}async detachAudioNode(){this.privIsAudioNodeDetached||(this.privIsAudioNodeDetached=!0,this.privAudioNode&&await this.privAudioNode.detach())}},Dh}var _h,Lh={};function xh(){if(_h)return Lh;_h=1,Object.defineProperty(Lh,"__esModule",{value:!0}),Lh.SpeechContext=void 0;return Lh.SpeechContext=class{constructor(e){this.privContext={},this.privDynamicGrammar=e}getSection(e){return this.privContext[e]||{}}setSection(e,t){this.privContext[e]=t}setPronunciationAssessmentParams(e,t,r=!1){void 0===this.privContext.phraseDetection&&(this.privContext.phraseDetection={enrichment:{pronunciationAssessment:{}}}),void 0===this.privContext.phraseDetection.enrichment&&(this.privContext.phraseDetection.enrichment={pronunciationAssessment:{}}),this.privContext.phraseDetection.enrichment.pronunciationAssessment=JSON.parse(e),r&&(this.privContext.phraseDetection.mode="Conversation"),this.setWordLevelTimings(),this.privContext.phraseOutput.detailed.options.push("PronunciationAssessment"),-1===this.privContext.phraseOutput.detailed.options.indexOf("SNR")&&this.privContext.phraseOutput.detailed.options.push("SNR"),t&&(this.privContext.phraseDetection.enrichment.contentAssessment={topic:t},this.privContext.phraseOutput.detailed.options.push("ContentAssessment"))}setDetailedOutputFormat(){void 0===this.privContext.phraseOutput&&(this.privContext.phraseOutput={detailed:{options:[]},format:{}}),void 0===this.privContext.phraseOutput.detailed&&(this.privContext.phraseOutput.detailed={options:[]}),this.privContext.phraseOutput.format="Detailed"}setWordLevelTimings(){void 0===this.privContext.phraseOutput&&(this.privContext.phraseOutput={detailed:{options:[]},format:{}}),void 0===this.privContext.phraseOutput.detailed&&(this.privContext.phraseOutput.detailed={options:[]}),this.privContext.phraseOutput.format="Detailed",-1===this.privContext.phraseOutput.detailed.options.indexOf("WordTimings")&&this.privContext.phraseOutput.detailed.options.push("WordTimings")}setSpeakerDiarizationAudioOffsetMs(e){this.privContext.phraseDetection.speakerDiarization.audioOffsetMs=e}toJSON(){const e=this.privDynamicGrammar.generateGrammarObject();this.setSection("dgi",e);return JSON.stringify(this.privContext)}},Lh}var Fh,Bh={};function jh(){if(Fh)return Bh;Fh=1,Object.defineProperty(Bh,"__esModule",{value:!0}),Bh.DynamicGrammarBuilder=void 0;return Bh.DynamicGrammarBuilder=class{addPhrase(e){this.privPhrases||(this.privPhrases=[]),e instanceof Array?this.privPhrases=this.privPhrases.concat(e):this.privPhrases.push(e)}clearPhrases(){this.privPhrases=void 0}addReferenceGrammar(e){this.privGrammars||(this.privGrammars=[]),e instanceof Array?this.privGrammars=this.privGrammars.concat(e):this.privGrammars.push(e)}clearGrammars(){this.privGrammars=void 0}generateGrammarObject(){if(void 0===this.privGrammars&&void 0===this.privPhrases)return;const e={};if(e.ReferenceGrammars=this.privGrammars,void 0!==this.privPhrases&&0!==this.privPhrases.length){const t=[];this.privPhrases.forEach((e=>{t.push({Text:e})})),e.Groups=[{Type:"Generic",Items:t}]}return e}},Bh}var Uh,qh={};function Wh(){return Uh||(Uh=1,Object.defineProperty(qh,"__esModule",{value:!0})),qh}var Hh,Vh,Kh,Jh,Gh={},$h={},Qh={},Xh={};function Zh(){return Hh||(Hh=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.MessageDataStreamType=e.ActivityPayloadResponse=void 0;class t{constructor(e){this.privActivityResponse=JSON.parse(e)}static fromJSON(e){return new t(e)}get conversationId(){return this.privActivityResponse.conversationId}get messageDataStreamType(){return this.privActivityResponse.messageDataStreamType}get messagePayload(){return this.privActivityResponse.messagePayload}get version(){return this.privActivityResponse.version}}var r;e.ActivityPayloadResponse=t,(r=e.MessageDataStreamType||(e.MessageDataStreamType={}))[r.None=0]="None",r[r.TextToSpeechAudio=1]="TextToSpeechAudio"}(Xh)),Xh}function Yh(){if(Vh)return Qh;Vh=1,Object.defineProperty(Qh,"__esModule",{value:!0}),Qh.DialogServiceTurnState=void 0;const e=hi(),t=di(),r=Zh();return Qh.DialogServiceTurnState=class{constructor(e,t){this.privRequestId=t,this.privIsCompleted=!1,this.privAudioStream=null,this.privTurnManager=e,this.resetTurnEndTimeout()}get audioStream(){return this.resetTurnEndTimeout(),this.privAudioStream}processActivityPayload(i,n){return i.messageDataStreamType===r.MessageDataStreamType.TextToSpeechAudio&&(this.privAudioStream=t.AudioOutputStream.createPullStream(),this.privAudioStream.format=void 0!==n?n:e.AudioOutputFormatImpl.getDefaultOutputFormat()),this.privAudioStream}endAudioStream(){null===this.privAudioStream||this.privAudioStream.isClosed||this.privAudioStream.close()}complete(){void 0!==this.privTimeoutToken&&clearTimeout(this.privTimeoutToken),this.endAudioStream()}resetTurnEndTimeout(){void 0!==this.privTimeoutToken&&clearTimeout(this.privTimeoutToken),this.privTimeoutToken=setTimeout((()=>{this.privTurnManager.CompleteTurn(this.privRequestId)}),2e3)}},Qh}function ed(){if(Kh)return $h;Kh=1,Object.defineProperty($h,"__esModule",{value:!0}),$h.DialogServiceTurnStateManager=void 0;const e=Se(),t=Yh();return $h.DialogServiceTurnStateManager=class{constructor(){this.privTurnMap=new Map}StartTurn(r){if(this.privTurnMap.has(r))throw new e.InvalidOperationError("Service error: There is already a turn with id:"+r);const i=new t.DialogServiceTurnState(this,r);return this.privTurnMap.set(r,i),this.privTurnMap.get(r)}GetTurn(e){return this.privTurnMap.get(e)}CompleteTurn(t){if(!this.privTurnMap.has(t))throw new e.InvalidOperationError("Service error: Received turn end for an unknown turn id:"+t);const r=this.privTurnMap.get(t);return r.complete(),this.privTurnMap.delete(t),r}},$h}function td(){if(Jh)return Gh;Jh=1,Object.defineProperty(Gh,"__esModule",{value:!0}),Gh.DialogServiceAdapter=void 0;const e=Vp(),t=be(),r=Gt(),i=hi(),n=Mp(),o=ed(),s=jv(),a=Zh(),c=iu();let p=class extends s.ServiceRecognizerBase{constructor(e,t,i,n,s){super(e,t,i,n,s),this.privEvents=new r.EventSource,this.privDialogServiceConnector=s,this.receiveMessageOverride=()=>this.receiveDialogMessageOverride(),this.privTurnStateManager=new o.DialogServiceTurnStateManager,this.recognizeOverride=(e,t,r)=>this.listenOnce(e,t,r),this.postConnectImplOverride=e=>this.dialogConnectImpl(e),this.configConnectionOverride=e=>this.configConnection(e),this.disconnectOverride=()=>this.privDisconnect(),this.privDialogAudioSource=i,this.agentConfigSent=!1,this.privLastResult=null,this.connectionEvents.attach((e=>{"ConnectionClosedEvent"===e.name&&(this.terminateMessageLoop=!0)}))}async sendMessage(e){const t=r.createGuid(),i=r.createNoDashGuid(),n={context:{interactionId:t},messagePayload:JSON.parse(e),version:.5},o=JSON.stringify(n),s=await this.fetchConnection();await s.send(new c.SpeechConnectionMessage(r.MessageType.Text,"agent",i,"application/json",o))}async privDisconnect(){await this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,n.CancellationReason.Error,n.CancellationErrorCode.NoError,"Disconnecting"),this.terminateMessageLoop=!0,this.agentConfigSent=!1}processTypeSpecificMessages(e){const t=new n.PropertyCollection;let i,o;switch(e.messageType===r.MessageType.Text&&t.setProperty(n.PropertyId.SpeechServiceResponse_JsonResult,e.textBody),e.path.toLowerCase()){case"speech.phrase":const r=s.SimpleSpeechPhrase.fromJSON(e.textBody,this.privRequestSession.currentTurnAudioOffset);if(this.privRequestSession.onPhraseRecognized(r.Offset+r.Duration),r.RecognitionStatus!==s.RecognitionStatus.TooManyRequests&&r.RecognitionStatus!==s.RecognitionStatus.Error){const e=this.fireEventForResult(r,t);if(this.privLastResult=e.result,this.privDialogServiceConnector.recognized)try{this.privDialogServiceConnector.recognized(this.privDialogServiceConnector,e)}catch(c){}}o=!0;break;case"speech.hypothesis":const a=s.SpeechHypothesis.fromJSON(e.textBody,this.privRequestSession.currentTurnAudioOffset);i=new n.SpeechRecognitionResult(this.privRequestSession.requestId,n.ResultReason.RecognizingSpeech,a.Text,a.Duration,a.Offset,a.Language,a.LanguageDetectionConfidence,void 0,void 0,a.asJson(),t),this.privRequestSession.onHypothesis(a.Offset);const p=new n.SpeechRecognitionEventArgs(i,a.Offset,this.privRequestSession.sessionId);if(this.privDialogServiceConnector.recognizing)try{this.privDialogServiceConnector.recognizing(this.privDialogServiceConnector,p)}catch(c){}o=!0;break;case"speech.keyword":const u=s.SpeechKeyword.fromJSON(e.textBody,this.privRequestSession.currentTurnAudioOffset);i=new n.SpeechRecognitionResult(this.privRequestSession.requestId,"Accepted"===u.Status?n.ResultReason.RecognizedKeyword:n.ResultReason.NoMatch,u.Text,u.Duration,u.Offset,void 0,void 0,void 0,void 0,u.asJson(),t),"Accepted"!==u.Status&&(this.privLastResult=i);const h=new n.SpeechRecognitionEventArgs(i,i.duration,i.resultId);if(this.privDialogServiceConnector.recognized)try{this.privDialogServiceConnector.recognized(this.privDialogServiceConnector,h)}catch(c){}o=!0;break;case"audio":{const t=e.requestId.toUpperCase(),r=this.privTurnStateManager.GetTurn(t);try{e.binaryBody?r.audioStream.write(e.binaryBody):r.endAudioStream()}catch(c){}}o=!0;break;case"response":this.handleResponseMessage(e),o=!0}const a=new r.Deferred;return a.resolve(o),a.promise}async cancelRecognition(e,t,r,i,o){if(this.terminateMessageLoop=!0,this.privRequestSession.isRecognizing&&await this.privRequestSession.onStopRecognizing(),this.privDialogServiceConnector.canceled){const t=new n.PropertyCollection;t.setProperty(s.CancellationErrorCodePropertyName,n.CancellationErrorCode[i]);const a=new n.SpeechRecognitionCanceledEventArgs(r,o,i,void 0,e);try{this.privDialogServiceConnector.canceled(this.privDialogServiceConnector,a)}catch{}if(this.privSuccessCallback){const e=new n.SpeechRecognitionResult(void 0,n.ResultReason.Canceled,void 0,void 0,void 0,void 0,void 0,void 0,o,void 0,t);try{this.privSuccessCallback(e),this.privSuccessCallback=void 0}catch{}}}}async listenOnce(t,r,i){this.privRecognizerConfig.recognitionMode=t,this.privSuccessCallback=r,this.privErrorCallback=i,this.privRequestSession.startNewRecognition(),this.privRequestSession.listenForServiceTelemetry(this.privDialogAudioSource.events),this.privRecognizerConfig.parameters.setProperty(n.PropertyId.Speech_SessionId,this.privRequestSession.sessionId);const o=this.connectImpl(),s=this.sendPreAudioMessages(),a=await this.privDialogAudioSource.attach(this.privRequestSession.audioNodeId),c=await this.privDialogAudioSource.format,p=await this.privDialogAudioSource.deviceInfo,u=new e.ReplayableAudioNode(a,c.avgBytesPerSec);await this.privRequestSession.onAudioSourceAttachCompleted(u,!1),this.privRecognizerConfig.SpeechServiceConfig.Context.audio={source:p};try{await o,await s}catch(d){return await this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,n.CancellationReason.Error,n.CancellationErrorCode.ConnectionFailure,d),Promise.resolve()}const h=new n.SessionEventArgs(this.privRequestSession.sessionId);this.privRecognizer.sessionStarted&&this.privRecognizer.sessionStarted(this.privRecognizer,h);this.sendAudio(u).then((()=>{}),(async e=>{await this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,n.CancellationReason.Error,n.CancellationErrorCode.RuntimeError,e)}))}dialogConnectImpl(e){return this.privConnectionLoop=this.startMessageLoop(),e}receiveDialogMessageOverride(){const e=new r.Deferred,t=async()=>{try{const o=this.isDisposed(),a=!this.isDisposed()&&this.terminateMessageLoop;if(o||a)return void e.resolve(void 0);const p=await this.fetchConnection(),u=await p.read();if(!u)return t();const h=c.SpeechConnectionMessage.fromConnectionMessage(u);switch(h.path.toLowerCase()){case"turn.start":{const e=h.requestId.toUpperCase();e!==this.privRequestSession.requestId.toUpperCase()?this.privTurnStateManager.StartTurn(e):this.privRequestSession.onServiceTurnStartResponse()}break;case"speech.startdetected":const e=s.SpeechDetected.fromJSON(h.textBody,this.privRequestSession.currentTurnAudioOffset),t=new n.RecognitionEventArgs(e.Offset,this.privRequestSession.sessionId);this.privRecognizer.speechStartDetected&&this.privRecognizer.speechStartDetected(this.privRecognizer,t);break;case"speech.enddetected":let o;o=h.textBody.length>0?h.textBody:"{ Offset: 0 }";const a=s.SpeechDetected.fromJSON(o,this.privRequestSession.currentTurnAudioOffset);this.privRequestSession.onServiceRecognized(a.Offset);const c=new n.RecognitionEventArgs(a.Offset,this.privRequestSession.sessionId);this.privRecognizer.speechEndDetected&&this.privRecognizer.speechEndDetected(this.privRecognizer,c);break;case"turn.end":{const e=h.requestId.toUpperCase();if(e!==this.privRequestSession.requestId.toUpperCase())this.privTurnStateManager.CompleteTurn(e);else{const e=new n.SessionEventArgs(this.privRequestSession.sessionId);if(await this.privRequestSession.onServiceTurnEndResponse(!1),this.privRecognizerConfig.isContinuousRecognition&&!this.privRequestSession.isSpeechEnded&&this.privRequestSession.isRecognizing||this.privRecognizer.sessionStopped&&this.privRecognizer.sessionStopped(this.privRecognizer,e),this.privSuccessCallback&&this.privLastResult){try{this.privSuccessCallback(this.privLastResult),this.privLastResult=null}catch(i){this.privErrorCallback&&this.privErrorCallback(i)}this.privSuccessCallback=void 0,this.privErrorCallback=void 0}}}break;default:try{await this.processTypeSpecificMessages(h)||this.serviceEvents&&this.serviceEvents.onEvent(new r.ServiceEvent(h.path.toLowerCase(),h.textBody))}catch(i){}}return t()}catch(o){this.terminateMessageLoop=!0,e.resolve()}};return t().catch((e=>{r.Events.instance.onEvent(new r.BackgroundEvent(e))})),e.promise}async startMessageLoop(){this.terminateMessageLoop=!1;try{await this.receiveDialogMessageOverride()}catch(e){await this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,n.CancellationReason.Error,n.CancellationErrorCode.RuntimeError,e)}return Promise.resolve()}async configConnection(e){return this.terminateMessageLoop?(this.terminateMessageLoop=!1,Promise.reject("Connection to service terminated.")):(await this.sendSpeechServiceConfig(e,this.privRequestSession,this.privRecognizerConfig.SpeechServiceConfig.serialize()),await this.sendAgentConfig(e),e)}async sendPreAudioMessages(){const e=await this.fetchConnection();this.addKeywordContextData(),await this.sendSpeechContext(e,!0),await this.sendAgentContext(e),await this.sendWaveHeader(e)}sendAgentConfig(e){if(this.agentConfig&&!this.agentConfigSent){if(this.privRecognizerConfig.parameters.getProperty(n.PropertyId.Conversation_DialogType)===n.DialogServiceConfig.DialogTypes.CustomCommands){const e=this.agentConfig.get();e.botInfo.commandsCulture=this.privRecognizerConfig.parameters.getProperty(n.PropertyId.SpeechServiceConnection_RecoLanguage,"en-us"),this.agentConfig.set(e)}this.onEvent(new t.SendingAgentContextMessageEvent(this.agentConfig));const i=this.agentConfig.toJsonString();return this.agentConfigSent=!0,e.send(new c.SpeechConnectionMessage(r.MessageType.Text,"agent.config",this.privRequestSession.requestId,"application/json",i))}}sendAgentContext(e){const t=r.createGuid(),i=this.privDialogServiceConnector.properties.getProperty(n.PropertyId.Conversation_Speech_Activity_Template),o={channelData:"",context:{interactionId:t},messagePayload:void 0===typeof i?void 0:i,version:.5},s=JSON.stringify(o);return e.send(new c.SpeechConnectionMessage(r.MessageType.Text,"speech.agent.context",this.privRequestSession.requestId,"application/json",s))}fireEventForResult(e,t){const r=s.EnumTranslation.implTranslateRecognitionResult(e.RecognitionStatus),i=new n.SpeechRecognitionResult(this.privRequestSession.requestId,r,e.DisplayText,e.Duration,e.Offset,e.Language,e.LanguageDetectionConfidence,void 0,void 0,e.asJson(),t);return new n.SpeechRecognitionEventArgs(i,e.Offset,this.privRequestSession.sessionId)}handleResponseMessage(e){const t=JSON.parse(e.textBody);switch(t.messageType.toLowerCase()){case"message":const s=e.requestId.toUpperCase(),c=a.ActivityPayloadResponse.fromJSON(e.textBody),p=this.privTurnStateManager.GetTurn(s);if(c.conversationId){const e=this.agentConfig.get();e.botInfo.conversationId=c.conversationId,this.agentConfig.set(e)}const u=p.processActivityPayload(c,i.AudioOutputFormatImpl.fromSpeechSynthesisOutputFormatString(this.privDialogServiceConnector.properties.getProperty(n.PropertyId.SpeechServiceConnection_SynthOutputFormat,void 0))),h=new n.ActivityReceivedEventArgs(c.messagePayload,u);if(this.privDialogServiceConnector.activityReceived)try{this.privDialogServiceConnector.activityReceived(this.privDialogServiceConnector,h)}catch(o){}break;case"messagestatus":if(this.privDialogServiceConnector.turnStatusReceived)try{this.privDialogServiceConnector.turnStatusReceived(this.privDialogServiceConnector,new n.TurnStatusReceivedEventArgs(e.textBody))}catch(o){}break;default:r.Events.instance.onEvent(new r.BackgroundEvent(`Unexpected response of type ${t.messageType}. Ignoring.`))}}onEvent(e){this.privEvents.onEvent(e),r.Events.instance.onEvent(e)}addKeywordContextData(){const e=this.privRecognizerConfig.parameters.getProperty("SPEECH-KeywordsToDetect");if(void 0===e)return;const t=this.privRecognizerConfig.parameters.getProperty("SPEECH-KeywordsToDetect-Offsets"),r=this.privRecognizerConfig.parameters.getProperty("SPEECH-KeywordsToDetect-Durations"),i=e.split(";"),n=void 0===t?[]:t.split(";"),o=void 0===r?[]:r.split(";"),s=[];for(let a=0;a<i.length;a++){const e={};e.text=i[a],a<n.length&&(e.offset=Number(n[a])),a<o.length&&(e.duration=Number(o[a])),s.push(e)}this.speechContext.setSection("invocationSource","VoiceActivationWithKeyword"),this.speechContext.setSection("keywordDetection",[{clientDetectedKeywords:s,onReject:{action:"EndOfTurn"},type:"startTrigger"}])}};return Gh.DialogServiceAdapter=p,Gh}var rd,id={};function nd(){if(rd)return id;rd=1,Object.defineProperty(id,"__esModule",{value:!0}),id.AgentConfig=void 0;return id.AgentConfig=class{toJsonString(){return JSON.stringify(this.iPrivConfig)}get(){return this.iPrivConfig}set(e){this.iPrivConfig=e}},id}var od,sd,ad={},cd={},pd={};function ud(){if(od)return pd;od=1,Object.defineProperty(pd,"__esModule",{value:!0}),pd.ConversationConnectionConfig=void 0;const e=Hp();let t=class t extends e.RestConfigBase{static get host(){return t.privHost}static get apiVersion(){return t.privApiVersion}static get clientAppId(){return t.privClientAppId}static get defaultLanguageCode(){return t.privDefaultLanguageCode}static get restPath(){return t.privRestPath}static get webSocketPath(){return t.privWebSocketPath}static get transcriptionEventKeys(){return t.privTranscriptionEventKeys}};return pd.ConversationConnectionConfig=t,t.privHost="dev.microsofttranslator.com",t.privRestPath="/capito/room",t.privApiVersion="2.0",t.privDefaultLanguageCode="en-US",t.privClientAppId="FC539C22-1767-4F1F-84BC-B4D811114F15",t.privWebSocketPath="/capito/translate",t.privTranscriptionEventKeys=["iCalUid","callId","organizer","FLAC","MTUri","DifferentiateGuestSpeakers","audiorecording","Threadid","OrganizerMri","OrganizerTenantId","UserToken"],pd}function hd(){if(sd)return cd;sd=1,Object.defineProperty(cd,"__esModule",{value:!0}),cd.ConversationManager=void 0;const e=Vp(),t=Rr(),r=Mp(),i=ud();return cd.ConversationManager=class{constructor(){this.privRequestParams=i.ConversationConnectionConfig.configParams,this.privErrors=i.ConversationConnectionConfig.restErrors,this.privHost=i.ConversationConnectionConfig.host,this.privApiVersion=i.ConversationConnectionConfig.apiVersion,this.privRestPath=i.ConversationConnectionConfig.restPath,this.privRestAdapter=new e.RestMessageAdapter({})}createOrJoin(n,o,s,a){try{t.Contracts.throwIfNullOrUndefined(n,"args");const c=n.getProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage,i.ConversationConnectionConfig.defaultLanguageCode),p=n.getProperty(r.PropertyId.ConversationTranslator_Name,"conversation_host"),u=n.getProperty(r.PropertyId.ConversationTranslator_Host,this.privHost),h=n.getProperty(r.PropertyId.ConversationTranslator_CorrelationId),d=n.getProperty(r.PropertyId.SpeechServiceConnection_Key),v=n.getProperty(r.PropertyId.SpeechServiceConnection_Region),l=n.getProperty(r.PropertyId.SpeechServiceAuthorization_Token);t.Contracts.throwIfNullOrWhitespace(c,"languageCode"),t.Contracts.throwIfNullOrWhitespace(p,"nickname"),t.Contracts.throwIfNullOrWhitespace(u,"endpointHost");const g={};g[this.privRequestParams.apiVersion]=this.privApiVersion,g[this.privRequestParams.languageCode]=c,g[this.privRequestParams.nickname]=p;const f={};h&&(f[this.privRequestParams.correlationId]=h),f[this.privRequestParams.clientAppId]=i.ConversationConnectionConfig.clientAppId,void 0!==o?g[this.privRequestParams.roomId]=o:(t.Contracts.throwIfNullOrUndefined(v,this.privErrors.authInvalidSubscriptionRegion),f[this.privRequestParams.subscriptionRegion]=v,d?f[this.privRequestParams.subscriptionKey]=d:l?f[this.privRequestParams.authorization]=`Bearer ${l}`:t.Contracts.throwIfNullOrUndefined(d,this.privErrors.authInvalidSubscriptionKey));const m={};m.headers=f,this.privRestAdapter.options=m;const S=`https://${u}${this.privRestPath}`;this.privRestAdapter.request(e.RestRequestType.Post,S,g,null).then((t=>{const r=e.RestMessageAdapter.extractHeaderValue(this.privRequestParams.requestId,t.headers);if(!t.ok){if(a){let e,i=this.privErrors.invalidCreateJoinConversationResponse.replace("{status}",t.status.toString());try{e=JSON.parse(t.data),i+=` [${e.error.code}: ${e.error.message}]`}catch(n){i+=` [${t.data}]`}r&&(i+=` ${r}`),a(i)}return}const i=JSON.parse(t.data);if(i&&(i.requestId=r),s){try{s(i)}catch(n){a&&a(n)}s=void 0}})).catch((()=>{}))}catch(c){if(a)if(c instanceof Error){const e=c;a(e.name+": "+e.message)}else a(c)}}leave(i,n){return new Promise(((o,s)=>{try{t.Contracts.throwIfNullOrUndefined(i,this.privErrors.invalidArgs.replace("{arg}","config")),t.Contracts.throwIfNullOrWhitespace(n,this.privErrors.invalidArgs.replace("{arg}","token"));const s=i.getProperty(r.PropertyId.ConversationTranslator_Host,this.privHost),a=i.getProperty(r.PropertyId.ConversationTranslator_CorrelationId),c={};c[this.privRequestParams.apiVersion]=this.privApiVersion,c[this.privRequestParams.sessionToken]=n;const p={};a&&(p[this.privRequestParams.correlationId]=a);const u={};u.headers=p,this.privRestAdapter.options=u;const h=`https://${s}${this.privRestPath}`;this.privRestAdapter.request(e.RestRequestType.Delete,h,c,null).then((e=>{e.ok,o()})).catch((()=>{}))}catch(a){if(a instanceof Error){const e=a;s(e.name+": "+e.message)}else s(a)}}))}},cd}var dd,vd,ld,gd={},fd={},md={},Sd={};function yd(){if(dd)return Sd;dd=1,Object.defineProperty(Sd,"__esModule",{value:!0}),Sd.ConversationConnectionMessage=void 0;const e=Gt();let t=class extends e.ConnectionMessage{constructor(e,t,r,i){super(e,t,r,i);const n=JSON.parse(this.textBody);void 0!==n.type&&(this.privConversationMessageType=n.type)}get conversationMessageType(){return this.privConversationMessageType}};return Sd.ConversationConnectionMessage=t,Sd}function Cd(){if(vd)return md;vd=1,Object.defineProperty(md,"__esModule",{value:!0}),md.ConversationWebsocketMessageFormatter=void 0;const e=Gt(),t=yd();return md.ConversationWebsocketMessageFormatter=class{toConnectionMessage(r){const i=new e.Deferred;try{if(r.messageType===e.MessageType.Text){const e=new t.ConversationConnectionMessage(r.messageType,r.textContent,{},r.id);i.resolve(e)}else r.messageType===e.MessageType.Binary&&i.resolve(new t.ConversationConnectionMessage(r.messageType,r.binaryContent,void 0,r.id))}catch(n){i.reject(`Error formatting the message. Error: ${n}`)}return i.promise}fromConnectionMessage(t){const r=new e.Deferred;try{if(t.messageType===e.MessageType.Text){const i=`${t.textBody?t.textBody:""}`;r.resolve(new e.RawWebsocketMessage(e.MessageType.Text,i,t.id))}}catch(i){r.reject(`Error formatting the message. ${i}`)}return r.promise}},md}function Pd(){if(ld)return fd;ld=1,Object.defineProperty(fd,"__esModule",{value:!0}),fd.ConversationConnectionFactory=void 0;const e=Vp(),t=Gt(),r=Rr(),i=Mp(),n=Zo(),o=ud(),s=Cd();let a=class extends n.ConnectionFactoryBase{create(n,a,c){const p=n.parameters.getProperty(i.PropertyId.ConversationTranslator_Host,o.ConversationConnectionConfig.host),u=n.parameters.getProperty(i.PropertyId.ConversationTranslator_CorrelationId,t.createGuid()),h=`wss://${p}${o.ConversationConnectionConfig.webSocketPath}`,d=n.parameters.getProperty(i.PropertyId.ConversationTranslator_Token,void 0);r.Contracts.throwIfNullOrUndefined(d,"token");const v={};v[o.ConversationConnectionConfig.configParams.apiVersion]=o.ConversationConnectionConfig.apiVersion,v[o.ConversationConnectionConfig.configParams.token]=d,v[o.ConversationConnectionConfig.configParams.correlationId]=u;const l="true"===n.parameters.getProperty("SPEECH-EnableWebsocketCompression","false");return new e.WebsocketConnection(h,v,{},new s.ConversationWebsocketMessageFormatter,e.ProxyInfo.fromRecognizerConfig(n),l,c)}};return fd.ConversationConnectionFactory=a,fd}var Rd,Id={},wd={};function Td(){if(Rd)return wd;Rd=1,Object.defineProperty(wd,"__esModule",{value:!0}),wd.ConversationRequestSession=void 0;const e=Gt();return wd.ConversationRequestSession=class{constructor(t){this.privIsDisposed=!1,this.privDetachables=new Array,this.privSessionId=t,this.privRequestId=e.createNoDashGuid(),this.privRequestCompletionDeferral=new e.Deferred}get sessionId(){return this.privSessionId}get requestId(){return this.privRequestId}get completionPromise(){return this.privRequestCompletionDeferral.promise}onPreConnectionStart(e,t){this.privSessionId=t}onAuthCompleted(e){e&&this.onComplete()}onConnectionEstablishCompleted(e){200!==e&&403===e&&this.onComplete()}onServiceTurnEndResponse(t){t?this.privRequestId=e.createNoDashGuid():this.onComplete()}async dispose(){if(!this.privIsDisposed){this.privIsDisposed=!0;for(const e of this.privDetachables)await e.detach()}}onComplete(){}},wd}var Ad,Ed={};function bd(){if(Ad)return Ed;Ad=1,Object.defineProperty(Ed,"__esModule",{value:!0}),Ed.ConversationReceivedTranslationEventArgs=Ed.ParticipantsListEventArgs=Ed.ParticipantAttributeEventArgs=Ed.ParticipantEventArgs=Ed.LockRoomEventArgs=Ed.MuteAllEventArgs=void 0;const e=Mp();class t extends e.SessionEventArgs{constructor(e,t){super(t),this.privIsMuted=e}get isMuted(){return this.privIsMuted}}Ed.MuteAllEventArgs=t;class r extends e.SessionEventArgs{constructor(e,t){super(t),this.privIsLocked=e}get isMuted(){return this.privIsLocked}}Ed.LockRoomEventArgs=r;class i extends e.SessionEventArgs{constructor(e,t){super(t),this.privParticipant=e}get participant(){return this.privParticipant}}Ed.ParticipantEventArgs=i;class n extends e.SessionEventArgs{constructor(e,t,r,i){super(i),this.privKey=t,this.privValue=r,this.privParticipantId=e}get value(){return this.privValue}get key(){return this.privKey}get id(){return this.privParticipantId}}Ed.ParticipantAttributeEventArgs=n;class o extends e.SessionEventArgs{constructor(e,t,r,i,n,o,s,a,c){super(c),this.privRoomId=e,this.privSessionToken=t,this.privTranslateTo=r,this.privProfanityFilter=i,this.privRoomProfanityFilter=n,this.privIsRoomLocked=o,this.privIsRoomLocked=s,this.privParticipants=a}get sessionToken(){return this.privSessionToken}get conversationId(){return this.privRoomId}get translateTo(){return this.privTranslateTo}get profanityFilter(){return this.privProfanityFilter}get roomProfanityFilter(){return this.privRoomProfanityFilter}get isRoomLocked(){return this.privIsRoomLocked}get isMuteAll(){return this.privIsMuteAll}get participants(){return this.privParticipants}}Ed.ParticipantsListEventArgs=o;return Ed.ConversationReceivedTranslationEventArgs=class{constructor(e,t,r){this.privPayload=t,this.privCommand=e,this.privSessionId=r}get payload(){return this.privPayload}get command(){return this.privCommand}get sessionId(){return this.privSessionId}},Ed}var Od,Md={};function Dd(){if(Od)return Md;Od=1,Object.defineProperty(Md,"__esModule",{value:!0}),Md.ConversationTranslatorCommandTypes=Md.ConversationTranslatorMessageTypes=Md.InternalParticipants=void 0;return Md.InternalParticipants=class{constructor(e=[],t){this.participants=e,this.meId=t}addOrUpdateParticipant(e){if(void 0===e)return;const t=this.getParticipantIndex(e.id);return t>-1?this.participants.splice(t,1,e):this.participants.push(e),this.getParticipant(e.id)}getParticipantIndex(e){return this.participants.findIndex((t=>t.id===e))}getParticipant(e){return this.participants.find((t=>t.id===e))}deleteParticipant(e){this.participants=this.participants.filter((t=>t.id!==e))}get host(){return this.participants.find((e=>!0===e.isHost))}get me(){return this.getParticipant(this.meId)}},Md.ConversationTranslatorMessageTypes={command:"command",final:"final",info:"info",instantMessage:"instant_message",keepAlive:"keep_alive",partial:"partial",participantCommand:"participant_command",translatedMessage:"translated_message"},Md.ConversationTranslatorCommandTypes={changeNickname:"ChangeNickname",disconnectSession:"DisconnectSession",ejectParticipant:"EjectParticipant",instant_message:"instant_message",joinSession:"JoinSession",leaveSession:"LeaveSession",participantList:"ParticipantList",roomExpirationWarning:"RoomExpirationWarning",setLockState:"SetLockState",setMute:"SetMute",setMuteAll:"SetMuteAll",setProfanityFiltering:"SetProfanityFiltering",setTranslateToLanguages:"SetTranslateToLanguages",setUseTTS:"SetUseTTS"},Md}var kd,zd={},Nd={};function _d(){if(kd)return Nd;kd=1,Object.defineProperty(Nd,"__esModule",{value:!0}),Nd.CommandResponsePayload=void 0;return Nd.CommandResponsePayload=class e{constructor(e){this.privCommandResponse=(e=>JSON.parse(e))(e)}get type(){return this.privCommandResponse.type}get command(){return this.privCommandResponse.command}get id(){return this.privCommandResponse.id}get nickname(){return this.privCommandResponse.nickname}get participantId(){return this.privCommandResponse.participantId}get roomid(){return this.privCommandResponse.roomid}get value(){return this.privCommandResponse.value}get token(){return this.privCommandResponse.token}static fromJSON(t){return new e(t)}},Nd}var Ld,xd={};function Fd(){if(Ld)return xd;Ld=1,Object.defineProperty(xd,"__esModule",{value:!0}),xd.ParticipantPayloadResponse=xd.ParticipantsListPayloadResponse=void 0;class e{constructor(e){this.privParticipantsPayloadResponse=(e=>JSON.parse(e))(e)}get roomid(){return this.privParticipantsPayloadResponse.roomid}get id(){return this.privParticipantsPayloadResponse.id}get command(){return this.privParticipantsPayloadResponse.command}get participants(){return this.privParticipantsPayloadResponse.participants}get token(){return this.privParticipantsPayloadResponse.token}get translateTo(){return this.privParticipantsPayloadResponse.translateTo}get profanityFilter(){return this.privParticipantsPayloadResponse.profanityFilter}get roomProfanityFilter(){return this.privParticipantsPayloadResponse.roomProfanityFilter}get roomLocked(){return this.privParticipantsPayloadResponse.roomLocked}get muteAll(){return this.privParticipantsPayloadResponse.muteAll}get type(){return this.privParticipantsPayloadResponse.type}static fromJSON(t){return new e(t)}}xd.ParticipantsListPayloadResponse=e;class t{constructor(e){this.privParticipantPayloadResponse=(e=>JSON.parse(e))(e)}get nickname(){return this.privParticipantPayloadResponse.nickname}get locale(){return this.privParticipantPayloadResponse.locale}get usetts(){return this.privParticipantPayloadResponse.usetts}get ismuted(){return this.privParticipantPayloadResponse.ismuted}get ishost(){return this.privParticipantPayloadResponse.ishost}get participantId(){return this.privParticipantPayloadResponse.participantId}get avatar(){return this.privParticipantPayloadResponse.avatar}static fromJSON(e){return new t(e)}}return xd.ParticipantPayloadResponse=t,xd}var Bd,jd,Ud,qd,Wd={};function Hd(){if(Bd)return Wd;Bd=1,Object.defineProperty(Wd,"__esModule",{value:!0}),Wd.TextResponsePayload=Wd.SpeechResponsePayload=void 0;class e{constructor(e){this.privSpeechResponse=(e=>JSON.parse(e))(e)}get recognition(){return this.privSpeechResponse.recognition}get translations(){return this.privSpeechResponse.translations}get id(){return this.privSpeechResponse.id}get language(){return this.privSpeechResponse.language}get nickname(){return this.privSpeechResponse.nickname}get participantId(){return this.privSpeechResponse.participantId}get roomid(){return this.privSpeechResponse.roomid}get timestamp(){return this.privSpeechResponse.timestamp}get type(){return this.privSpeechResponse.type}get isFinal(){return"final"===this.privSpeechResponse.type}static fromJSON(t){return new e(t)}}Wd.SpeechResponsePayload=e;class t{constructor(e){this.privTextResponse=(e=>JSON.parse(e))(e)}get originalText(){return this.privTextResponse.originalText}get translations(){return this.privTextResponse.translations}get id(){return this.privTextResponse.id}get language(){return this.privTextResponse.language}get nickname(){return this.privTextResponse.nickname}get participantId(){return this.privTextResponse.participantId}get roomid(){return this.privTextResponse.roomid}get timestamp(){return this.privTextResponse.timestamp}get type(){return this.privTextResponse.type}static fromJSON(e){return new t(e)}}return Wd.TextResponsePayload=t,Wd}function Vd(){return jd||(jd=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=_d();Object.defineProperty(e,"CommandResponsePayload",{enumerable:!0,get:function(){return t.CommandResponsePayload}});var r=Fd();Object.defineProperty(e,"ParticipantsListPayloadResponse",{enumerable:!0,get:function(){return r.ParticipantsListPayloadResponse}}),Object.defineProperty(e,"ParticipantPayloadResponse",{enumerable:!0,get:function(){return r.ParticipantPayloadResponse}});var i=Hd();Object.defineProperty(e,"SpeechResponsePayload",{enumerable:!0,get:function(){return i.SpeechResponsePayload}}),Object.defineProperty(e,"TextResponsePayload",{enumerable:!0,get:function(){return i.TextResponsePayload}})}(zd)),zd}function Kd(){if(Ud)return Id;Ud=1,Object.defineProperty(Id,"__esModule",{value:!0}),Id.ConversationServiceAdapter=void 0;const e=Gt(),t=Mp(),r=jv(),i=yd(),n=Td(),o=bd(),s=Dd(),a=Vd();let c=class extends r.ServiceRecognizerBase{constructor(t,r,i,o,s){super(t,r,i,o,s),this.privConnectionConfigPromise=void 0,this.privLastPartialUtteranceId="",this.privConversationServiceConnector=s,this.privConversationAuthentication=t,this.receiveMessageOverride=()=>this.receiveConversationMessageOverride(),this.recognizeOverride=()=>this.noOp(),this.postConnectImplOverride=e=>this.conversationConnectImpl(e),this.configConnectionOverride=()=>this.configConnection(),this.disconnectOverride=()=>this.privDisconnect(),this.privConversationRequestSession=new n.ConversationRequestSession(e.createNoDashGuid()),this.privConversationConnectionFactory=r,this.privConversationIsDisposed=!1}isDisposed(){return super.isDisposed()||this.privConversationIsDisposed}async dispose(e){if(this.privConversationIsDisposed=!0,void 0!==this.privConnectionConfigPromise){const t=await this.privConnectionConfigPromise;await t.dispose(e)}await super.dispose(e)}async sendMessage(t){return(await this.fetchConnection()).send(new i.ConversationConnectionMessage(e.MessageType.Text,t))}async sendMessageAsync(t){const r=await this.fetchConnection();await r.send(new i.ConversationConnectionMessage(e.MessageType.Text,t))}privDisconnect(){if(!this.terminateMessageLoop)return this.cancelRecognition(this.privConversationRequestSession.sessionId,this.privConversationRequestSession.requestId,t.CancellationReason.Error,t.CancellationErrorCode.NoError,"Disconnecting"),this.terminateMessageLoop=!0,Promise.resolve()}async processTypeSpecificMessages(){return!0}cancelRecognition(e,r,i,n,o){this.terminateMessageLoop=!0;const s=new t.ConversationTranslationCanceledEventArgs(i,o,n,void 0,e);try{this.privConversationServiceConnector.canceled&&this.privConversationServiceConnector.canceled(this.privConversationServiceConnector,s)}catch{}}async conversationConnectImpl(e){return this.privConnectionLoop=this.startMessageLoop(),e}async receiveConversationMessageOverride(){if(this.isDisposed()||this.terminateMessageLoop)return Promise.resolve();const i=new e.Deferred;try{const e=await this.fetchConnection(),c=await e.read();if(this.isDisposed()||this.terminateMessageLoop)return i.resolve(),Promise.resolve();if(!c)return this.receiveConversationMessageOverride();const p=this.privConversationRequestSession.sessionId,u=c.conversationMessageType.toLowerCase();let h=!1;try{switch(u){case"info":case"participant_command":case"command":const e=a.CommandResponsePayload.fromJSON(c.textBody);switch(e.command.toLowerCase()){case"participantlist":const i=a.ParticipantsListPayloadResponse.fromJSON(c.textBody),n=i.participants.map((e=>({avatar:e.avatar,displayName:e.nickname,id:e.participantId,isHost:e.ishost,isMuted:e.ismuted,isUsingTts:e.usetts,preferredLanguage:e.locale})));this.privConversationServiceConnector.participantsListReceived&&this.privConversationServiceConnector.participantsListReceived(this.privConversationServiceConnector,new o.ParticipantsListEventArgs(i.roomid,i.token,i.translateTo,i.profanityFilter,i.roomProfanityFilter,i.roomLocked,i.muteAll,n,p));break;case"settranslatetolanguages":this.privConversationServiceConnector.participantUpdateCommandReceived&&this.privConversationServiceConnector.participantUpdateCommandReceived(this.privConversationServiceConnector,new o.ParticipantAttributeEventArgs(e.participantId,s.ConversationTranslatorCommandTypes.setTranslateToLanguages,e.value,p));break;case"setprofanityfiltering":this.privConversationServiceConnector.participantUpdateCommandReceived&&this.privConversationServiceConnector.participantUpdateCommandReceived(this.privConversationServiceConnector,new o.ParticipantAttributeEventArgs(e.participantId,s.ConversationTranslatorCommandTypes.setProfanityFiltering,e.value,p));break;case"setmute":this.privConversationServiceConnector.participantUpdateCommandReceived&&this.privConversationServiceConnector.participantUpdateCommandReceived(this.privConversationServiceConnector,new o.ParticipantAttributeEventArgs(e.participantId,s.ConversationTranslatorCommandTypes.setMute,e.value,p));break;case"setmuteall":this.privConversationServiceConnector.muteAllCommandReceived&&this.privConversationServiceConnector.muteAllCommandReceived(this.privConversationServiceConnector,new o.MuteAllEventArgs(e.value,p));break;case"roomexpirationwarning":this.privConversationServiceConnector.conversationExpiration&&this.privConversationServiceConnector.conversationExpiration(this.privConversationServiceConnector,new t.ConversationExpirationEventArgs(e.value,this.privConversationRequestSession.sessionId));break;case"setusetts":this.privConversationServiceConnector.participantUpdateCommandReceived&&this.privConversationServiceConnector.participantUpdateCommandReceived(this.privConversationServiceConnector,new o.ParticipantAttributeEventArgs(e.participantId,s.ConversationTranslatorCommandTypes.setUseTTS,e.value,p));break;case"setlockstate":this.privConversationServiceConnector.lockRoomCommandReceived&&this.privConversationServiceConnector.lockRoomCommandReceived(this.privConversationServiceConnector,new o.LockRoomEventArgs(e.value,p));break;case"changenickname":this.privConversationServiceConnector.participantUpdateCommandReceived&&this.privConversationServiceConnector.participantUpdateCommandReceived(this.privConversationServiceConnector,new o.ParticipantAttributeEventArgs(e.participantId,s.ConversationTranslatorCommandTypes.changeNickname,e.value,p));break;case"joinsession":const u=a.ParticipantPayloadResponse.fromJSON(c.textBody),h={avatar:u.avatar,displayName:u.nickname,id:u.participantId,isHost:u.ishost,isMuted:u.ismuted,isUsingTts:u.usetts,preferredLanguage:u.locale};this.privConversationServiceConnector.participantJoinCommandReceived&&this.privConversationServiceConnector.participantJoinCommandReceived(this.privConversationServiceConnector,new o.ParticipantEventArgs(h,p));break;case"leavesession":const d={id:e.participantId};this.privConversationServiceConnector.participantLeaveCommandReceived&&this.privConversationServiceConnector.participantLeaveCommandReceived(this.privConversationServiceConnector,new o.ParticipantEventArgs(d,p));break;case"disconnectsession":e.participantId;break;case"token":const v=new r.CognitiveTokenAuthentication((()=>{const t=e.token;return Promise.resolve(t)}),(()=>{const t=e.token;return Promise.resolve(t)}));this.authentication=v,this.privConversationServiceConnector.onToken(v)}break;case"partial":case"final":const i=a.SpeechResponsePayload.fromJSON(c.textBody),n="final"===u?t.ResultReason.TranslatedParticipantSpeech:t.ResultReason.TranslatingParticipantSpeech,d=new t.ConversationTranslationResult(i.participantId,this.getTranslations(i.translations),i.language,i.id,n,i.recognition,void 0,void 0,c.textBody,void 0);i.isFinal?((void 0!==d.text&&d.text.length>0||i.id===this.privLastPartialUtteranceId)&&(h=!0),h&&this.privConversationServiceConnector.translationReceived&&this.privConversationServiceConnector.translationReceived(this.privConversationServiceConnector,new o.ConversationReceivedTranslationEventArgs(s.ConversationTranslatorMessageTypes.final,d,p))):void 0!==d.text&&(this.privLastPartialUtteranceId=i.id,this.privConversationServiceConnector.translationReceived&&this.privConversationServiceConnector.translationReceived(this.privConversationServiceConnector,new o.ConversationReceivedTranslationEventArgs(s.ConversationTranslatorMessageTypes.partial,d,p)));break;case"translated_message":const v=a.TextResponsePayload.fromJSON(c.textBody),l=new t.ConversationTranslationResult(v.participantId,this.getTranslations(v.translations),v.language,void 0,void 0,v.originalText,void 0,void 0,void 0,c.textBody,void 0);this.privConversationServiceConnector.translationReceived&&this.privConversationServiceConnector.translationReceived(this.privConversationServiceConnector,new o.ConversationReceivedTranslationEventArgs(s.ConversationTranslatorMessageTypes.instantMessage,l,p))}}catch(n){}return this.receiveConversationMessageOverride()}catch(n){this.terminateMessageLoop=!0}return i.promise}async startMessageLoop(){if(this.isDisposed())return Promise.resolve();this.terminateMessageLoop=!1;const e=this.receiveConversationMessageOverride();try{return await e}catch(r){return this.cancelRecognition(this.privRequestSession?this.privRequestSession.sessionId:"",this.privRequestSession?this.privRequestSession.requestId:"",t.CancellationReason.Error,t.CancellationErrorCode.RuntimeError,r),null}}configConnection(){return this.isDisposed()?Promise.resolve(void 0):void 0!==this.privConnectionConfigPromise?this.privConnectionConfigPromise.then((t=>t.state()===e.ConnectionState.Disconnected?(this.privConnectionId=null,this.privConnectionConfigPromise=void 0,this.configConnection()):this.privConnectionConfigPromise),(()=>(this.privConnectionId=null,this.privConnectionConfigPromise=void 0,this.configConnection()))):this.terminateMessageLoop?Promise.resolve(void 0):(this.privConnectionConfigPromise=this.connectImpl().then((e=>e)),this.privConnectionConfigPromise)}getTranslations(e){let r;if(void 0!==e){r=new t.Translations;for(const t of e)r.set(t.lang,t.translation)}return r}};return Id.ConversationServiceAdapter=c,Id}function Jd(){if(qd)return gd;qd=1,Object.defineProperty(gd,"__esModule",{value:!0}),gd.ConversationTranslatorRecognizer=gd.ConversationRecognizerFactory=void 0;const e=jv(),t=Gt(),r=Rr(),i=Mp(),n=Pd(),o=Kd();gd.ConversationRecognizerFactory=class{static fromConfig(e,t,r){return new s(e,t,r)}};let s=class extends i.Recognizer{constructor(e,o,s){const a=o;r.Contracts.throwIfNull(a,"speechConfig");const c=e;r.Contracts.throwIfNull(c,"conversationImpl"),super(s,a.properties,new n.ConversationConnectionFactory),this.privConversation=c,this.privIsDisposed=!1,this.privProperties=a.properties.clone(),this.privConnection=i.Connection.fromRecognizer(this);"on"===this.privProperties.getProperty(i.PropertyId.WebWorkerLoadType,"on").toLowerCase()&&"undefined"!=typeof Blob&&"undefined"!=typeof Worker?(this.privSetTimeout=t.Timeout.setTimeout,this.privClearTimeout=t.Timeout.clearTimeout):"undefined"!=typeof window?(this.privSetTimeout=window.setTimeout.bind(window),this.privClearTimeout=window.clearTimeout.bind(window)):(this.privSetTimeout=setTimeout,this.privClearTimeout=clearTimeout)}set connected(e){this.privConnection.connected=e}set disconnected(e){this.privConnection.disconnected=e}get speechRecognitionLanguage(){return this.privSpeechRecognitionLanguage}get properties(){return this.privProperties}isDisposed(){return this.privIsDisposed}connect(e,t,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),r.Contracts.throwIfNullOrWhitespace(e,"token"),this.privReco.conversationTranslatorToken=e,this.resetConversationTimeout(),this.privReco.connectAsync(t,i)}catch(n){if(i)if(n instanceof Error){const e=n;i(e.name+": "+e.message)}else i(n)}}disconnect(e,i){try{r.Contracts.throwIfDisposed(this.privIsDisposed),void 0!==this.privTimeoutToken&&this.privClearTimeout(this.privTimeoutToken),this.privReco.disconnect().then((()=>{e&&e()}),(e=>{i&&i(e)}))}catch(n){if(i)if(n instanceof Error){const e=n;i(e.name+": "+e.message)}else i(n);this.dispose(!0).catch((e=>{t.Events.instance.onEvent(new t.BackgroundEvent(e))}))}}sendRequest(e,i,n){try{r.Contracts.throwIfDisposed(this.privIsDisposed),this.sendMessage(e,i,n)}catch(o){if(n)if(o instanceof Error){const e=o;n(e.name+": "+e.message)}else n(o);this.dispose(!0).catch((e=>{t.Events.instance.onEvent(new t.BackgroundEvent(e))}))}}onToken(e){this.privConversation.onToken(e)}async close(){this.privIsDisposed||(this.privConnection&&(this.privConnection.closeConnection(),this.privConnection.close()),this.privConnection=void 0,await this.dispose(!0))}async dispose(e){this.privIsDisposed||e&&(void 0!==this.privTimeoutToken&&this.privClearTimeout(this.privTimeoutToken),this.privIsDisposed=!0,this.privConnection&&(this.privConnection.closeConnection(),this.privConnection.close(),this.privConnection=void 0),await super.dispose(e))}createRecognizerConfig(t){return new e.RecognizerConfig(t,this.privProperties)}createServiceRecognizer(e,t,r,i){const n=r;return new o.ConversationServiceAdapter(e,t,n,i,this)}sendMessage(e,t,r){const i=this.privReco;var n,o,s;n=i.sendMessageAsync(e),o=t,s=r,void 0!==n?n.then((()=>{try{o&&o()}catch(e){s&&s(`'Unhandled error on promise callback: ${e}'`)}}),(e=>{try{s&&s(e)}catch(t){}})):s&&s("Null promise"),this.resetConversationTimeout()}resetConversationTimeout(){void 0!==this.privTimeoutToken&&this.privClearTimeout(this.privTimeoutToken),this.privTimeoutToken=this.privSetTimeout((()=>{this.sendRequest(this.privConversation.getKeepAlive())}),6e4)}};return gd.ConversationTranslatorRecognizer=s,gd}var Gd,$d,Qd={};function Xd(){if(Gd)return Qd;Gd=1,Object.defineProperty(Qd,"__esModule",{value:!0}),Qd.TranscriberRecognizer=void 0;const e=Gt(),t=Rr(),r=Mp(),i=jv();let n=class extends r.Recognizer{constructor(e,n){const o=e;t.Contracts.throwIfNull(o,"speechTranslationConfig");const s=n;t.Contracts.throwIfNull(s,"audioConfigImpl"),t.Contracts.throwIfNullOrWhitespace(o.speechRecognitionLanguage,r.PropertyId[r.PropertyId.SpeechServiceConnection_RecoLanguage]),super(n,o.properties,new i.TranscriberConnectionFactory),this.privDisposedRecognizer=!1,this.isMeetingRecognizer=!1}get speechRecognitionLanguage(){return t.Contracts.throwIfDisposed(this.privDisposedRecognizer),this.properties.getProperty(r.PropertyId.SpeechServiceConnection_RecoLanguage)}get properties(){return this.privProperties}get authorizationToken(){return this.properties.getProperty(r.PropertyId.SpeechServiceAuthorization_Token)}set authorizationToken(e){t.Contracts.throwIfNullOrWhitespace(e,"token"),this.properties.setProperty(r.PropertyId.SpeechServiceAuthorization_Token,e)}set conversation(e){t.Contracts.throwIfNullOrUndefined(e,"Conversation"),this.isMeetingRecognizer=!1,this.privConversation=e}getConversationInfo(){return t.Contracts.throwIfNullOrUndefined(this.privConversation,"Conversation"),this.privConversation.conversationInfo}set meeting(e){t.Contracts.throwIfNullOrUndefined(e,"Meeting"),this.isMeetingRecognizer=!0,this.privMeeting=e}getMeetingInfo(){return t.Contracts.throwIfNullOrUndefined(this.privMeeting,"Meeting"),this.privMeeting.meetingInfo}IsMeetingRecognizer(){return this.isMeetingRecognizer}startContinuousRecognitionAsync(t,r){e.marshalPromiseToCallbacks(this.startContinuousRecognitionAsyncImpl(i.RecognitionMode.Conversation),t,r)}stopContinuousRecognitionAsync(t,r){e.marshalPromiseToCallbacks(this.stopContinuousRecognitionAsyncImpl(),t,r)}async close(){this.privDisposedRecognizer||await this.dispose(!0)}async pushConversationEvent(e,r){const i=this.privReco;t.Contracts.throwIfNullOrUndefined(i,"serviceRecognizer"),await i.sendSpeechEventAsync(e,r)}async pushMeetingEvent(e,r){const i=this.privReco;t.Contracts.throwIfNullOrUndefined(i,"serviceRecognizer"),await i.sendMeetingSpeechEventAsync(e,r)}async enforceAudioGating(){const e=this.audioConfig,t=(await e.format).channels;if(1===t){if("true"!==this.properties.getProperty("f0f5debc-f8c9-4892-ac4b-90a7ab359fd2","false").toLowerCase())throw new Error("Single channel audio configuration for MeetingTranscriber is currently under private preview, <NAME_EMAIL> for more details")}else if(8!==t)throw new Error(`Unsupported audio configuration: Detected ${t}-channel audio`)}connectMeetingCallbacks(e){this.isMeetingRecognizer=!0,this.canceled=(t,r)=>{e.canceled&&e.canceled(e,r)},this.recognizing=(t,r)=>{e.transcribing&&e.transcribing(e,r)},this.recognized=(t,r)=>{e.transcribed&&e.transcribed(e,r)},this.sessionStarted=(t,r)=>{e.sessionStarted&&e.sessionStarted(e,r)},this.sessionStopped=(t,r)=>{e.sessionStopped&&e.sessionStopped(e,r)}}disconnectCallbacks(){this.canceled=void 0,this.recognizing=void 0,this.recognized=void 0,this.sessionStarted=void 0,this.sessionStopped=void 0}async dispose(e){this.privDisposedRecognizer||(e&&(this.privDisposedRecognizer=!0,await this.implRecognizerStop()),await super.dispose(e))}createRecognizerConfig(e){return new i.RecognizerConfig(e,this.properties)}createServiceRecognizer(e,t,r,n){const o=r;return new i.TranscriptionServiceRecognizer(e,t,o,n,this)}};return Qd.TranscriberRecognizer=n,Qd}function Zd(){return $d||($d=1,function(e){Object.defineProperty(e,"__esModule",{value:!0});var t=hd();Object.defineProperty(e,"ConversationManager",{enumerable:!0,get:function(){return t.ConversationManager}});var r=ud();Object.defineProperty(e,"ConversationConnectionConfig",{enumerable:!0,get:function(){return r.ConversationConnectionConfig}});var i=Jd();Object.defineProperty(e,"ConversationRecognizerFactory",{enumerable:!0,get:function(){return i.ConversationRecognizerFactory}});var n=Xd();Object.defineProperty(e,"TranscriberRecognizer",{enumerable:!0,get:function(){return n.TranscriberRecognizer}});var o=bd();Object.defineProperty(e,"ConversationReceivedTranslationEventArgs",{enumerable:!0,get:function(){return o.ConversationReceivedTranslationEventArgs}}),Object.defineProperty(e,"LockRoomEventArgs",{enumerable:!0,get:function(){return o.LockRoomEventArgs}}),Object.defineProperty(e,"MuteAllEventArgs",{enumerable:!0,get:function(){return o.MuteAllEventArgs}}),Object.defineProperty(e,"ParticipantAttributeEventArgs",{enumerable:!0,get:function(){return o.ParticipantAttributeEventArgs}}),Object.defineProperty(e,"ParticipantEventArgs",{enumerable:!0,get:function(){return o.ParticipantEventArgs}}),Object.defineProperty(e,"ParticipantsListEventArgs",{enumerable:!0,get:function(){return o.ParticipantsListEventArgs}});var s=Dd();Object.defineProperty(e,"ConversationTranslatorCommandTypes",{enumerable:!0,get:function(){return s.ConversationTranslatorCommandTypes}}),Object.defineProperty(e,"ConversationTranslatorMessageTypes",{enumerable:!0,get:function(){return s.ConversationTranslatorMessageTypes}}),Object.defineProperty(e,"InternalParticipants",{enumerable:!0,get:function(){return s.InternalParticipants}})}(ad)),ad}var Yd,ev={};function tv(){return Yd||(Yd=1,function(e){var t;Object.defineProperty(e,"__esModule",{value:!0}),e.SynthesisAudioMetadata=e.MetadataType=void 0,(t=e.MetadataType||(e.MetadataType={})).WordBoundary="WordBoundary",t.Bookmark="Bookmark",t.Viseme="Viseme",t.SentenceBoundary="SentenceBoundary",t.SessionEnd="SessionEnd",t.AvatarSignal="TalkingAvatarSignal";class r{constructor(e){this.privSynthesisAudioMetadata=JSON.parse(e)}static fromJSON(e){return new r(e)}get Metadata(){return this.privSynthesisAudioMetadata.Metadata}}e.SynthesisAudioMetadata=r}(ev)),ev}var rv,iv,nv={},ov={};function sv(){if(rv)return ov;rv=1,Object.defineProperty(ov,"__esModule",{value:!0}),ov.SynthesisStartedEvent=ov.ConnectingToSynthesisServiceEvent=ov.SynthesisTriggeredEvent=ov.SpeechSynthesisEvent=void 0;const e=Gt();class t extends e.PlatformEvent{constructor(t,r,i=e.EventType.Info){super(t,i),this.privRequestId=r}get requestId(){return this.privRequestId}}ov.SpeechSynthesisEvent=t;ov.SynthesisTriggeredEvent=class extends t{constructor(e,t,r){super("SynthesisTriggeredEvent",e),this.privSessionAudioDestinationId=t,this.privTurnAudioDestinationId=r}get audioSessionDestinationId(){return this.privSessionAudioDestinationId}get audioTurnDestinationId(){return this.privTurnAudioDestinationId}};ov.ConnectingToSynthesisServiceEvent=class extends t{constructor(e,t){super("ConnectingToSynthesisServiceEvent",e),this.privAuthFetchEventId=t}get authFetchEventId(){return this.privAuthFetchEventId}};return ov.SynthesisStartedEvent=class extends t{constructor(e,t){super("SynthesisStartedEvent",e),this.privAuthFetchEventId=t}get authFetchEventId(){return this.privAuthFetchEventId}},ov}function av(){if(iv)return nv;iv=1,Object.defineProperty(nv,"__esModule",{value:!0}),nv.SynthesisTurn=void 0;const e=Gt(),t=di(),r=Mp(),i=tv(),n=sv();return nv.SynthesisTurn=class o{constructor(){this.privIsDisposed=!1,this.privIsSynthesizing=!1,this.privIsSynthesisEnded=!1,this.privBytesReceived=0,this.privInTurn=!1,this.privTextOffset=0,this.privNextSearchTextIndex=0,this.privSentenceOffset=0,this.privNextSearchSentenceIndex=0,this.privRequestId=e.createNoDashGuid(),this.privTurnDeferral=new e.Deferred,this.privTurnDeferral.resolve()}get requestId(){return this.privRequestId}get streamId(){return this.privStreamId}set streamId(e){this.privStreamId=e}get audioOutputFormat(){return this.privAudioOutputFormat}set audioOutputFormat(e){this.privAudioOutputFormat=e}get turnCompletionPromise(){return this.privTurnDeferral.promise}get isSynthesisEnded(){return this.privIsSynthesisEnded}get isSynthesizing(){return this.privIsSynthesizing}get currentTextOffset(){return this.privTextOffset}get currentSentenceOffset(){return this.privSentenceOffset}get bytesReceived(){return this.privBytesReceived}get audioDuration(){return this.privAudioDuration}get extraProperties(){if(this.privWebRTCSDP){const e=new r.PropertyCollection;return e.setProperty(r.PropertyId.TalkingAvatarService_WebRTC_SDP,this.privWebRTCSDP),e}}async getAllReceivedAudio(){return this.privReceivedAudio?Promise.resolve(this.privReceivedAudio):this.privIsSynthesisEnded?(await this.readAllAudioFromStream(),Promise.resolve(this.privReceivedAudio)):null}async getAllReceivedAudioWithHeader(){if(this.privReceivedAudioWithHeader)return this.privReceivedAudioWithHeader;if(!this.privIsSynthesisEnded)return null;if(this.audioOutputFormat.hasHeader){const e=await this.getAllReceivedAudio();return this.privReceivedAudioWithHeader=this.audioOutputFormat.addHeader(e),this.privReceivedAudioWithHeader}return this.getAllReceivedAudio()}startNewSynthesis(e,r,i,o){this.privIsSynthesisEnded=!1,this.privIsSynthesizing=!0,this.privRequestId=e,this.privRawText=r,this.privIsSSML=i,this.privAudioOutputStream=new t.PullAudioOutputStreamImpl,this.privAudioOutputStream.format=this.privAudioOutputFormat,this.privReceivedAudio=null,this.privReceivedAudioWithHeader=null,this.privBytesReceived=0,this.privTextOffset=0,this.privNextSearchTextIndex=0,this.privSentenceOffset=0,this.privNextSearchSentenceIndex=0,this.privPartialVisemeAnimation="",this.privWebRTCSDP="",void 0!==o&&(this.privTurnAudioDestination=o,this.privTurnAudioDestination.format=this.privAudioOutputFormat),this.onEvent(new n.SynthesisTriggeredEvent(this.requestId,void 0,void 0===o?void 0:o.id()))}onPreConnectionStart(e){this.privAuthFetchEventId=e,this.onEvent(new n.ConnectingToSynthesisServiceEvent(this.privRequestId,this.privAuthFetchEventId))}onAuthCompleted(e){e&&this.onComplete()}onConnectionEstablishCompleted(e){if(200===e)return this.onEvent(new n.SynthesisStartedEvent(this.requestId,this.privAuthFetchEventId)),void(this.privBytesReceived=0);403===e&&this.onComplete()}onServiceResponseMessage(e){const t=JSON.parse(e);this.streamId=t.audio.streamId}onServiceTurnEndResponse(){this.privInTurn=!1,this.privTurnDeferral.resolve(),this.onComplete()}onServiceTurnStartResponse(t){this.privTurnDeferral&&this.privInTurn&&(this.privTurnDeferral.reject("Another turn started before current completed."),this.privTurnDeferral.promise.then().catch((()=>{}))),this.privInTurn=!0,this.privTurnDeferral=new e.Deferred;const r=JSON.parse(t);r.webrtc&&(this.privWebRTCSDP=r.webrtc.connectionString)}onAudioChunkReceived(e){this.isSynthesizing&&(this.privAudioOutputStream.write(e),this.privBytesReceived+=e.byteLength,void 0!==this.privTurnAudioDestination&&this.privTurnAudioDestination.write(e))}onTextBoundaryEvent(e){this.updateTextOffset(e.Data.text.Text,e.Type)}onVisemeMetadataReceived(e){void 0!==e.Data.AnimationChunk&&(this.privPartialVisemeAnimation+=e.Data.AnimationChunk)}onSessionEnd(e){this.privAudioDuration=e.Data.Offset}async constructSynthesisResult(){const e=await this.getAllReceivedAudioWithHeader();return new r.SpeechSynthesisResult(this.requestId,r.ResultReason.SynthesizingAudioCompleted,e,void 0,this.extraProperties,this.audioDuration)}dispose(){this.privIsDisposed||(this.privIsDisposed=!0)}onStopSynthesizing(){this.onComplete()}getAndClearVisemeAnimation(){const e=this.privPartialVisemeAnimation;return this.privPartialVisemeAnimation="",e}onEvent(t){e.Events.instance.onEvent(t)}static isXmlTag(e){return e.length>=2&&"<"===e[0]&&">"===e[e.length-1]}updateTextOffset(e,t){t===i.MetadataType.WordBoundary?(this.privTextOffset=this.privRawText.indexOf(e,this.privNextSearchTextIndex),this.privTextOffset>=0&&(this.privNextSearchTextIndex=this.privTextOffset+e.length,this.privIsSSML&&this.withinXmlTag(this.privTextOffset)&&!o.isXmlTag(e)&&this.updateTextOffset(e,t))):(this.privSentenceOffset=this.privRawText.indexOf(e,this.privNextSearchSentenceIndex),this.privSentenceOffset>=0&&(this.privNextSearchSentenceIndex=this.privSentenceOffset+e.length,this.privIsSSML&&this.withinXmlTag(this.privSentenceOffset)&&!o.isXmlTag(e)&&this.updateTextOffset(e,t)))}onComplete(){this.privIsSynthesizing&&(this.privIsSynthesizing=!1,this.privIsSynthesisEnded=!0,this.privAudioOutputStream.close(),this.privInTurn=!1,void 0!==this.privTurnAudioDestination&&(this.privTurnAudioDestination.close(),this.privTurnAudioDestination=void 0))}async readAllAudioFromStream(){if(this.privIsSynthesisEnded){this.privReceivedAudio=new ArrayBuffer(this.bytesReceived);try{await this.privAudioOutputStream.read(this.privReceivedAudio)}catch(e){this.privReceivedAudio=new ArrayBuffer(0)}}}withinXmlTag(e){return this.privRawText.indexOf("<",e+1)>this.privRawText.indexOf(">",e+1)}},nv}var cv,pv={};function uv(){if(cv)return pv;cv=1,Object.defineProperty(pv,"__esModule",{value:!0}),pv.SynthesisAdapterBase=void 0;const e=Gt(),t=Mp(),r=jv(),i=iu();let n=class{constructor(i,n,o,s){if(this.speakOverride=void 0,this.receiveMessageOverride=void 0,this.connectImplOverride=void 0,this.configConnectionOverride=void 0,this.privConnectionConfigurationPromise=void 0,!i)throw new e.ArgumentNullError("authentication");if(!n)throw new e.ArgumentNullError("connectionFactory");if(!o)throw new e.ArgumentNullError("synthesizerConfig");this.privAuthentication=i,this.privConnectionFactory=n,this.privSynthesizerConfig=o,this.privIsDisposed=!1,this.privSessionAudioDestination=s,this.privSynthesisTurn=new r.SynthesisTurn,this.privConnectionEvents=new e.EventSource,this.privServiceEvents=new e.EventSource,this.privSynthesisContext=new r.SynthesisContext,this.privAgentConfig=new r.AgentConfig,this.connectionEvents.attach((e=>{if("ConnectionClosedEvent"===e.name){const r=e;1e3!==r.statusCode&&this.cancelSynthesisLocal(t.CancellationReason.Error,1007===r.statusCode?t.CancellationErrorCode.BadRequestParameters:t.CancellationErrorCode.ConnectionFailure,`${r.reason} websocket error code: ${r.statusCode}`)}}))}get synthesisContext(){return this.privSynthesisContext}get agentConfig(){return this.privAgentConfig}get connectionEvents(){return this.privConnectionEvents}get serviceEvents(){return this.privServiceEvents}set activityTemplate(e){this.privActivityTemplate=e}get activityTemplate(){return this.privActivityTemplate}set audioOutputFormat(e){this.privAudioOutputFormat=e,this.privSynthesisTurn.audioOutputFormat=e,void 0!==this.privSessionAudioDestination&&(this.privSessionAudioDestination.format=e),void 0!==this.synthesisContext&&(this.synthesisContext.audioOutputFormat=e)}isDisposed(){return this.privIsDisposed}async dispose(e){if(this.privIsDisposed=!0,void 0!==this.privSessionAudioDestination&&this.privSessionAudioDestination.close(),void 0!==this.privConnectionConfigurationPromise){const t=await this.privConnectionConfigurationPromise;await t.dispose(e)}}async connect(){await this.connectImpl()}async sendNetworkMessage(t,r){const n="string"==typeof r?e.MessageType.Text:e.MessageType.Binary,o="string"==typeof r?"application/json":"";return(await this.fetchConnection()).send(new i.SpeechConnectionMessage(n,t,this.privSynthesisTurn.requestId,o,r))}async Speak(e,r,i,n,o,s){let a;if(a=r?e:this.privSynthesizer.buildSsml(e),void 0!==this.speakOverride)return this.speakOverride(a,i,n,o);this.privSuccessCallback=n,this.privErrorCallback=o,this.privSynthesisTurn.startNewSynthesis(i,e,r,s);try{await this.connectImpl();const e=await this.fetchConnection();await this.sendSynthesisContext(e),await this.sendSsmlMessage(e,a,i),this.onSynthesisStarted(i),this.receiveMessage()}catch(c){return this.cancelSynthesisLocal(t.CancellationReason.Error,t.CancellationErrorCode.ConnectionFailure,c),Promise.reject(c)}}async stopSpeaking(){await this.connectImpl();return(await this.fetchConnection()).send(new i.SpeechConnectionMessage(e.MessageType.Text,"synthesis.control",this.privSynthesisTurn.requestId,"application/json",JSON.stringify({action:"stop"})))}cancelSynthesis(e,i,n,o){const s=new t.PropertyCollection;s.setProperty(r.CancellationErrorCodePropertyName,t.CancellationErrorCode[n]);const a=new t.SpeechSynthesisResult(e,t.ResultReason.Canceled,void 0,o,s);if(this.onSynthesisCancelled(a),this.privSuccessCallback)try{this.privSuccessCallback(a)}catch{}}cancelSynthesisLocal(e,t,r){this.privSynthesisTurn.isSynthesizing&&(this.privSynthesisTurn.onStopSynthesizing(),this.cancelSynthesis(this.privSynthesisTurn.requestId,e,t,r))}processTypeSpecificMessages(e){return!0}async receiveMessage(){try{const o=await this.fetchConnection(),s=await o.read();if(void 0!==this.receiveMessageOverride)return this.receiveMessageOverride();if(this.privIsDisposed)return;if(!s)return this.privSynthesisTurn.isSynthesizing?this.receiveMessage():void 0;const a=i.SpeechConnectionMessage.fromConnectionMessage(s);if(a.requestId.toLowerCase()===this.privSynthesisTurn.requestId.toLowerCase())switch(a.path.toLowerCase()){case"turn.start":this.privSynthesisTurn.onServiceTurnStartResponse(a.textBody);break;case"response":this.privSynthesisTurn.onServiceResponseMessage(a.textBody);break;case"audio":this.privSynthesisTurn.streamId.toLowerCase()===a.streamId.toLowerCase()&&a.binaryBody&&(this.privSynthesisTurn.onAudioChunkReceived(a.binaryBody),this.onSynthesizing(a.binaryBody),void 0!==this.privSessionAudioDestination&&this.privSessionAudioDestination.write(a.binaryBody));break;case"audio.metadata":const i=r.SynthesisAudioMetadata.fromJSON(a.textBody).Metadata;for(const e of i)switch(e.Type){case r.MetadataType.WordBoundary:case r.MetadataType.SentenceBoundary:this.privSynthesisTurn.onTextBoundaryEvent(e);const i=new t.SpeechSynthesisWordBoundaryEventArgs(e.Data.Offset,e.Data.Duration,e.Data.text.Text,e.Data.text.Length,e.Type===r.MetadataType.WordBoundary?this.privSynthesisTurn.currentTextOffset:this.privSynthesisTurn.currentSentenceOffset,e.Data.text.BoundaryType);this.onWordBoundary(i);break;case r.MetadataType.Bookmark:const n=new t.SpeechSynthesisBookmarkEventArgs(e.Data.Offset,e.Data.Bookmark);this.onBookmarkReached(n);break;case r.MetadataType.Viseme:if(this.privSynthesisTurn.onVisemeMetadataReceived(e),e.Data.IsLastAnimation){const r=new t.SpeechSynthesisVisemeEventArgs(e.Data.Offset,e.Data.VisemeId,this.privSynthesisTurn.getAndClearVisemeAnimation());this.onVisemeReceived(r)}break;case r.MetadataType.AvatarSignal:this.onAvatarEvent(e);break;case r.MetadataType.SessionEnd:this.privSynthesisTurn.onSessionEnd(e)}break;case"turn.end":let o;this.privSynthesisTurn.onServiceTurnEndResponse();try{o=await this.privSynthesisTurn.constructSynthesisResult(),this.privSuccessCallback&&this.privSuccessCallback(o)}catch(n){this.privErrorCallback&&this.privErrorCallback(n)}this.onSynthesisCompleted(o);break;default:this.processTypeSpecificMessages(a)||this.privServiceEvents&&this.serviceEvents.onEvent(new e.ServiceEvent(a.path.toLowerCase(),a.textBody))}return this.receiveMessage()}catch(o){}}sendSynthesisContext(t){this.setSynthesisContextSynthesisSection();const r=this.synthesisContext.toJSON();if(r)return t.send(new i.SpeechConnectionMessage(e.MessageType.Text,"synthesis.context",this.privSynthesisTurn.requestId,"application/json",r))}setSpeechConfigSynthesisSection(){}connectImpl(r=!1){if(null!=this.privConnectionPromise)return this.privConnectionPromise.then((t=>t.state()===e.ConnectionState.Disconnected?(this.privConnectionId=null,this.privConnectionPromise=null,this.connectImpl()):this.privConnectionPromise),(()=>(this.privConnectionId=null,this.privConnectionPromise=null,this.connectImpl())));this.privAuthFetchEventId=e.createNoDashGuid(),this.privConnectionId=e.createNoDashGuid(),this.privSynthesisTurn.onPreConnectionStart(this.privAuthFetchEventId);const i=r?this.privAuthentication.fetchOnExpiry(this.privAuthFetchEventId):this.privAuthentication.fetch(this.privAuthFetchEventId);return this.privConnectionPromise=i.then((async e=>{this.privSynthesisTurn.onAuthCompleted(!1);const i=this.privConnectionFactory.create(this.privSynthesizerConfig,e,this.privConnectionId);i.events.attach((e=>{this.connectionEvents.onEvent(e)}));const n=await i.open();return 200===n.statusCode?(this.privSynthesisTurn.onConnectionEstablishCompleted(n.statusCode),Promise.resolve(i)):403!==n.statusCode||r?(this.privSynthesisTurn.onConnectionEstablishCompleted(n.statusCode),Promise.reject(`Unable to contact server. StatusCode: ${n.statusCode},\n                    ${this.privSynthesizerConfig.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Url)} Reason: ${n.reason}`)):this.connectImpl(!0)}),(e=>{throw this.privSynthesisTurn.onAuthCompleted(!0),new Error(e)})),this.privConnectionPromise.catch((()=>{})),this.privConnectionPromise}sendSpeechServiceConfig(t,r){if(r)return t.send(new i.SpeechConnectionMessage(e.MessageType.Text,"speech.config",this.privSynthesisTurn.requestId,"application/json",r))}sendSsmlMessage(t,r,n){return t.send(new i.SpeechConnectionMessage(e.MessageType.Text,"ssml",n,"application/ssml+xml",r))}async fetchConnection(){return void 0!==this.privConnectionConfigurationPromise?this.privConnectionConfigurationPromise.then((t=>t.state()===e.ConnectionState.Disconnected?(this.privConnectionId=null,this.privConnectionConfigurationPromise=void 0,this.fetchConnection()):this.privConnectionConfigurationPromise),(()=>(this.privConnectionId=null,this.privConnectionConfigurationPromise=void 0,this.fetchConnection()))):(this.privConnectionConfigurationPromise=this.configureConnection(),await this.privConnectionConfigurationPromise)}async configureConnection(){const e=await this.connectImpl();return void 0!==this.configConnectionOverride?this.configConnectionOverride(e):(this.setSpeechConfigSynthesisSection(),await this.sendSpeechServiceConfig(e,this.privSynthesizerConfig.SpeechServiceConfig.serialize()),e)}onAvatarEvent(e){}onSynthesisStarted(e){}onSynthesizing(e){}onSynthesisCancelled(e){}onSynthesisCompleted(e){}onWordBoundary(e){}onVisemeReceived(e){}onBookmarkReached(e){}};return pv.SynthesisAdapterBase=n,n.telemetryDataEnabled=!0,pv}var hv,dv={};function vv(){if(hv)return dv;hv=1,Object.defineProperty(dv,"__esModule",{value:!0}),dv.AvatarSynthesisAdapter=void 0;const e=Mp(),t=jv();let r=class extends t.SynthesisAdapterBase{constructor(e,t,r,i,n){super(e,t,r,void 0),this.privAvatarSynthesizer=i,this.privSynthesizer=i,this.privAvatarConfig=n}setSynthesisContextSynthesisSection(){this.privSynthesisContext.setSynthesisSection(void 0)}setSpeechConfigSynthesisSection(){var t,r,i,n,o,s,a,c,p,u,h,d,v,l,g,f,m;this.privSynthesizerConfig.synthesisVideoSection={format:{bitrate:null==(t=this.privAvatarConfig.videoFormat)?void 0:t.bitrate,codec:null==(r=this.privAvatarConfig.videoFormat)?void 0:r.codec,crop:{bottomRight:{x:null==(o=null==(n=null==(i=this.privAvatarConfig.videoFormat)?void 0:i.cropRange)?void 0:n.bottomRight)?void 0:o.x,y:null==(c=null==(a=null==(s=this.privAvatarConfig.videoFormat)?void 0:s.cropRange)?void 0:a.bottomRight)?void 0:c.y},topLeft:{x:null==(h=null==(u=null==(p=this.privAvatarConfig.videoFormat)?void 0:p.cropRange)?void 0:u.topLeft)?void 0:h.x,y:null==(l=null==(v=null==(d=this.privAvatarConfig.videoFormat)?void 0:d.cropRange)?void 0:v.topLeft)?void 0:l.y}},resolution:{height:null==(g=this.privAvatarConfig.videoFormat)?void 0:g.height,width:null==(f=this.privAvatarConfig.videoFormat)?void 0:f.width}},protocol:{name:"WebRTC",webrtcConfig:{clientDescription:btoa(this.privSynthesizerConfig.parameters.getProperty(e.PropertyId.TalkingAvatarService_WebRTC_SDP)),iceServers:this.privAvatarConfig.remoteIceServers??this.privAvatarSynthesizer.iceServers}},talkingAvatar:{background:{color:this.privAvatarConfig.backgroundColor,image:{url:null==(m=this.privAvatarConfig.backgroundImage)?void 0:m.toString()}},character:this.privAvatarConfig.character,customized:this.privAvatarConfig.customized,style:this.privAvatarConfig.style,useBuiltInVoice:this.privAvatarConfig.useBuiltInVoice}}}onAvatarEvent(t){if(this.privAvatarSynthesizer.avatarEventReceived){const i=new e.AvatarEventArgs(t.Data.Offset,t.Data.Name);try{this.privAvatarSynthesizer.avatarEventReceived(this.privAvatarSynthesizer,i)}catch(r){}}}};return dv.AvatarSynthesisAdapter=r,dv}var lv,gv={};function fv(){if(lv)return gv;lv=1,Object.defineProperty(gv,"__esModule",{value:!0}),gv.SpeechSynthesisAdapter=void 0;const e=Mp(),t=jv();let r=class extends t.SynthesisAdapterBase{constructor(e,t,r,i,n){super(e,t,r,n),this.privSpeechSynthesizer=i,this.privSynthesizer=i}setSynthesisContextSynthesisSection(){this.privSynthesisContext.setSynthesisSection(this.privSpeechSynthesizer)}onSynthesisStarted(t){const r=new e.SpeechSynthesisEventArgs(new e.SpeechSynthesisResult(t,e.ResultReason.SynthesizingAudioStarted));this.privSpeechSynthesizer.synthesisStarted&&this.privSpeechSynthesizer.synthesisStarted(this.privSpeechSynthesizer,r)}onSynthesizing(t){if(this.privSpeechSynthesizer.synthesizing)try{const r=this.privSynthesisTurn.audioOutputFormat.addHeader(t),i=new e.SpeechSynthesisEventArgs(new e.SpeechSynthesisResult(this.privSynthesisTurn.requestId,e.ResultReason.SynthesizingAudio,r));this.privSpeechSynthesizer.synthesizing(this.privSpeechSynthesizer,i)}catch(r){}}onSynthesisCancelled(t){if(this.privSpeechSynthesizer.SynthesisCanceled){const r=new e.SpeechSynthesisEventArgs(t);try{this.privSpeechSynthesizer.SynthesisCanceled(this.privSpeechSynthesizer,r)}catch{}}}onSynthesisCompleted(t){if(this.privSpeechSynthesizer.synthesisCompleted)try{this.privSpeechSynthesizer.synthesisCompleted(this.privSpeechSynthesizer,new e.SpeechSynthesisEventArgs(t))}catch(r){}}onWordBoundary(e){if(this.privSpeechSynthesizer.wordBoundary)try{this.privSpeechSynthesizer.wordBoundary(this.privSpeechSynthesizer,e)}catch(t){}}onVisemeReceived(e){if(this.privSpeechSynthesizer.visemeReceived)try{this.privSpeechSynthesizer.visemeReceived(this.privSpeechSynthesizer,e)}catch(t){}}onBookmarkReached(e){if(this.privSpeechSynthesizer.bookmarkReached)try{this.privSpeechSynthesizer.bookmarkReached(this.privSpeechSynthesizer,e)}catch(t){}}};return gv.SpeechSynthesisAdapter=r,gv}var mv,Sv={};function yv(){if(mv)return Sv;mv=1,Object.defineProperty(Sv,"__esModule",{value:!0}),Sv.SynthesisRestAdapter=void 0;const e=Vp(),t=Mp(),r=Zo(),i=Xt();return Sv.SynthesisRestAdapter=class{constructor(i,n){let o=i.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Endpoint,void 0);if(!o){const e=i.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Region,"westus"),n=r.ConnectionFactoryBase.getHostSuffix(e);o=i.parameters.getProperty(t.PropertyId.SpeechServiceConnection_Host,`https://${e}.tts.speech${n}`)}this.privUri=`${o}/cognitiveservices/voices/list`;const s=e.RestConfigBase.requestOptions;this.privRestAdapter=new e.RestMessageAdapter(s),this.privAuthentication=n}getVoicesList(t){return this.privRestAdapter.setHeaders(i.HeaderNames.ConnectionId,t),this.privAuthentication.fetch(t).then((t=>(this.privRestAdapter.setHeaders(t.headerName,t.token),this.privRestAdapter.request(e.RestRequestType.Get,this.privUri))))}},Sv}var Cv,Pv={};function Rv(){return Cv||(Cv=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.SynthesizerConfig=e.SynthesisServiceType=void 0;const t=jv();var r,i;(i=r=e.SynthesisServiceType||(e.SynthesisServiceType={}))[i.Standard=0]="Standard",i[i.Custom=1]="Custom";e.SynthesizerConfig=class{constructor(e,i){this.privSynthesisServiceType=r.Standard,this.avatarEnabled=!1,this.privSpeechServiceConfig=e||new t.SpeechServiceConfig(new t.Context(null)),this.privParameters=i}get parameters(){return this.privParameters}get synthesisServiceType(){return this.privSynthesisServiceType}set synthesisServiceType(e){this.privSynthesisServiceType=e}set synthesisVideoSection(e){this.privSpeechServiceConfig.Context.synthesis={video:e}}get SpeechServiceConfig(){return this.privSpeechServiceConfig}}}(Pv)),Pv}var Iv,wv={};function Tv(){if(Iv)return wv;Iv=1,Object.defineProperty(wv,"__esModule",{value:!0}),wv.SynthesisContext=void 0;const e=Mp();return wv.SynthesisContext=class{constructor(){this.privContext={}}setSection(e,t){this.privContext[e]=t}set audioOutputFormat(e){this.privAudioOutputFormat=e}toJSON(){return JSON.stringify(this.privContext)}setSynthesisSection(e){const t=this.buildSynthesisContext(e);this.setSection("synthesis",t)}buildSynthesisContext(t){return{audio:{metadataOptions:{bookmarkEnabled:!!(null==t?void 0:t.bookmarkReached),punctuationBoundaryEnabled:null==t?void 0:t.properties.getProperty(e.PropertyId.SpeechServiceResponse_RequestPunctuationBoundary,!!(null==t?void 0:t.wordBoundary)),sentenceBoundaryEnabled:null==t?void 0:t.properties.getProperty(e.PropertyId.SpeechServiceResponse_RequestSentenceBoundary,!1),sessionEndEnabled:!0,visemeEnabled:!!(null==t?void 0:t.visemeReceived),wordBoundaryEnabled:null==t?void 0:t.properties.getProperty(e.PropertyId.SpeechServiceResponse_RequestWordBoundary,!!(null==t?void 0:t.wordBoundary))},outputFormat:this.privAudioOutputFormat.requestAudioFormatString},language:{autoDetection:null==t?void 0:t.autoDetectSourceLanguage}}}},wv}var Av,Ev={};function bv(){if(Av)return Ev;Av=1,Object.defineProperty(Ev,"__esModule",{value:!0}),Ev.SpeakerRecognitionConfig=void 0;const e=jv();return Ev.SpeakerRecognitionConfig=class{constructor(t,r){this.privContext=t||new e.Context(null),this.privParameters=r}get parameters(){return this.privParameters}get Context(){return this.privContext}},Ev}var Ov,Mv={};function Dv(){if(Ov)return Mv;Ov=1,Object.defineProperty(Mv,"__esModule",{value:!0}),Mv.SpeakerServiceRecognizer=void 0;const e=Vp(),t=Gt(),r=Mp(),i=jv(),n=iu();let o=class extends i.ServiceRecognizerBase{constructor(e,t,r,i,n){super(e,t,r,i,n),this.privSpeakerRecognizer=n,this.privSpeakerAudioSource=r,this.recognizeSpeaker=e=>this.recognizeSpeakerOnce(e),this.sendPrePayloadJSONOverride=()=>this.noOp()}processTypeSpecificMessages(e){let i=!1;const n=new r.PropertyCollection;if(e.messageType===t.MessageType.Text&&n.setProperty(r.PropertyId.SpeechServiceResponse_JsonResult,e.textBody),"speaker.response"===e.path.toLowerCase()){const t=JSON.parse(e.textBody);let n;n="success"!==t.status.statusCode.toLowerCase()?new r.SpeakerRecognitionResult(t,r.ResultReason.Canceled,r.CancellationErrorCode.ServiceError,t.status.reason):new r.SpeakerRecognitionResult(t,r.ResultReason.RecognizedSpeaker),this.privResultDeferral&&this.privResultDeferral.resolve(n),i=!0}const o=new t.Deferred;return o.resolve(i),o.promise}cancelRecognition(e,t,n,o,s){if((new r.PropertyCollection).setProperty(i.CancellationErrorCodePropertyName,r.CancellationErrorCode[o]),this.privResultDeferral){const e=new r.SpeakerRecognitionResult({scenario:this.privSpeakerModel.scenario,status:{statusCode:s,reason:s}},r.ResultReason.Canceled,o,s);try{this.privResultDeferral.resolve(e)}catch(a){this.privResultDeferral.reject(a)}}}async recognizeSpeakerOnce(i){this.privSpeakerModel=i,this.voiceProfileType=i.scenario,this.privResultDeferral||(this.privResultDeferral=new t.Deferred),this.privRequestSession.startNewRecognition(),this.privRequestSession.listenForServiceTelemetry(this.privSpeakerAudioSource.events),this.privRecognizerConfig.parameters.setProperty(r.PropertyId.Speech_SessionId,this.privRequestSession.sessionId);const n=this.connectImpl(),o=this.sendPreAudioMessages(this.extractSpeakerContext(i)),s=await this.privSpeakerAudioSource.attach(this.privRequestSession.audioNodeId),a=await this.privSpeakerAudioSource.format,c=await this.privSpeakerAudioSource.deviceInfo,p=new e.ReplayableAudioNode(s,a.avgBytesPerSec);await this.privRequestSession.onAudioSourceAttachCompleted(p,!1),this.privRecognizerConfig.SpeechServiceConfig.Context.audio={source:c};try{await n,await o}catch(h){this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,r.CancellationReason.Error,r.CancellationErrorCode.ConnectionFailure,h)}const u=new r.SessionEventArgs(this.privRequestSession.sessionId);this.privRecognizer.sessionStarted&&this.privRecognizer.sessionStarted(this.privRecognizer,u),this.receiveMessage();return this.sendAudio(p).then((()=>{}),(e=>{this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,r.CancellationReason.Error,r.CancellationErrorCode.RuntimeError,e)})),this.privResultDeferral.promise}async sendPreAudioMessages(e){const t=await this.fetchConnection();await this.sendSpeakerRecognition(t,e)}async sendSpeakerRecognition(e,r){const i=JSON.stringify(r);return e.send(new n.SpeechConnectionMessage(t.MessageType.Text,"speaker.context",this.privRequestSession.requestId,"application/json; charset=utf-8",i))}extractSpeakerContext(e){return{features:{interimResult:"enabled",progressiveDetection:"disabled"},profileIds:e.profileIds,scenario:e.scenario}}};return Mv.SpeakerServiceRecognizer=o,Mv}var kv,zv={};function Nv(){if(kv)return zv;kv=1,Object.defineProperty(zv,"__esModule",{value:!0}),zv.VoiceServiceRecognizer=void 0;const e=Vp(),t=Gt(),r=Mp(),i=jv(),n=iu();let o=class extends i.ServiceRecognizerBase{constructor(e,r,i,n,o){super(e,r,i,n,o),this.privDeferralMap=new t.DeferralMap,this.privSpeakerAudioSource=i,this.sendPrePayloadJSONOverride=()=>this.noOp()}set SpeakerAudioSource(e){this.privSpeakerAudioSource=e}processTypeSpecificMessages(e){let i=!1;const n=new r.PropertyCollection;switch(e.messageType===t.MessageType.Text&&n.setProperty(r.PropertyId.SpeechServiceResponse_JsonResult,e.textBody),e.path.toLowerCase()){case"speaker.profiles":const t=JSON.parse(e.textBody);switch(t.operation.toLowerCase()){case"create":this.handleCreateResponse(t,e.requestId);break;case"delete":case"reset":this.handleResultResponse(t,e.requestId);break;case"fetch":const r=JSON.parse(e.textBody);this.handleFetchResponse(r,e.requestId)}i=!0;break;case"speaker.phrases":const n=JSON.parse(e.textBody);this.handlePhrasesResponse(n,e.requestId),i=!0;break;case"speaker.profile.enrollment":const o=JSON.parse(e.textBody),s=new r.VoiceProfileEnrollmentResult(this.enrollmentReasonFrom(o.enrollment?o.enrollment.enrollmentStatus:o.status.statusCode),o.enrollment?JSON.stringify(o.enrollment):void 0,o.status.reason);this.privDeferralMap.getId(e.requestId)&&this.privDeferralMap.complete(e.requestId,s),this.privRequestSession.onSpeechEnded(),i=!0}const o=new t.Deferred;return o.resolve(i),o.promise}cancelRecognition(e,t,n,o,s){(new r.PropertyCollection).setProperty(i.CancellationErrorCodePropertyName,r.CancellationErrorCode[o]);const a=new r.VoiceProfileEnrollmentResult(r.ResultReason.Canceled,s,s);this.privDeferralMap.getId(t)&&this.privDeferralMap.complete(t,a)}async createProfile(e,r){this.voiceProfileType=e.toString();const i=this.connectImpl();try{const n=new t.Deferred;return await i,await this.sendCreateProfile(n,e,r),this.receiveMessage(),n.promise}catch(n){throw n}}async resetProfile(e){return this.voiceProfileType=e.profileType.toString(),this.sendCommonRequest("reset",e.profileType,e)}async deleteProfile(e){return this.voiceProfileType=e.profileType.toString(),this.sendCommonRequest("delete",e.profileType,e)}async retrieveEnrollmentResult(e){return this.voiceProfileType=e.profileType.toString(),this.privExpectedProfileId=e.profileId,this.sendCommonRequest("fetch",e.profileType,e)}async getAllProfiles(e){return this.voiceProfileType=e.toString(),this.sendCommonRequest("fetch",e)}async getActivationPhrases(e,r){this.voiceProfileType=e.toString();const i=this.connectImpl();try{const n=new t.Deferred;return await i,await this.sendPhrasesRequest(n,e,r),this.receiveMessage(),n.promise}catch(n){throw n}}async enrollProfile(i){this.voiceProfileType=i.profileType.toString();const n=new t.Deferred;this.privRequestSession.startNewRecognition(),this.privRequestSession.listenForServiceTelemetry(this.privSpeakerAudioSource.events),this.privRecognizerConfig.parameters.setProperty(r.PropertyId.Speech_SessionId,this.privRequestSession.sessionId);const o=this.connectImpl(),s=this.sendPreAudioMessages(i,n),a=await this.privSpeakerAudioSource.attach(this.privRequestSession.audioNodeId),c=await this.privSpeakerAudioSource.format,p=await this.privSpeakerAudioSource.deviceInfo,u=new e.ReplayableAudioNode(a,c.avgBytesPerSec);await this.privRequestSession.onAudioSourceAttachCompleted(u,!1),this.privRecognizerConfig.SpeechServiceConfig.Context.audio={source:p};try{await o,await s}catch(d){this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,r.CancellationReason.Error,r.CancellationErrorCode.ConnectionFailure,d)}const h=new r.SessionEventArgs(this.privRequestSession.sessionId);this.privRecognizer.sessionStarted&&this.privRecognizer.sessionStarted(this.privRecognizer,h),this.receiveMessage();return this.sendAudio(u).then((()=>{}),(e=>{this.cancelRecognition(this.privRequestSession.sessionId,this.privRequestSession.requestId,r.CancellationReason.Error,r.CancellationErrorCode.RuntimeError,e)})),n.promise}async sendPreAudioMessages(e,t){const r=await this.fetchConnection();this.privRequestSession.onSpeechContext(),this.privDeferralMap.add(this.privRequestSession.requestId,t),await this.sendBaseRequest(r,"enroll",this.scenarioFrom(e.profileType),e)}async sendPhrasesRequest(e,r,i){const o=await this.fetchConnection();this.privRequestSession.onSpeechContext(),this.privDeferralMap.add(this.privRequestSession.requestId,e);const s={locale:i,scenario:this.scenarioFrom(r)};return o.send(new n.SpeechConnectionMessage(t.MessageType.Text,"speaker.profile.phrases",this.privRequestSession.requestId,"application/json; charset=utf-8",JSON.stringify(s)))}async sendCreateProfile(e,i,o){const s=await this.fetchConnection();this.privRequestSession.onSpeechContext(),this.privDeferralMap.add(this.privRequestSession.requestId,e);const a={locale:o,number:"1",scenario:i===r.VoiceProfileType.TextIndependentIdentification?"TextIndependentIdentification":i===r.VoiceProfileType.TextIndependentVerification?"TextIndependentVerification":"TextDependentVerification"};return s.send(new n.SpeechConnectionMessage(t.MessageType.Text,"speaker.profile.create",this.privRequestSession.requestId,"application/json; charset=utf-8",JSON.stringify(a)))}async sendCommonRequest(e,r,i=void 0){const n=this.connectImpl();try{const o=new t.Deferred;this.privRequestSession.onSpeechContext(),await n;const s=await this.fetchConnection();return this.privDeferralMap.add(this.privRequestSession.requestId,o),await this.sendBaseRequest(s,e,this.scenarioFrom(r),i),this.receiveMessage(),o.promise}catch(o){throw o}}async sendBaseRequest(e,r,i,o){const s={scenario:i};return o?s.profileIds=[o.profileId]:s.maxPageSize=-1,e.send(new n.SpeechConnectionMessage(t.MessageType.Text,`speaker.profile.${r}`,this.privRequestSession.requestId,"application/json; charset=utf-8",JSON.stringify(s)))}extractSpeakerContext(e){return{features:{interimResult:"enabled",progressiveDetection:"disabled"},profileIds:e.profileIds,scenario:e.scenario}}handlePhrasesResponse(e,t){if(!this.privDeferralMap.getId(t))throw new Error(`Voice Profile get activation phrases request for requestID ${t} not found`);if("success"!==e.status.statusCode.toLowerCase()){const i=r.ResultReason.Canceled,n=new r.VoiceProfilePhraseResult(i,e.status.statusCode,e.passPhraseType,[]);this.privDeferralMap.complete(t,n)}else{if(!(e.phrases&&e.phrases.length>0))throw new Error("Voice Profile get activation phrases failed, no phrases received");{const i=r.ResultReason.EnrollingVoiceProfile,n=new r.VoiceProfilePhraseResult(i,e.status.statusCode,e.passPhraseType,e.phrases);this.privDeferralMap.complete(t,n)}}}handleCreateResponse(e,t){if(!(e.profiles&&e.profiles.length>0))throw new Error("Voice Profile create failed, no profile id received");if(!this.privDeferralMap.getId(t))throw new Error(`Voice Profile create request for requestID ${t} not found`);{const r=e.profiles.map((e=>e.profileId));this.privDeferralMap.complete(t,r)}}handleResultResponse(e,t){if(!this.privDeferralMap.getId(t))throw new Error(`Voice Profile create request for requestID ${t} not found`);{const i="delete"===e.operation.toLowerCase()?r.ResultReason.DeletedVoiceProfile:r.ResultReason.ResetVoiceProfile,n="success"===e.status.statusCode.toLowerCase()?i:r.ResultReason.Canceled,o=new r.VoiceProfileResult(n,`statusCode: ${e.status.statusCode}, errorDetails: ${e.status.reason}`);this.privDeferralMap.complete(t,o)}}handleFetchResponse(e,t){if(!this.privDeferralMap.getId(t)||!e.profiles[0])throw new Error(`Voice Profile fetch request for requestID ${t} not found`);if(this.privExpectedProfileId&&1===e.profiles.length&&e.profiles[0].profileId===this.privExpectedProfileId){this.privExpectedProfileId=void 0;const i=e.profiles[0],n=new r.VoiceProfileEnrollmentResult(this.enrollmentReasonFrom(i.enrollmentStatus),JSON.stringify(i),e.status.reason);this.privDeferralMap.complete(t,n)}else if(e.profiles.length>0){const i=e.profiles,n=[];for(const t of i)n.push(new r.VoiceProfileEnrollmentResult(this.enrollmentReasonFrom(t.enrollmentStatus),JSON.stringify(t),e.status.reason));this.privDeferralMap.complete(t,n)}}enrollmentReasonFrom(e){switch(e.toLowerCase()){case"enrolled":return r.ResultReason.EnrolledVoiceProfile;case"invalidlocale":case"invalidphrase":case"invalidaudioformat":case"invalidscenario":case"invalidprofilecount":case"invalidoperation":case"audiotooshort":case"audiotoolong":case"toomanyenrollments":case"storageconflict":case"profilenotfound":case"incompatibleprofiles":case"incompleteenrollment":return r.ResultReason.Canceled;default:return r.ResultReason.EnrollingVoiceProfile}}scenarioFrom(e){return e===r.VoiceProfileType.TextIndependentIdentification?"TextIndependentIdentification":e===r.VoiceProfileType.TextIndependentVerification?"TextIndependentVerification":"TextDependentVerification"}};return zv.VoiceServiceRecognizer=o,zv}var _v,Lv,xv,Fv={};function Bv(){return _v||(_v=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.type=e.connectivity=e.Device=e.OS=e.System=e.Context=e.SpeechServiceConfig=void 0;e.SpeechServiceConfig=class{constructor(e){this.context=e}serialize(){return JSON.stringify(this,((e,t)=>{if(t&&"object"==typeof t&&!Array.isArray(t)){const e={};for(const r in t)Object.hasOwnProperty.call(t,r)&&(e[r&&r.charAt(0).toLowerCase()+r.substring(1)]=t[r]);return e}return t}))}get Context(){return this.context}get Recognition(){return this.recognition}set Recognition(e){this.recognition=e.toLowerCase()}};e.Context=class{constructor(e){this.system=new t,this.os=e}};class t{constructor(){this.name="SpeechSDK",this.version="1.43.1",this.build="JavaScript",this.lang="JavaScript"}}e.System=t;e.OS=class{constructor(e,t,r){this.platform=e,this.name=t,this.version=r}};var r,i;e.Device=class{constructor(e,t,r){this.manufacturer=e,this.model=t,this.version=r}},(r=e.connectivity||(e.connectivity={})).Bluetooth="Bluetooth",r.Wired="Wired",r.WiFi="WiFi",r.Cellular="Cellular",r.InBuilt="InBuilt",r.Unknown="Unknown",(i=e.type||(e.type={})).Phone="Phone",i.Speaker="Speaker",i.Car="Car",i.Headset="Headset",i.Thermostat="Thermostat",i.Microphones="Microphones",i.Deskphone="Deskphone",i.RemoteControl="RemoteControl",i.Unknown="Unknown",i.File="File",i.Stream="Stream"}(Fv)),Fv}function jv(){return Lv||(Lv=1,function(e){var t=s&&s.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=s&&s.__exportStar||function(e,r){for(var i in e)"default"===i||r.hasOwnProperty(i)||t(r,e,i)};Object.defineProperty(e,"__esModule",{value:!0}),e.AutoDetectSourceLanguagesOpenRangeOptionName=e.ForceDictationPropertyName=e.ServicePropertiesPropertyName=e.CancellationErrorCodePropertyName=e.OutputFormatPropertyName=void 0,r(rr(),e),r(or(),e),r(tr(),e),r(cr(),e),r(hr(),e),r(Kp(),e),r($p(),e),r(Zp(),e),r(nu(),e),r(au(),e),r(uu(),e),r(vu(),e),r(fu(),e),r(yu(),e),r(Ru(),e),r(Tu(),e),r(bu(),e),r(mp(),e),r(Du(),e),r(Nu(),e),r(xu(),e),r(ju(),e),r(Wu(),e),r(Ku(),e),r($u(),e),r(Zu(),e),r(th(),e),r(nh(),e),r(ah(),e),r(uh(),e),r(vh(),e),r(fh(),e),r(yh(),e),r(Rh(),e),r(Th(),e),r(bh(),e),r(Nh(),e),r(xh(),e),r(jh(),e),r(Wh(),e),r(td(),e),r(nd(),e),r(Zd(),e),r(tv(),e),r(av(),e),r(uv(),e);var i=vv();Object.defineProperty(e,"AvatarSynthesisAdapter",{enumerable:!0,get:function(){return i.AvatarSynthesisAdapter}});var n=fv();Object.defineProperty(e,"SpeechSynthesisAdapter",{enumerable:!0,get:function(){return n.SpeechSynthesisAdapter}}),r(yv(),e),r(Rv(),e),r(Tv(),e),r(bv(),e),r(Dv(),e),r(Nv(),e),r(Bv(),e),e.OutputFormatPropertyName="OutputFormat",e.CancellationErrorCodePropertyName="CancellationErrorCode",e.ServicePropertiesPropertyName="ServiceProperties",e.ForceDictationPropertyName="ForceDictation",e.AutoDetectSourceLanguagesOpenRangeOptionName="UND"}(s)),s}function Uv(){return xv||(xv=1,function(e){var t=o&&o.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),r=o&&o.__exportStar||function(e,r){for(var i in e)"default"===i||r.hasOwnProperty(i)||t(r,e,i)};Object.defineProperty(e,"__esModule",{value:!0});new(jv().AgentConfig),r(Mp(),e)}(o)),o}var qv=Uv();const Wv=class e{constructor(){t(this,"audio",null),t(this,"isPlaying",!1),t(this,"currentAudioBlob",null),t(this,"currentMessageId",null),t(this,"configLoaded",!1),t(this,"configLoading",!1),t(this,"siliconFlowApiKey",""),t(this,"openaiApiKey",""),t(this,"useOpenAI",!1),t(this,"openaiModel","tts-1"),t(this,"openaiVoice","alloy"),t(this,"openaiResponseFormat","mp3"),t(this,"openaiSpeed",1),t(this,"useOpenAIStream",!1),t(this,"azureApiKey",""),t(this,"azureRegion","eastus"),t(this,"useAzure",!1),t(this,"azureVoiceName","zh-CN-XiaoxiaoNeural"),t(this,"azureLanguage","zh-CN"),t(this,"azureOutputFormat","audio-24khz-160kbitrate-mono-mp3"),t(this,"azureRate","medium"),t(this,"azurePitch","medium"),t(this,"azureVolume","medium"),t(this,"azureStyle","general"),t(this,"azureStyleDegree",1),t(this,"azureRole","default"),t(this,"azureUseSSML",!0),t(this,"defaultModel","FunAudioLLM/CosyVoice2-0.5B"),t(this,"defaultVoice","FunAudioLLM/CosyVoice2-0.5B:alex"),t(this,"audioContext",null),t(this,"sourceNode",null),t(this,"isStreamPlaying",!1),"speechSynthesis"in window&&window.speechSynthesis.getVoices(),this.audio=new Audio,this.audio.onended=()=>{this.isPlaying=!1,this.currentMessageId=null},this.audio.onerror=()=>{console.error("音频播放错误"),this.isPlaying=!1,this.currentMessageId=null};try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(e){console.warn("AudioContext初始化失败，流式播放可能不可用",e)}}static getInstance(){return e.instance||(e.instance=new e),e.instance}setApiKey(e){this.siliconFlowApiKey=e}setOpenAIApiKey(e){this.openaiApiKey=e}setUseOpenAI(e){this.useOpenAI=e}setAzureApiKey(e){this.azureApiKey=e}setAzureRegion(e){this.azureRegion=e}setUseAzure(e){this.useAzure=e}setAzureVoiceName(e){this.azureVoiceName=e}setAzureLanguage(e){this.azureLanguage=e}setAzureOutputFormat(e){this.azureOutputFormat=e}setAzureRate(e){this.azureRate=e}setAzurePitch(e){this.azurePitch=e}setAzureVolume(e){this.azureVolume=e}setAzureStyle(e){this.azureStyle=e}setAzureStyleDegree(e){this.azureStyleDegree=Math.max(.01,Math.min(2,e))}setAzureRole(e){this.azureRole=e}setAzureUseSSML(e){this.azureUseSSML=e}setOpenAIModel(e){this.openaiModel=e}setOpenAIVoice(e){this.openaiVoice=e}setOpenAIResponseFormat(e){this.openaiResponseFormat=e}setOpenAISpeed(e){e<.25&&(e=.25),e>4&&(e=4),this.openaiSpeed=e}setUseOpenAIStream(e){this.useOpenAIStream=e}setDefaultVoice(e,t){e&&(this.defaultModel=e),t&&(this.defaultVoice=t)}async initializeConfig(){if(this.configLoaded)return!0;if(this.configLoading)return new Promise((e=>{const t=()=>{this.configLoaded?e(!0):this.configLoading?setTimeout(t,50):e(!1)};t()}));this.configLoading=!0;try{const{getStorageItem:e}=await r((async()=>{const{getStorageItem:e}=await import("./index-BtK6VV6Z.js").then((e=>e.di));return{getStorageItem:e}}),__vite__mapDeps([0,1,2,3,4,5])),[t,i,n,o]=await Promise.all([e("siliconflow_api_key"),e("tts_model"),e("tts_voice"),e("enable_tts")]);return t&&this.setApiKey(t),i&&(this.defaultModel=i),n&&(this.defaultVoice=n.includes(":")?n:`${i||this.defaultModel}:${n}`),this.configLoaded=!0,this.configLoading=!1,!0}catch(e){return console.error("TTSService配置初始化失败:",e),this.configLoading=!1,!1}}isConfigLoaded(){return this.configLoaded}stop(){this.audio&&(this.audio.pause(),this.audio.currentTime=0),"speechSynthesis"in window&&window.speechSynthesis.cancel(),this.stopStream(),this.isPlaying=!1,this.currentMessageId=null,this.currentAudioBlob&&(URL.revokeObjectURL(this.currentAudioBlob),this.currentAudioBlob=null)}stopStream(){if(this.sourceNode){try{this.sourceNode.stop(),this.sourceNode.disconnect()}catch(e){console.error("停止音频源时出错",e)}this.sourceNode=null}this.isStreamPlaying=!1}getIsPlaying(){return this.isPlaying||this.isStreamPlaying}getCurrentMessageId(){return this.currentMessageId}async togglePlayback(e,t,r=this.defaultVoice){if((this.isPlaying||this.isStreamPlaying)&&this.currentMessageId===e)return this.stop(),!1;(this.isPlaying||this.isStreamPlaying)&&this.stop();const i=await this.speak(t,r);return i&&(this.currentMessageId=e),i}async speak(e,t=this.defaultVoice){try{if((this.isPlaying||this.isStreamPlaying)&&this.stop(),!e||""===e.trim())return console.error("文本为空，无法播放"),!1;if(this.isPlaying=!0,this.useAzure){if(await this.speakWithAzure(e))return!0}if(this.useOpenAI)if(this.useOpenAIStream&&this.audioContext){if(await this.speakWithOpenAIStream(e))return!0}else{if(await this.speakWithOpenAI(e))return!0}return!!(await this.speakWithSiliconFlow(e,t))||(!!this.speakWithWebSpeechAPI(e,t)||(console.warn("所有TTS方法播放失败"),this.isPlaying=!1,!1))}catch(r){return console.error("播放文本失败:",r),this.isPlaying=!1,this.currentMessageId=null,!1}}async speakWithOpenAI(e){try{if(!this.openaiApiKey)return console.warn("OpenAI API密钥未设置，尝试其他方法"),!1;const r="https://api.openai.com/v1/audio/speech",i={model:this.openaiModel,input:e,voice:this.openaiVoice,response_format:this.openaiResponseFormat,speed:this.openaiSpeed};this.openaiModel,this.openaiVoice,this.openaiResponseFormat,this.openaiSpeed;const n=await fetch(r,{method:"POST",headers:{Authorization:`Bearer ${this.openaiApiKey}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!n.ok){const e=await n.json().catch((()=>({})));return console.error("OpenAI TTS API请求失败:",n.status,e),!1}const o=await n.blob();if(this.currentAudioBlob&&URL.revokeObjectURL(this.currentAudioBlob),this.currentAudioBlob=URL.createObjectURL(o),this.audio){this.audio.src=this.currentAudioBlob;try{await this.audio.play()}catch(t){return console.error("OpenAI TTS API播放失败:",t),this.isPlaying=!1,!1}return!0}return!1}catch(t){return console.error("OpenAI TTS API播放失败:",t),!1}}async speakWithOpenAIStream(e){var t;try{if(!this.openaiApiKey)return console.warn("OpenAI API密钥未设置，尝试其他方法"),!1;if(!this.audioContext)return console.warn("AudioContext不可用，无法使用流式播放"),!1;const r="https://api.openai.com/v1/audio/speech",i={model:this.openaiModel,input:e,voice:this.openaiVoice,response_format:this.openaiResponseFormat,speed:this.openaiSpeed,stream:!0};this.openaiModel,this.openaiVoice,this.openaiResponseFormat,this.openaiSpeed;const n=await fetch(r,{method:"POST",headers:{Authorization:`Bearer ${this.openaiApiKey}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!n.ok){const e=await n.json().catch((()=>({})));return console.error("OpenAI TTS API流式请求失败:",n.status,e),!1}this.stopStream(),this.isStreamPlaying=!0;const o=null==(t=n.body)?void 0:t.getReader();if(!o)return console.error("无法获取响应流"),!1;let s="audio/mp3";switch(this.openaiResponseFormat){case"mp3":s="audio/mp3";break;case"opus":s="audio/opus";break;case"aac":s="audio/aac";break;case"flac":s="audio/flac"}const a=new MediaSource;(new Audio).src=URL.createObjectURL(a);const c=[];return(async()=>{let e=!1;for(;!e;){const{value:r,done:i}=await o.read();if(e=i,r&&c.push(r),e){const e=c.reduce(((e,t)=>e+t.length),0),r=new Uint8Array(e);let i=0;for(const t of c)r.set(t,i),i+=t.length;const n=new Blob([r],{type:s}),o=URL.createObjectURL(n);if(this.audio){this.audio.src=o;try{return await this.audio.play(),this.audio.onended=()=>{URL.revokeObjectURL(o),this.isStreamPlaying=!1,this.currentMessageId=null},!0}catch(t){return console.error("OpenAI TTS API流式播放失败:",t),URL.revokeObjectURL(o),this.isStreamPlaying=!1,!1}}}}return!1})().catch((e=>{console.error("处理音频流时出错:",e),this.isStreamPlaying=!1})),!0}catch(r){return console.error("OpenAI TTS API流式播放失败:",r),this.isStreamPlaying=!1,!1}}async speakWithSiliconFlow(e,t){try{if(!this.siliconFlowApiKey)return console.warn("硅基流动API密钥未设置，尝试其他方法"),!1;const i="https://api.siliconflow.cn/v1/audio/speech",n=this.defaultModel;let o=t||this.defaultVoice;o&&!o.includes(":")&&(o=`${n}:${o}`),e.length,e.substring(0,100),e.length;const s={model:n,input:e,voice:o,response_format:"mp3"};JSON.stringify(s,null,2);const a=await fetch(i,{method:"POST",headers:{Authorization:`Bearer ${this.siliconFlowApiKey}`,"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){const e=await a.json().catch((()=>({})));return console.error("硅基流动API请求失败:",a.status,e),!1}const c=await a.blob();if(this.currentAudioBlob&&URL.revokeObjectURL(this.currentAudioBlob),this.currentAudioBlob=URL.createObjectURL(c),this.audio){this.audio.src=this.currentAudioBlob;try{await this.audio.play()}catch(r){return console.error("硅基流动API播放失败:",r),this.isPlaying=!1,!1}return!0}return!1}catch(r){return console.error("硅基流动API播放失败:",r),!1}}speakWithWebSpeechAPI(e,t){try{if(!("speechSynthesis"in window))return console.error("浏览器不支持Web Speech API"),!1;window.speechSynthesis.cancel();const r=new SpeechSynthesisUtterance(e);let i=window.speechSynthesis.getVoices();return 0===i.length?(setTimeout((()=>{i=window.speechSynthesis.getVoices(),this.setVoiceAndPlay(r,i,t)}),100),!0):this.setVoiceAndPlay(r,i,t)}catch(r){return console.error("Web Speech API播放失败:",r),this.isPlaying=!1,!1}}setVoiceAndPlay(e,t,r){try{let i=t.find((e=>e.name===r));if(i)i.name;else if(console.warn("未找到指定的语音:",r),i=t.find((e=>"zh-CN"===e.lang)),i)i.name;else{if(!(t.length>0))return console.warn("没有可用的语音"),this.isPlaying=!1,!1;i=t[0],i.name}return e.voice=i,e.rate=1,e.pitch=1,e.volume=1,e.onend=()=>{this.isPlaying=!1,this.currentMessageId=null},e.onerror=e=>{console.error("语音播放错误:",e),this.isPlaying=!1,this.currentMessageId=null},window.speechSynthesis.speak(e),!0}catch(i){return console.error("设置语音失败:",i),this.isPlaying=!1,!1}}async speakWithAzure(e){try{if(!this.azureApiKey||!this.azureRegion)return console.warn("Azure API密钥或区域未设置，尝试其他方法"),!1;this.azureRegion,this.azureVoiceName,this.azureLanguage,this.azureOutputFormat,this.azureRate,this.azurePitch,this.azureVolume,this.azureStyle,this.azureStyleDegree,this.azureRole,this.azureUseSSML;const t=qv.SpeechConfig.fromSubscription(this.azureApiKey,this.azureRegion);t.speechSynthesisOutputFormat=this.getAzureOutputFormat(),t.speechSynthesisVoiceName=this.azureVoiceName;const r=qv.AudioConfig.fromDefaultSpeakerOutput(),i=new qv.SpeechSynthesizer(t,r);let n=e;return this.azureUseSSML&&(n=this.buildSSMLText(e)),n.length,new Promise((e=>{Date.now();this.azureUseSSML?i.speakSsmlAsync(n,(t=>{Date.now();t.reason===qv.ResultReason.SynthesizingAudioCompleted?(this.isPlaying=!1,this.currentMessageId=null,e(!0)):(console.error("Azure TTS语音合成失败:",t.errorDetails),this.isPlaying=!1,e(!1)),i.close()}),(t=>{console.error("Azure TTS合成错误:",t),this.isPlaying=!1,i.close(),e(!1)})):i.speakTextAsync(n,(t=>{Date.now();t.reason===qv.ResultReason.SynthesizingAudioCompleted?(this.isPlaying=!1,this.currentMessageId=null,e(!0)):(console.error("Azure TTS语音合成失败:",t.errorDetails),this.isPlaying=!1,e(!1)),i.close()}),(t=>{console.error("Azure TTS合成错误:",t),this.isPlaying=!1,i.close(),e(!1)}))}))}catch(t){return console.error("Azure TTS播放失败:",t),this.isPlaying=!1,!1}}buildSSMLText(e){let t=`<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="${this.azureLanguage}">`;t+=`<voice name="${this.azureVoiceName}">`;const r=[];if("medium"!==this.azureRate&&r.push(`rate="${this.azureRate}"`),"medium"!==this.azurePitch&&r.push(`pitch="${this.azurePitch}"`),"medium"!==this.azureVolume&&r.push(`volume="${this.azureVolume}"`),r.length>0&&(t+=`<prosody ${r.join(" ")}>`),"general"!==this.azureStyle&&this.azureVoiceName.includes("Neural")){const e=[`style="${this.azureStyle}"`];1!==this.azureStyleDegree&&e.push(`styledegree="${this.azureStyleDegree}"`),"default"!==this.azureRole&&e.push(`role="${this.azureRole}"`),t+=`<mstts:express-as ${e.join(" ")}>`}return t+=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"),"general"!==this.azureStyle&&this.azureVoiceName.includes("Neural")&&(t+="</mstts:express-as>"),r.length>0&&(t+="</prosody>"),t+="</voice>",t+="</speak>",t}getAzureOutputFormat(){return{"audio-16khz-32kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3,"audio-16khz-64kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio16Khz64KBitRateMonoMp3,"audio-16khz-128kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio16Khz128KBitRateMonoMp3,"audio-24khz-48kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio24Khz48KBitRateMonoMp3,"audio-24khz-96kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio24Khz96KBitRateMonoMp3,"audio-24khz-160kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio24Khz160KBitRateMonoMp3,"audio-48khz-96kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio48Khz96KBitRateMonoMp3,"audio-48khz-192kbitrate-mono-mp3":qv.SpeechSynthesisOutputFormat.Audio48Khz192KBitRateMonoMp3,"webm-16khz-16bit-mono-opus":qv.SpeechSynthesisOutputFormat.Webm16Khz16BitMonoOpus,"webm-24khz-16bit-mono-opus":qv.SpeechSynthesisOutputFormat.Webm24Khz16BitMonoOpus,"ogg-16khz-16bit-mono-opus":qv.SpeechSynthesisOutputFormat.Ogg16Khz16BitMonoOpus,"ogg-24khz-16bit-mono-opus":qv.SpeechSynthesisOutputFormat.Ogg24Khz16BitMonoOpus,"raw-16khz-16bit-mono-pcm":qv.SpeechSynthesisOutputFormat.Raw16Khz16BitMonoPcm,"raw-24khz-16bit-mono-pcm":qv.SpeechSynthesisOutputFormat.Raw24Khz16BitMonoPcm,"raw-48khz-16bit-mono-pcm":qv.SpeechSynthesisOutputFormat.Raw48Khz16BitMonoPcm,"riff-16khz-16bit-mono-pcm":qv.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm,"riff-24khz-16bit-mono-pcm":qv.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm,"riff-48khz-16bit-mono-pcm":qv.SpeechSynthesisOutputFormat.Riff48Khz16BitMonoPcm}[this.azureOutputFormat]||qv.SpeechSynthesisOutputFormat.Audio24Khz160KBitRateMonoMp3}};t(Wv,"instance");let Hv=Wv;export{Hv as TTSService};
