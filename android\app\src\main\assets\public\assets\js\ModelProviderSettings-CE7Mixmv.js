const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/index-Ck4sQVom.js","assets/js/mui-vendor-DsBXMegs.js","assets/js/react-vendor-Be-rfjCm.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/js/utils-vendor-D1FP9aB2.js","assets/css/index-nouvoMKC.css"])))=>i.map(i=>d[i]);
import{c as e,j as r,o as s,p as t,C as o,B as i,t as n,D as a,q as l,b as d,V as c,W as p,J as x,a as h,I as m,X as u,L as g,w as b,x as j,r as y,m as v,g as f,R as C,n as k,z as S,d as w,h as I,P as R,u as E,y as A,F as W,e as z,S as M,M as T,A as O,T as D,U as P}from"./mui-vendor-DsBXMegs.js";import{r as V,u as L,e as B}from"./react-vendor-Be-rfjCm.js";import{b4 as F,b5 as U,b6 as _,E as G,e as H,_ as $,f as q,u as K,a as X,ay as Y,A as J,b7 as N,b8 as Q,b9 as Z,ba as ee}from"./index-Ck4sQVom.js";import{A as re}from"./Add-CgwaVY5w.js";import{E as se}from"./Edit-_cexwR-h.js";import{S as te}from"./Search-D7hYSVTA.js";import{_ as oe}from"./syntax-vendor-DfDNeb5M.js";import{M as ie}from"./index-DPlvhaOy.js";import{A as ne}from"./AvatarUploader-CqIroiLk.js";import{I as ae}from"./InfoOutlined-sXanJGZQ.js";import"./utils-vendor-D1FP9aB2.js";import"./CloudUpload-vPcGu9ot.js";const le=e([r.jsx("path",{d:"M12.03 6.3h-.06l-1.02 2.89h2.1zM3 17h2v5H3z"},"0"),r.jsx("path",{d:"M12 15c3.31 0 6-2.69 6-6s-2.69-6-6-6-6 2.69-6 6 2.69 6 6 6m-.63-10h1.25l2.63 7h-1.21l-.63-1.79h-2.83L9.96 12H8.74zM7 17h2v5H7zm4 0h2v5h-2zm4 0h6v5h-6z"},"1")]),de=e(r.jsx("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"})),ce=e(r.jsx("path",{d:"M19 13H5v-2h14z"})),pe=e(r.jsx("path",{d:"m23 12-2.44-2.79.34-3.69-3.61-.82-1.89-3.2L12 2.96 8.6 1.5 6.71 4.69 3.1 5.5l.34 3.7L1 12l2.44 2.79-.34 3.7 3.61.82L8.6 22.5l3.4-1.47 3.4 1.46 1.89-3.19 3.61-.82-.34-3.69zm-12.91 4.72-3.8-3.81 1.48-1.48 2.32 2.33 5.85-5.87 1.48 1.48z"}));const xe=({open:e,onClose:f,provider:C,onAddModel:k,onAddModels:S,onRemoveModel:w,onRemoveModels:I,existingModels:R})=>{const[E,A]=V.useState(!1),[W,z]=V.useState([]),[M,T]=V.useState(""),[O,D]=V.useState(new Set),[P,L]=V.useState(new Map),B=V.useRef(null),H=V.useCallback((e=>R.some((r=>r.id===e))||!0===P.get(e)),[R,P]),$=V.useCallback((()=>W.filter((e=>e.name.toLowerCase().includes(M.toLowerCase())||e.id.toLowerCase().includes(M.toLowerCase()))).reduce(((e,r)=>{const s=r.group||"其他模型";return e[s]||(e[s]=[]),e[s].push(r),e}),{})),[W,M])(),q=async()=>{try{A(!0);const e=B.current||C,r=await async function(e){try{F("获取模型列表","INFO",{provider:e.id});const{fetchModels:r}=await oe((async()=>{const{fetchModels:e}=await import("./index-Ck4sQVom.js").then((e=>e.dg));return{fetchModels:e}}),__vite__mapDeps([0,1,2,3,4,5])),s=await r(e);return U("获取模型列表",200,{provider:e.id,modelsCount:s.length}),s}catch(r){return _(r,"APIService.fetchModels",{logLevel:"ERROR",additionalData:{provider:e.id}}),U("获取模型列表",500,{provider:e.id,error:r instanceof Error?r.message:"未知错误"}),[]}}(e),s=[...r];z(s);const t=new Set;s.forEach((e=>{e.group&&t.add(e.group)})),D(t)}catch(e){console.error("加载模型失败:",e)}finally{A(!1)}};return V.useEffect((()=>{e?(B.current||(B.current=C),q(),L(new Map)):B.current=null}),[e]),r.jsxs(s,{open:e,onClose:f,fullWidth:!0,maxWidth:"md",PaperProps:{sx:{borderRadius:2,maxHeight:"90vh"}},children:[r.jsxs(t,{sx:{fontWeight:700,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent",display:"flex",alignItems:"center",justifyContent:"space-between",p:3},children:[C.name,"模型管理",E&&r.jsx(o,{size:24,sx:{ml:2}})]}),r.jsx(i,{sx:{px:3,pb:2},children:r.jsx(n,{fullWidth:!0,placeholder:"搜索模型...",size:"small",value:M,onChange:e=>T(e.target.value),InputProps:{startAdornment:r.jsx(te,{sx:{mr:1,color:"text.secondary"}}),sx:{borderRadius:2}}})}),r.jsx(a,{}),r.jsx(l,{sx:{p:2,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:E?r.jsx(i,{sx:{display:"flex",justifyContent:"center",my:4},children:r.jsx(o,{})}):r.jsx(r.Fragment,{children:0===Object.keys($).length?r.jsx(d,{variant:"body1",sx:{textAlign:"center",my:4,color:"text.secondary"},children:"找不到匹配的模型"}):Object.entries($).map((([e,s])=>{const t=s.every((e=>H(e.id)));return r.jsxs(c,{expanded:O.has(e),onChange:()=>(e=>{const r=new Set(O);r.has(e)?r.delete(e):r.add(e),D(r)})(e),sx:{mb:2,border:"1px solid",borderColor:"divider",borderRadius:"8px !important","&:before":{display:"none"},boxShadow:"none"},children:[r.jsxs(i,{sx:{position:"relative"},children:[r.jsx(p,{expandIcon:r.jsx(G,{}),sx:{borderRadius:"8px",pr:6},children:r.jsxs(i,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(d,{variant:"subtitle1",fontWeight:600,children:e}),r.jsx(x,{label:s.length,size:"small",sx:{ml:1,height:20,bgcolor:e=>h(e.palette.success.main,.1),color:"success.main",fontWeight:600,fontSize:"0.7rem"}})]})}),r.jsx(m,{size:"small",color:t?"error":"primary",onClick:r=>{r.stopPropagation(),t?(e=>{const r=$[e].filter((e=>H(e.id))).map((e=>e.id));if(r.length>0){const e=new Map(P);I?(r.forEach((r=>{e.delete(r)})),L(e),I(r)):(r.forEach((r=>{e.delete(r),w(r)})),L(e))}})(e):(e=>{const r=$[e].filter((e=>!H(e.id)));if(r.length>0){const e=new Map(P);if(S){const s=r.map((e=>({...e})));s.forEach((r=>{e.set(r.id,!0)})),L(e),S(s)}else r.forEach((r=>{e.set(r.id,!0),k({...r})})),L(e)}})(e)},sx:{position:"absolute",right:48,top:"50%",transform:"translateY(-50%)",bgcolor:e=>h(t?e.palette.error.main:e.palette.primary.main,.1),"&:hover":{bgcolor:e=>h(t?e.palette.error.main:e.palette.primary.main,.2)}},children:t?r.jsx(ce,{}):r.jsx(re,{})})]}),r.jsx(u,{sx:{p:1},children:r.jsx(g,{disablePadding:!0,children:s.map((e=>r.jsx(b,{sx:{mb:1,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:H(e.id)?e=>h(e.palette.success.main,.05):"transparent",transition:"all 0.2s"},secondaryAction:H(e.id)?r.jsx(m,{edge:"end",color:"error",onClick:()=>(e=>{const r=new Map(P);r.delete(e),L(r),w(e)})(e.id),sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)}},children:r.jsx(ce,{})}):r.jsx(m,{edge:"end",color:"primary",onClick:()=>(e=>{if(!H(e.id)){const r=new Map(P);r.set(e.id,!0),L(r),k(e)}})(e),sx:{bgcolor:e=>h(e.palette.primary.main,.1),"&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)}},children:r.jsx(re,{})}),children:r.jsx(j,{primary:r.jsx(d,{variant:"body1",fontWeight:600,children:e.name}),secondary:r.jsx(d,{variant:"body2",color:"text.secondary",fontSize:"0.8rem",children:e.id})})},e.id)))})})]},e)}))})}),r.jsx(a,{}),r.jsx(y,{sx:{p:2},children:r.jsx(v,{onClick:f,sx:{borderRadius:2,px:3,py:1,fontWeight:600},children:"关闭"})})]})},he=[{pattern:"VL|vision|visual|image",types:[ie.Vision,ie.Chat]},{pattern:"gpt-4-vision|gpt-4v",types:[ie.Vision,ie.Chat],provider:"openai"},{pattern:"gpt-4o",types:[ie.Vision,ie.Chat,ie.FunctionCalling,ie.WebSearch],provider:"openai"},{pattern:"gemini-(pro|ultra)-vision",types:[ie.Vision,ie.Chat],provider:"google"},{pattern:"claude-3-(opus|sonnet|haiku)",types:[ie.Vision,ie.Chat,ie.FunctionCalling],provider:"anthropic"},{pattern:"qwen.*vl",types:[ie.Vision,ie.Chat],provider:"siliconflow"},{pattern:"FLUX|flux|black-forest|stable-diffusion|sd|dalle|midjourney",types:[ie.ImageGen]},{pattern:"image|img|picture|generate",types:[ie.ImageGen]},{pattern:"embedding|text-embedding|embeddings",types:[ie.Embedding]},{pattern:"text-embedding-.*|embedding-.*",types:[ie.Embedding],provider:"openai"},{pattern:".*-embedding-.*",types:[ie.Embedding],provider:"google"},{pattern:"tts|whisper|audio|speech",types:[ie.Audio,ie.Transcription]},{pattern:"tool|function|plugin",types:[ie.Tool,ie.Chat,ie.FunctionCalling]},{pattern:"function.*calling|function-calling",types:[ie.FunctionCalling,ie.Chat]},{pattern:"gpt-4-turbo|gpt-3.5-turbo",types:[ie.Chat,ie.FunctionCalling],provider:"openai"},{pattern:"web.*search|search|browse",types:[ie.WebSearch,ie.Chat]},{pattern:"gpt-4-turbo-search|gpt-4o-search",types:[ie.WebSearch,ie.Chat],provider:"openai"},{pattern:"rerank|rank",types:[ie.Rerank]},{pattern:"code|codex|coder|copilot|starcoder|codellama",types:[ie.CodeGen,ie.Chat]},{pattern:"translate|translation",types:[ie.Translation,ie.Chat]},{pattern:"reasoning|think|coder|math",types:[ie.Reasoning,ie.Chat]},{pattern:".*-thinking-.*",types:[ie.Reasoning,ie.Chat]},{pattern:"deepseek-reasoner|deepseek-r1",types:[ie.Reasoning,ie.Chat],provider:"deepseek"},{pattern:".*",types:[ie.Chat]}];function me(e,r,s=he){for(const o of s)if(!o.provider||o.provider===r)try{if(new RegExp(o.pattern,"i").test(e))return o.types}catch(t){if(e.toLowerCase().includes(o.pattern.toLowerCase()))return o.types}return[ie.Chat]}function ue(e){return{[ie.Chat]:"聊天",[ie.Vision]:"视觉",[ie.Audio]:"语音",[ie.Embedding]:"嵌入向量",[ie.Tool]:"工具使用",[ie.Reasoning]:"推理",[ie.ImageGen]:"图像生成",[ie.FunctionCalling]:"函数调用",[ie.WebSearch]:"网络搜索",[ie.Rerank]:"重排序",[ie.CodeGen]:"代码生成",[ie.Translation]:"翻译",[ie.Transcription]:"转录"}[e]||e}const ge=({open:e,onClose:o,rules:a,onSave:c,modelId:p,provider:h})=>{const[u,g]=V.useState([]),[b,j]=V.useState({pattern:"",types:[ie.Chat]}),[S,w]=V.useState([]),[I,R]=V.useState(!1);V.useEffect((()=>{e&&(g(a||[]),R(!1))}),[e,a]);const E=(e,r,s)=>{const t=[...u];t[e]="types"===r?{...t[e],types:s}:{...t[e],[r]:s},g(t)},A=(e,r)=>{j("types"===e?{...b,types:r}:{...b,[e]:r})},W=e=>{const r=[...u];r.splice(e,1),g(r)};return r.jsxs(s,{open:e,onClose:o,maxWidth:"md",fullWidth:!0,children:[r.jsx(t,{children:r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(d,{variant:"h6",children:"模型类型管理"}),r.jsx(v,{color:I?"primary":"secondary",onClick:()=>{R(!I)},children:I?"完成编辑":"编辑规则"})]})}),r.jsx(l,{children:I?r.jsxs(i,{children:[r.jsx(d,{variant:"subtitle1",gutterBottom:!0,children:"编辑模型类型匹配规则"}),r.jsx(d,{variant:"body2",color:"textSecondary",sx:{mb:2},children:"规则按顺序匹配，第一条匹配成功的规则将被应用。"}),r.jsx(i,{sx:{mb:3},children:u.map(((e,s)=>r.jsxs(i,{sx:{p:2,mb:2,border:"1px solid #e0e0e0",borderRadius:1,backgroundColor:"#f9f9f9"},children:[r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(i,{children:r.jsx(n,{fullWidth:!0,label:"匹配模式",value:e.pattern,onChange:e=>E(s,"pattern",e.target.value),helperText:"支持正则表达式或简单字符串",size:"small"})}),r.jsx(m,{color:"error",onClick:()=>W(s),children:r.jsx(H,{})})]}),r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{mt:1},children:[r.jsx(i,{children:r.jsx(n,{fullWidth:!0,label:"提供商限制（可选）",value:e.provider||"",onChange:e=>E(s,"provider",e.target.value||void 0),placeholder:"例如: openai, google",size:"small"})}),r.jsx(i,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:Object.values(ie).map((t=>r.jsx(f,{control:r.jsx(C,{checked:e.types.includes(t),onChange:e=>((e,r,s)=>{let t=[...u[e].types];s?t.includes(r)||t.push(r):(t=t.filter((e=>e!==r)),0===t.length&&(t=[ie.Chat])),E(e,"types",t)})(s,t,e.target.checked),size:"small"}),label:ue(t)},t)))})]})]},s)))}),r.jsxs(i,{sx:{p:2,mb:2,border:"1px dashed #a0a0a0",borderRadius:1},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,children:"添加新规则"}),r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(i,{children:r.jsx(n,{fullWidth:!0,label:"匹配模式",value:b.pattern,onChange:e=>A("pattern",e.target.value),helperText:"支持正则表达式或简单字符串",size:"small"})}),r.jsx(m,{color:"primary",onClick:()=>{""!==b.pattern.trim()&&(0!==b.types.length?(g([...u,b]),j({pattern:"",types:[ie.Chat]})):j({...b,types:[ie.Chat]}))},children:r.jsx(re,{})})]}),r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{mt:1},children:[r.jsx(i,{children:r.jsx(n,{fullWidth:!0,label:"提供商限制（可选）",value:b.provider||"",onChange:e=>A("provider",e.target.value||void 0),placeholder:"例如: openai, google",size:"small"})}),r.jsx(i,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:Object.values(ie).map((e=>r.jsx(f,{control:r.jsx(C,{checked:b.types.includes(e),onChange:r=>((e,r)=>{let s=[...b.types];r?s.includes(e)||s.push(e):(s=s.filter((r=>r!==e)),0===s.length&&(s=[ie.Chat])),A("types",s)})(e,r.target.checked),size:"small"}),label:ue(e)},e)))})]})]})]}):r.jsxs(i,{children:[r.jsx(d,{variant:"subtitle1",gutterBottom:!0,children:"当前模型类型匹配规则"}),u.length>0?u.map(((e,s)=>r.jsxs(i,{sx:{p:2,mb:2,border:"1px solid #e0e0e0",borderRadius:1,backgroundColor:"#f9f9f9"},children:[r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsxs(i,{children:[r.jsxs(d,{variant:"body2",children:[r.jsx("strong",{children:"匹配模式:"})," ",e.pattern]}),e.provider&&r.jsxs(d,{variant:"body2",children:[r.jsx("strong",{children:"提供商:"})," ",e.provider]})]}),r.jsx(m,{color:"error",onClick:()=>W(s),children:r.jsx(H,{})})]}),r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{mt:1},children:[r.jsx(i,{children:r.jsx(d,{variant:"body2",sx:{mb:1},children:r.jsx("strong",{children:"适用类型:"})})}),r.jsx(i,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:e.types.map((e=>r.jsx(x,{label:ue(e),size:"small",sx:{mr:.5,mb:.5}},e)))})]})]},s))):r.jsx(k,{severity:"info",sx:{mt:2},children:"尚未配置任何类型匹配规则，将使用默认规则。"}),p&&h&&r.jsxs(i,{sx:{mt:4,p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,children:"测试模型类型匹配"}),r.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[r.jsxs(d,{variant:"body2",sx:{mr:1},children:[r.jsx("strong",{children:"模型ID:"})," ",p]}),r.jsxs(d,{variant:"body2",children:[r.jsx("strong",{children:"提供商:"})," ",h]})]}),r.jsx(v,{variant:"outlined",color:"primary",onClick:()=>{if(p&&h){const e=me(p,h,u);w(e)}},size:"small",children:"测试匹配"}),S.length>0&&r.jsxs(i,{sx:{mt:2},children:[r.jsx(d,{variant:"body2",children:"匹配结果:"}),r.jsx(i,{display:"flex",flexWrap:"wrap",gap:.5,sx:{mt:1},children:S.map((e=>r.jsx(x,{label:ue(e),color:"primary",size:"small",sx:{mr:.5,mb:.5}},e)))})]})]})]})}),r.jsxs(y,{children:[r.jsx(v,{onClick:o,children:"取消"}),r.jsx(v,{onClick:()=>{c(u),o()},variant:"contained",color:"primary",children:"保存"})]})]})},be={basic:["chat"],input:["vision","audio"],output:["image_gen","transcription","translation"],advanced:["reasoning","function_calling","web_search","tool","code_gen"],data:["embedding","rerank"]},je={chat:{description:"基础聊天功能，支持文本对话",color:"#4285F4"},vision:{description:"支持图像理解和分析",color:"#EA4335"},audio:{description:"支持音频处理和理解",color:"#FBBC05"},embedding:{description:"生成文本的向量表示，用于相似度搜索",color:"#34A853"},tool:{description:"支持使用外部工具和API",color:"#8E44AD"},reasoning:{description:"增强的推理能力，适合复杂问题解决",color:"#1ABC9C"},image_gen:{description:"生成图像的能力",color:"#E74C3C"},function_calling:{description:"支持函数调用，可与代码交互",color:"#3498DB"},web_search:{description:"支持网络搜索，获取实时信息",color:"#F39C12"},rerank:{description:"重新排序搜索结果或文档",color:"#16A085"},code_gen:{description:"代码生成和编程辅助",color:"#2C3E50"},translation:{description:"文本翻译功能",color:"#27AE60"},transcription:{description:"语音转文字功能",color:"#D35400"}},ye=({modelTypes:e,onChange:s,autoDetect:t,onAutoDetectChange:o,modelId:n,provider:a})=>{const l=S(),[c,p]=V.useState(!1),[u,g]=V.useState([]);V.useEffect((()=>{(async()=>{try{const e=await q.getSetting("modelTypeRules");g(e||he)}catch(e){console.error("[EnhancedModelTypeSelector] 加载模型类型规则失败:",e),g(he)}})()}),[]);const b=e=>({basic:"基础功能",input:"输入能力",output:"输出能力",advanced:"高级功能",data:"数据处理"}[e]||e);return r.jsxs(i,{sx:{mt:3},children:[r.jsxs(i,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1},children:[r.jsxs(i,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(d,{variant:"subtitle2",children:"模型类型"}),r.jsx(w,{title:"模型类型决定了模型的能力和适用场景。您可以根据需要选择多种类型。",children:r.jsx(m,{size:"small",sx:{ml:.5},children:r.jsx(ae,{fontSize:"small"})})})]}),r.jsxs(i,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(f,{control:r.jsx(I,{checked:t,onChange:e=>o(e.target.checked),size:"small"}),label:"自动检测"}),r.jsx(w,{title:"管理模型类型规则",children:r.jsx(m,{size:"small",onClick:()=>{p(!0)},sx:{ml:1},children:r.jsx($,{fontSize:"small"})})})]})]}),r.jsx(R,{elevation:0,sx:{p:2,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"dark"===l.palette.mode?h(l.palette.background.paper,.6):h(l.palette.background.paper,.8)},children:Object.entries(be).map((([o,n])=>r.jsxs(i,{sx:{mb:2},children:[r.jsx(d,{variant:"subtitle2",sx:{mb:1,color:"text.secondary",fontSize:"0.8rem",textTransform:"uppercase"},children:b(o)}),r.jsx(i,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:n.map((o=>{const i=o,n=je[i]||{description:"",color:l.palette.primary.main};return r.jsx(w,{title:n.description,arrow:!0,placement:"top",children:r.jsx(x,{label:ue(i),color:e.includes(i)?"primary":"default",onClick:()=>!t&&(r=>{if(!t){const t=[...e],o=t.indexOf(r);-1===o?t.push(r):t.splice(o,1),0===t.length&&t.push(ie.Chat),s(t)}})(i),sx:{mr:.5,mb:.5,bgcolor:e.includes(i)?h(n.color,.2):"default",color:e.includes(i)?n.color:"text.secondary",borderColor:e.includes(i)?n.color:"divider","&:hover":{bgcolor:e.includes(i)?h(n.color,.3):h(l.palette.action.hover,.1)}},disabled:t,variant:e.includes(i)?"filled":"outlined"})},i)}))})]},o)))}),r.jsx(E,{children:t?"根据模型ID和提供商自动检测模型类型":"点击类型标签来添加或移除"}),r.jsx(ge,{open:c,onClose:()=>p(!1),rules:u,onSave:async e=>{g(e);try{await q.saveSetting("modelTypeRules",e)}catch(r){console.error("[EnhancedModelTypeSelector] 保存模型类型规则失败:",r)}},modelId:n,provider:a})]})},ve=({open:e,onClose:o,onSave:a,editModel:c})=>{const[p,x]=V.useState({id:"",name:"",provider:"openai",enabled:!0,isDefault:!1}),[h,u]=V.useState({}),[g,b]=V.useState([ie.Chat]),[j,f]=V.useState(!0),[C,k]=V.useState(""),[S,I]=V.useState(!1);V.useEffect((()=>{if(c){if(x(c),c.modelTypes)b(c.modelTypes),f(!1);else{const e=me(c.id,c.provider);b(e),f(!0)}(async()=>{try{const e=await q.getModel(c.id);(null==e?void 0:e.avatar)&&k(e.avatar)}catch(e){console.error("[SimpleModelDialog] 加载模型头像失败:",e)}})()}}),[c,e]);const R=(e,r)=>{if(x({...p,[e]:r}),("id"===e||"provider"===e)&&j){const s={...p,[e]:r},t=me(s.id,s.provider);b(t)}h[e]&&u({...h,[e]:""})};return r.jsxs(s,{open:e,onClose:o,maxWidth:"sm",fullWidth:!0,children:[r.jsx(t,{children:c?"编辑模型":"添加模型"}),r.jsxs(l,{children:[r.jsxs(i,{sx:{mb:3,mt:1},children:[r.jsxs(i,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2,p:2,bgcolor:"rgba(25, 118, 210, 0.08)",borderRadius:1,border:"1px solid rgba(25, 118, 210, 0.2)"},children:[r.jsxs(i,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(A,{src:C,sx:{width:48,height:48,mr:2,bgcolor:"#1677ff"},children:!C&&p.name.charAt(0)}),r.jsxs(i,{children:[r.jsx(d,{variant:"body2",fontWeight:"medium",children:"模型头像"}),r.jsx(d,{variant:"caption",color:"text.secondary",children:"为此模型设置自定义头像"})]})]}),r.jsx(w,{title:"设置头像",children:r.jsx(m,{color:"primary",onClick:()=>{I(!0)},size:"small",sx:{bgcolor:"rgba(25, 118, 210, 0.12)","&:hover":{bgcolor:"rgba(25, 118, 210, 0.2)"}},children:r.jsx(de,{})})})]}),r.jsx(n,{fullWidth:!0,label:"模型名称",value:p.name,onChange:e=>R("name",e.target.value),margin:"normal",error:!!h.name,helperText:h.name,required:!0}),r.jsxs(W,{fullWidth:!0,margin:"normal",children:[r.jsx(z,{id:"provider-label",children:"提供商"}),r.jsxs(M,{labelId:"provider-label",value:p.provider,onChange:e=>R("provider",e.target.value),label:"提供商",children:[r.jsx(T,{value:"openai",children:"OpenAI"}),r.jsx(T,{value:"anthropic",children:"Anthropic"}),r.jsx(T,{value:"google",children:"Google"}),r.jsx(T,{value:"deepseek",children:"DeepSeek"}),r.jsx(T,{value:"siliconflow",children:"SiliconFlow"}),r.jsx(T,{value:"volcengine",children:"火山引擎"}),r.jsx(T,{value:"custom",children:"自定义"})]}),r.jsx(E,{children:"选择API提供商，可以与模型ID自由组合"})]}),r.jsx(n,{fullWidth:!0,label:"模型ID",value:p.id,onChange:e=>R("id",e.target.value),margin:"normal",helperText:"模型的唯一标识符，例如：gpt-4、claude-3-opus"}),r.jsx(ye,{modelTypes:g,onChange:b,autoDetect:j,onAutoDetectChange:()=>{const e=!j;if(f(e),e){const e=me(p.id,p.provider);b(e)}},modelId:p.id,provider:p.provider})]}),r.jsx(ne,{open:S,onClose:()=>{I(!1)},onSave:async e=>{if(k(e),p.id)try{const r={...await q.getModel(p.id),id:p.id,avatar:e,updatedAt:(new Date).toISOString()};await q.saveModel(p.id,r),p.id}catch(r){console.error("[SimpleModelDialog] 保存模型头像到数据库失败:",r)}},currentAvatar:C,title:"设置模型头像"})]}),r.jsxs(y,{children:[r.jsx(v,{onClick:o,children:"取消"}),r.jsx(v,{onClick:async()=>{if((()=>{const e={};return p.name.trim()||(e.name="请输入模型名称"),u(e),0===Object.keys(e).length})()){const r={...p,modelTypes:j?void 0:g,capabilities:{...p.capabilities,multimodal:g.includes(ie.Vision)}};if(c&&c.id!==p.id&&C)try{await q.saveModel(p.id,{id:p.id,avatar:C,updatedAt:(new Date).toISOString()})}catch(e){console.error("[SimpleModelDialog] 保存模型头像到数据库失败:",e)}try{const e={...r,avatar:C,updatedAt:(new Date).toISOString()};await q.saveModel(r.id,e),r.id}catch(e){console.error("[SimpleModelDialog] 保存模型配置到数据库失败:",e)}a(r),o()}},variant:"contained",color:"primary",children:"保存"})]})]})},fe=e=>{if(e.endsWith("/")||e.endsWith("volces.com/api/v3"))return e;let r=e.trim();return r.endsWith("/")&&(r=r.slice(0,-1)),`${r}/v1/`},Ce=(e,r)=>e.trim()?((e,r)=>{if(e.endsWith("#"))return e.replace("#","");let s=e;return s.startsWith("@")&&(s=s.substring(1)),"openai"===r?fe(s)+"chat/completions":fe(s)+"responses"})(e,r):"",ke=()=>{const e=L(),c=K(),{providerId:p}=B(),x=X((e=>e.settings.providers.find((e=>e.id===p)))),[u,C]=V.useState(""),[S,w]=V.useState(""),[E,W]=V.useState(!0),[z,M]=V.useState(!1),[T,F]=V.useState(!1),[U,_]=V.useState(!1),[G,q]=V.useState(void 0),[te,oe]=V.useState(""),[ie,ne]=V.useState(""),[ae,de]=V.useState(""),[ce,he]=V.useState(!1),[me,ue]=V.useState(!1),[ge,be]=V.useState(null),[je,ye]=V.useState(null),[fe,ke]=V.useState(!1),[Se,we]=V.useState(!1),[Ie,Re]=V.useState(""),[Ee,Ae]=V.useState({}),[We,ze]=V.useState(""),[Me,Te]=V.useState(""),[Oe,De]=V.useState(!1);V.useEffect((()=>{x&&(C(x.apiKey||""),w(x.baseUrl||""),W(x.isEnabled),Ae(x.extraHeaders||{}))}),[x]);const Pe=()=>{e("/settings/default-model",{replace:!0})},Ve=()=>!!x&&(S&&!ee(S)?(de("请输入有效的URL"),!1):(c(Y({id:x.id,updates:{apiKey:u,baseUrl:S.trim(),isEnabled:E,extraHeaders:Ee}})),!0)),Le=(e,r,s)=>{Ae((t=>{const o={...t};return e!==r&&delete o[e],o[r]=s,o}))},Be=e=>{if(x){const r=x.models.filter((r=>r.id!==e));c(Y({id:x.id,updates:{models:r}}))}},Fe=V.useCallback((e=>{if(x){const r={...e,provider:x.id,providerType:x.providerType,enabled:!0};if(x.models.some((r=>r.id===e.id)))return;const s=[...x.models,r];c(Y({id:x.id,updates:{models:s}}))}}),[x,c]),Ue=V.useCallback((e=>{if(x&&e.length>0){const r=e.filter((e=>!x.models.some((r=>r.id===e.id)))).map((e=>({...e,provider:x.id,providerType:x.providerType,enabled:!0})));if(0===r.length)return;const s=[...x.models,...r];c(Y({id:x.id,updates:{models:s}}))}}),[x,c]),_e=V.useCallback((e=>{if(x&&e.length>0){const r=x.models.filter((r=>!e.includes(r.id)));c(Y({id:x.id,updates:{models:r}}))}}),[x,c]);return V.useEffect((()=>{ge&&ge.message&&ge.message.length>80&&ke(!0)}),[ge]),x?r.jsxs(i,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?h(e.palette.primary.main,.02):h(e.palette.background.default,.9)},children:[r.jsx(O,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(D,{children:[r.jsx(m,{edge:"start",onClick:Pe,"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(J,{})}),r.jsx(d,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:x.name}),r.jsx(v,{onClick:()=>{Ve()&&e("/settings/default-model",{replace:!0})},sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"保存"})]})}),r.jsxs(i,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[r.jsxs(R,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)",mb:3},children:[r.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:3},children:[r.jsx(A,{sx:{width:56,height:56,bgcolor:x.color||"#9333EA",fontSize:"1.5rem",mr:2,boxShadow:"0 4px 8px rgba(0,0,0,0.1)"},children:x.avatar}),r.jsxs(i,{children:[r.jsx(d,{variant:"h6",sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:x.name}),r.jsx(d,{variant:"body2",color:"text.secondary",children:x.isSystem?"系统供应商":"openai"===x.providerType?"OpenAI API":"anthropic"===x.providerType?"Anthropic API":"gemini"===x.providerType?"Google Generative AI API":"自定义API"})]}),r.jsx(i,{sx:{ml:"auto",display:"flex",gap:1},children:!x.isSystem&&r.jsxs(r.Fragment,{children:[r.jsx(m,{onClick:()=>{x&&(Re(x.name),we(!0))},sx:{bgcolor:e=>h(e.palette.info.main,.1),"&:hover":{bgcolor:e=>h(e.palette.info.main,.2)}},children:r.jsx(se,{color:"info"})}),r.jsx(m,{color:"error",onClick:()=>F(!0),sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)}},children:r.jsx(H,{})})]})})]}),x.isSystem?r.jsxs(i,{sx:{p:2,bgcolor:e=>h(e.palette.info.main,.1),borderRadius:2,border:"1px solid",borderColor:e=>h(e.palette.info.main,.3)},children:[r.jsx(d,{variant:"body2",color:"info.main",sx:{fontWeight:500},children:"🧠 系统供应商说明"}),r.jsx(d,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"模型组合供应商是系统内置的虚拟供应商，它使用您配置的模型组合来提供服务。 模型组合中的各个模型会使用它们各自配置的 API 密钥和基础 URL。"})]}):r.jsxs(r.Fragment,{children:[r.jsx(a,{sx:{my:3}}),r.jsx(d,{variant:"subtitle1",sx:{mb:2,fontWeight:600,color:"text.primary"},children:"API配置"}),r.jsxs(i,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"启用状态"}),r.jsx(f,{control:r.jsx(I,{checked:E,onChange:e=>W(e.target.checked),color:"primary"}),label:E?"已启用":"已禁用"})]}),r.jsxs(i,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"API密钥"}),r.jsx(n,{fullWidth:!0,placeholder:"输入API密钥",value:u,onChange:e=>C(e.target.value),variant:"outlined",type:"password",size:"small",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})]}),r.jsxs(i,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"基础URL (可选)"}),r.jsx(n,{fullWidth:!0,placeholder:"输入基础URL，例如: https://tow.bt6.top",value:S,onChange:e=>{w(e.target.value),de("")},error:!!ae,helperText:r.jsxs("span",{children:[ae&&r.jsx("span",{style:{display:"block",color:"error.main",marginBottom:"4px",fontSize:"0.75rem"},children:ae}),r.jsx("span",{style:{display:"block",color:"text.secondary",marginBottom:"4px",fontSize:"0.75rem"},children:"在URL末尾添加#可强制使用自定义格式，末尾添加/也可保持原格式"}),S&&r.jsxs("span",{style:{display:"inline-block",color:S.endsWith("#")||S.endsWith("/")?"#ed6c02":"#666",fontFamily:"monospace",fontSize:"0.7rem",backgroundColor:"rgba(0, 0, 0, 0.04)",padding:"2px 6px",borderRadius:"4px",marginTop:"4px"},children:[S.endsWith("#")?"强制使用: ":S.endsWith("/")?"保持原格式: ":"完整地址: ",Ce(S,null==x?void 0:x.providerType)]})]}),variant:"outlined",size:"small",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})]}),r.jsxs(i,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"自定义请求头 (可选)"}),r.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:2},children:[r.jsx(v,{variant:"outlined",startIcon:r.jsx($,{}),onClick:()=>De(!0),sx:{borderRadius:2,borderColor:e=>h(e.palette.secondary.main,.5),color:"secondary.main","&:hover":{borderColor:"secondary.main",bgcolor:e=>h(e.palette.secondary.main,.1)}},children:"配置请求头"}),Object.keys(Ee).length>0&&r.jsxs(d,{variant:"caption",color:"text.secondary",children:["已配置 ",Object.keys(Ee).length," 个请求头"]})]})]}),r.jsx(i,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:r.jsx(v,{variant:"outlined",startIcon:me?r.jsx(o,{size:16}):r.jsx(pe,{}),onClick:async()=>{if(x){if(!Ve()&&ae)return void be({success:!1,message:"请输入有效的基础URL"});ue(!0),be(null);try{const e={id:x.models.length>0?x.models[0].id:"gpt-3.5-turbo",name:x.name,provider:x.id,providerType:x.providerType,apiKey:u,baseUrl:S,enabled:!0},r=await N(e);be(r?{success:!0,message:"连接成功！API配置有效。"}:{success:!1,message:"连接失败，请检查API密钥和基础URL是否正确。"})}catch(e){console.error("测试API连接时出错:",e),be({success:!1,message:`连接错误: ${e instanceof Error?e.message:String(e)}`})}finally{ue(!1)}}},disabled:me||!u,sx:{borderRadius:2,borderColor:e=>h(e.palette.info.main,.5),color:"info.main","&:hover":{borderColor:"info.main",bgcolor:e=>h(e.palette.info.main,.1)}},children:me?"测试中...":"测试连接"})})]})]}),r.jsxs(R,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:3},children:[r.jsx(d,{variant:"subtitle1",sx:{fontWeight:600,flex:1,color:"text.primary"},children:x.isSystem?"模型组合":"可用模型"}),x.isSystem?r.jsx(v,{variant:"outlined",startIcon:r.jsx($,{}),onClick:()=>window.location.href="/settings/model-combo",sx:{borderRadius:2,borderColor:e=>h(e.palette.primary.main,.5),color:"primary.main","&:hover":{borderColor:"primary.main",bgcolor:e=>h(e.palette.primary.main,.1)}},children:"管理组合"}):r.jsxs(r.Fragment,{children:[r.jsx(d,{variant:"caption",color:"text.secondary",sx:{mr:2,display:{xs:"none",sm:"block"}},children:"点击✓测试单个模型"}),r.jsx(v,{variant:"outlined",startIcon:r.jsx(le,{}),onClick:()=>{Ve()?he(!0):ae&&alert("请输入有效的基础URL")},sx:{mr:2,borderRadius:2,borderColor:e=>h(e.palette.info.main,.5),color:"info.main","&:hover":{borderColor:"info.main",bgcolor:e=>h(e.palette.info.main,.1)}},children:"自动获取"}),r.jsx(v,{startIcon:r.jsx(re,{}),onClick:()=>M(!0),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"手动添加"})]})]}),r.jsxs(g,{sx:{width:"100%"},children:[x.models.map((e=>r.jsx(R,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",transition:"all 0.2s","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.05)",borderColor:e=>h(e.palette.primary.main,.3)}},children:r.jsx(b,{secondaryAction:x.isSystem?r.jsx(i,{children:r.jsx(m,{"aria-label":"edit-combo",onClick:()=>window.location.href="/settings/model-combo",sx:{bgcolor:e=>h(e.palette.primary.main,.1),"&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)}},children:r.jsx($,{color:"primary"})})}):r.jsxs(i,{children:[r.jsx(m,{"aria-label":"test",onClick:()=>(async e=>{var r;if(x){ye(e.id),be(null);try{const s={...e,apiKey:u,baseUrl:S,enabled:!0},t=await Q({messages:[{role:"user",content:"这是一条API测试消息，请简短回复以验证连接。"}],modelId:s.id});t.success?be({success:!0,message:`模型 ${e.name} 连接成功!\n\n响应内容: "${null==(r=t.content)?void 0:r.substring(0,100)}${t.content&&t.content.length>100?"...":""}"`}):be({success:!1,message:`模型 ${e.name} 连接失败：${t.error||"未知错误"}`})}catch(s){console.error("测试模型连接时出错:",s),be({success:!1,message:`连接错误: ${s instanceof Error?s.message:String(s)}`})}finally{ye(null)}}})(e),disabled:null!==je,sx:{mr:1,bgcolor:e=>h(e.palette.success.main,.1),"&:hover":{bgcolor:e=>h(e.palette.success.main,.2)}},children:je===e.id?r.jsx(o,{size:16,color:"success"}):r.jsx(pe,{color:"success"})}),r.jsx(m,{"aria-label":"edit",onClick:()=>(e=>{q(e),oe(e.name),ne(e.id),_(!0)})(e),sx:{mr:1,bgcolor:e=>h(e.palette.info.main,.1),"&:hover":{bgcolor:e=>h(e.palette.info.main,.2)}},children:r.jsx(se,{color:"info"})}),r.jsx(m,{"aria-label":"delete",onClick:()=>Be(e.id),sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)}},children:r.jsx(H,{color:"error"})})]}),children:r.jsx(j,{primary:r.jsxs(i,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(d,{variant:"subtitle2",fontWeight:600,children:e.name}),e.isDefault&&r.jsx(i,{sx:{ml:1,px:1,py:.2,borderRadius:1,fontSize:"0.7rem",fontWeight:600,bgcolor:e=>h(e.palette.success.main,.1),color:"success.main"},children:"默认"})]}),secondary:r.jsxs(d,{variant:"body2",color:"text.secondary",fontSize:"0.8rem",children:["ID: ",e.id]})})})},e.id))),0===x.models.length&&r.jsxs(i,{sx:{textAlign:"center",py:3},children:[r.jsx(d,{color:"text.secondary",children:x.isSystem?"尚未创建任何模型组合":"尚未添加任何模型"}),x.isSystem&&r.jsx(v,{variant:"outlined",startIcon:r.jsx(re,{}),onClick:()=>window.location.href="/settings/model-combo",sx:{mt:2},children:"创建模型组合"})]})]})]}),r.jsx(P,{open:null!==ge&&!fe,autoHideDuration:6e3,onClose:()=>be(null),anchorOrigin:{vertical:"bottom",horizontal:"center"},action:r.jsx(v,{color:"inherit",size:"small",onClick:()=>ke(!0),children:"查看详情"}),children:r.jsx(k,{onClose:()=>be(null),severity:(null==ge?void 0:ge.success)?"success":"error",variant:"filled",sx:{width:"100%"},children:(null==ge?void 0:ge.success)?"连接测试成功!":"连接测试失败"})}),r.jsxs(s,{open:fe,onClose:()=>ke(!1),maxWidth:"md",slotProps:{paper:{sx:{width:"100%",maxWidth:500,borderRadius:2}}},children:[r.jsxs(t,{sx:{fontWeight:600,color:(null==ge?void 0:ge.success)?"success.main":"error.main",display:"flex",alignItems:"center"},children:[(null==ge?void 0:ge.success)?r.jsx(pe,{sx:{mr:1}}):null,"API测试结果"]}),r.jsx(l,{children:r.jsx(d,{sx:{whiteSpace:"pre-wrap"},children:(null==ge?void 0:ge.message)||""})}),r.jsx(y,{sx:{p:2},children:r.jsx(v,{onClick:()=>ke(!1),variant:"contained",color:(null==ge?void 0:ge.success)?"success":"primary",sx:{borderRadius:2},children:"确定"})})]})]}),r.jsxs(s,{open:z,onClose:()=>M(!1),children:[r.jsx(t,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"添加模型"}),r.jsxs(l,{children:[r.jsx(n,{autoFocus:!0,margin:"dense",label:"模型名称",placeholder:"例如: GPT-4o",type:"text",fullWidth:!0,variant:"outlined",value:te,onChange:e=>oe(e.target.value),sx:{mb:2,mt:2}}),r.jsx(n,{margin:"dense",label:"模型ID",placeholder:"例如: gpt-4o",type:"text",fullWidth:!0,variant:"outlined",value:ie,onChange:e=>ne(e.target.value)})]}),r.jsxs(y,{sx:{p:2},children:[r.jsx(v,{onClick:()=>M(!1),children:"取消"}),r.jsx(v,{onClick:()=>{if(x&&te&&ie){const e={id:ie,name:te,provider:x.id,providerType:x.providerType,enabled:!0,isDefault:!1},r=[...x.models,e];c(Y({id:x.id,updates:{models:r}})),oe(""),ne(""),M(!1)}},disabled:!te||!ie,sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})]}),r.jsx(ve,{open:U,onClose:()=>_(!1),onSave:e=>{if(x&&e){const r=x.models.filter((e=>!G||e.id!==G.id));r.push(e),c(Y({id:x.id,updates:{models:r}})),q(void 0),_(!1)}},editModel:G}),r.jsxs(s,{open:T,onClose:()=>F(!1),children:[r.jsx(t,{fontWeight:600,children:"删除提供商"}),r.jsx(l,{children:r.jsxs(d,{children:["确定要删除 ",r.jsx("b",{children:x.name})," 提供商吗？这将同时删除所有相关的模型配置。"]})}),r.jsxs(y,{sx:{p:2},children:[r.jsx(v,{onClick:()=>F(!1),children:"取消"}),r.jsx(v,{onClick:()=>{x&&c(Z(x.id)),F(!1),e("/settings/default-model",{replace:!0})},color:"error",sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)},borderRadius:2},children:"删除"})]})]}),r.jsxs(s,{open:Se,onClose:()=>we(!1),children:[r.jsx(t,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"编辑供应商名称"}),r.jsx(l,{children:r.jsx(n,{autoFocus:!0,margin:"dense",label:"供应商名称",placeholder:"例如: 我的智谱AI",type:"text",fullWidth:!0,variant:"outlined",value:Ie,onChange:e=>Re(e.target.value),sx:{mb:2,mt:2}})}),r.jsxs(y,{sx:{p:2},children:[r.jsx(v,{onClick:()=>we(!1),children:"取消"}),r.jsx(v,{onClick:()=>{x&&Ie.trim()&&(c(Y({id:x.id,updates:{name:Ie.trim()}})),we(!1),Re(""))},disabled:!Ie.trim(),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"保存"})]})]}),r.jsxs(s,{open:Oe,onClose:()=>De(!1),maxWidth:"md",fullWidth:!0,children:[r.jsx(t,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"配置自定义请求头"}),r.jsxs(l,{children:[r.jsx(d,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"用于解决 CORS 问题或添加特殊认证头"}),r.jsxs(i,{sx:{mb:3,p:2,bgcolor:"grey.50",borderRadius:2},children:[r.jsx(d,{variant:"subtitle2",sx:{mb:2,fontWeight:600},children:"快速操作"}),r.jsxs(i,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[r.jsx(v,{size:"small",variant:"outlined",onClick:()=>{Ae((e=>({...e,"x-stainless-timeout":"REMOVE"})))},sx:{fontSize:"0.75rem"},children:"禁用 x-stainless-timeout"}),r.jsx(v,{size:"small",variant:"outlined",onClick:()=>{Ae((e=>({...e,"x-stainless-retry-count":"REMOVE"})))},sx:{fontSize:"0.75rem"},children:"禁用 x-stainless-retry-count"}),r.jsx(v,{size:"small",variant:"outlined",color:"error",onClick:()=>{Ae((e=>({...e,"x-stainless-timeout":"REMOVE","x-stainless-retry-count":"REMOVE","x-stainless-arch":"REMOVE","x-stainless-lang":"REMOVE","x-stainless-os":"REMOVE","x-stainless-package-version":"REMOVE","x-stainless-runtime":"REMOVE","x-stainless-runtime-version":"REMOVE"})))},sx:{fontSize:"0.75rem"},children:"禁用所有 stainless 头部"})]}),r.jsx(d,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:1},children:'设置值为 "REMOVE" 可以禁用默认的请求头'})]}),Object.entries(Ee).map((([e,s])=>r.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:2,gap:1},children:[r.jsx(n,{size:"small",label:"请求头名称",value:e,onChange:r=>Le(e,r.target.value,s),sx:{flex:1}}),r.jsx(n,{size:"small",label:"请求头值",value:s,onChange:r=>Le(e,e,r.target.value),sx:{flex:1,"& .MuiInputBase-input":{color:"REMOVE"===s?"error.main":"inherit"}},helperText:"REMOVE"===s?"此头部将被禁用":"",slotProps:{formHelperText:{sx:{color:"error.main",fontSize:"0.7rem"}}}}),r.jsx(m,{onClick:()=>(e=>{Ae((r=>{const s={...r};return delete s[e],s}))})(e),sx:{color:"error.main","&:hover":{bgcolor:e=>h(e.palette.error.main,.1)}},children:r.jsx(H,{})})]},e))),r.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1,mt:3,p:2,bgcolor:"grey.50",borderRadius:2},children:[r.jsx(n,{size:"small",label:"新请求头名称",placeholder:"例如: x-stainless-timeout",value:We,onChange:e=>ze(e.target.value),sx:{flex:1}}),r.jsx(n,{size:"small",label:"新请求头值",placeholder:"例如: 30000",value:Me,onChange:e=>Te(e.target.value),sx:{flex:1}}),r.jsx(v,{startIcon:r.jsx(re,{}),onClick:()=>{We.trim()&&Me.trim()&&(Ae((e=>({...e,[We.trim()]:Me.trim()}))),ze(""),Te(""))},disabled:!We.trim()||!Me.trim(),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})]}),r.jsxs(y,{sx:{p:2},children:[r.jsx(v,{onClick:()=>De(!1),children:"取消"}),r.jsx(v,{onClick:()=>De(!1),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"确定"})]})]}),x&&r.jsx(xe,{open:ce,onClose:()=>he(!1),provider:x,onAddModel:Fe,onAddModels:Ue,onRemoveModel:Be,onRemoveModels:_e,existingModels:x.models||[]})]}):r.jsxs(i,{sx:{p:3},children:[r.jsx(d,{children:"未找到该提供商，请返回设置页面"}),r.jsx(v,{onClick:Pe,children:"返回"})]})};export{ke as default};
