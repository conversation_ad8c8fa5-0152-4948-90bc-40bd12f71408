/**
 * 语义搜索服务
 * 基于向量相似度的记忆搜索
 */

import type { MemoryRecord, MemorySearchResult } from '../../types/internalMemory';
import { InternalMemoryService } from './InternalMemoryService';
import { MemoryEmbeddingService } from './EmbeddingService';
import { getMemoryConfig } from '../../config/internalMemoryConfig';
import LoggerService from '../LoggerService';

/**
 * 语义搜索服务类
 * 提供基于向量相似度的记忆搜索功能
 */
export class SemanticSearchService {
  private static instance: SemanticSearchService;
  private memoryService: InternalMemoryService;
  private embeddingService: MemoryEmbeddingService;

  private constructor() {
    this.memoryService = InternalMemoryService.getInstance();
    this.embeddingService = MemoryEmbeddingService.getInstance();
  }

  /**
   * 获取当前配置（实时获取最新配置）
   */
  private getConfig() {
    return getMemoryConfig();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): SemanticSearchService {
    if (!SemanticSearchService.instance) {
      SemanticSearchService.instance = new SemanticSearchService();
    }
    return SemanticSearchService.instance;
  }

  /**
   * 搜索相似记忆
   */
  public async searchSimilarMemories(
    query: string,
    userId: string,
    options: {
      limit?: number;
      threshold?: number;
      category?: string;
    } = {}
  ): Promise<MemorySearchResult[]> {
    try {
      const config = this.getConfig();
      if (!config.enabled || !query.trim()) {
        return [];
      }

      const {
        limit = config.search.maxResults,
        threshold = config.search.similarityThreshold,
        category
      } = options;

      // 🔧 修复：为查询向量生成添加错误处理，防止阻塞记忆搜索
      let queryEmbedding: number[];
      try {
        // 生成查询向量
        queryEmbedding = await this.embeddingService.generateMemoryEmbedding(query);
        if (!this.embeddingService.validateEmbedding(queryEmbedding)) {
          LoggerService.log('WARN', 'Invalid query embedding generated, falling back to text search');
          return [];
        }
      } catch (embeddingError) {
        LoggerService.log('WARN', 'Query embedding generation failed, falling back to text search', {
          error: embeddingError,
          query: query.substring(0, 50)
        });
        return [];
      }

      // 获取用户记忆
      let memories: MemoryRecord[];
      if (category) {
        memories = await this.memoryService.getMemoriesByCategory(userId, category as any);
      } else {
        memories = await this.memoryService.getUserMemories(userId);
      }

      // 过滤出有有效嵌入向量的记忆
      const memoriesWithEmbeddings = memories.filter(memory => 
        this.embeddingService.validateEmbedding(memory.embedding)
      );

      if (memoriesWithEmbeddings.length === 0) {
        LoggerService.log('INFO', 'No memories with valid embeddings found', { userId });
        return [];
      }

      // 计算相似度
      const similarities: MemorySearchResult[] = memoriesWithEmbeddings.map(memory => ({
        memory,
        similarity: this.embeddingService.calculateCosineSimilarity(
          queryEmbedding,
          memory.embedding
        )
      }));

      // 过滤和排序
      const results = similarities
        .filter(result => result.similarity >= threshold)
        .sort((a, b) => {
          // 首先按相似度排序
          if (Math.abs(a.similarity - b.similarity) > 0.001) {
            return b.similarity - a.similarity;
          }
          // 相似度相近时按重要性排序
          return b.memory.importance - a.memory.importance;
        })
        .slice(0, limit);

      LoggerService.log('INFO', 'Semantic search completed', {
        query: query.substring(0, 50),
        userId,
        totalMemories: memories.length,
        memoriesWithEmbeddings: memoriesWithEmbeddings.length,
        resultsFound: results.length,
        threshold
      });

      return results;
    } catch (error) {
      LoggerService.log('ERROR', 'Semantic search failed', { 
        error, 
        query: query.substring(0, 50), 
        userId 
      });
      return [];
    }
  }

  /**
   * 搜索最相关的记忆（简化版）
   */
  public async findRelevantMemories(
    query: string,
    userId: string,
    maxResults: number = 5
  ): Promise<MemoryRecord[]> {
    try {
      const searchResults = await this.searchSimilarMemories(query, userId, {
        limit: maxResults
      });

      return searchResults.map(result => result.memory);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to find relevant memories', { error, query, userId });
      return [];
    }
  }

  /**
   * 混合搜索（语义搜索 + 文本搜索）
   */
  public async hybridSearch(
    query: string,
    userId: string,
    options: {
      limit?: number;
      semanticWeight?: number; // 语义搜索权重 (0-1)
      textWeight?: number;     // 文本搜索权重 (0-1)
    } = {}
  ): Promise<MemorySearchResult[]> {
    try {
      const config = this.getConfig();
      if (!config.enabled || !query.trim()) {
        return [];
      }

      const {
        limit = config.search.maxResults,
        semanticWeight = 0.7,
        textWeight = 0.3
      } = options;

      // 语义搜索
      const semanticResults = await this.searchSimilarMemories(query, userId, {
        limit: limit * 2 // 获取更多结果用于混合
      });

      // 文本搜索
      const textResults = await this.textSearch(query, userId, limit * 2);

      // 合并结果
      const combinedResults = this.combineSearchResults(
        semanticResults,
        textResults,
        semanticWeight,
        textWeight
      );

      // 去重和排序
      const uniqueResults = this.deduplicateResults(combinedResults);
      
      return uniqueResults
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      LoggerService.log('ERROR', 'Hybrid search failed', { error, query, userId });
      return [];
    }
  }

  /**
   * 文本搜索（作为语义搜索的补充）
   */
  private async textSearch(
    query: string,
    userId: string,
    limit: number
  ): Promise<MemorySearchResult[]> {
    try {
      const memories = await this.memoryService.getUserMemories(userId);

      const textResults: MemorySearchResult[] = memories
        .map(memory => ({
          memory,
          similarity: this.calculateTextSimilarity(memory.content, query)
        }))
        .filter(result => result.similarity > 0)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

      return textResults;
    } catch (error) {
      LoggerService.log('ERROR', 'Text search failed', { error, query, userId });
      return [];
    }
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(content: string, query: string): number {
    const contentLower = content.toLowerCase();
    const queryLower = query.toLowerCase();

    // 完全匹配
    if (contentLower.includes(queryLower)) {
      return 0.9;
    }

    // 单词匹配
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    const contentWords = contentLower.split(/\s+/);

    if (queryWords.length === 0) {
      return 0;
    }

    let matchCount = 0;
    for (const queryWord of queryWords) {
      if (contentWords.some(contentWord => 
        contentWord.includes(queryWord) || queryWord.includes(contentWord)
      )) {
        matchCount++;
      }
    }

    return (matchCount / queryWords.length) * 0.7;
  }

  /**
   * 合并搜索结果
   */
  private combineSearchResults(
    semanticResults: MemorySearchResult[],
    textResults: MemorySearchResult[],
    semanticWeight: number,
    textWeight: number
  ): MemorySearchResult[] {
    const resultMap = new Map<string, MemorySearchResult>();

    // 添加语义搜索结果
    for (const result of semanticResults) {
      resultMap.set(result.memory.id, {
        memory: result.memory,
        similarity: result.similarity * semanticWeight
      });
    }

    // 合并文本搜索结果
    for (const result of textResults) {
      const existing = resultMap.get(result.memory.id);
      if (existing) {
        // 合并分数
        existing.similarity += result.similarity * textWeight;
      } else {
        resultMap.set(result.memory.id, {
          memory: result.memory,
          similarity: result.similarity * textWeight
        });
      }
    }

    return Array.from(resultMap.values());
  }

  /**
   * 去重搜索结果
   */
  private deduplicateResults(results: MemorySearchResult[]): MemorySearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      if (seen.has(result.memory.id)) {
        return false;
      }
      seen.add(result.memory.id);
      return true;
    });
  }

  /**
   * 获取相似记忆的聚类
   */
  public async getMemoryClusters(
    userId: string,
    options: {
      minClusterSize?: number;
      maxClusters?: number;
      similarityThreshold?: number;
    } = {}
  ): Promise<MemoryRecord[][]> {
    try {
      const {
        minClusterSize = 2,
        maxClusters = 10,
        similarityThreshold = 0.8
      } = options;

      const memories = await this.memoryService.getUserMemories(userId);
      const memoriesWithEmbeddings = memories.filter(memory => 
        this.embeddingService.validateEmbedding(memory.embedding)
      );

      if (memoriesWithEmbeddings.length < minClusterSize) {
        return [];
      }

      // 简单的聚类算法
      const clusters: MemoryRecord[][] = [];
      const used = new Set<string>();

      for (const memory of memoriesWithEmbeddings) {
        if (used.has(memory.id)) {
          continue;
        }

        const cluster = [memory];
        used.add(memory.id);

        // 查找相似的记忆
        for (const otherMemory of memoriesWithEmbeddings) {
          if (used.has(otherMemory.id)) {
            continue;
          }

          const similarity = this.embeddingService.calculateCosineSimilarity(
            memory.embedding,
            otherMemory.embedding
          );

          if (similarity >= similarityThreshold) {
            cluster.push(otherMemory);
            used.add(otherMemory.id);
          }
        }

        if (cluster.length >= minClusterSize) {
          clusters.push(cluster);
        }

        if (clusters.length >= maxClusters) {
          break;
        }
      }

      LoggerService.log('INFO', 'Memory clustering completed', {
        userId,
        totalMemories: memoriesWithEmbeddings.length,
        clustersFound: clusters.length
      });

      return clusters;
    } catch (error) {
      LoggerService.log('ERROR', 'Memory clustering failed', { error, userId });
      return [];
    }
  }
}
