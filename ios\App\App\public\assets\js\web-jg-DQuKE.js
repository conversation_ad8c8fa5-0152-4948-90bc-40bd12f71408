import{a6 as e}from"./index-Dnlt-eWK.js";import"./mui-vendor-DsBXMegs.js";import"./react-vendor-Be-rfjCm.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";class t extends e{available(){throw this.unimplemented("Method not implemented on web.")}start(e){throw this.unimplemented("Method not implemented on web.")}stop(){throw this.unimplemented("Method not implemented on web.")}getSupportedLanguages(){throw this.unimplemented("Method not implemented on web.")}hasPermission(){throw this.unimplemented("Method not implemented on web.")}isListening(){throw this.unimplemented("Method not implemented on web.")}requestPermission(){throw this.unimplemented("Method not implemented on web.")}checkPermissions(){throw this.unimplemented("Method not implemented on web.")}requestPermissions(){throw this.unimplemented("Method not implemented on web.")}}const n=new t;export{n as SpeechRecognition,t as SpeechRecognitionWeb};
