import{a as e,r as t,b as r,c as n}from"./react-vendor-C9ilihHH.js";import{b as o,c as i,a,d as s}from"./utils-vendor-BDm_82Hk.js";import{au as l,av as c}from"./index-BtK6VV6Z.js";import{a9 as d}from"./mui-vendor-hRDvsX89.js";var p=function(e){var t=e.top,r=e.right,n=e.bottom,o=e.left;return{top:t,right:r,bottom:n,left:o,width:r-o,height:n-t,x:o,y:t,center:{x:(r+o)/2,y:(n+t)/2}}},u=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},g=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},m={top:0,right:0,bottom:0,left:0},f=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?m:r,o=e.border,i=void 0===o?m:o,a=e.padding,s=void 0===a?m:a,l=p(u(t,n)),c=p(g(t,i)),d=p(g(c,s));return{marginBox:l,borderBox:p(t),paddingBox:c,contentBox:d,margin:n,border:i,padding:s}},b=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&function(){throw new Error("Invariant failed")}(),r},h=function(e,t){var r,n,o=e.borderBox,i=e.border,a=e.margin,s=e.padding,l=(n=t,{top:(r=o).top+n.y,left:r.left+n.x,bottom:r.bottom+n.y,right:r.right+n.x});return f({borderBox:l,border:i,margin:a,padding:s})},v=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),h(e,t)},y=function(e,t){var r={top:b(t.marginTop),right:b(t.marginRight),bottom:b(t.marginBottom),left:b(t.marginLeft)},n={top:b(t.paddingTop),right:b(t.paddingRight),bottom:b(t.paddingBottom),left:b(t.paddingLeft)},o={top:b(t.borderTopWidth),right:b(t.borderRightWidth),bottom:b(t.borderBottomWidth),left:b(t.borderLeftWidth)};return f({borderBox:e,margin:r,padding:n,border:o})},I=function(e){var t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return y(t,r)},x=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];t=o,r||(r=requestAnimationFrame((function(){r=null,e.apply(void 0,t)})))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n};function D(e,t){}function E(){}function A(e,t,r){const n=t.map((t=>{const n=(o=r,i=t.options,{...o,...i});var o,i;return e.addEventListener(t.eventName,t.fn,n),function(){e.removeEventListener(t.eventName,t.fn,n)}}));return function(){n.forEach((e=>{e()}))}}D.bind(null,"warn"),D.bind(null,"error");class w extends Error{}function C(e,t){throw new w("Invariant failed")}w.prototype.toString=function(){return this.message};class S extends e.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=E,this.onWindowError=e=>{const t=this.getCallbacks();t.isDragging()&&t.tryAbort();e.error instanceof w&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=A(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(!(e instanceof w))throw e;this.setState({})}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const B=e=>e+1,R=(e,t)=>{const r=e.droppableId===t.droppableId,n=B(e.index),o=B(t.index);return r?`\n      You have moved the item from position ${n}\n      to position ${o}\n    `:`\n    You have moved the item from position ${n}\n    in list ${e.droppableId}\n    to list ${t.droppableId}\n    in position ${o}\n  `},N=(e,t,r)=>t.droppableId===r.droppableId?`\n      The item ${e}\n      has been combined with ${r.draggableId}`:`\n      The item ${e}\n      in list ${t.droppableId}\n      has been combined with ${r.draggableId}\n      in list ${r.droppableId}\n    `,O=e=>`\n  The item has returned to its starting position\n  of ${B(e.index)}\n`,P={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:e=>`\n  You have lifted an item in position ${B(e.source.index)}\n`,onDragUpdate:e=>{const t=e.destination;if(t)return R(e.source,t);const r=e.combine;return r?N(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`\n      Movement cancelled.\n      ${O(e.source)}\n    `;const t=e.destination,r=e.combine;return t?`\n      You have dropped the item.\n      ${R(e.source,t)}\n    `:r?`\n      You have dropped the item.\n      ${N(e.draggableId,e.source,r)}\n    `:`\n    The item has been dropped while not over a drop area.\n    ${O(e.source)}\n  `}};function T(e,t){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(r=e[o],n=t[o],!(r===n||Number.isNaN(r)&&Number.isNaN(n)))return!1;var r,n;return!0}function L(e,r){const n=t.useState((()=>({inputs:r,result:e()})))[0],o=t.useRef(!0),i=t.useRef(n),a=o.current||Boolean(r&&i.current.inputs&&T(r,i.current.inputs))?i.current:{inputs:r,result:e()};return t.useEffect((()=>{o.current=!1,i.current=a}),[a]),a.result}function G(e,t){return L((()=>e),t)}const M={x:0,y:0},_=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),F=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),k=(e,t)=>e.x===t.x&&e.y===t.y,W=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),U=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},$=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),H=(e,t)=>Math.min(...t.map((t=>$(e,t)))),j=e=>t=>({x:e(t.x),y:e(t.y)});const V=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),q=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],z=(e,t)=>t&&t.shouldClipSubject?((e,t)=>{const r=p({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r})(t.pageMarginBox,e):p(e);var Y=({page:e,withPlaceholder:t,axis:r,frame:n})=>{const o=((e,t)=>t?V(e,t.scroll.diff.displacement):e)(e.marginBox,n),i=((e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e)(o,r,t);return{page:e,withPlaceholder:t,active:z(i,n)}},J=(e,t)=>{e.frame||C();const r=e.frame,n=F(t,r.scroll.initial),o=W(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:o},max:r.scroll.max}},a=Y({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}};function X(e,t=T){let r=null;function n(...n){if(r&&r.lastThis===this&&t(n,r.lastArgs))return r.lastResult;const o=e.apply(this,n);return r={lastResult:o,lastArgs:n,lastThis:this},o}return n.clear=function(){r=null},n}const K=X((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),Q=X((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),Z=X((e=>Object.values(e))),ee=X((e=>Object.values(e)));var te=X(((e,t)=>ee(t).filter((t=>e===t.descriptor.droppableId)).sort(((e,t)=>e.descriptor.index-t.descriptor.index))));function re(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function ne(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var oe=X(((e,t)=>t.filter((t=>t.descriptor.id!==e.descriptor.id)))),ie=(e,t)=>e.descriptor.droppableId===t.descriptor.id;const ae={point:M,value:0},se={invisible:{},visible:{},all:[]},le={displaced:se,displacedBy:ae,at:null};var ce=(e,t)=>r=>e<=r&&r<=t,de=e=>{const t=ce(e.top,e.bottom),r=ce(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;const o=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(o&&i)return!0;const a=n.top<e.top&&n.bottom>e.bottom,s=n.left<e.left&&n.right>e.right;if(a&&s)return!0;return a&&i||s&&o}},pe=e=>{const t=ce(e.top,e.bottom),r=ce(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};const ue={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},ge={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};const me=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:o})=>{const i=n?((e,t)=>{const r=t.frame?t.frame.scroll.diff.displacement:M;return V(e,r)})(e,t):e;return((e,t,r)=>!!t.subject.active&&r(t.subject.active)(e))(i,t,o)&&((e,t,r)=>r(t)(e))(i,r,o)},fe=e=>me({...e,isVisibleThroughFrameFn:pe});function be({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:o,last:i}){return e.reduce((function(e,a){const s=function(e,t){const r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return p(u(r,n))}(a,r),l=a.descriptor.id;e.all.push(l);var c;if(!(c={target:s,destination:t,viewport:n,withDroppableDisplacement:!0},me({...c,isVisibleThroughFrameFn:de})))return e.invisible[a.descriptor.id]=!0,e;const d=((e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;const{invisible:n,visible:o}=t;if(n[e])return!1;const i=o[e];return!i||i.shouldAnimate})(l,i,o),g={draggableId:l,shouldAnimate:d};return e.visible[l]=g,e}),{all:[],visible:{},invisible:{}})}function he({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){const o=function(e,t){if(!e.length)return 0;const r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:se,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:o}}}}function ve({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:o,last:i,index:a,forceShouldAnimate:s}){const l=ie(e,r);if(null==a)return he({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const c=t.find((e=>e.descriptor.index===a));if(!c)return he({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const d=oe(e,t),p=t.indexOf(c);return{displaced:be({afterDragging:d.slice(p),destination:r,displacedBy:o,last:i,viewport:n.frame,forceShouldAnimate:s}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function ye(e,t){return Boolean(t.effected[e])}var Ie=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:o,insideDestination:i,previousImpact:a,viewport:s,afterCritical:l})=>{const c=a.at;if(c||C(),"REORDER"===c.type){const n=(({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;const o=n.index,i=e?o+1:o-1,a=r[0].descriptor.index,s=r[r.length-1].descriptor.index;return i<a||i>(t?s:s+1)?null:i})({isMovingForward:e,isInHomeList:t,location:c.destination,insideDestination:i});return null==n?null:ve({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:n})}const d=(({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:o})=>{if(!t.isCombineEnabled)return null;const i=n.draggableId,a=r[i].descriptor.index;return ye(i,o)?e?a:a-1:e?a+1:a})({isMovingForward:e,destination:o,displaced:a.displaced,draggables:n,combine:c.combine,afterCritical:l});return null==d?null:ve({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:d})},xe=({afterCritical:e,impact:t,draggables:r})=>{const n=ne(t);n||C();const o=n.draggableId,i=r[o].page.borderBox.center,a=(({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{const o=Boolean(e.visible[r]||e.invisible[r]);return ye(r,t)?o?M:W(n.point):o?n.point:M})({displaced:t.displaced,afterCritical:e,combineWith:o,displacedBy:t.displacedBy});return _(i,a)};const De=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,Ee=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,Ae=({axis:e,moveRelativeTo:t,isMoving:r})=>U(e.line,t.marginBox[e.end]+De(e,r),Ee(e,t.marginBox,r)),we=({axis:e,moveRelativeTo:t,isMoving:r})=>U(e.line,t.marginBox[e.start]-((e,t)=>t.margin[e.end]+t.borderBox[e.size]/2)(e,r),Ee(e,t.marginBox,r));var Ce=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o})=>{const i=te(n.descriptor.id,r),a=t.page,s=n.axis;if(!i.length)return(({axis:e,moveInto:t,isMoving:r})=>U(e.line,t.contentBox[e.start]+De(e,r),Ee(e,t.contentBox,r)))({axis:s,moveInto:n.page,isMoving:a});const{displaced:l,displacedBy:c}=e,d=l.all[0];if(d){const e=r[d];if(ye(d,o))return we({axis:s,moveRelativeTo:e.page,isMoving:a});const t=h(e.page,c.point);return we({axis:s,moveRelativeTo:t,isMoving:a})}const p=i[i.length-1];if(p.descriptor.id===t.descriptor.id)return a.borderBox.center;if(ye(p.descriptor.id,o)){const e=h(p.page,W(o.displacedBy.point));return Ae({axis:s,moveRelativeTo:e,isMoving:a})}return Ae({axis:s,moveRelativeTo:p.page,isMoving:a})},Se=(e,t)=>{const r=e.frame;return r?_(t,r.scroll.diff.displacement):t};var Be=e=>{const t=(({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:o})=>{const i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?Ce({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:o}):xe({impact:e,draggables:n,afterCritical:o}):i})(e),r=e.droppable;return r?Se(r,t):t},Re=(e,t)=>{const r=F(t,e.scroll.initial),n=W(r);return{frame:p({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function Ne(e,t){return e.map((e=>t[e]))}var Oe=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{const n=((e,t)=>_(e.scroll.diff.displacement,t))(r,e),o=F(n,t.page.borderBox.center);return _(t.client.borderBox.center,o)},Pe=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:o,onlyOnMainAxis:i=!1})=>{const a=F(r,e.page.borderBox.center),s={target:V(e.page.borderBox,a),destination:t,withDroppableDisplacement:o,viewport:n};return i?(e=>{return me({...e,isVisibleThroughFrameFn:(t=e.destination.axis,e=>{const r=ce(e.top,e.bottom),n=ce(e.left,e.right);return e=>t===ue?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)})});var t})(s):fe(s)},Te=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:o,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:s,afterCritical:l})=>{if(!r.isEnabled)return null;const c=te(r.descriptor.id,n),d=ie(t,r),p=(({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:o})=>{if(!r.isCombineEnabled)return null;if(!re(o))return null;function i(e){const t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...o,at:t}}const a=o.displaced.all,s=a.length?a[0]:null;if(e)return s?i(s):null;const l=oe(t,n);if(!s)return l.length?i(l[l.length-1].descriptor.id):null;const c=l.findIndex((e=>e.descriptor.id===s));-1===c&&C();const d=c-1;return d<0?null:i(l[d].descriptor.id)})({isMovingForward:e,draggable:t,destination:r,insideDestination:c,previousImpact:o})||Ie({isMovingForward:e,isInHomeList:d,draggable:t,draggables:n,destination:r,insideDestination:c,previousImpact:o,viewport:i,afterCritical:l});if(!p)return null;const u=Be({impact:p,draggable:t,droppable:r,draggables:n,afterCritical:l});if(Pe({draggable:t,destination:r,newPageBorderBoxCenter:u,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})){return{clientSelection:Oe({pageBorderBoxCenter:u,draggable:t,viewport:i}),impact:p,scrollJumpRequest:null}}const g=F(u,a),m=(({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:o})=>{const i=Re(t,_(t.scroll.current,o)),a=r.frame?J(r,_(r.frame.scroll.current,o)):r,s=e.displaced,l=be({afterDragging:Ne(s.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:s,forceShouldAnimate:!1}),c=be({afterDragging:Ne(s.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:s,forceShouldAnimate:!1}),d={},p={},u=[s,l,c];return s.all.forEach((e=>{const t=function(e,t){for(let r=0;r<t.length;r++){const n=t[r].visible[e];if(n)return n}return null}(e,u);t?p[e]=t:d[e]=!0})),{...e,displaced:{all:s.all,invisible:d,visible:p}}})({impact:p,viewport:i,destination:r,draggables:n,maxScrollChange:g});return{clientSelection:s,impact:m,scrollJumpRequest:g}};const Le=e=>{const t=e.subject.active;return t||C(),t};const Ge=(e,t)=>{const r=e.page.borderBox.center;return ye(e.descriptor.id,t)?F(r,t.displacedBy.point):r},Me=(e,t)=>{const r=e.page.borderBox;return ye(e.descriptor.id,t)?V(r,W(t.displacedBy.point)):r};var _e=X((function(e,t){const r=t[e.line];return{value:r,point:U(e.line,r)}}));const Fe=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),ke=(e,t,r)=>{const n=e.frame;ie(t,e)&&C(),e.subject.withPlaceholder&&C();const o=_e(e.axis,t.displaceBy).point,i=((e,t,r)=>{const n=e.axis;if("virtual"===e.descriptor.mode)return U(n.line,t[n.line]);const o=e.subject.page.contentBox[n.size],i=te(e.descriptor.id,r).reduce(((e,t)=>e+t.client.marginBox[n.size]),0)+t[n.line]-o;return i<=0?null:U(n.line,i)})(e,o,r),a={placeholderSize:o,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){const t=Y({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}const s=i?_(n.scroll.max,i):n.scroll.max,l=Fe(n,s),c=Y({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:l});return{...e,subject:c,frame:l}};var We=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:o,droppables:i,viewport:a,afterCritical:s})=>{const l=(({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:o})=>{const i=r.subject.active;if(!i)return null;const a=r.axis,s=ce(i[a.start],i[a.end]),l=Z(n).filter((e=>e!==r)).filter((e=>e.isEnabled)).filter((e=>Boolean(e.subject.active))).filter((e=>de(o.frame)(Le(e)))).filter((t=>{const r=Le(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]})).filter((e=>{const t=Le(e),r=ce(t[a.start],t[a.end]);return s(t[a.start])||s(t[a.end])||r(i[a.start])||r(i[a.end])})).sort(((t,r)=>{const n=Le(t)[a.crossAxisStart],o=Le(r)[a.crossAxisStart];return e?n-o:o-n})).filter(((e,t,r)=>Le(e)[a.crossAxisStart]===Le(r[0])[a.crossAxisStart]));if(!l.length)return null;if(1===l.length)return l[0];const c=l.filter((e=>ce(Le(e)[a.start],Le(e)[a.end])(t[a.line])));return 1===c.length?c[0]:c.length>1?c.sort(((e,t)=>Le(e)[a.start]-Le(t)[a.start]))[0]:l.sort(((e,r)=>{const n=H(t,q(Le(e))),o=H(t,q(Le(r)));return n!==o?n-o:Le(e)[a.start]-Le(r)[a.start]}))[0]})({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!l)return null;const c=te(l.descriptor.id,o),d=(({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:o})=>n.filter((e=>fe({target:Me(e,o),destination:r,viewport:t.frame,withDroppableDisplacement:!0}))).sort(((t,n)=>{const i=$(e,Se(r,Ge(t,o))),a=$(e,Se(r,Ge(n,o)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index}))[0]||null)({pageBorderBoxCenter:t,viewport:a,destination:l,insideDestination:c,afterCritical:s}),p=(({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:o,destination:i,viewport:a,afterCritical:s})=>{if(!t){if(r.length)return null;const e={displaced:se,displacedBy:ae,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=Be({impact:e,draggable:n,droppable:i,draggables:o,afterCritical:s}),l=ie(n,i)?i:ke(i,n,o);return Pe({draggable:n,destination:l,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}const l=Boolean(e[i.axis.line]<=t.page.borderBox.center[i.axis.line]),c=(()=>{const e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||l?e:e+1})(),d=_e(i.axis,n.displaceBy);return ve({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:d,last:se,index:c})})({previousPageBorderBoxCenter:t,destination:l,draggable:r,draggables:o,moveRelativeTo:d,insideDestination:c,viewport:a,afterCritical:s});if(!p)return null;const u=Be({impact:p,draggable:r,droppable:l,draggables:o,afterCritical:s});return{clientSelection:Oe({pageBorderBoxCenter:u,draggable:r,viewport:a}),impact:p,scrollJumpRequest:null}},Ue=e=>{const t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};var $e=({state:e,type:t})=>{const r=((e,t)=>{const r=Ue(e);return r?t[r]:null})(e.impact,e.dimensions.droppables),n=Boolean(r),o=e.dimensions.droppables[e.critical.droppable.id],i=r||o,a=i.axis.direction,s="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(s&&!n)return null;const l="MOVE_DOWN"===t||"MOVE_RIGHT"===t,c=e.dimensions.draggables[e.critical.draggable.id],d=e.current.page.borderBoxCenter,{draggables:p,droppables:u}=e.dimensions;return s?Te({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,destination:i,draggables:p,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):We({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,isOver:i,draggables:p,droppables:u,viewport:e.viewport,afterCritical:e.afterCritical})};function He(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function je(e){const t=ce(e.top,e.bottom),r=ce(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}function Ve({pageBorderBox:e,draggable:t,droppables:r}){const n=Z(r).filter((t=>{if(!t.isEnabled)return!1;const r=t.subject.active;if(!r)return!1;if(o=r,!((n=e).left<o.right&&n.right>o.left&&n.top<o.bottom&&n.bottom>o.top))return!1;var n,o;if(je(r)(e.center))return!0;const i=t.axis,a=r.center[i.crossAxisLine],s=e[i.crossAxisStart],l=e[i.crossAxisEnd],c=ce(r[i.crossAxisStart],r[i.crossAxisEnd]),d=c(s),p=c(l);return!d&&!p||(d?s<a:l>a)}));return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){const n=t.page.borderBox.center,o=r.map((t=>{const r=t.axis,o=U(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:$(n,o)}})).sort(((e,t)=>t.distance-e.distance));return o[0]?o[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}const qe=(e,t)=>p(V(e,t));function ze({displaced:e,id:t}){return Boolean(e.visible[t]||e.invisible[t])}var Ye=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:o,viewport:i,afterCritical:a})=>{const s=qe(t.page.borderBox,e),l=Ve({pageBorderBox:s,draggable:t,droppables:n});if(!l)return le;const c=n[l],d=te(c.descriptor.id,r),p=((e,t)=>{const r=e.frame;return r?qe(t,r.scroll.diff.value):t})(c,s);return(({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:o,afterCritical:i})=>{if(!n.isCombineEnabled)return null;const a=n.axis,s=_e(n.axis,e.displaceBy),l=s.value,c=t[a.start],d=t[a.end],p=oe(e,o).find((e=>{const t=e.descriptor.id,n=e.page.borderBox,o=n[a.size]/4,s=ye(t,i),p=ze({displaced:r.displaced,id:t});return s?p?d>n[a.start]+o&&d<n[a.end]-o:c>n[a.start]-l+o&&c<n[a.end]-l-o:p?d>n[a.start]+l+o&&d<n[a.end]+l-o:c>n[a.start]+o&&c<n[a.end]-o}));return p?{displacedBy:s,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:p.descriptor.id,droppableId:n.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:p,draggable:t,previousImpact:o,destination:c,insideDestination:d,afterCritical:a})||(({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:o,viewport:i,afterCritical:a})=>{const s=r.axis,l=_e(r.axis,t.displaceBy),c=l.value,d=e[s.start],p=e[s.end],u=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:oe(t,n).find((e=>{const t=e.descriptor.id,r=e.page.borderBox.center[s.line],n=ye(t,a),i=ze({displaced:o,id:t});return n?i?p<=r:d<r-c:i?p<=r+c:d<r}))||null,inHomeList:ie(t,r)});return ve({draggable:t,insideDestination:n,destination:r,viewport:i,last:o,displacedBy:l,index:u})})({pageBorderBoxWithDroppableScroll:p,draggable:t,destination:c,insideDestination:d,last:o.displaced,viewport:i,afterCritical:a})},Je=(e,t)=>({...e,[t.descriptor.id]:t});const Xe=({previousImpact:e,impact:t,droppables:r})=>{const n=Ue(e),o=Ue(t);if(!n)return r;if(n===o)return r;const i=r[n];if(!i.subject.withPlaceholder)return r;const a=(e=>{const t=e.subject.withPlaceholder;t||C();const r=e.frame;if(!r){const t=Y({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}const n=t.oldFrameMaxScroll;n||C();const o=Fe(r,n),i=Y({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null});return{...e,subject:i,frame:o}})(i);return Je(r,a)};var Ke=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:o,scrollJumpRequest:i})=>{const a=n||e.viewport,s=r||e.dimensions,l=t||e.current.client.selection,c=F(l,e.initial.client.selection),d={offset:c,selection:l,borderBoxCenter:_(e.initial.client.borderBoxCenter,c)},p={selection:_(d.selection,a.scroll.current),borderBoxCenter:_(d.borderBoxCenter,a.scroll.current),offset:_(d.offset,a.scroll.diff.value)},u={client:d,page:p};if("COLLECTING"===e.phase)return{...e,dimensions:s,viewport:a,current:u};const g=s.draggables[e.critical.draggable.id],m=o||Ye({pageOffset:p.offset,draggable:g,draggables:s.draggables,droppables:s.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),f=(({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:o})=>{const i=Xe({previousImpact:n,impact:o,droppables:r}),a=Ue(o);if(!a)return i;const s=r[a];if(ie(e,s))return i;if(s.subject.withPlaceholder)return i;const l=ke(s,e,t);return Je(i,l)})({draggable:g,impact:m,previousImpact:e.impact,draggables:s.draggables,droppables:s.droppables});return{...e,current:u,dimensions:{draggables:s.draggables,droppables:f},impact:m,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}};var Qe=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:o})=>{const i=e.displaced,a=function(e,t){return e.map((e=>t[e]))}(i.all,r),s=be({afterDragging:a,destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:o,last:i});return{...e,displaced:s}},Ze=({impact:e,draggable:t,droppable:r,draggables:n,viewport:o,afterCritical:i})=>{const a=Be({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i});return Oe({pageBorderBoxCenter:a,draggable:t,viewport:o})},et=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&C();const n=e.impact,o=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:s}=i,l=a[e.critical.draggable.id],c=Ue(n);c||C();const d=s[c],p=Qe({impact:n,viewport:o,destination:d,draggables:a}),u=Ze({impact:p,draggable:l,droppable:d,draggables:a,viewport:o,afterCritical:e.afterCritical});return Ke({impact:p,clientSelection:u,state:e,dimensions:i,viewport:o})},tt=({draggable:e,home:t,draggables:r,viewport:n})=>{const o=_e(t.axis,e.displaceBy),i=te(t.descriptor.id,r),a=i.indexOf(e);-1===a&&C();const s=i.slice(a+1),l=s.reduce(((e,t)=>(e[t.descriptor.id]=!0,e)),{}),c={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:o,effected:l};var d;return{impact:{displaced:be({afterDragging:s,destination:t,displacedBy:o,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:(d=e.descriptor,{index:d.index,droppableId:d.droppableId})}},afterCritical:c}},rt=({additions:e,updatedDroppables:t,viewport:r})=>{const n=r.scroll.diff.value;return e.map((e=>{const o=e.descriptor.droppableId,i=(e=>{const t=e.frame;return t||C(),t})(t[o]).scroll.diff.value,a=(({draggable:e,offset:t,initialWindowScroll:r})=>{const n=h(e.client,t),o=v(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:o}})({draggable:e,offset:_(n,i),initialWindowScroll:r.scroll.initial});return a}))};const nt=e=>"SNAP"===e.movementMode,ot=(e,t,r)=>{const n=((e,t)=>({draggables:e.draggables,droppables:Je(e.droppables,t)}))(e.dimensions,t);return!nt(e)||r?Ke({state:e,dimensions:n}):et({state:e,dimensions:n})};function it(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}const at={phase:"IDLE",completed:null,shouldFlush:!1};var st=(e=at,t)=>{if("FLUSH"===t.type)return{...at,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&C();const{critical:r,clientSelection:n,viewport:o,dimensions:i,movementMode:a}=t.payload,s=i.draggables[r.draggable.id],l=i.droppables[r.droppable.id],c={selection:n,borderBoxCenter:s.client.borderBox.center,offset:M},d={client:c,page:{selection:_(c.selection,o.scroll.initial),borderBoxCenter:_(c.selection,o.scroll.initial),offset:_(c.selection,o.scroll.diff.value)}},p=Z(i.droppables).every((e=>!e.isFixedOnPage)),{impact:u,afterCritical:g}=tt({draggable:s,home:l,draggables:i.draggables,viewport:o});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:d,current:d,isWindowScrollAllowed:p,impact:u,afterCritical:g,onLiftImpact:u,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&C();return{...e,phase:"COLLECTING"}}if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&C(),(({state:e,published:t})=>{const r=t.modified.map((t=>{const r=e.dimensions.droppables[t.droppableId];return J(r,t.scroll)})),n={...e.dimensions.droppables,...K(r)},o=Q(rt({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...o};t.removals.forEach((e=>{delete i[e]}));const a={droppables:n,draggables:i},s=Ue(e.impact),l=s?a.droppables[s]:null,c=a.draggables[e.critical.draggable.id],d=a.droppables[e.critical.droppable.id],{impact:p,afterCritical:u}=tt({draggable:c,home:d,draggables:i,viewport:e.viewport}),g=l&&l.isCombineEnabled?e.impact:p,m=Ye({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:g,viewport:e.viewport,afterCritical:u}),f={...e,phase:"DRAGGING",impact:m,onLiftImpact:p,dimensions:a,afterCritical:u,forceShouldAnimate:!1};return"COLLECTING"===e.phase?f:{...f,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}})({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;He(e)||C();const{client:r}=t.payload;return k(r,e.current.client.selection)?e:Ke({state:e,clientSelection:r,impact:nt(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return it(e);if("COLLECTING"===e.phase)return it(e);He(e)||C();const{id:r,newScroll:n}=t.payload,o=e.dimensions.droppables[r];if(!o)return e;const i=J(o,n);return ot(e,i,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;He(e)||C();const{id:r,isEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||C(),o.isEnabled===n&&C();const i={...o,isEnabled:n};return ot(e,i,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;He(e)||C();const{id:r,isCombineEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||C(),o.isCombineEnabled===n&&C();const i={...o,isCombineEnabled:n};return ot(e,i,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;He(e)||C(),e.isWindowScrollAllowed||C();const r=t.payload.newScroll;if(k(e.viewport.scroll.current,r))return it(e);const n=Re(e.viewport,r);return nt(e)?et({state:e,viewport:n}):Ke({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!He(e))return e;const r=t.payload.maxScroll;if(k(r,e.viewport.scroll.max))return e;const n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&C();const r=$e({state:e,type:t.type});return r?Ke({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){const r=t.payload.reason;"COLLECTING"!==e.phase&&C();return{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){const{completed:r,dropDuration:n,newHomeClientOffset:o}=t.payload;"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&C();return{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:o,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){const{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function lt(e,t){return e instanceof Object&&"type"in e&&e.type===t}const ct=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),dt=()=>({type:"COLLECTION_STARTING",payload:null}),pt=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),ut=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),gt=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),mt=e=>({type:"MOVE",payload:e}),ft=()=>({type:"MOVE_UP",payload:null}),bt=()=>({type:"MOVE_DOWN",payload:null}),ht=()=>({type:"MOVE_RIGHT",payload:null}),vt=()=>({type:"MOVE_LEFT",payload:null}),yt=e=>({type:"DROP_COMPLETE",payload:e}),It=e=>({type:"DROP",payload:e}),xt=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});const Dt="cubic-bezier(.2,1,.1,1)",Et={drop:0,combining:.7},At={drop:.75},wt={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Ct=`${wt.outOfTheWay}s ${"cubic-bezier(0.2, 0, 0, 1)"}`,St={fluid:`opacity ${Ct}`,snap:`transform ${Ct}, opacity ${Ct}`,drop:e=>{const t=`${e}s ${Dt}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${Ct}`,placeholder:`height ${Ct}, width ${Ct}, margin ${Ct}`},Bt=e=>k(e,M)?void 0:`translate(${e.x}px, ${e.y}px)`,Rt=Bt,Nt=(e,t)=>{const r=Bt(e);if(r)return t?`${r} scale(${At.drop})`:r},{minDropTime:Ot,maxDropTime:Pt}=wt,Tt=Pt-Ot;const Lt=({getState:e,dispatch:t})=>r=>n=>{if(!lt(n,"DROP"))return void r(n);const o=e(),i=n.payload.reason;if("COLLECTING"===o.phase)return void t((e=>({type:"DROP_PENDING",payload:e}))({reason:i}));if("IDLE"===o.phase)return;"DROP_PENDING"===o.phase&&o.isWaiting&&C(),"DRAGGING"!==o.phase&&"DROP_PENDING"!==o.phase&&C();const a=o.critical,s=o.dimensions,l=s.draggables[o.critical.draggable.id],{impact:c,didDropInsideDroppable:d}=(({draggables:e,reason:t,lastImpact:r,home:n,viewport:o,onLiftImpact:i})=>{if(!r.at||"DROP"!==t)return{impact:Qe({draggables:e,impact:i,destination:n,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1};return"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:se},didDropInsideDroppable:!0}})({reason:i,lastImpact:o.impact,afterCritical:o.afterCritical,onLiftImpact:o.onLiftImpact,home:o.dimensions.droppables[o.critical.droppable.id],viewport:o.viewport,draggables:o.dimensions.draggables}),p=d?re(c):null,u=d?ne(c):null,g={index:a.draggable.index,droppableId:a.droppable.id},m={draggableId:l.descriptor.id,type:l.descriptor.type,source:g,reason:i,mode:o.movementMode,destination:p,combine:u},f=(({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:o})=>{const{draggables:i,droppables:a}=r,s=Ue(e),l=s?a[s]:null,c=a[t.descriptor.droppableId],d=Ze({impact:e,draggable:t,draggables:i,afterCritical:o,droppable:l||c,viewport:n});return F(d,t.client.borderBox.center)})({impact:c,draggable:l,dimensions:s,viewport:o.viewport,afterCritical:o.afterCritical}),b={critical:o.critical,afterCritical:o.afterCritical,result:m,impact:c};if(!(!k(o.current.client.offset,f)||Boolean(m.combine)))return void t(yt({completed:b}));const h=(({current:e,destination:t,reason:r})=>{const n=$(e,t);if(n<=0)return Ot;if(n>=1500)return Pt;const o=Ot+Tt*(n/1500);return Number(("CANCEL"===r?.6*o:o).toFixed(2))})({current:o.current.client.offset,destination:f,reason:i});t((e=>({type:"DROP_ANIMATE",payload:e}))({newHomeClientOffset:f,dropDuration:h,completed:b}))};var Gt=()=>({x:window.pageXOffset,y:window.pageYOffset});function Mt({onWindowScroll:e}){const t=x((function(){e(Gt())})),r=(n=t,{eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{e.target!==window&&e.target!==window.document||n()}});var n;let o=E;function i(){return o!==E}return{start:function(){i()&&C(),o=A(window,[r])},stop:function(){i()||C(),t.cancel(),o(),o=E},isActive:i}}const _t=e=>{const t=Mt({onWindowScroll:t=>{e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return e=>r=>{!t.isActive()&&lt(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&(e=>lt(e,"DROP_COMPLETE")||lt(e,"DROP_ANIMATE")||lt(e,"FLUSH"))(r)&&t.stop(),e(r)}};var Ft=()=>{const e=[];return{add:t=>{const r=setTimeout((()=>(t=>{const r=e.findIndex((e=>e.timerId===t));-1===r&&C();const[n]=e.splice(r,1);n.callback()})(r))),n={timerId:r,callback:t};e.push(n)},flush:()=>{if(!e.length)return;const t=[...e];e.length=0,t.forEach((e=>{clearTimeout(e.timerId),e.callback()}))}}};const kt=(e,t)=>{t()},Wt=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function Ut(e,t,r,n){if(!e)return void r(n(t));const o=(e=>{let t=!1,r=!1;const n=setTimeout((()=>{r=!0})),o=o=>{t||r||(t=!0,e(o),clearTimeout(n))};return o.wasCalled=()=>t,o})(r);e(t,{announce:o}),o.wasCalled()||r(n(t))}var $t=(e,t)=>{const r=((e,t)=>{const r=Ft();let n=null;const o=r=>{n||C(),n=null,kt(0,(()=>Ut(e().onDragEnd,r,t,P.onDragEnd)))};return{beforeCapture:(t,r)=>{n&&C(),kt(0,(()=>{const n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})}))},beforeStart:(t,r)=>{n&&C(),kt(0,(()=>{const n=e().onBeforeDragStart;n&&n(Wt(t,r))}))},start:(o,i)=>{n&&C();const a=Wt(o,i);n={mode:i,lastCritical:o,lastLocation:a.source,lastCombine:null},r.add((()=>{kt(0,(()=>Ut(e().onDragStart,a,t,P.onDragStart)))}))},update:(o,i)=>{const a=re(i),s=ne(i);n||C();const l=!((e,t)=>{if(e===t)return!0;const r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n})(o,n.lastCritical);l&&(n.lastCritical=o);const c=(p=a,!(null==(d=n.lastLocation)&&null==p||null!=d&&null!=p&&d.droppableId===p.droppableId&&d.index===p.index));var d,p;c&&(n.lastLocation=a);const u=!((e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId)(n.lastCombine,s);if(u&&(n.lastCombine=s),!l&&!c&&!u)return;const g={...Wt(o,n.mode),combine:s,destination:a};r.add((()=>{kt(0,(()=>Ut(e().onDragUpdate,g,t,P.onDragUpdate)))}))},flush:()=>{n||C(),r.flush()},drop:o,abort:()=>{if(!n)return;const e={...Wt(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"};o(e)}}})(e,t);return e=>t=>n=>{if(lt(n,"BEFORE_INITIAL_CAPTURE"))return void r.beforeCapture(n.payload.draggableId,n.payload.movementMode);if(lt(n,"INITIAL_PUBLISH")){const e=n.payload.critical;return r.beforeStart(e,n.payload.movementMode),t(n),void r.start(e,n.payload.movementMode)}if(lt(n,"DROP_COMPLETE")){const e=n.payload.completed.result;return r.flush(),t(n),void r.drop(e)}if(t(n),lt(n,"FLUSH"))return void r.abort();const o=e.getState();"DRAGGING"===o.phase&&r.update(o.critical,o.impact)}};const Ht=e=>t=>r=>{if(!lt(r,"DROP_ANIMATION_FINISHED"))return void t(r);const n=e.getState();"DROP_ANIMATING"!==n.phase&&C(),e.dispatch(yt({completed:n.completed}))},jt=e=>{let t=null,r=null;return n=>o=>{if((lt(o,"FLUSH")||lt(o,"DROP_COMPLETE")||lt(o,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(o),!lt(o,"DROP_ANIMATE"))return;const i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};r=requestAnimationFrame((()=>{r=null,t=A(window,[i])}))}};var Vt=e=>t=>r=>n=>{if((e=>lt(e,"DROP_COMPLETE")||lt(e,"DROP_ANIMATE")||lt(e,"FLUSH"))(n))return e.stop(),void r(n);if(lt(n,"INITIAL_PUBLISH")){r(n);const o=t.getState();return"DRAGGING"!==o.phase&&C(),void e.start(o)}r(n),e.scroll(t.getState())};const qt=e=>t=>r=>{if(t(r),!lt(r,"PUBLISH_WHILE_DRAGGING"))return;const n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(It({reason:n.reason})))},zt=a;var Yt=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:o,autoScroller:a})=>{return i(st,zt(s((l=r,()=>e=>t=>{lt(t,"INITIAL_PUBLISH")&&l.dragging(),lt(t,"DROP_ANIMATE")&&l.dropping(t.payload.completed.result.reason),(lt(t,"FLUSH")||lt(t,"DROP_COMPLETE"))&&l.resting(),e(t)}),(e=>()=>t=>r=>{(lt(r,"DROP_COMPLETE")||lt(r,"FLUSH")||lt(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)})(e),(e=>({getState:t,dispatch:r})=>n=>o=>{if(!lt(o,"LIFT"))return void n(o);const{id:i,clientSelection:a,movementMode:s}=o.payload,l=t();"DROP_ANIMATING"===l.phase&&r(yt({completed:l.completed})),"IDLE"!==t().phase&&C(),r({type:"FLUSH",payload:null}),r({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:i,movementMode:s}});const c={draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===s}},{critical:d,dimensions:p,viewport:u}=e.startPublishing(c);r({type:"INITIAL_PUBLISH",payload:{critical:d,dimensions:p,clientSelection:a,movementMode:s,viewport:u}})})(e),Lt,Ht,jt,qt,Vt(a),_t,(e=>{let t=!1;return()=>r=>n=>{if(lt(n,"INITIAL_PUBLISH"))return t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),void e.tryRestoreFocusRecorded();if(r(n),t){if(lt(n,"FLUSH"))return t=!1,void e.tryRestoreFocusRecorded();if(lt(n,"DROP_COMPLETE")){t=!1;const r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}})(t),$t(n,o))));var l};var Jt=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{const o=F({x:t,y:e},{x:n,y:r});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},Xt=()=>{const e=document.documentElement;return e||C(),e},Kt=()=>{const e=Xt();return Jt({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},Qt=({critical:e,scrollOptions:t,registry:r})=>{const n=(()=>{const e=Gt(),t=Kt(),r=e.y,n=e.x,o=Xt(),i=o.clientWidth,a=o.clientHeight;return{frame:p({top:r,left:n,right:n+i,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:M,displacement:M}}}})(),o=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map((e=>e.callbacks.getDimensionAndWatchScroll(o,t))),s=r.draggable.getAllByType(e.draggable.type).map((e=>e.getDimension(o)));return{dimensions:{draggables:Q(s),droppables:K(a)},critical:e,viewport:n}};function Zt(e,t,r){if(r.descriptor.id===t.id)return!1;if(r.descriptor.type!==t.type)return!1;return"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var er=(e,t)=>{let r=null;const n=function({registry:e,callbacks:t}){let r={additions:{},removals:{},modified:{}},n=null;const o=()=>{n||(t.collectionStarting(),n=requestAnimationFrame((()=>{n=null;const{additions:o,removals:i,modified:a}=r,s=Object.keys(o).map((t=>e.draggable.getById(t).getDimension(M))).sort(((e,t)=>e.descriptor.index-t.descriptor.index)),l=Object.keys(a).map((t=>({droppableId:t,scroll:e.droppable.getById(t).callbacks.getScrollWhileDragging()}))),c={additions:s,removals:Object.keys(i),modified:l};r={additions:{},removals:{},modified:{}},t.publish(c)})))};return{add:e=>{const t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],o()},remove:e=>{const t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],o()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=t=>{r||C();const o=r.critical.draggable;"ADDITION"===t.type&&Zt(e,o,t.value)&&n.add(t.value),"REMOVAL"===t.type&&Zt(e,o,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:(n,o)=>{e.droppable.exists(n)||C(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:o})},updateDroppableIsCombineEnabled:(n,o)=>{r&&(e.droppable.exists(n)||C(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:o}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,o)=>{r&&(e.droppable.exists(n)||C(),t.updateDroppableScroll({id:n,newScroll:o}))},startPublishing:t=>{r&&C();const n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor},s=e.subscribe(o);return r={critical:a,unsubscribe:s},Qt({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();const t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach((e=>e.callbacks.dragStopped())),r.unsubscribe(),r=null}}},tr=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&(e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason),rr=e=>{window.scrollBy(e.x,e.y)};const nr=X((e=>Z(e).filter((e=>!!e.isEnabled&&!!e.frame))));var or=({center:e,destination:t,droppables:r})=>{if(t){const e=r[t];return e.frame?e:null}const n=((e,t)=>nr(t).find((t=>(t.frame||C(),je(t.frame.pageMarginBox)(e))))||null)(e,r);return n};const ir={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var ar=({startOfRange:e,endOfRange:t,current:r})=>{const n=t-e;if(0===n)return 0;return(r-e)/n},sr=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:o})=>{const i=((e,t,r=()=>ir)=>{const n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;const o=1-ar({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),i=n.maxPixelScroll*n.ease(o);return Math.ceil(i)})(e,t,o);return 0===i?0:n?Math.max(((e,t,r)=>{const n=r(),o=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=t,s=i,l=Date.now()-a;if(l>=i)return e;if(l<o)return 1;const c=ar({startOfRange:o,endOfRange:s,current:l}),d=e*n.ease(c);return Math.ceil(d)})(i,r,o),1):i},lr=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=((e,t,r=()=>ir)=>{const n=r();return{startScrollingFrom:e[t.size]*n.startFromPercentage,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}})(e,n,i);return t[n.end]<t[n.start]?sr({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i}):-1*sr({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})};const cr=j((e=>0===e?0:e));var dr=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},s=lr({container:t,distanceToEdges:a,dragStartTime:e,axis:ue,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),l=lr({container:t,distanceToEdges:a,dragStartTime:e,axis:ge,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),c=cr({x:l,y:s});if(k(c,M))return null;const d=(({container:e,subject:t,proposedScroll:r})=>{const n=t.height>e.height,o=t.width>e.width;return o||n?o&&n?null:{x:o?0:r.x,y:n?0:r.y}:r})({container:t,subject:r,proposedScroll:c});return d?k(d,M)?null:d:null};const pr=j((e=>0===e?0:e>0?1:-1)),ur=(()=>{const e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{const o=_(t,n),i={x:e(o.x,r.x),y:e(o.y,r.y)};return k(i,M)?null:i}})(),gr=({max:e,current:t,change:r})=>{const n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},o=pr(r),i=ur({max:n,current:t,change:o});return!i||(0!==o.x&&0===i.x||0!==o.y&&0===i.y)},mr=(e,t)=>gr({current:e.scroll.current,max:e.scroll.max,change:t}),fr=(e,t)=>{const r=e.frame;return!!r&&gr({current:r.scroll.current,max:r.scroll.max,change:t})};var br=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:o,getAutoScrollerOptions:i})=>{const a=e.current.page.borderBoxCenter,s=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){const o=(({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=dr({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return a&&mr(e,a)?a:null})({dragStartTime:t,viewport:e.viewport,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(o)return void n(o)}const l=or({center:a,destination:Ue(e.impact),droppables:e.dimensions.droppables});if(!l)return;const c=(({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=e.frame;if(!a)return null;const s=dr({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return s&&fr(e,s)?s:null})({dragStartTime:t,droppable:l,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});c&&o(l.descriptor.id,c)},hr=({move:e,scrollDroppable:t,scrollWindow:r})=>{const n=(e,r)=>{if(!fr(e,r))return r;const n=((e,t)=>{const r=e.frame;return r&&fr(e,t)?ur({current:r.scroll.current,max:r.scroll.max,change:t}):null})(e,r);if(!n)return t(e.descriptor.id,r),null;const o=F(r,n);t(e.descriptor.id,o);return F(r,o)},o=(e,t,n)=>{if(!e)return n;if(!mr(t,n))return n;const o=((e,t)=>{if(!mr(e,t))return null;const r=e.scroll.max,n=e.scroll.current;return ur({current:n,max:r,change:t})})(t,n);if(!o)return r(n),null;const i=F(n,o);r(i);return F(n,i)};return t=>{const r=t.scrollJumpRequest;if(!r)return;const i=Ue(t.impact);i||C();const a=n(t.dimensions.droppables[i],r);if(!a)return;const s=t.viewport,l=o(t.isWindowScrollAllowed,s,a);l&&((t,r)=>{const n=_(t.current.client.selection,r);e({client:n})})(t,l)}},vr=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{const o=(({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>ir})=>{const n=x(e),o=x(t);let i=null;const a=e=>{i||C();const{shouldUseTimeDampening:t,dragStartTime:a}=i;br({state:e,scrollWindow:n,scrollDroppable:o,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{i&&C();const t=Date.now();let n=!1;const o=()=>{n=!0};br({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:o,scrollDroppable:o,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},n&&a(e)},stop:()=>{i&&(n.cancel(),o.cancel(),i=null)},scroll:a}})({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=hr({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{n().disabled||"DRAGGING"!==e.phase||("FLUID"!==e.movementMode?e.scrollJumpRequest&&i(e):o.scroll(e))},start:o.start,stop:o.stop}};const yr="data-rfd",Ir=(()=>{const e=`${yr}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),xr=(()=>{const e=`${yr}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Dr=(()=>{const e=`${yr}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Er={contextId:`${yr}-scroll-container-context-id`},Ar=(e,t)=>e.map((e=>{const r=e.styles[t];return r?`${e.selector} { ${r} }`:""})).join(" ");const wr="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect,Cr=()=>{const e=document.querySelector("head");return e||C(),e},Sr=e=>{const t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function Br(e,r){const n=L((()=>(e=>{const t=(r=e,e=>`[${e}="${r}"]`);var r;const n=(()=>{const e="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ";return{selector:t(Ir.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),o=[(()=>{const e=`\n      transition: ${St.outOfTheWay};\n    `;return{selector:t(xr.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),n,{selector:t(Dr.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Ar(o,"always"),resting:Ar(o,"resting"),dragging:Ar(o,"dragging"),dropAnimating:Ar(o,"dropAnimating"),userCancel:Ar(o,"userCancel")}})(e)),[e]),o=t.useRef(null),i=t.useRef(null),a=G(X((e=>{const t=i.current;t||C(),t.textContent=e})),[]),s=G((e=>{const t=o.current;t||C(),t.textContent=e}),[]);wr((()=>{(o.current||i.current)&&C();const t=Sr(r),l=Sr(r);return o.current=t,i.current=l,t.setAttribute(`${yr}-always`,e),l.setAttribute(`${yr}-dynamic`,e),Cr().appendChild(t),Cr().appendChild(l),s(n.always),a(n.resting),()=>{const e=e=>{const t=e.current;t||C(),Cr().removeChild(t),e.current=null};e(o),e(i)}}),[r,s,a,n.always,n.resting,e]);const l=G((()=>a(n.dragging)),[a,n.dragging]),c=G((e=>{a("DROP"!==e?n.userCancel:n.dropAnimating)}),[a,n.dropAnimating,n.userCancel]),d=G((()=>{i.current&&a(n.resting)}),[a,n.resting]);return L((()=>({dragging:l,dropping:c,resting:d})),[l,c,d])}function Rr(e,t){return Array.from(e.querySelectorAll(t))}var Nr=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function Or(e){return e instanceof Nr(e).HTMLElement}function Pr(e){const r=t.useRef({}),n=t.useRef(null),o=t.useRef(null),i=t.useRef(!1),a=G((function(e,t){const n={id:e,focus:t};return r.current[e]=n,function(){const t=r.current;t[e]!==n&&delete t[e]}}),[]),s=G((function(t){const r=function(e,t){const r=`[${Ir.contextId}="${e}"]`,n=Rr(document,r);if(!n.length)return null;const o=n.find((e=>e.getAttribute(Ir.draggableId)===t));return o&&Or(o)?o:null}(e,t);r&&r!==document.activeElement&&r.focus()}),[e]),l=G((function(e,t){n.current===e&&(n.current=t)}),[]),c=G((function(){o.current||i.current&&(o.current=requestAnimationFrame((()=>{o.current=null;const e=n.current;e&&s(e)})))}),[s]),d=G((function(e){n.current=null;const t=document.activeElement;t&&t.getAttribute(Ir.draggableId)===e&&(n.current=e)}),[]);wr((()=>(i.current=!0,function(){i.current=!1;const e=o.current;e&&cancelAnimationFrame(e)})),[]);return L((()=>({register:a,tryRecordFocus:d,tryRestoreFocusRecorded:c,tryShiftRecord:l})),[a,d,c,l])}function Tr(){const e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach((t=>t(e)))}function n(t){return e.draggables[t]||null}function o(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{const n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{const o=t.descriptor.id,i=n(o);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[o],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){const t=n(e);return t||C(),t},findById:n,exists:e=>Boolean(n(e)),getAllByType:t=>Object.values(e.draggables).filter((e=>e.descriptor.type===t))},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{const r=o(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){const t=o(e);return t||C(),t},findById:o,exists:e=>Boolean(o(e)),getAllByType:t=>Object.values(e.droppables).filter((e=>e.descriptor.type===t))},subscribe:function(e){return t.push(e),function(){const r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var Lr=e.createContext(null),Gr=()=>{const e=document.body;return e||C(),e};const Mr={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"};const _r={separator:"::"};function Fr(t,r=_r){const n=e.useId();return L((()=>`${t}${r.separator}${n}`),[r.separator,t,n])}var kr=e.createContext(null);function Wr(e){const r=t.useRef(e);return t.useEffect((()=>{r.current=e})),r}function Ur(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}const $r=9,Hr=13,jr=33,Vr=34,qr=35,zr=36,Yr={[Hr]:!0,[$r]:!0};var Jr=e=>{Yr[e.keyCode]&&e.preventDefault()};const Xr=(()=>{const e="visibilitychange";if("undefined"==typeof document)return e;return[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find((e=>`on${e}`in document))||e})();const Kr={type:"IDLE"};function Qr({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{const{button:t,clientX:o,clientY:i}=e;if(0!==t)return;const a={x:o,y:i},s=r();if("DRAGGING"===s.type)return e.preventDefault(),void s.actions.move(a);"PENDING"!==s.type&&C();const l=s.point;if(c=l,d=a,!(Math.abs(d.x-c.x)>=5||Math.abs(d.y-c.y)>=5))return;var c,d;e.preventDefault();const p=s.actions.fluidLift(a);n({type:"DRAGGING",actions:p})}},{eventName:"mouseup",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"!==r().type)return 27===t.keyCode?(t.preventDefault(),void e()):void Jr(t);e()}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{const n=r();"IDLE"===n.type&&C(),n.actions.shouldRespectForcePress()?e():t.preventDefault()}},{eventName:Xr,fn:e}]}function Zr(){}const en={[Vr]:!0,[jr]:!0,[zr]:!0,[qr]:!0};function tn(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>27===n.keyCode?(n.preventDefault(),void r()):32===n.keyCode?(n.preventDefault(),t(),void e.drop()):40===n.keyCode?(n.preventDefault(),void e.moveDown()):38===n.keyCode?(n.preventDefault(),void e.moveUp()):39===n.keyCode?(n.preventDefault(),void e.moveRight()):37===n.keyCode?(n.preventDefault(),void e.moveLeft()):void(en[n.keyCode]?n.preventDefault():Jr(n))},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:Xr,fn:r}]}const rn={type:"IDLE"};const nn=["input","button","textarea","select","option","optgroup","video","audio"];function on(e,t){if(null==t)return!1;if(nn.includes(t.tagName.toLowerCase()))return!0;const r=t.getAttribute("contenteditable");return"true"===r||""===r||t!==e&&on(e,t.parentElement)}function an(e,t){const r=t.target;return!!Or(r)&&on(e,r)}var sn=e=>p(e.getBoundingClientRect()).center;const ln=(()=>{const e="matches";if("undefined"==typeof document)return e;return[e,"msMatchesSelector","webkitMatchesSelector"].find((e=>e in Element.prototype))||e})();function cn(e,t){return null==e?null:e[ln](t)?e:cn(e.parentElement,t)}function dn(e,t){return e.closest?e.closest(t):cn(e,t)}function pn(e,t){const r=t.target;if(!((n=r)instanceof Nr(n).Element))return null;var n;const o=function(e){return`[${Ir.contextId}="${e}"]`}(e),i=dn(r,o);return i&&Or(i)?i:null}function un(e){e.preventDefault()}function gn({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function mn({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;const o=r.draggable.findById(n);return!!o&&(!!o.options.isEnabled&&!!tr(t.getState(),n))}function fn({lockAPI:e,contextId:t,store:r,registry:n,draggableId:o,forceSensorStop:i,sourceEvent:a}){if(!mn({lockAPI:e,store:r,registry:n,draggableId:o}))return null;const s=n.draggable.getById(o),l=function(e,t){const r=`[${xr.contextId}="${e}"]`,n=Rr(document,r).find((e=>e.getAttribute(xr.id)===t));return n&&Or(n)?n:null}(t,s.descriptor.id);if(!l)return null;if(a&&!s.options.canDragInteractiveElements&&an(l,a))return null;const c=e.claim(i||E);let d="PRE_DRAG";function p(){return s.options.shouldRespectForcePress}function u(){return e.isActive(c)}const g=function(e,t){gn({expected:e,phase:d,isLockActive:u,shouldWarn:!0})&&r.dispatch(t())}.bind(null,"DRAGGING");function m(t){function n(){e.release(),d="COMPLETED"}function o(e,o={shouldBlockNextClick:!1}){if(t.cleanup(),o.shouldBlockNextClick){const e=A(window,[{eventName:"click",fn:un,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(e)}n(),r.dispatch(It({reason:e}))}return"PRE_DRAG"!==d&&(n(),C()),r.dispatch((e=>({type:"LIFT",payload:e}))(t.liftActionArgs)),d="DRAGGING",{isActive:()=>gn({expected:"DRAGGING",phase:d,isLockActive:u,shouldWarn:!1}),shouldRespectForcePress:p,drop:e=>o("DROP",e),cancel:e=>o("CANCEL",e),...t.actions}}return{isActive:()=>gn({expected:"PRE_DRAG",phase:d,isLockActive:u,shouldWarn:!1}),shouldRespectForcePress:p,fluidLift:function(e){const t=x((e=>{g((()=>mt({client:e})))}));return{...m({liftActionArgs:{id:o,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){const e={moveUp:()=>g(ft),moveRight:()=>g(ht),moveDown:()=>g(bt),moveLeft:()=>g(vt)};return m({liftActionArgs:{id:o,clientSelection:sn(l),movementMode:"SNAP"},cleanup:E,actions:e})},abort:function(){gn({expected:"PRE_DRAG",phase:d,isLockActive:u,shouldWarn:!0})&&e.release()}}}const bn=[function(e){const r=t.useRef(Kr),n=t.useRef(E),o=L((()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented)return;if(0!==t.button)return;if(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;const r=e.findClosestDraggableId(t);if(!r)return;const o=e.tryGetLock(r,s,{sourceEvent:t});if(!o)return;t.preventDefault();const i={x:t.clientX,y:t.clientY};n.current(),d(o,i)}})),[e]),i=L((()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const n=e.findOptionsForDraggable(r);n&&(n.shouldRespectForcePress||e.canGetLock(r)&&t.preventDefault())}})),[e]),a=G((function(){n.current=A(window,[i,o],{passive:!1,capture:!0})}),[i,o]),s=G((()=>{"IDLE"!==r.current.type&&(r.current=Kr,n.current(),a())}),[a]),l=G((()=>{const e=r.current;s(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[s]),c=G((function(){const e=Qr({cancel:l,completed:s,getPhase:()=>r.current,setPhase:e=>{r.current=e}});n.current=A(window,e,{capture:!0,passive:!1})}),[l,s]),d=G((function(e,t){"IDLE"!==r.current.type&&C(),r.current={type:"PENDING",point:t,actions:e},c()}),[c]);wr((function(){return a(),function(){n.current()}}),[a])},function(e){const r=t.useRef(Zr),n=L((()=>({eventName:"keydown",fn:function(t){if(t.defaultPrevented)return;if(32!==t.keyCode)return;const n=e.findClosestDraggableId(t);if(!n)return;const i=e.tryGetLock(n,l,{sourceEvent:t});if(!i)return;t.preventDefault();let a=!0;const s=i.snapLift();function l(){a||C(),a=!1,r.current(),o()}r.current(),r.current=A(window,tn(s,l),{capture:!0,passive:!1})}})),[e]),o=G((function(){r.current=A(window,[n],{passive:!1,capture:!0})}),[n]);wr((function(){return o(),function(){r.current()}}),[o])},function(e){const r=t.useRef(rn),n=t.useRef(E),o=G((function(){return r.current}),[]),i=G((function(e){r.current=e}),[]),a=L((()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const o=e.tryGetLock(r,l,{sourceEvent:t});if(!o)return;const i=t.touches[0],{clientX:a,clientY:s}=i,c={x:a,y:s};n.current(),u(o,c)}})),[e]),s=G((function(){n.current=A(window,[a],{capture:!0,passive:!1})}),[a]),l=G((()=>{const e=r.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(rn),n.current(),s())}),[s,i]),c=G((()=>{const e=r.current;l(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[l]),d=G((function(){const e={capture:!0,passive:!1},t={cancel:c,completed:l,getPhase:o},r=A(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{const n=r();if("DRAGGING"!==n.type)return void e();n.hasMoved=!0;const{clientX:o,clientY:i}=t.touches[0],a={x:o,y:i};t.preventDefault(),n.actions.move(a)}},{eventName:"touchend",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"touchcancel",fn:t=>{"DRAGGING"===r().type?(t.preventDefault(),e()):e()}},{eventName:"touchforcechange",fn:t=>{const n=r();"IDLE"===n.type&&C();const o=t.touches[0];if(!o)return;if(!(o.force>=.15))return;const i=n.actions.shouldRespectForcePress();if("PENDING"!==n.type)return i?n.hasMoved?void t.preventDefault():void e():void t.preventDefault();i&&e()}},{eventName:Xr,fn:e}]}(t),e),i=A(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{"DRAGGING"===t().type?(27===r.keyCode&&r.preventDefault(),e()):e()}},{eventName:Xr,fn:e}]}(t),e);n.current=function(){r(),i()}}),[c,o,l]),p=G((function(){const e=o();"PENDING"!==e.type&&C();const t=e.actions.fluidLift(e.point);i({type:"DRAGGING",actions:t,hasMoved:!1})}),[o,i]),u=G((function(e,t){"IDLE"!==o().type&&C();const r=setTimeout(p,120);i({type:"PENDING",point:t,actions:e,longPressTimerId:r}),d()}),[d,o,i,p]);wr((function(){return s(),function(){n.current();const e=o();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(rn))}}),[o,s,i]),wr((function(){return A(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])}),[])}];function hn({contextId:e,store:r,registry:n,customSensors:o,enableDefaultSensors:i}){const a=[...i?bn:[],...o||[]],s=t.useState((()=>function(){let e=null;function t(){e||C(),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&C();const r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()))[0],l=G((function(e,t){Ur(e)&&!Ur(t)&&s.tryAbandon()}),[s]);wr((function(){let e=r.getState();return r.subscribe((()=>{const t=r.getState();l(e,t),e=t}))}),[s,r,l]),wr((()=>s.tryAbandon),[s.tryAbandon]);const c=G((e=>mn({lockAPI:s,registry:n,store:r,draggableId:e})),[s,n,r]),d=G(((t,o,i)=>fn({lockAPI:s,registry:n,contextId:e,store:r,draggableId:t,forceSensorStop:o||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null})),[e,s,n,r]),p=G((t=>function(e,t){const r=pn(e,t);return r?r.getAttribute(Ir.draggableId):null}(e,t)),[e]),u=G((e=>{const t=n.draggable.findById(e);return t?t.options:null}),[n.draggable]),g=G((function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==r.getState().phase&&r.dispatch({type:"FLUSH",payload:null}))}),[s,r]),m=G((()=>s.isClaimed()),[s]),f=L((()=>({canGetLock:c,tryGetLock:d,findClosestDraggableId:p,findOptionsForDraggable:u,tryReleaseLock:g,isLockClaimed:m})),[c,d,p,u,g,m]);for(let t=0;t<a.length;t++)a[t](f)}function vn(e){return e.current||C(),e.current}function yn(r){const{contextId:i,setCallbacks:a,sensors:s,nonce:l,dragHandleUsageInstructions:p}=r,u=t.useRef(null),g=Wr(r),m=G((()=>(e=>({onBeforeCapture:t=>{n.flushSync((()=>{e.onBeforeCapture&&e.onBeforeCapture(t)}))},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}))(g.current)),[g]),f=G((()=>(e=>({...ir,...e.autoScrollerOptions,durationDampening:{...ir.durationDampening,...e.autoScrollerOptions}}))(g.current)),[g]),b=function(e){const r=L((()=>(e=>`rfd-announcement-${e}`)(e)),[e]),n=t.useRef(null);return t.useEffect((function(){const e=document.createElement("div");return n.current=e,e.id=r,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),d(e.style,Mr),Gr().appendChild(e),function(){setTimeout((function(){const t=Gr();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)}))}}),[r]),G((e=>{const t=n.current;t&&(t.textContent=e)}),[])}(i),h=function({contextId:e,text:r}){const n=Fr("hidden-text",{separator:"-"}),o=L((()=>function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`}({contextId:e,uniqueId:n})),[n,e]);return t.useEffect((function(){const e=document.createElement("div");return e.id=o,e.textContent=r,e.style.display="none",Gr().appendChild(e),function(){const t=Gr();t.contains(e)&&t.removeChild(e)}}),[o,r]),o}({contextId:i,text:p}),v=Br(i,l),y=G((e=>{vn(u).dispatch(e)}),[]),I=L((()=>o({publishWhileDragging:ct,updateDroppableScroll:pt,updateDroppableIsEnabled:ut,updateDroppableIsCombineEnabled:gt,collectionStarting:dt},y)),[y]),x=function(){const e=L(Tr,[]);return t.useEffect((()=>function(){e.clean()}),[e]),e}(),D=L((()=>er(x,I)),[x,I]),E=L((()=>vr({scrollWindow:rr,scrollDroppable:D.scrollDroppable,getAutoScrollerOptions:f,...o({move:mt},y)})),[D.scrollDroppable,y,f]),A=Pr(i),w=L((()=>Yt({announce:b,autoScroller:E,dimensionMarshal:D,focusMarshal:A,getResponders:m,styleMarshal:v})),[b,E,D,A,m,v]);u.current=w;const C=G((()=>{const e=vn(u);"IDLE"!==e.getState().phase&&e.dispatch({type:"FLUSH",payload:null})}),[]),S=G((()=>{const e=vn(u).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging}),[]);a(L((()=>({isDragging:S,tryAbort:C})),[S,C]));const B=G((e=>tr(vn(u).getState(),e)),[]),R=G((()=>He(vn(u).getState())),[]),N=L((()=>({marshal:D,focus:A,contextId:i,canLift:B,isMovementAllowed:R,dragHandleUsageInstructionsId:h,registry:x})),[i,D,h,A,B,R,x]);return hn({contextId:i,store:w,registry:x,customSensors:s||null,enableDefaultSensors:!1!==r.enableDefaultSensors}),t.useEffect((()=>C),[C]),e.createElement(kr.Provider,{value:N},e.createElement(c,{context:Lr,store:w},r.children))}function In(t){const r=e.useId(),n=t.dragHandleUsageInstructions||P.dragHandleUsageInstructions;return e.createElement(S,null,(o=>e.createElement(yn,{nonce:t.nonce,contextId:r,setCallbacks:o,dragHandleUsageInstructions:n,enableDefaultSensors:t.enableDefaultSensors,sensors:t.sensors,onBeforeCapture:t.onBeforeCapture,onBeforeDragStart:t.onBeforeDragStart,onDragStart:t.onDragStart,onDragUpdate:t.onDragUpdate,onDragEnd:t.onDragEnd,autoScrollerOptions:t.autoScrollerOptions},t.children)))}const xn=5e3,Dn=4500,En=(e,t)=>t?St.drop(t.duration):e?St.snap:St.fluid,An=(e,t)=>{if(e)return t?Et.drop:Et.combining};function wn(e){return"DRAGGING"===e.type?function(e){const t=e.dimension.client,{offset:r,combineWith:n,dropping:o}=e,i=Boolean(n),a=(e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode)(e),s=Boolean(o),l=s?Nt(r,i):Rt(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:En(a,o),transform:l,opacity:An(i,s),zIndex:s?Dn:xn,pointerEvents:"none"}}(e):{transform:Rt((t=e).offset),transition:t.shouldAnimateDisplacement?void 0:"none"};var t}function Cn(e){const r=Fr("draggable"),{descriptor:n,registry:o,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l}=e,c=L((()=>({canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l})),[a,l,s]),d=G((e=>{const t=i();return t||C(),function(e,t,r=M){const n=window.getComputedStyle(t),o=t.getBoundingClientRect(),i=y(o,n),a=v(i,r);return{descriptor:e,placeholder:{client:i,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(n,t,e)}),[n,i]),p=L((()=>({uniqueId:r,descriptor:n,options:c,getDimension:d})),[n,d,c,r]),u=t.useRef(p),g=t.useRef(!0);wr((()=>(o.draggable.register(u.current),()=>o.draggable.unregister(u.current))),[o.draggable]),wr((()=>{if(g.current)return void(g.current=!1);const e=u.current;u.current=p,o.draggable.update(p,e)}),[p,o.draggable])}var Sn=e.createContext(null);function Bn(e){const r=t.useContext(e);return r||C(),r}function Rn(e){e.preventDefault()}var Nn=(e,t)=>e===t,On=e=>{const{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};function Pn(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}const Tn={mapped:{type:"SECONDARY",offset:M,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Pn(null)}};const Ln=l((()=>{const e=function(){const e=X(((e,t)=>({x:e,y:t}))),t=X(((e,t,r=null,n=null,o=null)=>({isDragging:!0,isClone:t,isDropAnimating:Boolean(o),dropAnimation:o,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null}))),r=X(((e,r,n,o,i=null,a=null,s=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:s,snapshot:t(r,o,i,a,null)}})));return(n,o)=>{if(Ur(n)){if(n.critical.draggable.id!==o.draggableId)return null;const t=n.current.client.offset,a=n.dimensions.draggables[o.draggableId],s=Ue(n.impact),l=(i=n.impact).at&&"COMBINE"===i.at.type?i.at.combine.draggableId:null,c=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,a,o.isClone,s,l,c)}var i;if("DROP_ANIMATING"===n.phase){const e=n.completed;if(e.result.draggableId!==o.draggableId)return null;const r=o.isClone,i=n.dimensions.draggables[o.draggableId],a=e.result,s=a.mode,l=On(a),c=(e=>e.combine?e.combine.draggableId:null)(a),d={duration:n.dropDuration,curve:Dt,moveTo:n.newHomeClientOffset,opacity:c?Et.drop:null,scale:c?At.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:d,draggingOver:l,combineWith:c,mode:s,forceShouldAnimate:null,snapshot:t(s,r,l,c,d)}}}return null}}(),t=function(){const e=X(((e,t)=>({x:e,y:t}))),t=X(Pn),r=X(((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}}))),n=e=>e?r(M,e,!0):null,o=(t,o,i,a)=>{const s=i.displaced.visible[t],l=Boolean(a.inVirtualList&&a.effected[t]),c=ne(i),d=c&&c.draggableId===t?o:null;if(!s){if(!l)return n(d);if(i.displaced.invisible[t])return null;const o=W(a.displacedBy.point),s=e(o.x,o.y);return r(s,d,!0)}if(l)return n(d);const p=i.displacedBy.point,u=e(p.x,p.y);return r(u,d,s.shouldAnimate)};return(e,t)=>{if(Ur(e))return e.critical.draggable.id===t.draggableId?null:o(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){const r=e.completed;return r.result.draggableId===t.draggableId?null:o(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||Tn}),{dropAnimationFinished:xt},null,{context:Lr,areStatePropsEqual:Nn})((r=>{const o=t.useRef(null),i=G(((e=null)=>{o.current=e}),[]),a=G((()=>o.current),[]),{contextId:s,dragHandleUsageInstructionsId:l,registry:c}=Bn(kr),{type:d,droppableId:p}=Bn(Sn),u=L((()=>({id:r.draggableId,index:r.index,type:d,droppableId:p})),[r.draggableId,r.index,d,p]),{children:g,draggableId:m,isEnabled:f,shouldRespectForcePress:b,canDragInteractiveElements:h,isClone:v,mapped:y,dropAnimationFinished:I}=r;if(!v){Cn(L((()=>({descriptor:u,registry:c,getDraggableRef:a,canDragInteractiveElements:h,shouldRespectForcePress:b,isEnabled:f})),[u,c,a,h,b,f]))}const x=L((()=>f?{tabIndex:0,role:"button","aria-describedby":l,"data-rfd-drag-handle-draggable-id":m,"data-rfd-drag-handle-context-id":s,draggable:!1,onDragStart:Rn}:null),[s,l,m,f]),D=G((e=>{"DRAGGING"===y.type&&y.dropping&&"transform"===e.propertyName&&n.flushSync(I)}),[I,y]),E=L((()=>{const e=wn(y),t="DRAGGING"===y.type&&y.dropping?D:void 0;return{innerRef:i,draggableProps:{"data-rfd-draggable-context-id":s,"data-rfd-draggable-id":m,style:e,onTransitionEnd:t},dragHandleProps:x}}),[s,x,m,y,D,i]),A=L((()=>({draggableId:u.id,type:u.type,source:{index:u.index,droppableId:u.droppableId}})),[u.droppableId,u.id,u.index,u.type]);return e.createElement(e.Fragment,null,g(E,y.snapshot,A))}));function Gn(t){return Bn(Sn).isUsingCloneFor!==t.draggableId||t.isClone?e.createElement(Ln,t):null}function Mn(t){const r="boolean"!=typeof t.isDragDisabled||!t.isDragDisabled,n=Boolean(t.disableInteractiveElementBlocking),o=Boolean(t.shouldRespectForcePress);return e.createElement(Gn,d({},t,{isClone:!1,isEnabled:r,canDragInteractiveElements:n,shouldRespectForcePress:o}))}const _n=e=>t=>e===t,Fn=_n("scroll"),kn=_n("auto"),Wn=(e,t)=>t(e.overflowX)||t(e.overflowY),Un=e=>null==e||e===document.body||e===document.documentElement?null:(e=>{const t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return Wn(r,Fn)||Wn(r,kn)})(e)?e:Un(e.parentElement);var $n=e=>({x:e.scrollLeft,y:e.scrollTop});const Hn=e=>{if(!e)return!1;return"fixed"===window.getComputedStyle(e).position||Hn(e.parentElement)};var jn=({ref:e,descriptor:t,env:r,windowScroll:n,direction:o,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:s})=>{const l=r.closestScrollable,c=((e,t)=>{const r=I(e);if(!t)return r;if(e!==t)return r;const n=r.paddingBox.top-t.scrollTop,o=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a=o+t.scrollWidth,s=u({top:n,right:a,bottom:i,left:o},r.border);return f({borderBox:s,margin:r.margin,border:r.border,padding:r.padding})})(e,l),d=v(c,n),p=(()=>{if(!l)return null;const e=I(l),t={scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth};return{client:e,page:v(e,n),scroll:$n(l),scrollSize:t,shouldClipSubject:s}})(),g=(({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:o,client:i,page:a,closest:s})=>{const l=(()=>{if(!s)return null;const{scrollSize:e,client:t}=s,r=Jt({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:s.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:r,diff:{value:M,displacement:M}}}})(),c="vertical"===o?ue:ge;return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:c,isEnabled:t,client:i,page:a,frame:l,subject:Y({page:a,withPlaceholder:null,axis:c,frame:l})}})({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:o,client:c,page:d,closest:p});return g};const Vn={passive:!1},qn={passive:!0};var zn=e=>e.shouldPublishImmediately?Vn:qn;const Yn=e=>e&&e.env.closestScrollable||null;function Jn(e){const r=t.useRef(null),n=Bn(kr),o=Fr("droppable"),{registry:i,marshal:a}=n,s=Wr(e),l=L((()=>({id:e.droppableId,type:e.type,mode:e.mode})),[e.droppableId,e.mode,e.type]),c=t.useRef(l),d=L((()=>X(((e,t)=>{r.current||C();const n={x:e,y:t};a.updateDroppableScroll(l.id,n)}))),[l.id,a]),p=G((()=>{const e=r.current;return e&&e.env.closestScrollable?$n(e.env.closestScrollable):M}),[]),u=G((()=>{const e=p();d(e.x,e.y)}),[p,d]),g=L((()=>x(u)),[u]),m=G((()=>{const e=r.current,t=Yn(e);e&&t||C();e.scrollOptions.shouldPublishImmediately?u():g()}),[g,u]),f=G(((e,t)=>{r.current&&C();const o=s.current,i=o.getDroppableRef();i||C();const a={closestScrollable:Un(c=i),isFixedOnPage:Hn(c)};var c;const d={ref:i,descriptor:l,env:a,scrollOptions:t};r.current=d;const p=jn({ref:i,descriptor:l,env:a,windowScroll:e,direction:o.direction,isDropDisabled:o.isDropDisabled,isCombineEnabled:o.isCombineEnabled,shouldClipSubject:!o.ignoreContainerClipping}),u=a.closestScrollable;return u&&(u.setAttribute(Er.contextId,n.contextId),u.addEventListener("scroll",m,zn(d.scrollOptions))),p}),[n.contextId,l,m,s]),b=G((()=>{const e=r.current,t=Yn(e);return e&&t||C(),$n(t)}),[]),h=G((()=>{const e=r.current;e||C();const t=Yn(e);r.current=null,t&&(g.cancel(),t.removeAttribute(Er.contextId),t.removeEventListener("scroll",m,zn(e.scrollOptions)))}),[m,g]),v=G((e=>{const t=r.current;t||C();const n=Yn(t);n||C(),n.scrollTop+=e.y,n.scrollLeft+=e.x}),[]),y=L((()=>({getDimensionAndWatchScroll:f,getScrollWhileDragging:b,dragStopped:h,scroll:v})),[h,f,b,v]),I=L((()=>({uniqueId:o,descriptor:l,callbacks:y})),[y,l,o]);wr((()=>(c.current=I.descriptor,i.droppable.register(I),()=>{r.current&&h(),i.droppable.unregister(I)})),[y,l,h,I,a,i.droppable]),wr((()=>{r.current&&a.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)}),[e.isDropDisabled,a]),wr((()=>{r.current&&a.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,a])}function Xn(){}const Kn={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},Qn=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{const n=(({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?Kn:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin})({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?St.placeholder:null}};var Zn=e.memo((r=>{const n=t.useRef(null),o=G((()=>{n.current&&(clearTimeout(n.current),n.current=null)}),[]),{animate:i,onTransitionEnd:a,onClose:s,contextId:l}=r,[c,d]=t.useState("open"===r.animate);t.useEffect((()=>c?"open"!==i?(o(),d(!1),Xn):n.current?Xn:(n.current=setTimeout((()=>{n.current=null,d(!1)})),o):Xn),[i,c,o]);const p=G((e=>{"height"===e.propertyName&&(a(),"close"===i&&s())}),[i,s,a]),u=Qn({isAnimatingOpenOnMount:c,animate:r.animate,placeholder:r.placeholder});return e.createElement(r.placeholder.tagName,{style:u,"data-rfd-placeholder-context-id":l,onTransitionEnd:p,ref:r.innerRef})}));class eo extends e.PureComponent{constructor(...e){super(...e),this.state={isVisible:Boolean(this.props.on),data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}const to={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||C(),document.body}},ro=e=>{let t,r={...e};for(t in to)void 0===e[t]&&(r={...r,[t]:to[t]});return r},no=(e,t)=>e===t.droppable.type,oo=(e,t)=>t.draggables[e.draggable.id],io=l((()=>{const e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=X((e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}))),n=X(((n,o,i,a,s,l)=>{const c=s.descriptor.id;if(s.descriptor.droppableId===n){const e=l?{render:l,dragging:r(s.descriptor)}:null,t={isDraggingOver:i,draggingOverWith:i?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!1,snapshot:t,useClone:e}}if(!o)return t;if(!a)return e;const d={isDraggingOver:i,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!0,snapshot:d,useClone:null}}));return(r,o)=>{const i=ro(o),a=i.droppableId,s=i.type,l=!i.isDropDisabled,c=i.renderClone;if(Ur(r)){const e=r.critical;if(!no(s,e))return t;const o=oo(e,r.dimensions),i=Ue(r.impact)===a;return n(a,l,i,i,o,c)}if("DROP_ANIMATING"===r.phase){const e=r.completed;if(!no(s,e.critical))return t;const o=oo(e.critical,r.dimensions);return n(a,l,On(e.result)===a,Ue(e.impact)===a,o,c)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){const n=r.completed;if(!no(s,n.critical))return t;const o=Ue(n.impact)===a,i=Boolean(n.impact.at&&"COMBINE"===n.impact.at.type),l=n.critical.droppable.id===a;return o?i?e:t:l?e:t}return t}}),{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},((e,t,r)=>({...ro(r),...e,...t})),{context:Lr,areStatePropsEqual:Nn})((n=>{const o=t.useContext(kr);o||C();const{contextId:i,isMovementAllowed:a}=o,s=t.useRef(null),l=t.useRef(null),{children:c,droppableId:d,type:p,mode:u,direction:g,ignoreContainerClipping:m,isDropDisabled:f,isCombineEnabled:b,snapshot:h,useClone:v,updateViewportMaxScroll:y,getContainerForClone:I}=n,x=G((()=>s.current),[]),D=G(((e=null)=>{s.current=e}),[]);G((()=>l.current),[]);const E=G(((e=null)=>{l.current=e}),[]),A=G((()=>{a()&&y({maxScroll:Kt()})}),[a,y]);Jn({droppableId:d,type:p,mode:u,direction:g,isDropDisabled:f,isCombineEnabled:b,ignoreContainerClipping:m,getDroppableRef:x});const w=L((()=>e.createElement(eo,{on:n.placeholder,shouldAnimate:n.shouldAnimatePlaceholder},(({onClose:t,data:r,animate:n})=>e.createElement(Zn,{placeholder:r,onClose:t,innerRef:E,animate:n,contextId:i,onTransitionEnd:A})))),[i,A,n.placeholder,n.shouldAnimatePlaceholder,E]),S=L((()=>({innerRef:D,placeholder:w,droppableProps:{"data-rfd-droppable-id":d,"data-rfd-droppable-context-id":i}})),[i,d,w,D]),B=v?v.dragging.draggableId:null,R=L((()=>({droppableId:d,type:p,isUsingCloneFor:B})),[d,B,p]);return e.createElement(Sn.Provider,{value:R},c(S,h),function(){if(!v)return null;const{dragging:t,render:n}=v,o=e.createElement(Gn,{draggableId:t.draggableId,index:t.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},((e,r)=>n(e,r,t)));return r.createPortal(o,I())}())}));export{io as C,In as D,Mn as P};
