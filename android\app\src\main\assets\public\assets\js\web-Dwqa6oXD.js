import{a6 as e}from"./index-Ck4sQVom.js";import"./mui-vendor-DsBXMegs.js";import"./react-vendor-Be-rfjCm.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";class t extends e{constructor(){super(...arguments),this.group="CapacitorStorage"}async configure({group:e}){"string"==typeof e&&(this.group=e)}async get(e){return{value:this.impl.getItem(this.applyPrefix(e.key))}}async set(e){this.impl.setItem(this.applyPrefix(e.key),e.value)}async remove(e){this.impl.removeItem(this.applyPrefix(e.key))}async keys(){return{keys:this.rawKeys().map((e=>e.substring(this.prefix.length)))}}async clear(){for(const e of this.rawKeys())this.impl.removeItem(e)}async migrate(){var e;const t=[],i=[],s="_cap_",r=Object.keys(this.impl).filter((e=>0===e.indexOf(s)));for(const o of r){const s=o.substring(5),r=null!==(e=this.impl.getItem(o))&&void 0!==e?e:"",{value:a}=await this.get({key:s});"string"==typeof a?i.push(s):(await this.set({key:s,value:r}),t.push(s))}return{migrated:t,existing:i}}async removeOld(){const e=Object.keys(this.impl).filter((e=>0===e.indexOf("_cap_")));for(const t of e)this.impl.removeItem(t)}get impl(){return window.localStorage}get prefix(){return"NativeStorage"===this.group?"":`${this.group}.`}rawKeys(){return Object.keys(this.impl).filter((e=>0===e.indexOf(this.prefix)))}applyPrefix(e){return this.prefix+e}}export{t as PreferencesWeb};
