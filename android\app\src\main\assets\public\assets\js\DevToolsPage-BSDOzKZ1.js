import{c as e,j as r,z as s,B as t,t as a,aa as l,J as o,L as n,w as i,b as c,v as d,I as x,a3 as h,g as p,h as j,o as m,p as u,q as g,V as f,W as b,X as v,A as w,T as k,d as S,Z as C,_ as y,r as z,m as D,ac as E}from"./mui-vendor-DsBXMegs.js";import{r as I,u as T}from"./react-vendor-Be-rfjCm.js";import H from"./EnhancedConsoleService-C8d95yHs.js";import{S as A}from"./Search-D7hYSVTA.js";import{T as P}from"./PlayArrow-DAmWqkSm.js";import{I as W,aT as L,aU as B,E as U,A as M,_ as O,e as R}from"./index-Ck4sQVom.js";import G from"./EnhancedNetworkService-L9rVmLXg.js";import{H as q}from"./Http-BYjiteRT.js";import{C as J}from"./Clear-DBCbS65T.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const N=e(r.jsx("path",{d:"M20 8h-2.81c-.45-.78-1.07-1.45-1.82-1.96L17 4.41 15.59 3l-2.17 2.17C12.96 5.06 12.49 5 12 5s-.96.06-1.41.17L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20zm-6 8h-4v-2h4zm0-4h-4v-2h4z"})),F=e(r.jsx("path",{d:"M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 5.5c0-.28-.22-.5-.5-.5M1 9l2 2c2.88-2.88 6.79-4.08 10.53-3.62l1.19-2.68C9.89 3.84 4.74 5.27 1 9m20 2 2-2c-1.64-1.64-3.55-2.82-5.59-3.57l-.53 2.82c1.5.62 2.9 1.53 4.12 2.75m-4 4 2-2c-.8-.8-1.7-1.42-2.66-1.89l-.55 2.92c.42.27.83.59 1.21.97M5 13l2 2c1.13-1.13 2.56-1.79 4.03-2l1.28-2.88c-2.63-.08-5.3.87-7.31 2.88"})),V=e(r.jsx("path",{d:"M20 4H4c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.89-2-2-2m0 14H4V8h16zm-2-1h-6v-2h6zM7.5 17l-1.41-1.41L8.67 13l-2.59-2.59L7.5 9l4 4z"})),_=({autoScroll:e=!0})=>{const h=s(),[p,j]=I.useState([]),[m,u]=I.useState({levels:new Set(["log","info","warn","error","debug"]),searchText:"",showTimestamps:!0}),[g,f]=I.useState(""),[b,v]=I.useState([]),[w,k]=I.useState(-1),S=I.useRef(null),C=H.getInstance();I.useEffect((()=>{const e=C.addListener((e=>{j(e)}));return j(C.getEntries()),e}),[]),I.useEffect((()=>{e&&S.current&&S.current.scrollIntoView({behavior:"smooth"})}),[p,e]);const y=()=>{if(!g.trim())return;const e=[...b,g];v(e),k(-1),C.executeCommand(g),f("")},z=e=>{switch(e){case"error":return h.palette.error.main;case"warn":return h.palette.warning.main;case"info":return h.palette.info.main;case"debug":return h.palette.success.main;default:return h.palette.text.primary}},D=e=>{switch(e){case"error":return r.jsx(B,{fontSize:"small"});case"warn":return r.jsx(L,{fontSize:"small"});case"info":return r.jsx(W,{fontSize:"small"});case"debug":return r.jsx(N,{fontSize:"small"});default:return r.jsx(V,{fontSize:"small"})}},E=C.getFilteredEntries(m);return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[r.jsx(t,{sx:{p:1,borderBottom:1,borderColor:"divider"},children:r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:1},children:[r.jsx(a,{size:"small",placeholder:"过滤控制台输出...",value:m.searchText,onChange:e=>u((r=>({...r,searchText:e.target.value}))),InputProps:{startAdornment:r.jsx(l,{position:"start",children:r.jsx(A,{fontSize:"small"})})},fullWidth:!0}),r.jsx(t,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:["error","warn","info","log","debug"].map((e=>r.jsx(o,{label:e.toUpperCase(),size:"small",variant:m.levels.has(e)?"filled":"outlined",color:"error"===e?"error":"warn"===e?"warning":"default",onClick:()=>{const r=new Set(m.levels);r.has(e)?r.delete(e):r.add(e),u((e=>({...e,levels:r})))}},e)))})]})}),r.jsx(t,{sx:{flexGrow:1,overflow:"auto",bgcolor:"dark"===h.palette.mode?"#1e1e1e":"#fafafa"},children:r.jsxs(n,{dense:!0,sx:{p:0,fontFamily:"monospace"},children:[E.map((e=>r.jsx(i,{sx:{py:.25,px:1,borderBottom:"1px solid",borderColor:"divider","&:hover":{bgcolor:"action.hover"},alignItems:"flex-start"},children:r.jsxs(t,{sx:{display:"flex",alignItems:"flex-start",width:"100%",gap:1},children:[r.jsx(t,{sx:{color:z(e.level),mt:.5},children:D(e.level)}),r.jsxs(t,{sx:{flexGrow:1,minWidth:0},children:[m.showTimestamps&&r.jsx(c,{variant:"caption",sx:{color:"text.secondary",mr:1},children:new Date(e.timestamp).toLocaleTimeString()}),r.jsx(t,{sx:{wordBreak:"break-word",whiteSpace:"pre-wrap",maxHeight:"300px",overflow:"auto",border:e.args.some((e=>{const r=C.formatArg(e);return r.length>200||r.split("\n").length>5}))?"1px solid":"none",borderColor:"dark"===h.palette.mode?"rgba(255,255,255,0.1)":"rgba(0,0,0,0.1)",borderRadius:"4px",padding:e.args.some((e=>{const r=C.formatArg(e);return r.length>200||r.split("\n").length>5}))?"8px":"0",backgroundColor:e.args.some((e=>{const r=C.formatArg(e);return r.length>200||r.split("\n").length>5}))?"dark"===h.palette.mode?"rgba(255,255,255,0.02)":"rgba(0,0,0,0.02)":"transparent","&::-webkit-scrollbar":{width:"6px",height:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"dark"===h.palette.mode?"rgba(255,255,255,0.4)":"rgba(0,0,0,0.4)",borderRadius:"3px","&:hover":{backgroundColor:"dark"===h.palette.mode?"rgba(255,255,255,0.6)":"rgba(0,0,0,0.6)"}},"&::-webkit-scrollbar-track":{backgroundColor:"transparent"},"&::-webkit-scrollbar-corner":{backgroundColor:"transparent"}},children:e.args.map(((e,s)=>r.jsx("span",{style:{marginRight:"8px"},children:C.formatArg(e)},s)))}),e.stack&&r.jsx(d,{in:!0,children:r.jsx(c,{variant:"caption",component:"pre",sx:{color:"text.secondary",fontSize:"0.75rem",mt:.5,whiteSpace:"pre-wrap",wordBreak:"break-word",maxHeight:"200px",overflow:"auto",border:"1px solid",borderColor:"dark"===h.palette.mode?"rgba(255,255,255,0.1)":"rgba(0,0,0,0.1)",borderRadius:"4px",padding:"8px",backgroundColor:"dark"===h.palette.mode?"rgba(255,255,255,0.02)":"rgba(0,0,0,0.02)","&::-webkit-scrollbar":{width:"6px",height:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"dark"===h.palette.mode?"rgba(255,255,255,0.4)":"rgba(0,0,0,0.4)",borderRadius:"3px","&:hover":{backgroundColor:"dark"===h.palette.mode?"rgba(255,255,255,0.6)":"rgba(0,0,0,0.6)"}},"&::-webkit-scrollbar-track":{backgroundColor:"transparent"},"&::-webkit-scrollbar-corner":{backgroundColor:"transparent"}},children:e.stack})})]})]})},e.id))),r.jsx("div",{ref:S})]})}),r.jsx(t,{sx:{p:1,borderTop:1,borderColor:"divider"},children:r.jsx(a,{size:"small",placeholder:"输入 JavaScript 命令...",value:g,onChange:e=>f(e.target.value),onKeyDown:e=>{if("Enter"===e.key)y();else if("ArrowUp"===e.key){if(e.preventDefault(),w<b.length-1){const e=w+1;k(e),f(b[b.length-1-e])}}else if("ArrowDown"===e.key)if(e.preventDefault(),w>0){const e=w-1;k(e),f(b[b.length-1-e])}else 0===w&&(k(-1),f(""))},InputProps:{startAdornment:r.jsx(l,{position:"start",children:r.jsx(c,{variant:"body2",sx:{color:"primary.main"},children:">"})}),endAdornment:r.jsx(l,{position:"end",children:r.jsx(x,{size:"small",onClick:y,disabled:!g.trim(),children:r.jsx(P,{})})})},fullWidth:!0,sx:{fontFamily:"monospace"}})})]})},K=()=>{const e=s(),d=h(e.breakpoints.down("md")),[,w]=I.useState([]),[k,S]=I.useState({methods:new Set(["GET","POST","PUT","DELETE","PATCH","HEAD","OPTIONS"]),statuses:new Set(["pending","success","error","cancelled"]),searchText:"",hideDataUrls:!0,onlyErrors:!1}),[C,y]=I.useState(null),[z,D]=I.useState(!1),E=G.getInstance();I.useEffect((()=>{const e=E.addListener((e=>{w(e)}));return w(E.getEntries()),e}),[]);const T=E.getFilteredEntries(k);return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[r.jsx(t,{sx:{p:1,borderBottom:1,borderColor:"divider"},children:r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:1},children:[r.jsx(a,{size:"small",placeholder:"过滤网络请求...",value:k.searchText,onChange:e=>S((r=>({...r,searchText:e.target.value}))),InputProps:{startAdornment:r.jsx(l,{position:"start",children:r.jsx(A,{fontSize:"small"})})},fullWidth:!0}),r.jsxs(t,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[r.jsx(p,{control:r.jsx(j,{size:"small",checked:k.onlyErrors,onChange:e=>S((r=>({...r,onlyErrors:e.target.checked})))}),label:"仅错误"}),r.jsx(p,{control:r.jsx(j,{size:"small",checked:!k.hideDataUrls,onChange:e=>S((r=>({...r,hideDataUrls:!e.target.checked})))}),label:"Data URLs"})]})]})}),r.jsx(t,{sx:{flexGrow:1,overflow:"auto"},children:r.jsx(n,{dense:!0,sx:{p:0},children:T.map((e=>r.jsx(i,{sx:{py:1,px:2,borderBottom:"1px solid",borderColor:"divider","&:hover":{bgcolor:"action.hover"},cursor:"pointer"},onClick:()=>(e=>{y(e),D(!0)})(e),children:r.jsxs(t,{sx:{display:"flex",alignItems:"center",width:"100%",gap:1},children:[r.jsx(o,{label:e.method,size:"small",sx:{bgcolor:E.getMethodColor(e.method),color:"white",fontWeight:"bold",minWidth:"60px"}}),r.jsx(o,{label:e.statusCode||e.status,size:"small",color:"success"===e.status?"success":"error"===e.status?"error":"default"}),r.jsx(t,{sx:{flexGrow:1,minWidth:0,mx:1},children:r.jsx(c,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.url})}),r.jsxs(t,{sx:{display:"flex",gap:2,minWidth:"fit-content"},children:[r.jsx(c,{variant:"caption",color:"text.secondary",children:e.duration?E.formatDuration(e.duration):"-"}),r.jsx(c,{variant:"caption",color:"text.secondary",children:e.responseSize?E.formatSize(e.responseSize):"-"})]})]})},e.id)))})}),r.jsxs(m,{open:z,onClose:()=>D(!1),maxWidth:"md",fullWidth:!0,fullScreen:d,children:[r.jsx(u,{children:r.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:1},children:[r.jsx(q,{}),"网络请求详情",r.jsx(x,{size:"small",onClick:()=>D(!1),sx:{ml:"auto"},children:r.jsx(J,{})})]})}),r.jsx(g,{children:C&&r.jsxs(t,{children:[r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2,mb:2},children:[r.jsxs(t,{children:[r.jsx(c,{variant:"subtitle2",children:"请求URL:"}),r.jsx(c,{variant:"body2",sx:{wordBreak:"break-all"},children:C.url})]}),r.jsxs(t,{sx:{display:"flex",gap:2},children:[r.jsxs(t,{children:[r.jsx(c,{variant:"subtitle2",children:"请求方法:"}),r.jsx(o,{label:C.method,size:"small",sx:{bgcolor:E.getMethodColor(C.method),color:"white"}})]}),r.jsxs(t,{children:[r.jsx(c,{variant:"subtitle2",children:"状态:"}),r.jsx(o,{label:C.statusCode||C.status,size:"small",color:"success"===C.status?"success":"error"===C.status?"error":"default"})]}),C.duration&&r.jsxs(t,{children:[r.jsx(c,{variant:"subtitle2",children:"耗时:"}),r.jsx(c,{variant:"body2",children:E.formatDuration(C.duration)})]})]})]}),r.jsxs(f,{children:[r.jsx(b,{expandIcon:r.jsx(U,{}),children:r.jsx(c,{children:"请求头"})}),r.jsx(v,{children:r.jsx("pre",{style:{fontSize:"0.875rem",margin:0,whiteSpace:"pre-wrap"},children:JSON.stringify(C.requestHeaders,null,2)})})]}),C.requestPayload&&r.jsxs(f,{children:[r.jsx(b,{expandIcon:r.jsx(U,{}),children:r.jsx(c,{children:"请求体"})}),r.jsx(v,{children:r.jsx("pre",{style:{fontSize:"0.875rem",margin:0,whiteSpace:"pre-wrap"},children:"string"==typeof C.requestPayload?C.requestPayload:JSON.stringify(C.requestPayload,null,2)})})]}),r.jsxs(f,{children:[r.jsx(b,{expandIcon:r.jsx(U,{}),children:r.jsx(c,{children:"响应头"})}),r.jsx(v,{children:r.jsx("pre",{style:{fontSize:"0.875rem",margin:0,whiteSpace:"pre-wrap"},children:JSON.stringify(C.responseHeaders,null,2)})})]}),C.responseData&&r.jsxs(f,{children:[r.jsx(b,{expandIcon:r.jsx(U,{}),children:r.jsx(c,{children:"响应体"})}),r.jsx(v,{children:r.jsx("pre",{style:{fontSize:"0.875rem",margin:0,whiteSpace:"pre-wrap"},children:"string"==typeof C.responseData?C.responseData:JSON.stringify(C.responseData,null,2)})})]})]})})]})]})},X=()=>{const e=T(),a=s(),l=h(a.breakpoints.down("md")),[o,n]=I.useState(0),[i,d]=I.useState(!1),[f,b]=I.useState(!1),[v,A]=I.useState(!0),[P,W]=I.useState(!1),L=H.getInstance(),B=G.getInstance();return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[r.jsx(w,{position:"static",elevation:1,children:r.jsxs(k,{variant:l?"dense":"regular",children:[r.jsx(x,{edge:"start",color:"inherit",onClick:()=>{e(-1)},"aria-label":"返回",children:r.jsx(M,{})}),r.jsx(c,{variant:"h6",component:"div",sx:{flexGrow:1},children:"开发者工具"}),r.jsx(S,{title:"设置",children:r.jsx(x,{color:"inherit",onClick:()=>b(!0),children:r.jsx(O,{})})}),r.jsx(S,{title:"清除",children:r.jsx(x,{color:"inherit",onClick:()=>d(!0),children:r.jsx(R,{})})})]})}),r.jsxs(C,{value:o,onChange:(e,r)=>{n(r)},variant:l?"fullWidth":"standard",sx:{borderBottom:1,borderColor:"divider"},children:[r.jsx(y,{icon:r.jsx(V,{}),label:"控制台",iconPosition:"start",sx:{minHeight:l?48:64}}),r.jsx(y,{icon:r.jsx(F,{}),label:"网络",iconPosition:"start",sx:{minHeight:l?48:64}})]}),r.jsxs(t,{sx:{flexGrow:1,overflow:"hidden"},children:[0===o&&r.jsx(_,{autoScroll:v}),1===o&&r.jsx(K,{})]}),r.jsxs(m,{open:f,onClose:()=>b(!1),children:[r.jsx(u,{children:"开发者工具设置"}),r.jsx(g,{children:r.jsxs(t,{sx:{pt:1,display:"flex",flexDirection:"column",gap:1},children:[r.jsx(p,{control:r.jsx(j,{checked:v,onChange:e=>A(e.target.checked)}),label:"自动滚动到底部"}),r.jsx(p,{control:r.jsx(j,{checked:P,onChange:e=>W(e.target.checked)}),label:"保留日志"})]})}),r.jsx(z,{children:r.jsx(D,{onClick:()=>b(!1),children:"关闭"})})]}),r.jsxs(m,{open:i,onClose:()=>d(!1),children:[r.jsx(u,{children:"确认清除"}),r.jsx(g,{children:r.jsxs(E,{children:["确定要清除所有",0===o?"控制台日志":"网络记录","吗？此操作无法撤销。"]})}),r.jsxs(z,{children:[r.jsx(D,{onClick:()=>d(!1),children:"取消"}),r.jsx(D,{onClick:()=>{0===o?L.clear():1===o&&B.clear(),d(!1)},color:"error",children:"清除"})]})]})]})};export{X as default};
