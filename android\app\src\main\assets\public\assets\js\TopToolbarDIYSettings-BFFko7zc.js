import{j as e,B as s,b as o,m as t,P as n,d as i,I as r,a2 as a,k as l,g as c,h as d,f as x,J as h}from"./mui-vendor-DsBXMegs.js";import{u as m,r as p}from"./react-vendor-Be-rfjCm.js";import{c as g,u,a as j,b,a9 as y}from"./index-Ck4sQVom.js";import{A as v,B as f}from"./bot-D_6xUTu5.js";import{W as z}from"./wand-sparkles-aR_DKulP.js";import{I as k,S as w,A as C,M as T}from"./settings-U9ls_XoW.js";import{T as S}from"./trash-2-CLDVVUq8.js";import{P as B}from"./plus-5If-ii08.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const M=g("hand",[["path",{d:"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2",key:"1fvzgz"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2",key:"1kc0my"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8",key:"10h0bg"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]]),P=g("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),I=()=>{const g=m(),I=u(),D=j((e=>e.settings)),Y=p.useRef(null),N=D.topToolbar||{showSettingsButton:!0,showModelSelector:!0,modelSelectorStyle:"dialog",showChatTitle:!0,showTopicName:!1,showNewTopicButton:!1,showClearButton:!1,showMenuButton:!0,componentPositions:[]},[W,X]=p.useState({isDragging:!1,draggedComponent:null,startPosition:{x:0,y:0},currentPosition:{x:0,y:0}}),A={menuButton:{name:"菜单按钮",icon:e.jsx(C,{size:20}),key:"showMenuButton"},chatTitle:{name:"对话标题",icon:e.jsx(y,{size:20}),key:"showChatTitle"},topicName:{name:"话题名称",icon:e.jsx(T,{size:20}),key:"showTopicName"},newTopicButton:{name:"新建话题",icon:e.jsx(B,{size:20}),key:"showNewTopicButton"},clearButton:{name:"清空按钮",icon:e.jsx(S,{size:20}),key:"showClearButton"},modelSelector:{name:"模型选择器",icon:e.jsx(f,{size:20}),key:"showModelSelector"},settingsButton:{name:"设置按钮",icon:e.jsx(w,{size:20}),key:"showSettingsButton"}},L=p.useCallback(((e,s)=>{s.preventDefault();const o="touches"in s?s.touches[0].clientX:s.clientX,t="touches"in s?s.touches[0].clientY:s.clientY;X({isDragging:!0,draggedComponent:e,startPosition:{x:o,y:t},currentPosition:{x:o,y:t}})}),[]),R=p.useCallback((e=>{if(!W.isDragging)return;e.preventDefault();const s="touches"in e?e.touches[0].clientX:e.clientX,o="touches"in e?e.touches[0].clientY:e.clientY;X((e=>({...e,currentPosition:{x:s,y:o}})))}),[W.isDragging]),V=p.useCallback((e=>{if(!W.isDragging||!W.draggedComponent||!Y.current)return;const s="touches"in e?e.changedTouches[0].clientX:e.clientX,o="touches"in e?e.changedTouches[0].clientY:e.clientY,t=Y.current.getBoundingClientRect();if(s>=t.left&&s<=t.right&&o>=t.top&&o<=t.bottom){const e=(s-t.left)/t.width*100,n=(o-t.top)/t.height*100,i=[...N.componentPositions||[]],r=i.findIndex((e=>e.id===W.draggedComponent)),a={id:W.draggedComponent,x:Math.max(0,Math.min(90,e)),y:Math.max(0,Math.min(80,n))};r>=0?i[r]=a:i.push(a),I(b({topToolbar:{...N,componentPositions:i}}))}X({isDragging:!1,draggedComponent:null,startPosition:{x:0,y:0},currentPosition:{x:0,y:0}})}),[W,N,I]),E=(s,t)=>{const n=A[s];if(!n||!N[n.key])return null;const i=t?{position:"absolute",left:`${t.x}%`,top:`${t.y}%`,transform:"translate(-50%, -50%)",zIndex:10}:{};switch(s){case"menuButton":return e.jsx(r,{color:"inherit",size:"small",sx:i,children:e.jsx(C,{size:20})},s);case"chatTitle":return e.jsx(o,{variant:"h6",noWrap:!0,sx:i,children:"对话"},s);case"topicName":return e.jsx(o,{variant:"body2",noWrap:!0,sx:{...i,color:"text.secondary"},children:"示例话题"},s);case"newTopicButton":return e.jsx(r,{color:"inherit",size:"small",sx:i,children:e.jsx(B,{size:20})},s);case"clearButton":return e.jsx(r,{color:"inherit",size:"small",sx:i,children:e.jsx(S,{size:20})},s);case"modelSelector":return"dialog"===N.modelSelectorStyle?e.jsx(r,{color:"inherit",size:"small",sx:i,children:e.jsx(f,{size:20})},s):e.jsx(h,{label:"GPT-4",size:"small",variant:"outlined",sx:{...i,borderColor:"divider",color:"text.primary"}},s);case"settingsButton":return e.jsx(r,{color:"inherit",size:"small",sx:i,children:e.jsx(w,{size:20})},s);default:return null}};return e.jsxs(s,{sx:{height:"100vh",backgroundColor:"background.default",display:"flex",flexDirection:"column",overflow:"hidden"},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",padding:2,borderBottom:1,borderColor:"divider",backgroundColor:"background.paper",zIndex:10,flexShrink:0},children:[e.jsx(v,{size:20,style:{marginRight:16,cursor:"pointer"},onClick:()=>{g("/settings/appearance")}}),e.jsx(o,{variant:"h6",color:"primary",sx:{flexGrow:1},children:"顶部工具栏 DIY 设置"}),e.jsx(t,{startIcon:e.jsx(P,{size:16}),onClick:()=>{I(b({topToolbar:{...N,componentPositions:[]}}))},size:"small",variant:"outlined",children:"重置布局"})]}),e.jsxs(s,{sx:{p:2,flex:1,overflow:"auto"},children:[e.jsxs(n,{elevation:2,sx:{mb:3,overflow:"hidden"},children:[e.jsxs(s,{sx:{p:2,display:"flex",alignItems:"center",gap:1},children:[e.jsx(z,{size:20,color:"primary"}),e.jsx(o,{variant:"subtitle1",sx:{fontWeight:600},children:"DIY 布局预览"}),e.jsx(i,{title:"拖拽下方组件到此区域进行自由布局",children:e.jsx(r,{size:"small",children:e.jsx(k,{size:16})})})]}),e.jsxs(s,{ref:Y,sx:{position:"relative",height:200,bgcolor:"background.paper",border:"2px dashed",borderColor:"primary.main",borderTop:"1px solid",borderTopColor:"divider",overflow:"hidden"},onMouseMove:R,onMouseUp:V,onTouchMove:R,onTouchEnd:V,children:[e.jsx(s,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:"\n                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\n                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)\n              ",backgroundSize:"20px 20px",opacity:.3}}),(N.componentPositions||[]).map((e=>E(e.id,e))),W.isDragging&&W.draggedComponent&&e.jsx(s,{sx:{position:"fixed",left:W.currentPosition.x,top:W.currentPosition.y,transform:"translate(-50%, -50%)",zIndex:1e3,opacity:.8,pointerEvents:"none"},children:E(W.draggedComponent)}),0===(N.componentPositions||[]).length&&e.jsxs(s,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",textAlign:"center",color:"text.secondary"},children:[e.jsx(M,{size:48,style:{marginBottom:8,opacity:.5}}),e.jsx(o,{variant:"body2",children:"拖拽下方组件到此区域进行自由布局"})]})]})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(o,{variant:"subtitle1",children:"可用组件"}),e.jsx(i,{title:"长按组件拖拽到预览区域进行布局",children:e.jsx(r,{size:"small",sx:{ml:1},children:e.jsx(k,{size:16})})})]}),e.jsx(a,{container:!0,spacing:2,children:Object.entries(A).map((([t,n])=>{const i=N[n.key],r=(N.componentPositions||[]).some((e=>e.id===t));return e.jsx(a,{size:{xs:6,sm:4,md:3},children:e.jsxs(l,{sx:{p:2,textAlign:"center",cursor:i?"grab":"not-allowed",opacity:i?1:.5,border:r?"2px solid":"1px solid",borderColor:r?"success.main":"divider",bgcolor:r?"success.light":"background.paper",transition:"all 0.2s ease","&:hover":i?{transform:"translateY(-2px)",boxShadow:2}:{},"&:active":i?{cursor:"grabbing",transform:"scale(0.95)"}:{}},onMouseDown:i?e=>L(t,e):void 0,onTouchStart:i?e=>L(t,e):void 0,children:[e.jsx(s,{sx:{mb:1,color:i?"primary.main":"text.disabled"},children:n.icon}),e.jsx(o,{variant:"caption",sx:{fontWeight:500,color:i?"text.primary":"text.disabled"},children:n.name}),r&&e.jsx(o,{variant:"caption",color:"success.main",sx:{display:"block",mt:.5},children:"已放置"})]})},t)}))}),e.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:2},children:"💡 提示：长按组件并拖拽到预览区域的任意位置进行自由布局。灰色组件需要先在下方开启显示。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(o,{variant:"subtitle1",children:"组件显示设置"}),e.jsx(i,{title:"控制哪些组件可以在工具栏中显示",children:e.jsx(r,{size:"small",sx:{ml:1},children:e.jsx(k,{size:16})})})]}),e.jsx(a,{container:!0,spacing:2,children:Object.entries(A).map((([t,n])=>e.jsx(a,{size:{xs:12,sm:6},children:e.jsx(c,{control:e.jsx(d,{size:"small",checked:N[n.key],onChange:e=>((e,s)=>{const o=A[e];o&&I(b({topToolbar:{...N,[o.key]:s}}))})(t,e.target.checked)}),label:e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[n.icon,e.jsx(o,{variant:"body2",children:n.name})]})})},t)))})]}),N.showModelSelector&&e.jsxs(n,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(o,{variant:"subtitle1",children:"模型选择器样式"}),e.jsx(i,{title:"选择模型选择器的显示样式",children:e.jsx(r,{size:"small",sx:{ml:1},children:e.jsx(k,{size:16})})})]}),e.jsx(x,{row:!0,children:e.jsx(c,{control:e.jsx(d,{size:"small",checked:"dialog"===N.modelSelectorStyle,onChange:e=>{I(b({topToolbar:{...N,modelSelectorStyle:e.target.checked?"dialog":"dropdown"}}))}}),label:"dialog"===N.modelSelectorStyle?"弹窗式":"下拉式"})}),e.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"图标模式可以节省空间，适合小屏设备使用。"})]}),e.jsxs(n,{elevation:0,sx:{p:2,border:"1px solid #eee",bgcolor:"info.light"},children:[e.jsx(o,{variant:"subtitle2",sx:{mb:1,fontWeight:600},children:"🎨 DIY 布局使用说明"}),e.jsxs(s,{component:"ul",sx:{pl:2,m:0},children:[e.jsx(o,{component:"li",variant:"body2",sx:{mb:.5},children:'首先在"组件显示设置"中开启需要的组件'}),e.jsx(o,{component:"li",variant:"body2",sx:{mb:.5},children:'长按"可用组件"中的组件并拖拽到预览区域'}),e.jsx(o,{component:"li",variant:"body2",sx:{mb:.5},children:"可以将组件放置在工具栏的任意位置"}),e.jsx(o,{component:"li",variant:"body2",sx:{mb:.5},children:'点击"重置布局"可以清除所有自定义位置'}),e.jsx(o,{component:"li",variant:"body2",children:"设置会实时保存并应用到聊天页面"})]})]})]})]})};export{I as default};
