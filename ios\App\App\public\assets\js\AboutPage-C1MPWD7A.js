import{z as e,j as r,B as o,A as a,T as t,I as n,b as i,E as s,G as l,P as d,D as c,H as x,J as b,m as h}from"./mui-vendor-hRDvsX89.js";import{u as g}from"./react-vendor-C9ilihHH.js";import{A as f,C as p}from"./index-BtK6VV6Z.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const m=()=>{const m=g(),u=e();return r.jsxs(o,{sx:{minHeight:"100vh",background:"linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)",display:"flex",flexDirection:"column",overflowY:"auto",WebkitOverflowScrolling:"touch"},children:[r.jsx(a,{position:"sticky",elevation:0,sx:{background:"rgba(255,255,255,0.15)",backdropFilter:"blur(12px)",boxShadow:"0 2px 16px 0 rgba(147,51,234,0.08)",borderBottom:"1.5px solid "+("dark"===u.palette.mode?"rgba(147,51,234,0.18)":"rgba(147,51,234,0.12)"),zIndex:1100},children:r.jsxs(t,{children:[r.jsx(n,{edge:"start",color:"inherit",onClick:()=>{m("/settings")},"aria-label":"back",sx:{transition:"all 0.2s","&:hover":{color:"#9333ea",transform:"scale(1.12)",background:"rgba(147,51,234,0.08)"}},children:r.jsx(f,{})}),r.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:700,letterSpacing:1.2,color:"#9333ea",textShadow:"0 2px 8px rgba(147,51,234,0.08)"},children:"关于我们"})]})}),r.jsx(s,{maxWidth:"sm",sx:{py:4,flex:1,display:"flex",flexDirection:"column",justifyContent:"flex-start",overflowY:"auto"},children:r.jsx(l,{in:!0,timeout:800,children:r.jsxs(d,{elevation:0,sx:{p:{xs:2,sm:4},borderRadius:5,background:"rgba(255,255,255,0.35)",boxShadow:"0 8px 32px 0 rgba(31,38,135,0.18)",backdropFilter:"blur(18px)",border:"1.5px solid rgba(147,51,234,0.13)",position:"relative",overflow:"visible",width:"100%"},children:[r.jsx(o,{sx:{position:"absolute",top:0,left:0,width:"100%",height:6,background:"linear-gradient(90deg,#a18cd1,#fbc2eb,#9333ea)",opacity:.18,borderTopLeftRadius:20,borderTopRightRadius:20}}),r.jsx(i,{variant:"h6",gutterBottom:!0,sx:{fontWeight:700,letterSpacing:1.1,color:"#9333ea"},children:"关于AetherLink"}),r.jsx(c,{sx:{mb:3,background:"linear-gradient(90deg,#a18cd1,#fbc2eb,#9333ea)",height:3,borderRadius:2,opacity:.25}}),r.jsx(o,{sx:{display:"flex",justifyContent:"center",mb:3},children:r.jsx(o,{sx:{width:130,height:130,borderRadius:"50%",background:"linear-gradient(135deg,#a18cd1,#fbc2eb,#9333ea)",p:"2.5px",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 24px 0 rgba(147,51,234,0.13)",animation:"floatLogo 3.2s ease-in-out infinite","@keyframes floatLogo":{"0%":{transform:"translateY(0px)"},"50%":{transform:"translateY(-10px)"},"100%":{transform:"translateY(0px)"}}},children:r.jsx("img",{src:"/assets/logo.png",alt:"AetherLink Logo",style:{width:120,height:120,objectFit:"contain",borderRadius:"50%",background:"rgba(255,255,255,0.7)",boxShadow:"0 2px 12px 0 rgba(147,51,234,0.08)"}})})}),r.jsx(i,{variant:"body1",paragraph:!0,sx:{fontSize:18,fontWeight:500,color:"#3b0764",mb:1},children:"AetherLink是一个强大的AI助手应用，支持多种大语言模型，帮助您更高效地完成工作。"}),r.jsx(i,{variant:"body1",paragraph:!0,sx:{fontSize:17,color:"#5b21b6",mb:2},children:"我们致力于为用户提供最佳的AI辅助体验，让人工智能技术真正帮助到每一个人。"}),r.jsx(x,{in:!0,direction:"up",timeout:900,children:r.jsxs(o,{sx:{mt:3,mb:3,p:2.5,borderRadius:3,bgcolor:"rgba(147,51,234,0.07)",border:"1.5px solid rgba(147,51,234,0.13)",boxShadow:"0 2px 12px 0 rgba(147,51,234,0.08)",transition:"box-shadow 0.3s","&:hover":{boxShadow:"0 6px 24px 0 rgba(147,51,234,0.18)"}},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:1},children:[r.jsx(p,{sx:{mr:1,fontSize:22,color:"#9333ea",animation:"chatIconPulse 2.2s infinite"}}),r.jsx(i,{variant:"subtitle1",fontWeight:"medium",sx:{color:"#7c3aed",fontWeight:700},children:"用户交流群"})]}),r.jsx(i,{variant:"body2",sx:{mb:1.5,color:"#6d28d9",fontWeight:500},children:"如有问题或建议，欢迎加入我们的QQ群进行反馈"}),r.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:1,flexWrap:"wrap"},children:[r.jsx(b,{label:"群号: 930126592",variant:"outlined",size:"small",sx:{borderColor:"#a18cd1",color:"#7c3aed",fontWeight:700,fontSize:15}}),r.jsx(h,{variant:"contained",size:"small",onClick:async()=>{try{window.open("http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=V-b46WoBNLIM4oc34JMULwoyJ3hyrKac&authKey=q%2FSwCcxda4e55ygtwp3h9adQXhqBLZ9wJdvM0QxTjXQkbxAa2tHoraOGy2fiibyY&noverify=0&group_code=930126592","_blank")}catch(e){console.error("打开浏览器失败:",e)}},startIcon:r.jsx(p,{fontSize:"small"}),sx:{background:"linear-gradient(90deg,#a18cd1,#fbc2eb,#9333ea)",color:"#fff",fontWeight:700,boxShadow:"0 2px 8px 0 rgba(147,51,234,0.13)",transition:"all 0.18s","&:hover":{background:"linear-gradient(90deg,#9333ea,#a18cd1,#fbc2eb)",transform:"scale(1.08)",boxShadow:"0 6px 18px 0 rgba(147,51,234,0.18)"}},children:"加入QQ群"})]})]})}),r.jsxs(i,{variant:"body2",color:"text.secondary",sx:{mt:2,fontWeight:600,fontSize:15,letterSpacing:1},children:["版本: ",r.jsx("span",{style:{color:"#9333ea",fontWeight:700},children:"0.4.5"})]}),r.jsxs(o,{sx:{mt:5,display:"flex",gap:2,justifyContent:"center",flexWrap:"wrap"},children:[r.jsx(h,{variant:"outlined",href:"https://github.com/1600822305/CS-LLM-house",target:"_blank",sx:{borderColor:"#a18cd1",color:"#9333ea",fontWeight:700,px:3,borderRadius:3,transition:"all 0.18s","&:hover":{background:"linear-gradient(90deg,#a18cd1,#fbc2eb,#9333ea)",color:"#fff",borderColor:"#9333ea",transform:"scale(1.07)"}},children:"GitHub"}),r.jsx(h,{variant:"outlined",color:"secondary",onClick:()=>m("/devtools"),sx:{borderColor:"#fbc2eb",color:"#7c3aed",fontWeight:700,px:3,borderRadius:3,transition:"all 0.18s","&:hover":{background:"linear-gradient(90deg,#fbc2eb,#a18cd1,#9333ea)",color:"#fff",borderColor:"#9333ea",transform:"scale(1.07)"}},children:"开发者工具"})]})]})})}),r.jsx("style",{children:"\n        @keyframes floatLogo {\n          0% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n          100% { transform: translateY(0px); }\n        }\n        @keyframes chatIconPulse {\n          0% { filter: drop-shadow(0 0 0 #a18cd1); }\n          50% { filter: drop-shadow(0 0 8px #a18cd1); }\n          100% { filter: drop-shadow(0 0 0 #a18cd1); }\n        }\n        /* 增强移动端滚动体验 */\n        html, body {\n          -webkit-overflow-scrolling: touch;\n        }\n      "})]})};export{m as default};
