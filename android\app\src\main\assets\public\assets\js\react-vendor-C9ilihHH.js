function e(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var o,a,i={exports:{}},l={};function u(){if(o)return l;o=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),u=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),d=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,y={};function v(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||h}function g(){}function w(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},g.prototype=v.prototype;var b=w.prototype=new g;b.constructor=w,m(b,v.prototype),b.isPureReactComponent=!0;var E=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},R=Object.prototype.hasOwnProperty;function x(t,n,r,o,a,i){return r=i.ref,{$$typeof:e,type:t,key:n,ref:void 0!==r?r:null,props:i}}function C(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var _=/\/+/g;function O(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function $(){}function T(n,r,o,a,i){var l=typeof n;"undefined"!==l&&"boolean"!==l||(n=null);var u,s,c=!1;if(null===n)c=!0;else switch(l){case"bigint":case"string":case"number":c=!0;break;case"object":switch(n.$$typeof){case e:case t:c=!0;break;case p:return T((c=n._init)(n._payload),r,o,a,i)}}if(c)return i=i(n),c=""===a?"."+O(n,0):a,E(i)?(o="",null!=c&&(o=c.replace(_,"$&/")+"/"),T(i,r,o,"",(function(e){return e}))):null!=i&&(C(i)&&(u=i,s=o+(null==i.key||n&&n.key===i.key?"":(""+i.key).replace(_,"$&/")+"/")+c,i=x(u.type,s,void 0,0,0,u.props)),r.push(i)),1;c=0;var f,h=""===a?".":a+":";if(E(n))for(var m=0;m<n.length;m++)c+=T(a=n[m],r,o,l=h+O(a,m),i);else if("function"==typeof(m=null===(f=n)||"object"!=typeof f?null:"function"==typeof(f=d&&f[d]||f["@@iterator"])?f:null))for(n=m.call(n),m=0;!(a=n.next()).done;)c+=T(a=a.value,r,o,l=h+O(a,m++),i);else if("object"===l){if("function"==typeof n.then)return T(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then($,$):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),r,o,a,i);throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return c}function k(e,t,n){if(null==e)return e;var r=[],o=0;return T(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function A(){}return l.Children={map:k,forEach:function(e,t,n){k(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return k(e,(function(){t++})),t},toArray:function(e){return k(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},l.Component=v,l.Fragment=n,l.Profiler=a,l.PureComponent=w,l.StrictMode=r,l.Suspense=c,l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,l.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},l.cache=function(e){return function(){return e.apply(null,arguments)}},l.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!R.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var i=Array(a),l=0;l<a;l++)i[l]=arguments[l+2];r.children=i}return x(e.type,o,void 0,0,0,r)},l.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},l.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)R.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var i=arguments.length-2;if(1===i)o.children=n;else if(1<i){for(var l=Array(i),u=0;u<i;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===o[r]&&(o[r]=i[r]);return x(e,a,void 0,0,0,o)},l.createRef=function(){return{current:null}},l.forwardRef=function(e){return{$$typeof:s,render:e}},l.isValidElement=C,l.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:P}},l.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},l.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),o=S.S;null!==o&&o(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(A,L)}catch(a){L(a)}finally{S.T=t}},l.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},l.use=function(e){return S.H.use(e)},l.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},l.useCallback=function(e,t){return S.H.useCallback(e,t)},l.useContext=function(e){return S.H.useContext(e)},l.useDebugValue=function(){},l.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},l.useEffect=function(e,t,n){var r=S.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},l.useId=function(){return S.H.useId()},l.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},l.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},l.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},l.useMemo=function(e,t){return S.H.useMemo(e,t)},l.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},l.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},l.useRef=function(e){return S.H.useRef(e)},l.useState=function(e){return S.H.useState(e)},l.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},l.useTransition=function(){return S.H.useTransition()},l.version="19.1.0",l}function s(){return a||(a=1,i.exports=u()),i.exports}var c=s();const f=n(c),p=e({__proto__:null,default:f},[c]);var d,h,m={exports:{}},y={};function v(){if(d)return y;d=1;var e=s();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var r={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},o=Symbol.for("react.portal");var a=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function i(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return y.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,y.createPortal=function(e,n){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,n,null,r)},y.flushSync=function(e){var t=a.T,n=r.p;try{if(a.T=null,r.p=2,e)return e()}finally{a.T=t,r.p=n,r.d.f()}},y.preconnect=function(e,t){"string"==typeof e&&(t?t="string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,r.d.C(e,t))},y.prefetchDNS=function(e){"string"==typeof e&&r.d.D(e)},y.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,o=i(n,t.crossOrigin),a="string"==typeof t.integrity?t.integrity:void 0,l="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?r.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:o,integrity:a,fetchPriority:l}):"script"===n&&r.d.X(e,{crossOrigin:o,integrity:a,fetchPriority:l,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},y.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=i(t.as,t.crossOrigin);r.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&r.d.M(e)},y.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,o=i(n,t.crossOrigin);r.d.L(e,n,{crossOrigin:o,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},y.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=i(t.as,t.crossOrigin);r.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else r.d.m(e)},y.requestFormReset=function(e){r.d.r(e)},y.unstable_batchedUpdates=function(e,t){return e(t)},y.useFormState=function(e,t,n){return a.H.useFormState(e,t,n)},y.useFormStatus=function(){return a.H.useHostTransitionStatus()},y.version="19.1.0",y}function g(){if(h)return m.exports;return h=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),m.exports=v(),m.exports}var w=g();const b=n(w),E=e({__proto__:null,default:b},[w]);var S,R={};!function(){if(S)return R;S=1,Object.defineProperty(R,"__esModule",{value:!0}),R.parse=function(e,t){const n=new a,r=e.length;if(r<2)return n;const o=(null==t?void 0:t.decode)||u;let s=0;do{const t=e.indexOf("=",s);if(-1===t)break;const a=e.indexOf(";",s),u=-1===a?r:a;if(t>u){s=e.lastIndexOf(";",t-1)+1;continue}const c=i(e,s,t),f=l(e,t,c),p=e.slice(c,f);if(void 0===n[p]){let r=i(e,t+1,u),a=l(e,u,r);const s=o(e.slice(r,a));n[p]=s}s=u+1}while(s<r);return n},R.serialize=function(a,i,l){const u=(null==l?void 0:l.encode)||encodeURIComponent;if(!e.test(a))throw new TypeError(`argument name is invalid: ${a}`);const s=u(i);if(!t.test(s))throw new TypeError(`argument val is invalid: ${i}`);let c=a+"="+s;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!n.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!r.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===o.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}l.httpOnly&&(c+="; HttpOnly");l.secure&&(c+="; Secure");l.partitioned&&(c+="; Partitioned");if(l.priority){switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}}if(l.sameSite){switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,a=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function l(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var x="popstate";function C(e={}){return function(e,t,n,r={}){let{window:o=document.defaultView,v5Compat:a=!1}=r,i=o.history,l="POP",u=null,s=c();null==s&&(s=0,i.replaceState({...i.state,idx:s},""));function c(){return(i.state||{idx:null}).idx}function f(){l="POP";let e=c(),t=null==e?null:e-s;s=e,u&&u({action:l,location:m.location,delta:t})}function p(e,t){l="PUSH";let r=T(m.location,e,t);n&&n(r,e),s=c()+1;let f=$(r,s),p=m.createHref(r);try{i.pushState(f,"",p)}catch(d){if(d instanceof DOMException&&"DataCloneError"===d.name)throw d;o.location.assign(p)}a&&u&&u({action:l,location:m.location,delta:1})}function d(e,t){l="REPLACE";let r=T(m.location,e,t);n&&n(r,e),s=c();let o=$(r,s),f=m.createHref(r);i.replaceState(o,"",f),a&&u&&u({action:l,location:m.location,delta:0})}function h(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);_(n,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:k(e);r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r);return new URL(r,n)}(e)}let m={get action(){return l},get location(){return e(o,i)},listen(e){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(x,f),u=e,()=>{o.removeEventListener(x,f),u=null}},createHref:e=>t(o,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:p,replace:d,go:e=>i.go(e)};return m}((function(e,t){let{pathname:n="/",search:r="",hash:o=""}=P(e.location.hash.substring(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),T("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let n=e.document.querySelector("base"),r="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");r=-1===n?t:t.slice(0,n)}return r+"#"+("string"==typeof t?t:k(t))}),(function(e,t){O("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)}),e)}function _(e,t){if(!1===e||null==e)throw new Error(t)}function O(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function $(e,t){return{usr:e.state,key:e.key,idx:t}}function T(e,t,n=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?P(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function k({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function P(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function L(e,t,n="/"){return function(e,t,n,r){let o="string"==typeof t?P(t):t,a=Y(o.pathname||"/",n);if(null==a)return null;let i=A(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let l=null;for(let u=0;null==l&&u<i.length;++u){let e=V(a);l=B(i[u],e,r)}return l}(e,t,n,!1)}function A(e,t=[],n=[],r=""){let o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(_(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let l=G([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(_(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),A(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:W(l,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&(null==(n=e.path)?void 0:n.includes("?")))for(let r of N(e.path))o(e,t,r);else o(e,t)})),t}function N(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=N(r.join("/")),l=[];return l.push(...i.map((e=>""===e?a:[a,e].join("/")))),o&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var j=/^:[\w-]+$/,D=3,M=2,H=1,U=10,F=-2,I=e=>"*"===e;function W(e,t){let n=e.split("/"),r=n.length;return n.some(I)&&(r+=F),t&&(r+=M),n.filter((e=>!I(e))).reduce(((e,t)=>e+(j.test(t)?D:""===t?H:U)),r)}function B(e,t,n=!1){let{routesMeta:r}=e,o={},a="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],u=l===r.length-1,s="/"===a?t:t.slice(a.length)||"/",c=z({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),f=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=z({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(o,c.params),i.push({params:o,pathname:G([a,c.pathname]),pathnameBase:X(G([a,c.pathnameBase])),route:f}),"/"!==c.pathnameBase&&(a=G([a,c.pathnameBase]))}return i}function z(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){O("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let a=new RegExp(o,t?void 0:"i");return[a,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce(((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=l[r]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const o=l[r];return e[t]=n&&!o?void 0:(o||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function V(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return O(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Y(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function J(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function K(e){let t=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function q(e,t,n,r=!1){let o;"string"==typeof e?o=P(e):(o={...e},_(!o.pathname||!o.pathname.includes("?"),J("?","pathname","search",o)),_(!o.pathname||!o.pathname.includes("#"),J("#","pathname","hash",o)),_(!o.search||!o.search.includes("#"),J("#","search","hash",o)));let a,i=""===e||""===o.pathname,l=i?"/":o.pathname;if(null==l)a=n;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}a=e>=0?t[e]:"/"}let u=function(e,t="/"){let{pathname:n,search:r="",hash:o=""}="string"==typeof e?P(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:Q(r),hash:Z(o)}}(o,a),s=l&&"/"!==l&&l.endsWith("/"),c=(i||"."===l)&&n.endsWith("/");return u.pathname.endsWith("/")||!s&&!c||(u.pathname+="/"),u}var G=e=>e.join("/").replace(/\/\/+/g,"/"),X=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Q=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Z=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var ee=["POST","PUT","PATCH","DELETE"];new Set(ee);var te=["GET",...ee];new Set(te);var ne=c.createContext(null);ne.displayName="DataRouter";var re=c.createContext(null);re.displayName="DataRouterState";var oe=c.createContext({isTransitioning:!1});oe.displayName="ViewTransition",c.createContext(new Map).displayName="Fetchers",c.createContext(null).displayName="Await";var ae=c.createContext(null);ae.displayName="Navigation";var ie=c.createContext(null);ie.displayName="Location";var le=c.createContext({outlet:null,matches:[],isDataRoute:!1});le.displayName="Route";var ue=c.createContext(null);function se(){return null!=c.useContext(ie)}function ce(){return _(se(),"useLocation() may be used only in the context of a <Router> component."),c.useContext(ie).location}ue.displayName="RouteError";var fe="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function pe(e){c.useContext(ae).static||c.useLayoutEffect(e)}function de(){let{isDataRoute:e}=c.useContext(le);return e?function(){let{router:e}=function(e){let t=c.useContext(ne);return _(t,Ee(e)),t}("useNavigate"),t=Se("useNavigate"),n=c.useRef(!1);return pe((()=>{n.current=!0})),c.useCallback((async(r,o={})=>{O(n.current,fe),n.current&&("number"==typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...o}))}),[e,t])}():function(){_(se(),"useNavigate() may be used only in the context of a <Router> component.");let e=c.useContext(ne),{basename:t,navigator:n}=c.useContext(ae),{matches:r}=c.useContext(le),{pathname:o}=ce(),a=JSON.stringify(K(r)),i=c.useRef(!1);return pe((()=>{i.current=!0})),c.useCallback(((r,l={})=>{if(O(i.current,fe),!i.current)return;if("number"==typeof r)return void n.go(r);let u=q(r,JSON.parse(a),o,"path"===l.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:G([t,u.pathname])),(l.replace?n.replace:n.push)(u,l.state,l)}),[t,n,a,o,e])}()}function he(){let{matches:e}=c.useContext(le),t=e[e.length-1];return t?t.params:{}}function me(e,{relative:t}={}){let{matches:n}=c.useContext(le),{pathname:r}=ce(),o=JSON.stringify(K(n));return c.useMemo((()=>q(e,JSON.parse(o),r,"path"===t)),[e,o,r,t])}function ye(e,t,n,r){var o;_(se(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a,static:i}=c.useContext(ae),{matches:l}=c.useContext(le),u=l[l.length-1],s=u?u.params:{},f=u?u.pathname:"/",p=u?u.pathnameBase:"/",d=u&&u.route;{let e=d&&d.path||"";xe(f,!d||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${f}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let h,m=ce();if(t){let e="string"==typeof t?P(t):t;_("/"===p||(null==(o=e.pathname)?void 0:o.startsWith(p)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${p}" but pathname "${e.pathname}" was given in the \`location\` prop.`),h=e}else h=m;let y=h.pathname||"/",v=y;if("/"!==p){let e=p.replace(/^\//,"").split("/");v="/"+y.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=!i&&n&&n.matches&&n.matches.length>0?n.matches:L(e,{pathname:v});O(d||null!=g,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),O(null==g||void 0!==g[g.length-1].route.element||void 0!==g[g.length-1].route.Component||void 0!==g[g.length-1].route.lazy,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let w=function(e,t=[],n=null){if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=null==n?void 0:n.errors;if(null!=o){let e=r.findIndex((e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id])));_(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let a=!1,i=-1;if(n)for(let l=0;l<r.length;l++){let e=r[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(i=l),e.route.id){let{loaderData:t,errors:o}=n,l=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!o||void 0===o[e.route.id]);if(e.route.lazy||l){a=!0,r=i>=0?r.slice(0,i+1):[r[0]];break}}}return r.reduceRight(((e,l,u)=>{let s,f=!1,p=null,d=null;n&&(s=o&&l.route.id?o[l.route.id]:void 0,p=l.route.errorElement||ge,a&&(i<0&&0===u?(xe("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,d=null):i===u&&(f=!0,d=l.route.hydrateFallbackElement||null)));let h=t.concat(r.slice(0,u+1)),m=()=>{let t;return t=s?p:f?d:l.route.Component?c.createElement(l.route.Component,null):l.route.element?l.route.element:e,c.createElement(be,{match:l,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(l.route.ErrorBoundary||l.route.errorElement||0===u)?c.createElement(we,{location:n.location,revalidation:n.revalidation,component:p,error:s,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}(g&&g.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:G([p,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:G([p,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&w?c.createElement(ie.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:"POP"}},w):w}function ve(){let e=function(){var e;let t=c.useContext(ue),n=function(e){let t=c.useContext(re);return _(t,Ee(e)),t}("useRouteError"),r=Se("useRouteError");if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},a={padding:"2px 4px",backgroundColor:r},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=c.createElement(c.Fragment,null,c.createElement("p",null,"💿 Hey developer 👋"),c.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",c.createElement("code",{style:a},"ErrorBoundary")," or"," ",c.createElement("code",{style:a},"errorElement")," prop on your route.")),c.createElement(c.Fragment,null,c.createElement("h2",null,"Unexpected Application Error!"),c.createElement("h3",{style:{fontStyle:"italic"}},t),n?c.createElement("pre",{style:o},n):null,i)}c.createContext(null);var ge=c.createElement(ve,null),we=class extends c.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?c.createElement(le.Provider,{value:this.props.routeContext},c.createElement(ue.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function be({routeContext:e,match:t,children:n}){let r=c.useContext(ne);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),c.createElement(le.Provider,{value:e},n)}function Ee(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Se(e){let t=function(e){let t=c.useContext(le);return _(t,Ee(e)),t}(e),n=t.matches[t.matches.length-1];return _(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}var Re={};function xe(e,t,n){t||Re[e]||(Re[e]=!0,O(!1,n))}function Ce({to:e,replace:t,state:n,relative:r}){_(se(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=c.useContext(ae);O(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:a}=c.useContext(le),{pathname:i}=ce(),l=de(),u=q(e,K(a),i,"path"===r),s=JSON.stringify(u);return c.useEffect((()=>{l(JSON.parse(s),{replace:t,state:n,relative:r})}),[l,s,r,t,n]),null}function _e(e){_(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Oe({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:o,static:a=!1}){_(!se(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),l=c.useMemo((()=>({basename:i,navigator:o,static:a,future:{}})),[i,o,a]);"string"==typeof n&&(n=P(n));let{pathname:u="/",search:s="",hash:f="",state:p=null,key:d="default"}=n,h=c.useMemo((()=>{let e=Y(u,i);return null==e?null:{location:{pathname:e,search:s,hash:f,state:p,key:d},navigationType:r}}),[i,u,s,f,p,d,r]);return O(null!=h,`<Router basename="${i}"> is not able to match the URL "${u}${s}${f}" because it does not start with the basename, so the <Router> won't render anything.`),null==h?null:c.createElement(ae.Provider,{value:l},c.createElement(ie.Provider,{children:t,value:h}))}function $e({children:e,location:t}){return ye(Te(e),t)}function Te(e,t=[]){let n=[];return c.Children.forEach(e,((e,r)=>{if(!c.isValidElement(e))return;let o=[...t,r];if(e.type===c.Fragment)return void n.push.apply(n,Te(e.props.children,o));_(e.type===_e,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),_(!e.props.index||!e.props.children,"An index route cannot have child routes.");let a={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(a.children=Te(e.props.children,o)),n.push(a)})),n}c.memo((function({routes:e,future:t,state:n}){return ye(e,void 0,n,t)}));var ke="get",Pe="application/x-www-form-urlencoded";function Le(e){return null!=e&&"string"==typeof e.tagName}var Ae=null;var Ne=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function je(e){return null==e||Ne.has(e)?e:(O(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Pe}"`),null)}function De(e,t){let n,r,o,a,i;if(Le(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?Y(i,t):null,n=e.getAttribute("method")||ke,o=je(e.getAttribute("enctype"))||Pe,a=new FormData(e)}else if(function(e){return Le(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Le(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?Y(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||ke,o=je(e.getAttribute("formenctype"))||je(i.getAttribute("enctype"))||Pe,a=new FormData(i,e),!function(){if(null===Ae)try{new FormData(document.createElement("form"),0),Ae=!1}catch(e){Ae=!0}return Ae}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";a.append(`${e}x`,"0"),a.append(`${e}y`,"0")}else t&&a.append(t,r)}}else{if(Le(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ke,r=null,o=Pe,i=e}var l;return a&&"text/plain"===o&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:a,body:i}}function Me(e,t){if(!1===e||null==e)throw new Error(t)}function He(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function Ue(e,t,n){return function(e,t){let n=new Set;return new Set(t),e.reduce(((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e}),[])}((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(He).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}function Fe(e,t,n,r,o,a){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null==(r=n[t].route.path)?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===a?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===a?t.filter(((t,a)=>{var u;let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,a)||l(t,a))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:(null==(u=n[0])?void 0:u.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0})):[]}function Ie(e,t,{includeHydrateFallback:n}={}){return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o})).flat(1),[...new Set(r)];var r}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function We(){let e=c.useContext(ne);return Me(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var Be=c.createContext(void 0);function ze(){let e=c.useContext(Be);return Me(e,"You must render this element inside a <HydratedRouter> element"),e}function Ve(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Ye({page:e,...t}){let{router:n}=We(),r=c.useMemo((()=>L(n.routes,e,n.basename)),[n.routes,e,n.basename]);return r?c.createElement(Je,{page:e,matches:r,...t}):null}function Je({page:e,matches:t,...n}){let r=ce(),{manifest:o,routeModules:a}=ze(),{basename:i}=We(),{loaderData:l,matches:u}=function(){let e=c.useContext(re);return Me(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),s=c.useMemo((()=>Fe(e,t,u,o,r,"data")),[e,t,u,o,r]),f=c.useMemo((()=>Fe(e,t,u,o,r,"assets")),[e,t,u,o,r]),p=c.useMemo((()=>{if(e===r.pathname+r.search+r.hash)return[];let n=new Set,u=!1;if(t.forEach((e=>{var t;let r=o.routes[e.route.id];r&&r.hasLoader&&(!s.some((t=>t.route.id===e.route.id))&&e.route.id in l&&(null==(t=a[e.route.id])?void 0:t.shouldRevalidate)||r.hasClientLoader?u=!0:n.add(e.route.id))})),0===n.size)return[];let c=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===Y(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(e,i);return u&&n.size>0&&c.searchParams.set("_routes",t.filter((e=>n.has(e.route.id))).map((e=>e.route.id)).join(",")),[c.pathname+c.search]}),[i,l,r,o,s,t,e,a]),d=c.useMemo((()=>Ie(f,o)),[f,o]),h=function(e){let{manifest:t,routeModules:n}=ze(),[r,o]=c.useState([]);return c.useEffect((()=>{let r=!1;return Ue(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),r}(f);return c.createElement(c.Fragment,null,p.map((e=>c.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...n}))),d.map((e=>c.createElement("link",{key:e,rel:"modulepreload",href:e,...n}))),h.map((({key:e,link:t})=>c.createElement("link",{key:e,...t}))))}function Ke(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}Be.displayName="FrameworkContext";var qe="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{qe&&(window.__reactRouterVersion="7.6.0")}catch(nt){}function Ge({basename:e,children:t,window:n}){let r=c.useRef();null==r.current&&(r.current=C({window:n,v5Compat:!0}));let o=r.current,[a,i]=c.useState({action:o.action,location:o.location}),l=c.useCallback((e=>{c.startTransition((()=>i(e)))}),[i]);return c.useLayoutEffect((()=>o.listen(l)),[o,l]),c.createElement(Oe,{basename:e,children:t,location:a.location,navigationType:a.action,navigator:o})}var Xe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Qe=c.forwardRef((function({onClick:e,discover:t="render",prefetch:n="none",relative:r,reloadDocument:o,replace:a,state:i,target:l,to:u,preventScrollReset:s,viewTransition:f,...p},d){let h,{basename:m}=c.useContext(ae),y="string"==typeof u&&Xe.test(u),v=!1;if("string"==typeof u&&y&&(h=u,qe))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=Y(t.pathname,m);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:v=!0}catch(nt){O(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let g=function(e,{relative:t}={}){_(se(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=c.useContext(ae),{hash:o,pathname:a,search:i}=me(e,{relative:t}),l=a;return"/"!==n&&(l="/"===a?n:G([n,a])),r.createHref({pathname:l,search:i,hash:o})}(u,{relative:r}),[w,b,E]=function(e,t){let n=c.useContext(Be),[r,o]=c.useState(!1),[a,i]=c.useState(!1),{onFocus:l,onBlur:u,onMouseEnter:s,onMouseLeave:f,onTouchStart:p}=t,d=c.useRef(null);c.useEffect((()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{i(e.isIntersecting)}))}),{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}}),[e]),c.useEffect((()=>{if(r){let e=setTimeout((()=>{i(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let h=()=>{o(!0)},m=()=>{o(!1),i(!1)};return n?"intent"!==e?[a,d,{}]:[a,d,{onFocus:Ve(l,h),onBlur:Ve(u,m),onMouseEnter:Ve(s,h),onMouseLeave:Ve(f,m),onTouchStart:Ve(p,h)}]:[!1,d,{}]}(n,p),S=function(e,{target:t,replace:n,state:r,preventScrollReset:o,relative:a,viewTransition:i}={}){let l=de(),u=ce(),s=me(e,{relative:a});return c.useCallback((c=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(c,t)){c.preventDefault();let t=void 0!==n?n:k(u)===k(s);l(e,{replace:t,state:r,preventScrollReset:o,relative:a,viewTransition:i})}}),[u,l,s,n,r,t,e,o,a,i])}(u,{replace:a,state:i,target:l,preventScrollReset:s,relative:r,viewTransition:f});let R=c.createElement("a",{...p,...E,href:h||g,onClick:v||o?e:function(t){e&&e(t),t.defaultPrevented||S(t)},ref:Ke(d,b),target:l,"data-discover":y||"render"!==t?void 0:"true"});return w&&!y?c.createElement(c.Fragment,null,R,c.createElement(Ye,{page:g})):R}));function Ze(e){let t=c.useContext(ne);return _(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Qe.displayName="Link",c.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:n="",end:r=!1,style:o,to:a,viewTransition:i,children:l,...u},s){let f=me(a,{relative:u.relative}),p=ce(),d=c.useContext(re),{navigator:h,basename:m}=c.useContext(ae),y=null!=d&&function(e,t={}){let n=c.useContext(oe);_(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Ze("useViewTransitionState"),o=me(e,{relative:t.relative});if(!n.isTransitioning)return!1;let a=Y(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=Y(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=z(o.pathname,i)||null!=z(o.pathname,a)}(f)&&!0===i,v=h.encodeLocation?h.encodeLocation(f).pathname:f.pathname,g=p.pathname,w=d&&d.navigation&&d.navigation.location?d.navigation.location.pathname:null;t||(g=g.toLowerCase(),w=w?w.toLowerCase():null,v=v.toLowerCase()),w&&m&&(w=Y(w,m)||w);const b="/"!==v&&v.endsWith("/")?v.length-1:v.length;let E,S=g===v||!r&&g.startsWith(v)&&"/"===g.charAt(b),R=null!=w&&(w===v||!r&&w.startsWith(v)&&"/"===w.charAt(v.length)),x={isActive:S,isPending:R,isTransitioning:y},C=S?e:void 0;E="function"==typeof n?n(x):[n,S?"active":null,R?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let O="function"==typeof o?o(x):o;return c.createElement(Qe,{...u,"aria-current":C,className:E,ref:s,style:O,to:a,viewTransition:i},"function"==typeof l?l(x):l)})).displayName="NavLink",c.forwardRef((({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:o,state:a,method:i=ke,action:l,onSubmit:u,relative:s,preventScrollReset:f,viewTransition:p,...d},h)=>{let m=function(){let{router:e}=Ze("useSubmit"),{basename:t}=c.useContext(ae),n=Se("useRouteId");return c.useCallback((async(r,o={})=>{let{action:a,method:i,encType:l,formData:u,body:s}=De(r,t);if(!1===o.navigate){let t=o.fetcherKey||tt();await e.fetch(t,n,o.action||a,{preventScrollReset:o.preventScrollReset,formData:u,body:s,formMethod:o.method||i,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:u,body:s,formMethod:o.method||i,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})}),[e,t,n])}(),y=function(e,{relative:t}={}){let{basename:n}=c.useContext(ae),r=c.useContext(le);_(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),a={...me(e||".",{relative:t})},i=ce();if(null==e){a.search=i.search;let e=new URLSearchParams(a.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();a.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(a.pathname="/"===a.pathname?n:G([n,a.pathname]));return k(a)}(l,{relative:s}),v="get"===i.toLowerCase()?"get":"post",g="string"==typeof l&&Xe.test(l);return c.createElement("form",{ref:h,method:v,action:y,onSubmit:r?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,l=(null==r?void 0:r.getAttribute("formmethod"))||i;m(r||e.currentTarget,{fetcherKey:t,method:l,navigate:n,replace:o,state:a,relative:s,preventScrollReset:f,viewTransition:p})},...d,"data-discover":g||"render"!==e?void 0:"true"})})).displayName="Form";var et=0,tt=()=>`__${String(++et)}__`;export{Ge as H,Ce as N,p as R,f as a,b,w as c,t as d,he as e,ce as f,$e as g,_e as h,s as i,r as j,g as k,n as l,E as m,c as r,de as u};
