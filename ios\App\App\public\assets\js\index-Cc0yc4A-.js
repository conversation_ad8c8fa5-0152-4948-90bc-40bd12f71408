import{j as e,B as o,a as r,A as a,T as i,I as s,b as l,P as d,D as t,L as n,w as c,x,h as p,t as b,m as g}from"./mui-vendor-hRDvsX89.js";import{u as h,r as m}from"./react-vendor-C9ilihHH.js";import{o as u,d as j,A as v,b as f}from"./index-BtK6VV6Z.js";import{D as w}from"./DialogModelSelector-BK--n_cz.js";import{D as k}from"./DropdownModelSelector-DlmZp9jO.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const M=()=>{const M=h(),y=u(),C=j((e=>e.settings.defaultModelId)),S=j((e=>e.settings.topicNamingModelId)),I=j((e=>e.settings.providers)),N=j((e=>e.settings.modelSelectorStyle||"dialog")),D=j((e=>e.settings.enableTopicNaming)),P=j((e=>e.settings.topicNamingPrompt)),[R,W]=m.useState(!1),z=I.flatMap((e=>e.models.filter((e=>e.enabled)).map((o=>({...o,providerName:e.name}))))),A=z.find((e=>e.id===(S||C)))||null,G=e=>{y(f({topicNamingModelId:e.id})),W(!1)};return e.jsxs(o,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?r(e.palette.primary.main,.02):r(e.palette.background.default,.9)},children:[e.jsx(a,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(i,{children:[e.jsx(s,{edge:"start",color:"inherit",onClick:()=>{M("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(v,{})}),e.jsx(l,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #4f46e5, #8b5cf6)",backgroundClip:"text",color:"transparent"},children:"话题命名设置"})]})}),e.jsxs(o,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsxs(d,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(o,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(l,{variant:"subtitle1",sx:{fontWeight:600},children:"话题命名模型"}),e.jsx(l,{variant:"body2",color:"text.secondary",children:"选择用于自动为话题生成标题的AI模型"})]}),e.jsx(t,{}),e.jsx(o,{sx:{p:2},children:"dropdown"===N?e.jsx(k,{selectedModel:A,availableModels:z,handleModelSelect:G}):e.jsx(w,{selectedModel:A,availableModels:z,handleModelSelect:G,handleMenuClick:()=>{W(!0)},handleMenuClose:()=>{W(!1)},menuOpen:R})})]}),e.jsxs(d,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(o,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(l,{variant:"subtitle1",sx:{fontWeight:600},children:"自动命名话题"}),e.jsx(l,{variant:"body2",color:"text.secondary",children:"在对话进行3轮后，自动为话题生成标题"})]}),e.jsx(t,{}),e.jsx(n,{disablePadding:!0,children:e.jsxs(c,{children:[e.jsx(x,{primary:"自动命名话题"}),e.jsx(p,{edge:"end",checked:D,onChange:e=>{const o=e.target.checked;y(f({enableTopicNaming:o}))},inputProps:{"aria-labelledby":"enable-topic-naming-switch"}})]})})]}),e.jsxs(d,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(o,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(l,{variant:"subtitle1",sx:{fontWeight:600},children:"话题命名提示词"}),e.jsx(l,{variant:"body2",color:"text.secondary",children:"自定义话题命名时使用的提示词，留空则使用默认提示词"})]}),e.jsx(t,{}),e.jsxs(o,{sx:{p:2},children:[e.jsx(b,{fullWidth:!0,multiline:!0,rows:4,value:P,onChange:e=>{const o=e.target.value;y(f({topicNamingPrompt:o}))},placeholder:"你是一个话题生成专家。根据对话内容生成一个简洁、精确、具有描述性的标题。标题应简洁，不超过10个字。你只需要返回标题文本，不需要解释或扩展。",variant:"outlined",sx:{"& .MuiOutlinedInput-root":{borderRadius:1}}}),P&&e.jsx(g,{variant:"outlined",size:"small",sx:{mt:1},onClick:()=>y(f({topicNamingPrompt:""})),children:"重置为默认"})]})]})]})]})};export{M as default};
