import{c as e,j as o,B as r,b as s,P as i,f as l,g as t,h as n,A as a,T as d,I as c}from"./mui-vendor-DsBXMegs.js";import{u as p,r as x}from"./react-vendor-Be-rfjCm.js";import{u as h,a as m,A as j,_ as b,b as u}from"./index-Ck4sQVom.js";import{M as g}from"./ModelSelector-CylWtXBN.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";import"./DialogModelSelector-CIEKvKif.js";import"./DropdownModelSelector-Xqi36qMb.js";const v=e(o.jsx("path",{d:"M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z"})),f=()=>{const e=p(),f=h(),y=m((e=>e.settings)),[S,w]=x.useState(!1),C=y.topToolbar||{showSettingsButton:!0,showModelSelector:!0,modelSelectorStyle:"dialog",showChatTitle:!0,showTopicName:!1,showNewTopicButton:!1,showClearButton:!1,showMenuButton:!0},T=[{id:"gpt-4",name:"GPT-4",provider:"openai",description:"OpenAI GPT-4 模型"},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",description:"OpenAI GPT-3.5 Turbo 模型"},{id:"claude-3",name:"Claude 3",provider:"anthropic",description:"Anthropic Claude 3 模型"}],M=T[0];return o.jsxs(r,{sx:{height:"100vh",backgroundColor:"background.default",display:"flex",flexDirection:"column",overflow:"hidden"},children:[o.jsxs(r,{sx:{display:"flex",alignItems:"center",padding:2,borderBottom:1,borderColor:"divider",backgroundColor:"background.paper",zIndex:10,flexShrink:0},children:[o.jsx(j,{sx:{mr:2,cursor:"pointer"},onClick:()=>{e("/settings/appearance")}}),o.jsx(s,{variant:"h6",color:"primary",sx:{flexGrow:1},children:"模型选择器测试页面"})]}),o.jsxs(r,{sx:{p:2,flex:1,overflow:"auto"},children:[o.jsxs(i,{elevation:0,sx:{p:2,mb:3,border:"1px solid #eee"},children:[o.jsx(s,{variant:"subtitle1",sx:{mb:2},children:"模型选择器样式控制"}),o.jsx(l,{children:o.jsx(t,{control:o.jsx(n,{checked:"dialog"===C.modelSelectorStyle,onChange:()=>{const e="dialog"===C.modelSelectorStyle?"dropdown":"dialog";f(u({topToolbar:{...C,modelSelectorStyle:e}}))}}),label:"当前样式: "+("dialog"===C.modelSelectorStyle?"弹窗模式":"下拉模式")})}),o.jsx(s,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"切换开关来测试不同的模型选择器样式。弹窗模式使用对话框，下拉模式使用下拉菜单。"})]}),o.jsxs(i,{elevation:2,sx:{mb:3,overflow:"hidden"},children:[o.jsx(s,{variant:"subtitle2",sx:{p:2,pb:1,fontWeight:600},children:"模型选择器测试"}),o.jsx(a,{position:"static",elevation:0,sx:{bgcolor:"background.paper",color:"text.primary",borderTop:"1px solid",borderColor:"divider"},children:o.jsxs(d,{sx:{justifyContent:"space-between",minHeight:"56px !important"},children:[o.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1},children:[o.jsx(c,{edge:"start",color:"inherit",size:"small",children:o.jsx(v,{})}),o.jsx(s,{variant:"h6",noWrap:!0,component:"div",children:"对话"})]}),o.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1},children:[o.jsx(g,{selectedModel:M,availableModels:T,handleModelSelect:e=>{w(!1)},handleMenuClick:()=>{w(!0)},handleMenuClose:()=>{w(!1)},menuOpen:S}),o.jsx(c,{color:"inherit",onClick:()=>e("/settings"),children:o.jsx(b,{})})]})]})}),o.jsx(s,{variant:"caption",sx:{p:2,pt:1,color:"text.secondary",display:"block"},children:"点击模型选择器测试弹出功能。在弹窗模式下，点击按钮应该能正确打开模型选择对话框。"})]}),o.jsxs(i,{elevation:0,sx:{p:2,border:"1px solid #eee",bgcolor:"info.light"},children:[o.jsx(s,{variant:"subtitle2",sx:{mb:1,fontWeight:600},children:"🔧 测试说明"}),o.jsxs(r,{component:"ul",sx:{pl:2,m:0},children:[o.jsx(s,{component:"li",variant:"body2",sx:{mb:.5},children:"切换上方的开关来改变模型选择器样式"}),o.jsx(s,{component:"li",variant:"body2",sx:{mb:.5},children:"在弹窗模式下，应该显示带文字的按钮"}),o.jsx(s,{component:"li",variant:"body2",sx:{mb:.5},children:"在下拉模式下，应该显示下拉选择器"}),o.jsx(s,{component:"li",variant:"body2",children:"无论哪种模式，点击都应该能正确打开模型选择界面"})]})]})]})]})};export{f as default};
