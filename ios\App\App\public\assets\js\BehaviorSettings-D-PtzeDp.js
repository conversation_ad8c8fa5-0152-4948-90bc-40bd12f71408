import{c as e,j as r,B as s,a,A as o,T as t,I as i,b as l,P as n,D as d,L as x,w as c,g as h,Q as b,y as p,x as m,h as j}from"./mui-vendor-DsBXMegs.js";import{u as g}from"./react-vendor-Be-rfjCm.js";import{u as f,a as u,A as y,ad as v,ae as w}from"./index-Dnlt-eWK.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const k=e(r.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),C=e(r.jsx("path",{d:"M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"})),W=()=>{const e=g(),W=f(),I=u((e=>e.settings));return r.jsxs(s,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?a(e.palette.primary.main,.02):a(e.palette.background.default,.9)},children:[r.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(t,{children:[r.jsx(i,{edge:"start",color:"inherit",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(y,{})}),r.jsx(l,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"行为设置"})]})}),r.jsx(s,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:r.jsxs(n,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(s,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[r.jsx(l,{variant:"subtitle1",sx:{fontWeight:600},children:"交互行为"}),r.jsx(l,{variant:"body2",color:"text.secondary",children:"自定义应用的交互方式和通知设置"})]}),r.jsx(d,{}),r.jsxs(x,{disablePadding:!0,children:[r.jsx(c,{disablePadding:!0,children:r.jsx(s,{sx:{width:"100%",p:2},children:r.jsx(h,{sx:{display:"flex",justifyContent:"space-between",width:"100%",m:0,"& .MuiFormControlLabel-label":{flex:1}},labelPlacement:"start",control:r.jsx(j,{checked:I.sendWithEnter,onChange:e=>W(v(e.target.checked)),color:"primary",sx:{ml:2}}),label:r.jsxs(s,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(b,{sx:{minWidth:"auto",mr:2},children:r.jsx(p,{sx:{bgcolor:a("#06b6d4",.12),color:"#06b6d4",boxShadow:"0 2px 6px rgba(0,0,0,0.05)",width:40,height:40},children:r.jsx(C,{})})}),r.jsx(m,{primary:r.jsx(l,{sx:{fontWeight:600,color:"text.primary"},children:"使用Enter键发送消息"}),secondary:"按Enter键快速发送消息，使用Shift+Enter添加换行"})]})})})}),r.jsx(d,{variant:"inset",component:"li",sx:{ml:0}}),r.jsx(c,{disablePadding:!0,children:r.jsx(s,{sx:{width:"100%",p:2},children:r.jsx(h,{sx:{display:"flex",justifyContent:"space-between",width:"100%",m:0,"& .MuiFormControlLabel-label":{flex:1}},labelPlacement:"start",control:r.jsx(j,{checked:I.enableNotifications,onChange:e=>W(w(e.target.checked)),color:"primary",sx:{ml:2}}),label:r.jsxs(s,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(b,{sx:{minWidth:"auto",mr:2},children:r.jsx(p,{sx:{bgcolor:a("#8b5cf6",.12),color:"#8b5cf6",boxShadow:"0 2px 6px rgba(0,0,0,0.05)",width:40,height:40},children:r.jsx(k,{})})}),r.jsx(m,{primary:r.jsx(l,{sx:{fontWeight:600,color:"text.primary"},children:"启用通知"}),secondary:"当收到新消息或有重要更新时接收通知提醒"})]})})})})]})]})})]})};export{W as default};
