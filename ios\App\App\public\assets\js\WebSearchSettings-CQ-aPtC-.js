import{j as e,B as a,a as l,A as r,T as s,I as i,b as t,P as n,f as d,g as o,d as c,h as x,D as h,F as b,e as m,S as j,M as u,O as p,t as v,n as g,k as f,l as y,K as k,m as C,i as I}from"./mui-vendor-hRDvsX89.js";import{u as w,r as S}from"./react-vendor-C9ilihHH.js";import{o as P,d as A,A as B,t as W,e as R,r as z,s as T,v as K,w as L,x as D,y as U,z as F,B as E,F as M,G,H,J as O,K as _,L as Q,N as V,O as J,P as N,Q as X,R as Y,U as Z,V as $,X as q,Y as ee,Z as ae}from"./index-BtK6VV6Z.js";import{A as le}from"./Add-B_Z45Y27.js";import{E as re}from"./Edit-D9CSXufJ.js";import{L as se}from"./Language-u5hXJLQL.js";import{I as ie}from"./InfoOutlined-qyCTMRwB.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const te=()=>{var te;const ne=w(),de=P(),oe=A((e=>e.webSearch))||{enabled:!1,provider:"firecrawl",apiKey:"",includeInContext:!0,maxResults:5,showTimestamp:!0,filterSafeSearch:!0,searchMode:"auto",searchWithTime:!1,excludeDomains:[],providers:[],customProviders:[]},[ce,xe]=S.useState(null),[he,be]=S.useState(!1);S.useEffect((()=>{}),[oe]);const me=(e,a)=>{ce&&xe((l=>({...l,[e]:a})))};return e.jsxs(a,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?l(e.palette.primary.main,.02):l(e.palette.background.default,.9)},children:[e.jsx(r,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(s,{children:[e.jsx(i,{edge:"start",onClick:()=>{ne("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(B,{})}),e.jsxs(t,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,display:"flex",alignItems:"center",gap:1,color:e=>e.palette.text.primary},children:[e.jsx(se,{sx:{color:"#3b82f6"}})," 网络搜索设置"]})]})}),e.jsxs(a,{sx:{flexGrow:1,overflow:"auto",px:2,py:2,mt:8,mb:2,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsxs(n,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:"1px solid",borderColor:"divider"},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,color:e=>e.palette.text.primary,mb:2},children:"基本设置"}),e.jsx(d,{children:e.jsx(o,{control:e.jsx(x,{checked:oe.enabled,onChange:()=>{de(E())},color:"primary"}),label:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{mr:1},children:"启用网络搜索"}),e.jsx(c,{title:"开启后，AI可以通过网络搜索获取最新信息",children:e.jsx(ie,{fontSize:"small",sx:{color:"text.secondary"}})})]})})}),e.jsx(h,{sx:{my:2}}),e.jsxs(b,{fullWidth:!0,margin:"normal",children:[e.jsx(m,{id:"search-provider-label",children:"搜索服务商"}),e.jsxs(j,{labelId:"search-provider-label",value:oe.provider,onChange:e=>{de(M(e.target.value))},input:e.jsx(p,{label:"搜索服务商"}),disabled:!oe.enabled,children:[e.jsx(u,{value:"bing-free",children:"🆓 Bing 免费搜索 (推荐)"}),e.jsx(u,{value:"tavily",children:"💎 Tavily (付费)"}),e.jsx(u,{value:"exa",children:"🧠 Exa (神经搜索)"}),e.jsx(u,{value:"bocha",children:"🤖 Bocha (AI搜索)"}),e.jsx(u,{value:"firecrawl",children:" Firecrawl (网页抓取)"}),e.jsx(u,{value:"custom",children:"⚙️ 自定义服务"})]})]}),"custom"!==oe.provider&&e.jsxs(e.Fragment,{children:[e.jsx(v,{fullWidth:!0,margin:"normal",label:"API 密钥",type:"password",value:oe.apiKeys&&oe.apiKeys[oe.provider]||oe.apiKey||"",onChange:e=>{de(G(e.target.value))},disabled:!oe.enabled,variant:"outlined",placeholder:`请输入 ${oe.provider} API 密钥`,slotProps:{input:{"aria-invalid":!1,"aria-describedby":"websearch-api-key-helper-text"},formHelperText:{id:"websearch-api-key-helper-text"}}}),!1,"tavily"===oe.provider&&e.jsxs(g,{severity:"info",sx:{mt:2},children:["Tavily 是专为AI设计的搜索API，提供高质量的搜索结果。现在使用移动端兼容的 SDK，完全避免了 CORS 限制问题。访问",e.jsx("a",{href:"https://app.tavily.com",target:"_blank",rel:"noopener noreferrer",style:{marginLeft:5},children:"app.tavily.com"}),"获取 API 密钥。"]}),"exa"===oe.provider&&e.jsxs(g,{severity:"info",sx:{mt:2},children:["Exa 是基于神经网络的搜索引擎，提供语义搜索功能。访问",e.jsx("a",{href:"https://exa.ai",target:"_blank",rel:"noopener noreferrer",style:{marginLeft:5},children:"exa.ai"}),"获取 API 密钥。"]}),"bocha"===oe.provider&&e.jsxs(g,{severity:"info",sx:{mt:2},children:["Bocha 是AI驱动的搜索引擎，提供智能搜索结果。访问",e.jsx("a",{href:"https://bochaai.com",target:"_blank",rel:"noopener noreferrer",style:{marginLeft:5},children:"bochaai.com"}),"获取 API 密钥。"]}),"firecrawl"===oe.provider&&e.jsxs(g,{severity:"info",sx:{mt:2},children:["Firecrawl 提供强大的网络爬取和搜索功能。访问",e.jsx("a",{href:"https://firecrawl.dev",target:"_blank",rel:"noopener noreferrer",style:{marginLeft:5},children:"firecrawl.dev"}),"获取 API 密钥。"]})]}),"custom"===oe.provider&&oe.customProviders&&oe.customProviders.length>0&&e.jsxs(a,{sx:{mt:2},children:[e.jsx(t,{variant:"subtitle1",gutterBottom:!0,children:"自定义搜索服务列表"}),oe.customProviders.map((r=>e.jsxs(f,{variant:"outlined",sx:{mb:2,borderColor:r.enabled?l("#3b82f6",.5):"divider"},children:[e.jsxs(y,{children:[e.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(t,{variant:"h6",children:r.name}),e.jsx(o,{control:e.jsx(x,{size:"small",checked:r.enabled,onChange:()=>de(W(r.id))}),label:"启用"})]}),e.jsxs(t,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["API URL: ",r.baseUrl]})]}),e.jsxs(k,{children:[e.jsx(C,{size:"small",startIcon:e.jsx(re,{}),onClick:()=>(e=>{xe({...e}),be(!0)})(r),children:"编辑"}),e.jsx(C,{size:"small",startIcon:e.jsx(R,{}),color:"error",onClick:()=>{return e=r.id,void de(H(e));var e},children:"删除"})]})]},r.id)))]}),"custom"===oe.provider&&e.jsx(C,{startIcon:e.jsx(le,{}),variant:"outlined",sx:{mt:2},onClick:()=>{const e={id:O(),name:"新搜索服务",apiKey:"",baseUrl:"",enabled:!0};xe(e),be(!0)},disabled:!oe.enabled,children:"添加自定义搜索服务"})]}),e.jsxs(n,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:"1px solid",borderColor:"divider"},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,color:e=>e.palette.text.primary,mb:2},children:"搜索选项"}),e.jsxs(b,{fullWidth:!0,sx:{mb:3},children:[e.jsx(m,{id:"search-mode-label",children:"搜索模式"}),e.jsxs(j,{labelId:"search-mode-label",value:oe.searchMode,onChange:e=>{de(_(e.target.value))},input:e.jsx(p,{label:"搜索模式"}),disabled:!oe.enabled,children:[e.jsx(u,{value:"auto",children:"自动搜索 (AI 自动判断何时搜索)"}),e.jsx(u,{value:"manual",children:"手动搜索 (点击搜索按钮启动)"})]})]}),e.jsxs(a,{sx:{mb:3},children:[e.jsxs(t,{id:"max-results-slider",gutterBottom:!0,children:["最大结果数量: ",oe.maxResults]}),e.jsx(I,{"aria-labelledby":"max-results-slider",value:oe.maxResults,onChange:(e,a)=>{de(Q(a))},min:1,max:20,step:1,marks:[{value:1,label:"1"},{value:5,label:"5"},{value:10,label:"10"},{value:20,label:"20"}],disabled:!oe.enabled})]}),e.jsxs(d,{children:[e.jsx(o,{control:e.jsx(x,{checked:oe.includeInContext,onChange:()=>{de(V())},disabled:!oe.enabled}),label:"将搜索结果包含在上下文中"}),e.jsx(o,{control:e.jsx(x,{checked:oe.showTimestamp,onChange:()=>{de(J())},disabled:!oe.enabled}),label:"显示搜索结果时间戳"}),e.jsx(o,{control:e.jsx(x,{checked:oe.filterSafeSearch,onChange:()=>{de(N())},disabled:!oe.enabled}),label:"启用安全搜索过滤"}),e.jsx(o,{control:e.jsx(x,{checked:oe.searchWithTime,onChange:()=>de(z()),disabled:!oe.enabled}),label:"在搜索查询中添加当前日期"})]})]}),"tavily"===oe.provider&&e.jsxs(n,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:"1px solid",borderColor:"divider"},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,color:e=>e.palette.text.primary,mb:2},children:"🚀 Tavily 最佳实践设置"}),e.jsx(g,{severity:"info",sx:{mb:3},children:"这些设置基于 Tavily 官方最佳实践，可以显著提升搜索质量和相关性。"}),e.jsx(o,{control:e.jsx(x,{checked:oe.enableSmartSearch||!1,onChange:()=>de(T()),disabled:!oe.enabled,color:"primary"}),label:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{mr:1},children:"启用智能搜索"}),e.jsx(c,{title:"自动应用最佳实践设置，包括高级搜索深度、内容块优化等",children:e.jsx(ie,{fontSize:"small",sx:{color:"text.secondary"}})})]}),sx:{mb:3}}),e.jsxs(b,{fullWidth:!0,sx:{mb:3},children:[e.jsx(m,{id:"search-depth-label",children:"搜索深度"}),e.jsxs(j,{labelId:"search-depth-label",value:oe.searchDepth||"basic",onChange:e=>{de(X(e.target.value))},input:e.jsx(p,{label:"搜索深度"}),disabled:!oe.enabled||oe.enableSmartSearch,children:[e.jsx(u,{value:"basic",children:"基础搜索 (更快)"}),e.jsx(u,{value:"advanced",children:"高级搜索 (更准确，推荐)"})]})]}),e.jsxs(a,{sx:{mb:3},children:[e.jsxs(t,{id:"chunks-per-source-slider",gutterBottom:!0,children:["每个来源的内容块数量: ",oe.chunksPerSource||3]}),e.jsx(I,{"aria-labelledby":"chunks-per-source-slider",value:oe.chunksPerSource||3,onChange:(e,a)=>{de(Y(a))},min:1,max:5,step:1,marks:[{value:1,label:"1"},{value:3,label:"3"},{value:5,label:"5"}],disabled:!oe.enabled||oe.enableSmartSearch})]}),e.jsxs(a,{sx:{mb:3},children:[e.jsxs(t,{id:"min-score-slider",gutterBottom:!0,children:["最小相关性分数: ",Math.round(100*(oe.minScore||.3)),"%"]}),e.jsx(I,{"aria-labelledby":"min-score-slider",value:Math.round(100*(oe.minScore||.3)),onChange:(e,a)=>{de(Z(a/100))},min:0,max:100,step:5,marks:[{value:0,label:"0%"},{value:30,label:"30%"},{value:70,label:"70%"},{value:100,label:"100%"}],disabled:!oe.enabled||oe.enableSmartSearch}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"过滤掉相关性分数低于此阈值的搜索结果"})]}),e.jsxs(b,{fullWidth:!0,sx:{mb:3},children:[e.jsx(m,{id:"time-range-label",children:"时间范围过滤"}),e.jsxs(j,{labelId:"time-range-label",value:oe.timeRange||"week",onChange:e=>{de($(e.target.value))},input:e.jsx(p,{label:"时间范围过滤"}),disabled:!oe.enabled,children:[e.jsx(u,{value:"day",children:"最近一天"}),e.jsx(u,{value:"week",children:"最近一周"}),e.jsx(u,{value:"month",children:"最近一个月"}),e.jsx(u,{value:"year",children:"最近一年"})]})]}),e.jsxs(a,{sx:{mb:3},children:[e.jsxs(t,{id:"news-search-days-slider",gutterBottom:!0,children:["新闻搜索天数范围: ",oe.newsSearchDays||7," 天"]}),e.jsx(I,{"aria-labelledby":"news-search-days-slider",value:oe.newsSearchDays||7,onChange:(e,a)=>{de(q(a))},min:1,max:30,step:1,marks:[{value:1,label:"1天"},{value:7,label:"1周"},{value:14,label:"2周"},{value:30,label:"1月"}],disabled:!oe.enabled}),e.jsx(t,{variant:"body2",color:"text.secondary",children:'当搜索主题设置为"新闻"时使用'})]}),e.jsxs(d,{children:[e.jsx(o,{control:e.jsx(x,{checked:oe.includeRawContent||!1,onChange:()=>de(K()),disabled:!oe.enabled||oe.enableSmartSearch}),label:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{mr:1},children:"包含原始内容"}),e.jsx(c,{title:"获取完整的网页内容，用于深度分析",children:e.jsx(ie,{fontSize:"small",sx:{color:"text.secondary"}})})]})}),e.jsx(o,{control:e.jsx(x,{checked:oe.includeAnswer||!1,onChange:()=>de(L()),disabled:!oe.enabled}),label:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{mr:1},children:"包含AI答案摘要"}),e.jsx(c,{title:"Tavily生成的基于搜索结果的答案摘要",children:e.jsx(ie,{fontSize:"small",sx:{color:"text.secondary"}})})]})}),e.jsx(o,{control:e.jsx(x,{checked:!1!==oe.enableQueryValidation,onChange:()=>de(D()),disabled:!oe.enabled}),label:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{mr:1},children:"启用查询验证"}),e.jsx(c,{title:"验证查询长度和格式，提供优化建议",children:e.jsx(ie,{fontSize:"small",sx:{color:"text.secondary"}})})]})}),e.jsx(o,{control:e.jsx(x,{checked:!1!==oe.enablePostProcessing,onChange:()=>de(U()),disabled:!oe.enabled}),label:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(t,{sx:{mr:1},children:"启用结果后处理"}),e.jsx(c,{title:"基于相关性分数过滤和排序搜索结果",children:e.jsx(ie,{fontSize:"small",sx:{color:"text.secondary"}})})]})})]})]}),e.jsxs(n,{elevation:0,sx:{p:3,mb:3,borderRadius:2,border:"1px solid",borderColor:"divider"},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,color:e=>e.palette.text.primary,mb:2},children:"高级设置"}),e.jsx(t,{variant:"subtitle2",gutterBottom:!0,children:"排除域名 (每行一个)"}),e.jsx(v,{fullWidth:!0,multiline:!0,rows:3,value:(null==(te=oe.excludeDomains)?void 0:te.join("\n"))||"",onChange:e=>{const a=e.target.value.split("\n").filter((e=>e.trim()));de(F(a))},placeholder:"example.com\nspam-site.com",disabled:!oe.enabled,variant:"outlined",sx:{mb:2}}),e.jsx(t,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"这些域名将从搜索结果中排除"})]})]}),he&&ce&&e.jsx(a,{sx:{position:"fixed",top:0,left:0,right:0,bottom:0,bgcolor:"rgba(0, 0, 0, 0.5)",zIndex:1300,display:"flex",alignItems:"center",justifyContent:"center",p:2},children:e.jsxs(n,{sx:{p:3,width:"100%",maxWidth:500,maxHeight:"90vh",overflow:"auto",borderRadius:2},children:[e.jsx(t,{variant:"h6",gutterBottom:!0,children:ce.id?"编辑搜索服务":"添加搜索服务"}),e.jsx(v,{fullWidth:!0,margin:"normal",label:"服务名称",value:ce.name,onChange:e=>me("name",e.target.value),variant:"outlined"}),e.jsx(v,{fullWidth:!0,margin:"normal",label:"基础 URL",value:ce.baseUrl,onChange:e=>me("baseUrl",e.target.value),variant:"outlined",placeholder:"https://api.example.com"}),e.jsx(v,{fullWidth:!0,margin:"normal",label:"API 密钥",type:"password",value:ce.apiKey,onChange:e=>me("apiKey",e.target.value),variant:"outlined",slotProps:{input:{"aria-invalid":!1,"aria-describedby":"provider-api-key-helper-text"},formHelperText:{id:"provider-api-key-helper-text"}}}),e.jsx(o,{control:e.jsx(x,{checked:ce.enabled,onChange:e=>me("enabled",e.target.checked)}),label:"启用此服务"}),e.jsxs(a,{sx:{mt:3,display:"flex",justifyContent:"flex-end",gap:2},children:[e.jsx(C,{onClick:()=>{xe(null),be(!1)},children:"取消"}),e.jsx(C,{variant:"contained",onClick:()=>{var e;ce&&(ce.id&&(null==(e=oe.customProviders)?void 0:e.some((e=>e.id===ce.id)))?de(ee(ce)):de(ae(ce)),xe(null),be(!1))},sx:{bgcolor:"#3b82f6","&:hover":{bgcolor:"#2563eb"}},children:"保存"})]})]})})]})};export{te as default};
