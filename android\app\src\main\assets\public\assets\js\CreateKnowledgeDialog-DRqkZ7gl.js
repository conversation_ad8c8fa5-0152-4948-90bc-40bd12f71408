import{j as e,o as n,p as s,q as l,s as a,t,F as o,e as i,S as r,M as d,u,B as m,b as c,i as h,D as x,I as v,v as j,r as p,m as b}from"./mui-vendor-DsBXMegs.js";import{r as g}from"./react-vendor-Be-rfjCm.js";import{g as C,h as k,i as f,j as y,k as S,l as z,E,m as O,n as W}from"./index-Ck4sQVom.js";import{E as D}from"./ExpandLess-B3I8kUh8.js";const I=({open:I,onClose:w,onSave:B,initialData:L,isEditing:T=!1})=>{const[q,F]=g.useState([]),[P,M]=g.useState({name:(null==L?void 0:L.name)||"",model:(null==L?void 0:L.model)||"",dimensions:(null==L?void 0:L.dimensions)||S,documentCount:(null==L?void 0:L.documentCount)||y,chunkSize:(null==L?void 0:L.chunkSize)||f,chunkOverlap:(null==L?void 0:L.chunkOverlap)||k,threshold:(null==L?void 0:L.threshold)||C}),[A,G]=g.useState({}),[H,J]=g.useState(!1),[K,N]=g.useState(!1);g.useEffect((()=>{const e=z();F(e),T&&!P.model&&e.length>0&&M((n=>({...n,model:e[0].id,dimensions:1536})))}),[T]),g.useEffect((()=>{M(L?{name:L.name||"",model:L.model||"",dimensions:L.dimensions||S,documentCount:L.documentCount||y,chunkSize:L.chunkSize||f,chunkOverlap:L.chunkOverlap||k,threshold:L.threshold||C}:{name:"",model:"",dimensions:S,documentCount:y,chunkSize:f,chunkOverlap:k,threshold:C})}),[L]),g.useEffect((()=>{I||G({})}),[I]);const Q=e=>{const{name:n,value:s}=e.target;n&&(M((e=>({...e,[n]:s}))),A[n]&&G((e=>{const s={...e};return delete s[n],s})))};return e.jsxs(n,{open:I,onClose:w,fullWidth:!0,maxWidth:"md",children:[e.jsx(s,{children:T?"编辑知识库":"创建知识库"}),e.jsx(l,{dividers:!0,children:e.jsxs(a,{spacing:3,children:[e.jsx(t,{autoFocus:!0,name:"name",label:"知识库名称",fullWidth:!0,required:!0,value:P.name,onChange:Q,error:!!A.name,helperText:A.name||"给知识库起一个描述性的名称"}),e.jsxs(o,{fullWidth:!0,error:!!A.model,children:[e.jsx(i,{children:"嵌入模型 *"}),e.jsx(r,{name:"model",value:P.model||"",onChange:async e=>{const n=e.target.value;try{const e=O.getInstance(),s=await e.getEmbeddingDimensions(n);M((e=>({...e,model:n,dimensions:s})))}catch(s){console.error("获取模型维度失败:",s);const e=W(n);M((s=>({...s,model:n,dimensions:e})))}},label:"嵌入模型 *",children:q.length>0?q.map((n=>e.jsxs(d,{value:n.id,children:[n.name," (来自 ",n.provider,")"]},n.id))):e.jsx(d,{disabled:!0,value:"",children:"未找到可用的嵌入模型，请先在设置中配置"})}),e.jsx(u,{children:A.model||"用于将文本转换为向量的模型"})]}),e.jsxs(m,{children:[e.jsxs(c,{gutterBottom:!0,children:["请求文档段数量: ",P.documentCount]}),e.jsx(h,{name:"documentCount",value:P.documentCount||6,onChange:(e,n)=>M((e=>({...e,documentCount:n}))),min:1,max:30,step:1,marks:[{value:1,label:"1"},{value:6,label:"默认"},{value:30,label:"30"}],valueLabelDisplay:"auto","aria-label":"文档数量"}),e.jsx(c,{variant:"caption",color:"text.secondary",children:"搜索时返回的文档段数量，影响回答的详细程度"})]}),e.jsx(x,{}),e.jsxs(m,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsx(c,{variant:"subtitle1",fontWeight:"bold",children:"高级设置"}),e.jsx(v,{onClick:()=>N(!K),size:"small",children:K?e.jsx(D,{}):e.jsx(E,{})})]}),e.jsx(j,{in:K,children:e.jsxs(a,{spacing:3,sx:{mt:2},children:[e.jsx(t,{name:"chunkSize",label:"分块大小",type:"number",fullWidth:!0,value:P.chunkSize,onChange:Q,helperText:"将文档分割成的块的大小（字符数）",slotProps:{htmlInput:{min:100,max:5e3,step:100}}}),e.jsx(t,{name:"chunkOverlap",label:"重叠大小",type:"number",fullWidth:!0,value:P.chunkOverlap,onChange:Q,helperText:"每个块之间重叠的字符数",slotProps:{htmlInput:{min:0,max:1e3,step:50}}}),e.jsxs(m,{children:[e.jsxs(c,{gutterBottom:!0,children:["相似度阈值: ",P.threshold]}),e.jsx(h,{name:"threshold",value:P.threshold||.7,onChange:(e,n)=>M((e=>({...e,threshold:n}))),min:0,max:1,step:.05,marks:[{value:0,label:"0"},{value:.5,label:"0.5"},{value:1,label:"1"}],valueLabelDisplay:"auto","aria-label":"相似度阈值"}),e.jsx(c,{variant:"caption",color:"text.secondary",children:"搜索结果的最低相似度分数，值越高结果越精确"})]})]})})]})}),e.jsxs(p,{children:[e.jsx(b,{onClick:w,disabled:H,children:"取消"}),e.jsx(b,{onClick:async()=>{if((()=>{var e;const n={};return(null==(e=P.name)?void 0:e.trim())||(n.name="知识库名称不能为空"),P.model||(n.model="请选择嵌入模型"),G(n),0===Object.keys(n).length})())try{J(!0),await B(P),w()}catch(e){console.error("保存知识库失败:",e)}finally{J(!1)}},variant:"contained",color:"primary",disabled:H,children:H?"保存中...":T?"更新":"创建"})]})]})};export{I as C};
