import{a6 as t}from"./index-Dnlt-eWK.js";import{I as i,N as e}from"./index-u3IL88vk.js";import"./mui-vendor-DsBXMegs.js";import"./react-vendor-Be-rfjCm.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";import"./vue-vendor-Ch32PRwJ.js";import"./index-Bkdw0LBO.js";class r extends t{constructor(){super(...arguments),this.selectionStarted=!1}async impact(t){const i=this.patternForImpact(null==t?void 0:t.style);this.vibrateWithPattern(i)}async notification(t){const i=this.patternForNotification(null==t?void 0:t.type);this.vibrateWithPattern(i)}async vibrate(t){const i=(null==t?void 0:t.duration)||300;this.vibrateWithPattern([i])}async selectionStart(){this.selectionStarted=!0}async selectionChanged(){this.selectionStarted&&this.vibrateWithPattern([70])}async selectionEnd(){this.selectionStarted=!1}patternForImpact(t=i.Heavy){return t===i.Medium?[43]:t===i.Light?[20]:[61]}patternForNotification(t=e.Success){return t===e.Warning?[30,40,30,50,60]:t===e.Error?[27,45,50]:[35,65,21]}vibrateWithPattern(t){if(!navigator.vibrate)throw this.unavailable("Browser does not support the vibrate API");navigator.vibrate(t)}}export{r as HapticsWeb};
