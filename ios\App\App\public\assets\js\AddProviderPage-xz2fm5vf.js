import{j as e,B as a,a as r,A as o,T as t,I as i,b as l,P as n,y as s,t as p,F as d,e as c,S as u,M as x,m as b}from"./mui-vendor-DsBXMegs.js";import{u as h,r as m}from"./react-vendor-Be-rfjCm.js";import{u as g,A as v,p as A,q as f}from"./index-Dnlt-eWK.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const I=[{value:"openai",label:"OpenAI"},{value:"openai-aisdk",label:"OpenAI (AI SDK) - 流式优化"},{value:"azure-openai",label:"Azure OpenAI"},{value:"gemini",label:"Gemini"},{value:"anthropic",label:"Anthropic"},{value:"grok",label:"xAI (Grok)"},{value:"deepseek",label:"DeepSeek"},{value:"zhipu",label:"智谱AI"},{value:"siliconflow",label:"硅基流动 (SiliconFlow)"},{value:"volcengine",label:"火山引擎"},{value:"custom",label:"其他"}],j=()=>{const j=h(),k=g(),[y,w]=m.useState(""),[C,S]=m.useState(""),[z,P]=m.useState(""),O=()=>{j("/settings/default-model")},G=e=>{switch(e){case"openai":case"openai-aisdk":return"https://api.openai.com/v1";case"azure-openai":default:return"";case"anthropic":return"https://api.anthropic.com/v1";case"gemini":return"https://generativelanguage.googleapis.com/v1beta";case"grok":return"https://api.x.ai/v1";case"deepseek":return"https://api.deepseek.com";case"zhipu":return"https://open.bigmodel.cn/api/paas/v4/";case"siliconflow":return"https://api.siliconflow.cn/v1";case"volcengine":return"https://ark.cn-beijing.volces.com/api/v3"}},B=!y||!C;return e.jsxs(a,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?r(e.palette.primary.main,.02):r(e.palette.background.default,.9)},children:[e.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(t,{children:[e.jsx(i,{edge:"start",onClick:O,"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(v,{})}),e.jsx(l,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"添加提供商"})]})}),e.jsx(a,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:e.jsxs(n,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)",maxWidth:600,mx:"auto"},children:[e.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:4},children:[e.jsx(s,{sx:{width:64,height:64,bgcolor:"#9333EA",fontSize:"1.7rem",mr:2,boxShadow:"0 4px 8px rgba(0,0,0,0.1)"},children:z||"P"}),e.jsx(l,{variant:"h5",sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:y||"新提供商"})]}),e.jsx(l,{variant:"subtitle1",gutterBottom:!0,sx:{fontWeight:600,color:"text.primary",mb:3},children:"提供商信息"}),e.jsxs(a,{sx:{mb:3},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"提供商名称"}),e.jsx(p,{fullWidth:!0,placeholder:"例如 OpenAI",value:y,onChange:e=>{const a=e.target.value;w(a),"custom"===C&&a&&P(a.charAt(0).toUpperCase())},variant:"outlined",size:"small",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})]}),e.jsxs(a,{sx:{mb:4},children:[e.jsx(l,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"提供商类型"}),e.jsxs(d,{fullWidth:!0,size:"small",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}},children:[e.jsx(c,{children:"提供商类型"}),e.jsx(u,{value:C,onChange:e=>{const a=e.target.value;if(S(a),"custom"===a)y&&P(y.charAt(0).toUpperCase());else{const e=I.find((e=>e.value===a));e&&P(e.label.charAt(0).toUpperCase())}},label:"提供商类型",children:I.map((a=>e.jsx(x,{value:a.value,children:a.label},a.value)))})]}),C&&e.jsx(l,{variant:"body2",color:"text.secondary",sx:{mt:1,fontSize:"0.8rem"},children:"openai"===C?"添加OpenAI兼容的API服务":"openai-aisdk"===C?"添加OpenAI API服务（使用AI SDK，专为浏览器优化，解决流式响应延迟问题）":"azure-openai"===C?"添加Azure OpenAI API服务（需要配置endpoint和apiVersion）":"anthropic"===C?"添加Anthropic Claude API服务":"gemini"===C?"添加Google Gemini API服务":"grok"===C?"添加xAI (Grok) API服务":"deepseek"===C?"添加DeepSeek API服务（使用OpenAI兼容格式）":"zhipu"===C?"添加智谱AI (GLM) API服务（使用OpenAI兼容格式）":"siliconflow"===C?"添加硅基流动 (SiliconFlow) API服务":"volcengine"===C?"添加火山引擎 (豆包/DeepSeek) API服务":"添加自定义API服务"})]}),e.jsxs(a,{sx:{mt:4,display:"flex",justifyContent:"flex-end",gap:2},children:[e.jsx(b,{variant:"outlined",onClick:O,sx:{borderRadius:2,px:3},children:"取消"}),e.jsx(b,{onClick:()=>{const e=["#10a37f","#4285f4","#b83280","#8b5cf6","#6366f1","#ef4444","#f59e0b","#22c55e"],a=e[Math.floor(Math.random()*e.length)],r={id:A(),name:y,avatar:z,color:a,isEnabled:!0,apiKey:"",baseUrl:G(C),models:[],providerType:C};k(f(r)),j(`/settings/model-provider/${r.id}`)},disabled:B,sx:{bgcolor:e=>r(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>r(e.palette.primary.main,.2)},borderRadius:2,px:3},children:"下一步"})]})]})})]})};export{j as default};
