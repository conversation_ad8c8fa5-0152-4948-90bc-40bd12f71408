/**
 * 移动端消息 Token 显示组件
 */
import React, { useState, useMemo } from 'react';
import {
  Box,
  Chip,
  Popover,
  Paper,
  Typography,
  IconButton,
  Divider,
  useTheme,
  alpha,
  Fade,
  Zoom
} from '@mui/material';
import {
  Speed as SpeedIcon,
  TrendingUp as TrendingUpIcon,
  Info as InfoIcon,
  KeyboardArrowUp as ArrowUpIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import type { Message } from '../../shared/types/newMessage';

interface MessageTokensProps {
  message: Message;
  compact?: boolean;
  showOnHover?: boolean;
}

const MessageTokens: React.FC<MessageTokensProps> = ({
  message,
  compact = false,
  showOnHover = false
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [isHovered, setIsHovered] = useState(false);

  // 如果没有 usage 数据，不显示
  if (!message.usage) {
    return null;
  }

  const { usage, metrics } = message;
  const isAssistant = message.role === 'assistant';

  // 计算 token 速度（tokens/秒）
  const tokenSpeed = useMemo(() => {
    if (!metrics?.latency || !usage.completionTokens) return null;
    return Math.round(usage.completionTokens / (metrics.latency / 1000));
  }, [metrics?.latency, usage.completionTokens]);

  // 计算首 token 延迟
  const firstTokenLatency = metrics?.firstTokenLatency;

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  // 格式化时间显示
  const formatTime = (ms: number) => {
    if (ms >= 1000) {
      return `${(ms / 1000).toFixed(1)}s`;
    }
    return `${Math.round(ms)}ms`;
  };

  // 获取 token 使用效率颜色
  const getEfficiencyColor = () => {
    if (!usage.totalTokens) return theme.palette.text.secondary;
    
    if (usage.totalTokens < 1000) return theme.palette.success.main;
    if (usage.totalTokens < 5000) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  // 获取速度颜色
  const getSpeedColor = () => {
    if (!tokenSpeed) return theme.palette.text.secondary;
    
    if (tokenSpeed >= 50) return theme.palette.success.main;
    if (tokenSpeed >= 20) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  // 主要显示内容
  const mainContent = (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
        cursor: 'pointer',
        borderRadius: '12px',
        padding: compact ? '2px 6px' : '4px 8px',
        background: theme.palette.mode === 'dark'
          ? alpha(theme.palette.background.paper, 0.8)
          : alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(8px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          background: theme.palette.mode === 'dark'
            ? alpha(theme.palette.background.paper, 0.95)
            : alpha(theme.palette.background.paper, 1),
          borderColor: alpha(theme.palette.primary.main, 0.5),
          transform: 'translateY(-1px)',
          boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
        }
      }}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Token 总数 */}
      <Chip
        icon={<AnalyticsIcon sx={{ fontSize: '14px !important' }} />}
        label={formatNumber(usage.totalTokens)}
        size="small"
        variant="outlined"
        sx={{
          height: compact ? 20 : 24,
          fontSize: compact ? '0.65rem' : '0.7rem',
          fontWeight: 600,
          color: getEfficiencyColor(),
          borderColor: getEfficiencyColor(),
          '& .MuiChip-icon': {
            color: getEfficiencyColor(),
          }
        }}
      />

      {/* 输入/输出 tokens - 只在非紧凑模式显示 */}
      {!compact && (
        <>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.25 }}>
            <ArrowUpIcon sx={{ fontSize: 12, color: theme.palette.text.secondary }} />
            <Typography variant="caption" sx={{ fontSize: '0.65rem', color: theme.palette.text.secondary }}>
              {formatNumber(usage.promptTokens)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.25 }}>
            <ArrowDownIcon sx={{ fontSize: 12, color: theme.palette.text.secondary }} />
            <Typography variant="caption" sx={{ fontSize: '0.65rem', color: theme.palette.text.secondary }}>
              {formatNumber(usage.completionTokens)}
            </Typography>
          </Box>
        </>
      )}

      {/* 速度指示器 - 只在助手消息且有速度数据时显示 */}
      {isAssistant && tokenSpeed && (
        <Chip
          icon={<SpeedIcon sx={{ fontSize: '12px !important' }} />}
          label={`${tokenSpeed}/s`}
          size="small"
          variant="filled"
          sx={{
            height: compact ? 18 : 22,
            fontSize: '0.6rem',
            fontWeight: 500,
            backgroundColor: alpha(getSpeedColor(), 0.15),
            color: getSpeedColor(),
            '& .MuiChip-icon': {
              color: getSpeedColor(),
            }
          }}
        />
      )}

      {/* 信息图标 */}
      <IconButton
        size="small"
        sx={{
          width: 16,
          height: 16,
          padding: 0,
          color: theme.palette.text.secondary,
          opacity: isHovered ? 1 : 0.6,
          transition: 'opacity 0.2s ease',
        }}
      >
        <InfoIcon sx={{ fontSize: 12 }} />
      </IconButton>
    </Box>
  );

  // 详细信息弹窗内容
  const popoverContent = (
    <Paper
      elevation={8}
      sx={{
        p: 2,
        minWidth: 280,
        maxWidth: 320,
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
          : 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
        backdropFilter: 'blur(20px)',
        borderRadius: '16px',
        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
      }}
    >
      {/* 标题 */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
        <AnalyticsIcon sx={{ color: theme.palette.primary.main }} />
        <Typography variant="subtitle2" fontWeight={600}>
          Token 使用详情
        </Typography>
      </Box>

      {/* Token 统计 */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">总 Tokens</Typography>
          <Typography variant="body2" fontWeight={600} color={getEfficiencyColor()}>
            {usage.totalTokens.toLocaleString()}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">输入 Tokens</Typography>
          <Typography variant="body2" fontWeight={500}>
            {usage.promptTokens.toLocaleString()}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">输出 Tokens</Typography>
          <Typography variant="body2" fontWeight={500}>
            {usage.completionTokens.toLocaleString()}
          </Typography>
        </Box>
      </Box>

      {/* 性能指标 - 只在助手消息显示 */}
      {isAssistant && metrics && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
              <TrendingUpIcon sx={{ color: theme.palette.secondary.main, fontSize: 18 }} />
              <Typography variant="subtitle2" fontWeight={600}>
                性能指标
              </Typography>
            </Box>
            
            {tokenSpeed && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">生成速度</Typography>
                <Typography variant="body2" fontWeight={600} color={getSpeedColor()}>
                  {tokenSpeed} tokens/s
                </Typography>
              </Box>
            )}
            
            {metrics.latency && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">总延迟</Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formatTime(metrics.latency)}
                </Typography>
              </Box>
            )}
            
            {firstTokenLatency && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">首 Token 延迟</Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formatTime(firstTokenLatency)}
                </Typography>
              </Box>
            )}
          </Box>
        </>
      )}

      {/* 效率评估 */}
      <Divider sx={{ my: 2 }} />
      <Box sx={{ textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          {usage.totalTokens < 1000 && '高效使用 🎯'}
          {usage.totalTokens >= 1000 && usage.totalTokens < 5000 && '适中使用 ⚡'}
          {usage.totalTokens >= 5000 && '大量使用 🔥'}
        </Typography>
      </Box>
    </Paper>
  );

  // 如果设置了 showOnHover，只在悬停时显示
  if (showOnHover && !isHovered) {
    return (
      <Box
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        sx={{ position: 'relative' }}
      >
        <Fade in={isHovered} timeout={200}>
          <Box>{mainContent}</Box>
        </Fade>
      </Box>
    );
  }

  return (
    <>
      <Zoom in timeout={300}>
        {mainContent}
      </Zoom>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        sx={{
          '& .MuiPopover-paper': {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        {popoverContent}
      </Popover>
    </>
  );
};

export default MessageTokens;
