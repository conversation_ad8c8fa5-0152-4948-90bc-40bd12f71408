/**
 * 记忆提取器
 * 基于 Mem0 的 FACT_RETRIEVAL_PROMPT 实现
 */

import type { Message, Model } from '../../types';
import type { MemoryCategory } from '../../types/internalMemory';
import { getMemoryConfig } from '../../config/internalMemoryConfig';
import LoggerService from '../LoggerService';
import { sendChatRequest } from '../../api/openai';


// 记忆提取结果接口
interface MemoryExtractionResult {
  facts: string[];
  categories: MemoryCategory[];
  importance: number[];
}

/**
 * 记忆提取器类
 * 使用现有的 LLM API 从对话中提取用户记忆
 */
export class MemoryExtractor {
  private config = getMemoryConfig();

  // 基于 Mem0 的事实提取提示词
  private readonly FACT_EXTRACTION_PROMPT = `你是一个个人信息整理专家，专门从对话中准确提取和整理事实、用户记忆和偏好。

需要记住的信息类型：
1. 个人偏好：喜好、厌恶、特定偏好
2. 重要个人信息：姓名、关系、重要日期
3. 计划和意图：即将到来的事件、目标、计划
4. 活动偏好：餐饮、旅行、爱好偏好
5. 健康信息：饮食限制、健身习惯
6. 职业信息：工作、职业目标
7. 其他信息：喜欢的书籍、电影、品牌等

请从对话中提取值得记住的事实，每个事实应该：
- 简洁明确（1-2句话）
- 包含具体信息
- 对未来对话有价值
- 避免重复或过于琐碎的信息
- **重要：仔细检查已有记忆，避免提取重复或相似的内容**
- **如果新信息与已有记忆相似但有更新，则提取更新后的版本**
- **如果没有新的有价值信息，返回空数组**

返回JSON格式：
{
  "facts": ["事实1", "事实2"],
  "categories": ["preference", "background"],
  "importance": [7, 5]
}

其中：
- facts: 提取的事实列表
- categories: 对应的分类（preference/background/skill/habit/plan）
- importance: 重要性评分（1-10，10最重要）`;

  /**
   * 从消息列表中提取记忆
   */
  public async extractMemories(messages: Message[], model?: Model, userId?: string): Promise<MemoryExtractionResult> {
    try {
      if (!this.config.enabled || messages.length < this.config.extraction.minConversationLength) {
        return { facts: [], categories: [], importance: [] };
      }

      // 格式化对话内容
      const conversation = this.formatConversation(messages);

      // 获取已有记忆
      let existingMemoriesText = '';
      if (userId) {
        try {
          const { InternalMemoryService } = await import('./InternalMemoryService');
          const memoryService = InternalMemoryService.getInstance();
          const existingMemories = await memoryService.getUserMemories(userId);

          if (existingMemories.length > 0) {
            existingMemoriesText = '\n\n已有记忆（避免重复提取）：\n';
            existingMemories.forEach((memory, index) => {
              existingMemoriesText += `${index + 1}. ${memory.content}\n`;
            });
            existingMemoriesText += '\n请避免提取与上述已有记忆重复或相似的内容。';
          }
        } catch (error) {
          console.warn('获取已有记忆失败:', error);
        }
      }

      // 构建提取请求
      const extractionMessages = [
        {
          role: 'system',
          content: this.FACT_EXTRACTION_PROMPT + existingMemoriesText
        },
        {
          role: 'user',
          content: `对话内容：\n${conversation}`
        }
      ];

      // 调用 AI 进行记忆提取
      const response = await this.callAIForExtraction(extractionMessages, model);

      // 解析响应
      const result = this.parseExtractionResponse(response);

      LoggerService.log('INFO', 'Memory extraction completed', {
        factsCount: result.facts.length,
        existingMemoriesCount: existingMemoriesText ? existingMemoriesText.split('\n').length - 3 : 0,
        model: this.config.extraction.model
      });

      return result;
    } catch (error) {
      LoggerService.log('ERROR', 'Memory extraction failed', { error });
      // 降级：返回空结果
      return { facts: [], categories: [], importance: [] };
    }
  }

  /**
   * 格式化对话内容
   */
  private formatConversation(messages: Message[]): string {
    return messages
      .slice(-this.config.extraction.maxMemoriesPerExtraction * 2) // 限制消息数量
      .map(msg => {
        const role = msg.role === 'user' ? '用户' : 'AI助手';
        const content = this.getMessageContent(msg);
        return `${role}: ${content}`;
      })
      .join('\n\n');
  }

  /**
   * 获取消息内容
   */
  private getMessageContent(message: any): string {
    // 简化处理，支持多种消息格式
    if (typeof message.content === 'string') {
      return message.content;
    }

    // 处理新消息格式
    if (Array.isArray(message.content)) {
      return message.content
        .filter((item: any) => item.type === 'text')
        .map((item: any) => item.text)
        .join(' ');
    }

    return '';
  }

  /**
   * 调用 AI 进行记忆提取
   */
  private async callAIForExtraction(messages: any[], userModel?: Model): Promise<string> {
    try {
      // 使用传入的模型或创建默认模型配置
      let model: Model;

      if (userModel) {
        // 使用用户选择的模型，但调整参数以适合记忆提取
        model = {
          ...userModel,
          temperature: 0.1, // 低温度确保一致性
          maxTokens: 1000   // 限制输出长度
        };
      } else {
        // 创建默认模型配置
        model = {
          id: this.config.extraction.model,
          provider: 'openai',
          name: this.config.extraction.model,
          apiKey: '', // 将从环境变量或设置中获取
          baseUrl: '', // 将从环境变量或设置中获取
          temperature: 0.1, // 低温度确保一致性
          maxTokens: 1000
        };
      }

      // 调用 AI API
      const response = await sendChatRequest(messages, model);

      // 处理响应
      if (typeof response === 'string') {
        return response;
      } else if (response && typeof response === 'object' && 'content' in response) {
        return response.content;
      }

      return '';
    } catch (error) {
      LoggerService.log('ERROR', 'AI memory extraction failed', { error });
      throw error;
    }
  }

  /**
   * 解析提取响应
   */
  private parseExtractionResponse(response: string): MemoryExtractionResult {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // 验证响应格式
      if (!parsed.facts || !Array.isArray(parsed.facts)) {
        throw new Error('Invalid facts format');
      }

      const facts = parsed.facts.slice(0, this.config.extraction.maxMemoriesPerExtraction);
      const categories = this.validateCategories(parsed.categories || [], facts.length);
      const importance = this.validateImportance(parsed.importance || [], facts.length);

      return { facts, categories, importance };
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to parse extraction response', { error, response });
      
      // 尝试简单的文本提取作为降级
      return this.fallbackExtraction(response);
    }
  }

  /**
   * 验证分类数组
   */
  private validateCategories(categories: string[], factsCount: number): MemoryCategory[] {
    const validCategories: MemoryCategory[] = [];
    const defaultCategory = 'background' as MemoryCategory;

    for (let i = 0; i < factsCount; i++) {
      const category = categories[i];
      if (this.isValidCategory(category)) {
        validCategories.push(category as MemoryCategory);
      } else {
        validCategories.push(defaultCategory);
      }
    }

    return validCategories;
  }

  /**
   * 验证重要性数组
   */
  private validateImportance(importance: number[], factsCount: number): number[] {
    const validImportance: number[] = [];
    const defaultImportance = 5;

    for (let i = 0; i < factsCount; i++) {
      const imp = importance[i];
      if (typeof imp === 'number' && imp >= 1 && imp <= 10) {
        validImportance.push(Math.round(imp));
      } else {
        validImportance.push(defaultImportance);
      }
    }

    return validImportance;
  }

  /**
   * 检查是否为有效分类
   */
  private isValidCategory(category: string): boolean {
    const validCategories = ['preference', 'background', 'skill', 'habit', 'plan'];
    return validCategories.includes(category);
  }

  /**
   * 降级提取方法
   */
  private fallbackExtraction(response: string): MemoryExtractionResult {
    // 简单的文本分析，提取可能的事实
    const lines = response.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 10 && line.length < 200)
      .slice(0, 3); // 最多3个事实

    const facts = lines.length > 0 ? lines : [];
    const categories = facts.map(() => 'background' as MemoryCategory);
    const importance = facts.map(() => 5);

    return { facts, categories, importance };
  }
}
