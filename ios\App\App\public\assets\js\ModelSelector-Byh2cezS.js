import{j as e}from"./mui-vendor-DsBXMegs.js";import"./react-vendor-Be-rfjCm.js";import{d as l}from"./index-Dnlt-eWK.js";import{D as o}from"./DialogModelSelector-BXMaaYok.js";import{D as d}from"./DropdownModelSelector-Cblp38oH.js";const a=a=>"dropdown"===l((e=>e.settings.modelSelectorStyle))?e.jsx(d,{selectedModel:a.selectedModel,availableModels:a.availableModels,handleModelSelect:a.handleModelSelect}):e.jsx(o,{selectedModel:a.selectedModel,availableModels:a.availableModels,handleModelSelect:a.handleModelSelect,handleMenuClick:a.handleMenuClick,handleMenuClose:a.handleMenuClose,menuOpen:a.menuOpen});export{a as M};
