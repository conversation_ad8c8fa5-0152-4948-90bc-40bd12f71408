import{W as e}from"./index-BtK6VV6Z.js";import"./mui-vendor-hRDvsX89.js";import"./react-vendor-C9ilihHH.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";class t extends e{async show(e){if("undefined"!=typeof document){let t=2e3;e.duration&&(t="long"===e.duration?3500:2e3);const o=document.createElement("pwa-toast");o.duration=t,o.message=e.text,document.body.appendChild(o)}}}export{t as ToastWeb};
