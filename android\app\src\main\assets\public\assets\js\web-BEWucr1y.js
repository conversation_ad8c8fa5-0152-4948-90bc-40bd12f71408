import{W as e}from"./index-BtK6VV6Z.js";import"./mui-vendor-hRDvsX89.js";import"./react-vendor-C9ilihHH.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";class t extends e{available(){throw this.unimplemented("Method not implemented on web.")}start(e){throw this.unimplemented("Method not implemented on web.")}stop(){throw this.unimplemented("Method not implemented on web.")}getSupportedLanguages(){throw this.unimplemented("Method not implemented on web.")}hasPermission(){throw this.unimplemented("Method not implemented on web.")}isListening(){throw this.unimplemented("Method not implemented on web.")}requestPermission(){throw this.unimplemented("Method not implemented on web.")}checkPermissions(){throw this.unimplemented("Method not implemented on web.")}requestPermissions(){throw this.unimplemented("Method not implemented on web.")}}const n=new t;export{n as SpeechRecognition,t as SpeechRecognitionWeb};
