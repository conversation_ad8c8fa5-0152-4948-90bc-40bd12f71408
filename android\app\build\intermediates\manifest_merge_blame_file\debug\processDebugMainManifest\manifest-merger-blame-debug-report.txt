1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.llmhouse.app"
4    android:versionCode="1"
5    android:versionName="0.4.5" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:42:5-67
13-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:42:22-64
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:43:5-71
14-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:43:22-68
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:44:5-80
15-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:44:22-77
16
17    <queries>
17-->[:capacitor-community-speech-recognition] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\speech-recognition\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-13:15
18        <intent>
18-->[:capacitor-community-speech-recognition] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\speech-recognition\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-12:18
19            <action android:name="android.speech.RecognitionService" />
19-->[:capacitor-community-speech-recognition] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\speech-recognition\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-72
19-->[:capacitor-community-speech-recognition] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\speech-recognition\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-69
20        </intent>
21        <intent>
21-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
22            <action android:name="android.support.customtabs.action.CustomTabsService" />
22-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
22-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
23        </intent>
24        <intent>
24-->[:capacitor-camera] J:\Cherry\AetherLink-app2\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
25            <action android:name="android.media.action.IMAGE_CAPTURE" />
25-->[:capacitor-camera] J:\Cherry\AetherLink-app2\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
25-->[:capacitor-camera] J:\Cherry\AetherLink-app2\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
26        </intent>
27    </queries>
28
29    <uses-permission android:name="android.permission.VIBRATE" />
29-->[:capacitor-haptics] J:\Cherry\AetherLink-app2\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
29-->[:capacitor-haptics] J:\Cherry\AetherLink-app2\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[:capacitor-cors-bypass-enhanced] J:\Cherry\AetherLink-app2\node_modules\capacitor-cors-bypass-enhanced\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
30-->[:capacitor-cors-bypass-enhanced] J:\Cherry\AetherLink-app2\node_modules\capacitor-cors-bypass-enhanced\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
31-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:22-65
32    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
32-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
32-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
33-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
33-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
34
35    <permission
35-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
36        android:name="com.llmhouse.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.llmhouse.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
40
41    <application
41-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:4:5-38:19
42        android:allowBackup="true"
42-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:5:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:icon="@mipmap/ic_launcher"
46-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:6:9-43
47        android:label="@string/app_name"
47-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:7:9-41
48        android:networkSecurityConfig="@xml/network_security_config"
48-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:11:9-69
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:8:9-54
50        android:supportsRtl="true"
50-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:9:9-35
51        android:testOnly="true"
52        android:theme="@style/AppTheme"
52-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:10:9-40
53        android:usesCleartextTraffic="true" >
53-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:12:9-44
54        <activity
54-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:14:9-27:20
55            android:name="com.llmhouse.app.MainActivity"
55-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:16:13-41
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
56-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:15:13-140
57            android:exported="true"
57-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:20:13-36
58            android:label="@string/title_activity_main"
58-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:17:13-56
59            android:launchMode="singleTask"
59-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:19:13-44
60            android:theme="@style/AppTheme.NoActionBarLaunch" >
60-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:18:13-62
61            <intent-filter>
61-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:22:13-25:29
62                <action android:name="android.intent.action.MAIN" />
62-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:23:17-69
62-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:23:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:24:17-77
64-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:24:27-74
65            </intent-filter>
66        </activity>
67
68        <provider
69            android:name="androidx.core.content.FileProvider"
69-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:30:13-62
70            android:authorities="com.llmhouse.app.fileprovider"
70-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:31:13-64
71            android:exported="false"
71-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:32:13-37
72            android:grantUriPermissions="true" >
72-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:33:13-47
73            <meta-data
73-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:34:13-36:64
74                android:name="android.support.FILE_PROVIDER_PATHS"
74-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:35:17-67
75                android:resource="@xml/file_paths" />
75-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:36:17-51
76        </provider>
77        <provider
77-->[:capacitor-community-file-opener] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
78            android:name="com.ryltsov.alex.plugins.file.opener.FileOpenerProvider"
78-->[:capacitor-community-file-opener] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
79            android:authorities="com.llmhouse.app.file.opener.provider"
79-->[:capacitor-community-file-opener] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
80            android:exported="false"
80-->[:capacitor-community-file-opener] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
81            android:grantUriPermissions="true" >
81-->[:capacitor-community-file-opener] J:\Cherry\AetherLink-app2\node_modules\@capacitor-community\file-opener\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
82            <meta-data
82-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:34:13-36:64
83                android:name="android.support.FILE_PROVIDER_PATHS"
83-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:35:17-67
84                android:resource="@xml/file_opener_paths" />
84-->J:\Cherry\AetherLink-app2\android\app\src\main\AndroidManifest.xml:36:17-51
85        </provider>
86
87        <activity
87-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:75
88            android:name="com.capacitorjs.plugins.browser.BrowserControllerActivity"
88-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-85
89            android:exported="false"
89-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
90            android:launchMode="singleTask"
90-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-44
91            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
91-->[:capacitor-browser] J:\Cherry\AetherLink-app2\node_modules\@capacitor\browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-72
92
93        <provider
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
94            android:name="androidx.startup.InitializationProvider"
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
95            android:authorities="com.llmhouse.app.androidx-startup"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
96            android:exported="false" >
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
97            <meta-data
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.emoji2.text.EmojiCompatInitializer"
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
99                android:value="androidx.startup" />
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
101                android:name="androidx.work.WorkManagerInitializer"
101-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
102                android:value="androidx.startup" />
102-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
103            <meta-data
103-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
104-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
105                android:value="androidx.startup" />
105-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
108                android:value="androidx.startup" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
109        </provider>
110
111        <service
111-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
112            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
112-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
113            android:directBootAware="false"
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
114            android:enabled="@bool/enable_system_alarm_service_default"
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
115            android:exported="false" />
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
116        <service
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
117            android:name="androidx.work.impl.background.systemjob.SystemJobService"
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
118            android:directBootAware="false"
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
119            android:enabled="@bool/enable_system_job_service_default"
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
120            android:exported="true"
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
121            android:permission="android.permission.BIND_JOB_SERVICE" />
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
122        <service
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
123            android:name="androidx.work.impl.foreground.SystemForegroundService"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
124            android:directBootAware="false"
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
125            android:enabled="@bool/enable_system_foreground_service_default"
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
126            android:exported="false" />
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
127
128        <receiver
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
129            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
130            android:directBootAware="false"
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
131            android:enabled="true"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
132            android:exported="false" />
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
133        <receiver
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
134            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
136            android:enabled="false"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
137            android:exported="false" >
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
138            <intent-filter>
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
139                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
140                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
141            </intent-filter>
142        </receiver>
143        <receiver
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
144            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
146            android:enabled="false"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
147            android:exported="false" >
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
148            <intent-filter>
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
149                <action android:name="android.intent.action.BATTERY_OKAY" />
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
150                <action android:name="android.intent.action.BATTERY_LOW" />
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
151            </intent-filter>
152        </receiver>
153        <receiver
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
154            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
156            android:enabled="false"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
157            android:exported="false" >
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
158            <intent-filter>
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
159                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
160                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
161            </intent-filter>
162        </receiver>
163        <receiver
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
169                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
173            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
178                <action android:name="android.intent.action.BOOT_COMPLETED" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
179                <action android:name="android.intent.action.TIME_SET" />
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
180                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
186            android:enabled="@bool/enable_system_alarm_service_default"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
189                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
193            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
195            android:enabled="true"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
196            android:exported="true"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
197            android:permission="android.permission.DUMP" >
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
198            <intent-filter>
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
199                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c569202273c21479f1315d000b46f5d5\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
200            </intent-filter>
201        </receiver>
202
203        <uses-library
203-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1228b5b4280c65f74eddcb791a9d17\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
204            android:name="androidx.window.extensions"
204-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1228b5b4280c65f74eddcb791a9d17\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
205            android:required="false" />
205-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1228b5b4280c65f74eddcb791a9d17\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
206        <uses-library
206-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1228b5b4280c65f74eddcb791a9d17\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
207            android:name="androidx.window.sidecar"
207-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1228b5b4280c65f74eddcb791a9d17\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
208            android:required="false" />
208-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1228b5b4280c65f74eddcb791a9d17\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
209
210        <receiver
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
211            android:name="androidx.profileinstaller.ProfileInstallReceiver"
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
212            android:directBootAware="false"
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
213            android:enabled="true"
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
214            android:exported="true"
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
215            android:permission="android.permission.DUMP" >
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
217                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
220                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
221            </intent-filter>
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
223                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
224            </intent-filter>
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
226                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
227            </intent-filter>
228        </receiver>
229
230        <service
230-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22ca111fcb639f5e14d95fe16d3d7e42\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
231            android:name="androidx.room.MultiInstanceInvalidationService"
231-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22ca111fcb639f5e14d95fe16d3d7e42\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
232            android:directBootAware="true"
232-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22ca111fcb639f5e14d95fe16d3d7e42\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
233            android:exported="false" />
233-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22ca111fcb639f5e14d95fe16d3d7e42\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
234    </application>
235
236</manifest>
