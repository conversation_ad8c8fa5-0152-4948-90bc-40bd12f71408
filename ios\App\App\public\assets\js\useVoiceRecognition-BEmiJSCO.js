const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/web-BEWucr1y.js","assets/js/index-BtK6VV6Z.js","assets/js/mui-vendor-hRDvsX89.js","assets/js/react-vendor-C9ilihHH.js","assets/js/utils-vendor-BDm_82Hk.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/css/index-C_2Yhxqf.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,i,s)=>((t,i,s)=>i in t?e(t,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[i]=s)(t,"symbol"!=typeof i?i+"":i,s);import{r as i}from"./react-vendor-C9ilihHH.js";import{_ as s}from"./syntax-vendor-DfDNeb5M.js";import{aw as a,ax as n,ay as r}from"./index-BtK6VV6Z.js";const o=a("SpeechRecognition",{web:()=>s((()=>import("./web-BEWucr1y.js")),__vite__mapDeps([0,1,2,3,4,5,6])).then((e=>new e.SpeechRecognitionWeb))}),c=class e{constructor(){t(this,"apiKey",""),t(this,"model","whisper-1"),t(this,"language"),t(this,"temperature",0),t(this,"responseFormat","json")}static getInstance(){return e.instance||(e.instance=new e),e.instance}setApiKey(e){this.apiKey=e}setModel(e){this.model=e}setLanguage(e){this.language=e}setTemperature(e){this.temperature=e}setResponseFormat(e){this.responseFormat=e}async transcribeAudio(e){var t;if(!this.apiKey)throw new Error("API密钥未设置，请先设置API密钥");try{const i=new FormData;i.append("file",e,"recording.webm"),i.append("model",this.model),this.language&&i.append("language",this.language),i.append("temperature",this.temperature.toString()),i.append("response_format",this.responseFormat);const s=await fetch("https://api.openai.com/v1/audio/transcriptions",{method:"POST",headers:{Authorization:`Bearer ${this.apiKey}`},body:i});if(!s.ok){const e=await s.json();throw new Error(`OpenAI API错误: ${(null==(t=e.error)?void 0:t.message)||s.statusText}`)}return await s.json()}catch(i){throw i}}async recordAndTranscribe(e=5e3){try{const t=await navigator.mediaDevices.getUserMedia({audio:!0}),i=new MediaRecorder(t),s=[];return i.addEventListener("dataavailable",(e=>{s.push(e.data)})),i.start(),await new Promise((t=>setTimeout(t,e))),new Promise(((e,a)=>{i.addEventListener("stop",(async()=>{try{t.getTracks().forEach((e=>e.stop()));const i=new Blob(s,{type:"audio/webm"}),a=await this.transcribeAudio(i);e(a)}catch(i){a(i)}})),i.stop()}))}catch(t){throw console.error("录制或转录失败:",t),t}}getAvailableModels(){return["whisper-1"]}};t(c,"instance");const h=c.getInstance(),l=class e{constructor(){if(t(this,"isListening",!1),t(this,"partialResultsCallback",null),t(this,"errorCallback",null),t(this,"listeningStateCallback",null),t(this,"provider","capacitor"),t(this,"recordingDuration",5e3),t(this,"recordingTimeoutId",null),t(this,"recordingStream",null),t(this,"mediaRecorder",null),t(this,"audioChunks",[]),t(this,"isWebEnvironment",!1),t(this,"webSpeechRecognition",null),this.isWebEnvironment="undefined"!=typeof window&&!window.hasOwnProperty("Capacitor"),this.isWebEnvironment&&(window.SpeechRecognition||window.webkitSpeechRecognition)){const e=window.SpeechRecognition||window.webkitSpeechRecognition;this.webSpeechRecognition=new e,this.webSpeechRecognition&&(this.webSpeechRecognition.continuous=!0,this.webSpeechRecognition.interimResults=!0,this.webSpeechRecognition.onresult=e=>{if("capacitor"!==this.provider)return;const t=e.results[e.results.length-1];(t.isFinal&&this.partialResultsCallback||this.partialResultsCallback)&&this.partialResultsCallback(t[0].transcript)},this.webSpeechRecognition.onend=()=>{"capacitor"===this.provider&&(this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"))},this.webSpeechRecognition.onerror=e=>{"capacitor"===this.provider&&(this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"),this.errorCallback&&this.errorCallback(new Error(`语音识别错误: ${e.error}`)))})}this.loadProvider(),this.isWebEnvironment||o.addListener("partialResults",(e=>{"capacitor"===this.provider&&e.matches&&e.matches.length>0&&this.partialResultsCallback&&this.partialResultsCallback(e.matches[0])}))}async loadProvider(){try{const e=await n("speech_recognition_provider");"openai"!==e&&"capacitor"!==e||(this.provider=e)}catch(e){}}static getInstance(){return e.instance||(e.instance=new e),e.instance}async setProvider(e){this.isListening&&await this.stopRecognition(),this.provider=e,await r("speech_recognition_provider",e)}getProvider(){return this.provider}async checkPermissions(){if("capacitor"===this.provider)if(this.isWebEnvironment)try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach((e=>e.stop())),{speechRecognition:"granted"}}catch(e){return e instanceof DOMException&&"NotAllowedError"===e.name?{speechRecognition:"denied"}:{speechRecognition:"prompt"}}else try{return await o.checkPermissions()}catch(e){return{speechRecognition:"unknown"}}else try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach((e=>e.stop())),{speechRecognition:"granted"}}catch(e){return e instanceof DOMException&&"NotAllowedError"===e.name?{speechRecognition:"denied"}:{speechRecognition:"prompt"}}}async requestPermissions(){if("capacitor"===this.provider)if(this.isWebEnvironment)try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach((e=>e.stop())),{speechRecognition:"granted"}}catch(e){return e instanceof DOMException&&e.name,{speechRecognition:"denied"}}else try{return await o.requestPermissions()}catch(e){return{speechRecognition:"denied"}}else try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach((e=>e.stop())),{speechRecognition:"granted"}}catch(e){return e instanceof DOMException&&e.name,{speechRecognition:"denied"}}}isVoiceRecognitionAvailable(){if("capacitor"===this.provider){if(this.isWebEnvironment){const e=!(!window.SpeechRecognition&&!window.webkitSpeechRecognition),t=window.isSecureContext||"https:"===window.location.protocol||"localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname;return e&&t&&!!this.webSpeechRecognition}return!0}return!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)}async startRecognition(e){if(this.isListening)try{await this.stopRecognition(),await new Promise((e=>setTimeout(e,300)))}catch(t){}if(!this.isVoiceRecognitionAvailable()){const e=new Error("语音识别在当前环境下不可用");throw this.errorCallback&&this.errorCallback(e),e}try{if(this.isListening=!0,this.listeningStateCallback&&this.listeningStateCallback("started"),"capacitor"===this.provider)if(this.isWebEnvironment&&this.webSpeechRecognition){this.webSpeechRecognition.onend=()=>{this.isListening&&(this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"))},this.webSpeechRecognition.onerror=e=>{this.isListening&&(this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped")),this.errorCallback&&this.errorCallback(new Error(`语音识别错误: ${e.error}`))},this.webSpeechRecognition.lang=(null==e?void 0:e.language)||"zh-CN";try{this.webSpeechRecognition.start()}catch(i){throw this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"),i}}else await o.start({language:(null==e?void 0:e.language)||"zh-CN",maxResults:(null==e?void 0:e.maxResults)||5,partialResults:!1!==(null==e?void 0:e.partialResults),popup:(null==e?void 0:e.popup)||!1});else await this.startWhisperRecognition()}catch(t){throw this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"),this.errorCallback&&this.errorCallback(t),t}}async startWhisperRecognition(){try{this.recordingStream=await navigator.mediaDevices.getUserMedia({audio:!0}),this.mediaRecorder=new MediaRecorder(this.recordingStream),this.audioChunks=[],this.mediaRecorder.addEventListener("dataavailable",(e=>{this.audioChunks.push(e.data)})),this.mediaRecorder.addEventListener("stop",(async()=>{try{const e=new Blob(this.audioChunks,{type:"audio/webm"});this.recordingStream&&(this.recordingStream.getTracks().forEach((e=>e.stop())),this.recordingStream=null),await this.loadWhisperSettings();const t=await h.transcribeAudio(e);this.partialResultsCallback&&t.text&&this.partialResultsCallback(t.text)}catch(e){this.errorCallback&&this.errorCallback(e)}finally{this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"),this.mediaRecorder=null}})),this.mediaRecorder.start(),this.recordingTimeoutId=window.setTimeout((()=>{this.stopWhisperRecording()}),this.recordingDuration)}catch(e){throw this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"),this.errorCallback&&this.errorCallback(e),e}}stopWhisperRecording(){this.recordingTimeoutId&&(clearTimeout(this.recordingTimeoutId),this.recordingTimeoutId=null),this.mediaRecorder&&"inactive"!==this.mediaRecorder.state&&this.mediaRecorder.stop()}async loadWhisperSettings(){try{const e=await n("whisper_api_key")||"",t=await n("whisper_model")||"whisper-1",i=await n("whisper_language"),s=Number(await n("whisper_temperature")||"0"),a=await n("whisper_response_format")||"json";h.setApiKey(e),h.setModel(t),i&&h.setLanguage(i),h.setTemperature(s),h.setResponseFormat(a)}catch(e){}}async stopRecognition(){if(this.isListening)try{if(this.isListening=!1,this.listeningStateCallback&&this.listeningStateCallback("stopped"),"capacitor"===this.provider)if(this.isWebEnvironment&&this.webSpeechRecognition)try{this.webSpeechRecognition.stop()}catch(e){}else await o.stop().catch((()=>{}));else this.stopWhisperRecording()}catch(t){this.errorCallback&&this.errorCallback(t)}}setPartialResultsCallback(e){this.partialResultsCallback=e}setListeningStateCallback(e){this.listeningStateCallback=e}setErrorCallback(e){this.errorCallback=e}getIsListening(){return this.isListening}async getSupportedLanguages(){if("capacitor"!==this.provider)return["zh","en","ja","ko","fr","de","es","ru","pt","it","nl","tr","pl","ar","hi","id","fi","vi","he","uk","el","ms","cs","ro","da","hu","ta","no","th","ur","hr","bg","lt","la","mi","ml","cy","sk","te","fa","lv","bn","sr","az","sl","kn","et","mk","br","eu","is","hy","ne","mn","bs","kk","sq","sw","gl","mr","pa","si","km","sn","yo","so","af","oc","ka","be","tg","sd","gu","am","yi","lo","uz","fo","ht","ps","tk","nn","mt","sa","lb","my","bo","tl","mg","as","tt","haw","ln","ha","ba","jw","su"];if(this.isWebEnvironment)return["zh-CN","en-US","fr-FR","de-DE","ja-JP","ko-KR","es-ES","it-IT","pt-BR","ru-RU"];try{return(await o.getSupportedLanguages()).languages||[]}catch(e){return[]}}setRecordingDuration(e){this.recordingDuration=e}};t(l,"instance");const p=l.getInstance(),g=()=>{const[e,t]=i.useState(p.getIsListening()),[s,a]=i.useState(""),[n,r]=i.useState("unknown"),[o,c]=i.useState(null);i.useEffect((()=>{const e=p;return e.setPartialResultsCallback((e=>{a(e)})),e.setListeningStateCallback((e=>{t("started"===e),"stopped"===e&&a("")})),e.setErrorCallback((e=>{c(e),t(!1)})),e.checkPermissions().then((e=>{r(e.speechRecognition)})),()=>{}}),[]);const h=i.useCallback((async()=>{const e=await p.checkPermissions();if(r(e.speechRecognition),"granted"!==e.speechRecognition){const e=await p.requestPermissions();return r(e.speechRecognition),"granted"===e.speechRecognition}return"granted"===e.speechRecognition}),[]),l=i.useCallback((async e=>{c(null);if(await h())try{await p.startRecognition(e)}catch(t){c(t)}else c(new Error("Speech recognition permission not granted."))}),[h]),g=i.useCallback((async()=>{try{await p.stopRecognition()}catch(e){c(e)}}),[]),d=i.useCallback((async()=>await p.getSupportedLanguages()),[]);return{isListening:e,recognitionText:s,permissionStatus:n,error:o,startRecognition:l,stopRecognition:g,checkAndRequestPermissions:h,getSupportedLanguages:d}};export{h as o,g as u,p as v};
