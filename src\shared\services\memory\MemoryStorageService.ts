/**
 * 记忆存储服务
 * 处理记忆的存储、检索和管理
 */

import type { MemoryRecord, MemoryCategory } from '../../types/internalMemory';
import { InternalMemoryService } from './InternalMemoryService';
import { getMemoryConfig } from '../../config/internalMemoryConfig';
import LoggerService from '../LoggerService';

/**
 * 记忆存储服务类
 * 提供高级的记忆存储和管理功能
 */
export class MemoryStorageService {
  private static instance: MemoryStorageService;
  private memoryService: InternalMemoryService;
  private config = getMemoryConfig();

  private constructor() {
    this.memoryService = InternalMemoryService.getInstance();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): MemoryStorageService {
    if (!MemoryStorageService.instance) {
      MemoryStorageService.instance = new MemoryStorageService();
    }
    return MemoryStorageService.instance;
  }

  /**
   * 批量保存记忆
   */
  public async saveMemories(
    userId: string,
    facts: string[],
    categories: MemoryCategory[],
    importance: number[],
    metadata: Record<string, any> = {}
  ): Promise<MemoryRecord[]> {
    try {
      if (!this.memoryService.isEnabled()) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return [];
      }

      const savedMemories: MemoryRecord[] = [];

      for (let i = 0; i < facts.length; i++) {
        const fact = facts[i];
        const category = categories[i] || 'background';
        const imp = importance[i] || 5;

        // 检查是否已存在相同内容的记忆
        const contentHash = this.generateContentHash(fact);
        const exists = await this.memoryService.isMemoryExists(userId, contentHash);

        if (exists) {
          LoggerService.log('INFO', 'Memory already exists, skipping', { 
            userId, 
            contentHash 
          });
          continue;
        }

        // 创建新记忆
        const memory = this.memoryService.createMemoryRecord(
          userId,
          fact,
          category,
          imp,
          {
            ...metadata,
            extractedAt: new Date().toISOString()
          }
        );

        // 保存记忆
        await this.memoryService.saveMemory(memory);
        savedMemories.push(memory);

        LoggerService.log('INFO', 'Memory saved', {
          memoryId: memory.id,
          category: memory.category,
          importance: memory.importance
        });
      }

      // 检查是否需要清理旧记忆
      await this.enforceStorageLimits(userId);

      return savedMemories;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to save memories', { error, userId });
      return [];
    }
  }

  /**
   * 获取用户记忆（带分页）
   */
  public async getUserMemoriesWithPagination(
    userId: string,
    options: {
      category?: MemoryCategory;
      limit?: number;
      offset?: number;
      sortBy?: 'createdAt' | 'updatedAt' | 'importance';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    memories: MemoryRecord[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      if (!this.memoryService.isEnabled()) {
        return { memories: [], total: 0, hasMore: false };
      }

      const {
        category,
        limit = 20,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      // 获取记忆
      let memories: MemoryRecord[];
      if (category) {
        memories = await this.memoryService.getMemoriesByCategory(userId, category);
      } else {
        memories = await this.memoryService.getUserMemories(userId);
      }

      // 排序
      memories.sort((a, b) => {
        let aValue: any, bValue: any;
        
        switch (sortBy) {
          case 'createdAt':
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
            break;
          case 'updatedAt':
            aValue = a.updatedAt.getTime();
            bValue = b.updatedAt.getTime();
            break;
          case 'importance':
            aValue = a.importance;
            bValue = b.importance;
            break;
          default:
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
        }

        if (sortOrder === 'asc') {
          return aValue - bValue;
        } else {
          return bValue - aValue;
        }
      });

      // 分页
      const total = memories.length;
      const paginatedMemories = memories.slice(offset, offset + limit);
      const hasMore = offset + limit < total;

      return {
        memories: paginatedMemories,
        total,
        hasMore
      };
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get user memories with pagination', { 
        error, 
        userId 
      });
      return { memories: [], total: 0, hasMore: false };
    }
  }

  /**
   * 搜索记忆（文本搜索）
   */
  public async searchMemories(
    userId: string,
    query: string,
    options: {
      category?: MemoryCategory;
      limit?: number;
    } = {}
  ): Promise<MemoryRecord[]> {
    try {
      if (!this.memoryService.isEnabled() || !query.trim()) {
        return [];
      }

      const { category, limit = 10 } = options;

      // 获取记忆
      let memories: MemoryRecord[];
      if (category) {
        memories = await this.memoryService.getMemoriesByCategory(userId, category);
      } else {
        memories = await this.memoryService.getUserMemories(userId);
      }

      // 简单的文本搜索
      const queryLower = query.toLowerCase();
      const matchedMemories = memories
        .filter(memory => 
          memory.content.toLowerCase().includes(queryLower)
        )
        .sort((a, b) => {
          // 按重要性和相关性排序
          const aRelevance = this.calculateTextRelevance(a.content, query);
          const bRelevance = this.calculateTextRelevance(b.content, query);
          
          if (aRelevance !== bRelevance) {
            return bRelevance - aRelevance;
          }
          
          return b.importance - a.importance;
        })
        .slice(0, limit);

      return matchedMemories;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to search memories', { error, userId, query });
      return [];
    }
  }

  /**
   * 强制执行存储限制
   */
  private async enforceStorageLimits(userId: string): Promise<void> {
    try {
      const maxMemories = this.config.storage.maxMemoriesPerUser;
      if (maxMemories <= 0) {
        return; // 无限制
      }

      const memories = await this.memoryService.getUserMemories(userId);
      
      if (memories.length > maxMemories) {
        // 按重要性和时间排序，删除最不重要的旧记忆
        const sortedMemories = memories.sort((a, b) => {
          // 首先按重要性排序
          if (a.importance !== b.importance) {
            return a.importance - b.importance;
          }
          // 然后按创建时间排序（旧的优先删除）
          return a.createdAt.getTime() - b.createdAt.getTime();
        });

        const toDelete = sortedMemories.slice(0, memories.length - maxMemories);
        
        for (const memory of toDelete) {
          await this.memoryService.deleteMemory(memory.id);
        }

        LoggerService.log('INFO', 'Enforced storage limits', {
          userId,
          deletedCount: toDelete.length,
          remainingCount: maxMemories
        });
      }

      // 清理过期记忆
      await this.memoryService.cleanupExpiredMemories(userId);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to enforce storage limits', { error, userId });
    }
  }

  /**
   * 计算文本相关性
   */
  private calculateTextRelevance(content: string, query: string): number {
    const contentLower = content.toLowerCase();
    const queryLower = query.toLowerCase();
    
    // 完全匹配得分最高
    if (contentLower.includes(queryLower)) {
      return 10;
    }
    
    // 计算单词匹配度
    const queryWords = queryLower.split(/\s+/);
    const contentWords = contentLower.split(/\s+/);
    
    let matchCount = 0;
    for (const queryWord of queryWords) {
      if (contentWords.some(contentWord => contentWord.includes(queryWord))) {
        matchCount++;
      }
    }
    
    return (matchCount / queryWords.length) * 5;
  }

  /**
   * 生成内容哈希
   */
  private generateContentHash(content: string): string {
    // 简单的哈希函数
    let hash = 0;
    if (content.length === 0) return hash.toString();
    
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取记忆统计信息（扩展版）
   */
  public async getExtendedMemoryStats(userId: string): Promise<{
    basic: any;
    categoryDistribution: Record<string, number>;
    importanceDistribution: Record<string, number>;
    timeDistribution: {
      thisWeek: number;
      thisMonth: number;
      older: number;
    };
  }> {
    try {
      const basicStats = await this.memoryService.getMemoryStats(userId);
      const memories = await this.memoryService.getUserMemories(userId);

      // 重要性分布
      const importanceDistribution: Record<string, number> = {};
      for (let i = 1; i <= 10; i++) {
        importanceDistribution[i.toString()] = 0;
      }
      
      memories.forEach(memory => {
        const importance = memory.importance.toString();
        importanceDistribution[importance] = (importanceDistribution[importance] || 0) + 1;
      });

      // 时间分布
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const timeDistribution = {
        thisWeek: memories.filter(m => m.createdAt > oneWeekAgo).length,
        thisMonth: memories.filter(m => m.createdAt > oneMonthAgo && m.createdAt <= oneWeekAgo).length,
        older: memories.filter(m => m.createdAt <= oneMonthAgo).length
      };

      return {
        basic: basicStats,
        categoryDistribution: basicStats.categoryCounts,
        importanceDistribution,
        timeDistribution
      };
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get extended memory stats', { error, userId });
      return {
        basic: { totalMemories: 0, categoryCounts: {}, recentGrowth: 0, averageImportance: 0 },
        categoryDistribution: {},
        importanceDistribution: {},
        timeDistribution: { thisWeek: 0, thisMonth: 0, older: 0 }
      };
    }
  }
}
