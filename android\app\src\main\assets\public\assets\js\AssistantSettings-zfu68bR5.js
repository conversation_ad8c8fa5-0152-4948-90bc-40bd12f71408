import{j as s,B as e,a as r,A as o,T as i,I as t,b as x,P as l,D as a,y as n,J as d,L as c,N as m,Q as b,x as h}from"./mui-vendor-DsBXMegs.js";import{u as j,r as p,a as g}from"./react-vendor-Be-rfjCm.js";import{d as f,A as v}from"./index-Ck4sQVom.js";import{T as y}from"./Tune-DzMCixvP.js";import{P as u}from"./Person-DE7LeOLt.js";import{C as S}from"./ChevronRight-DT2g11mS.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const z=()=>{const z=j(),[w,k]=p.useState(null),A=f((s=>s.assistants.currentAssistant)),C=f((s=>s.assistants.assistants));p.useEffect((()=>{A?k(A):C.length>0&&k(C[0])}),[A,C]);return s.jsxs(e,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:s=>"light"===s.palette.mode?r(s.palette.primary.main,.02):r(s.palette.background.default,.9)},children:[s.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:s=>s.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:s.jsxs(i,{children:[s.jsx(t,{edge:"start",color:"inherit",onClick:()=>{z("/chat")},"aria-label":"back",sx:{color:s=>s.palette.primary.main},children:s.jsx(v,{})}),s.jsx(x,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"助手设置"})]})}),s.jsxs(e,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[w&&s.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[s.jsxs(e,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[s.jsx(x,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"当前选中助手"}),s.jsx(x,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"查看和配置选中助手的详细信息"})]}),s.jsx(a,{}),s.jsx(e,{sx:{p:{xs:1.5,sm:2}},children:s.jsxs(e,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(n,{sx:{width:{xs:40,sm:48},height:{xs:40,sm:48},bgcolor:r("#9333EA",.12),color:"#9333EA",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:w.emoji||w.name.charAt(0)}),s.jsxs(e,{sx:{flex:1},children:[s.jsx(x,{variant:"h6",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:w.name}),s.jsx(x,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:w.description||"暂无描述"}),w.isSystem&&s.jsx(d,{label:"系统助手",size:"small",sx:{mt:.5,bgcolor:r("#9333EA",.1),color:"#9333EA",fontWeight:500,fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24}}})]})]})})]}),s.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[s.jsxs(e,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[s.jsx(x,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"选择助手"}),s.jsx(x,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"选择要配置的助手"})]}),s.jsx(a,{}),s.jsx(c,{disablePadding:!0,children:C.map(((e,o)=>s.jsxs(g.Fragment,{children:[s.jsxs(m,{selected:(null==w?void 0:w.id)===e.id,onClick:()=>(s=>{k(s)})(e),sx:{transition:"all 0.2s","&.Mui-selected":{bgcolor:s=>r(s.palette.primary.main,.1),"&:hover":{bgcolor:s=>r(s.palette.primary.main,.15)}},"&:hover":{bgcolor:s=>r(s.palette.primary.main,.05)}},children:[s.jsx(b,{children:s.jsx(n,{sx:{width:{xs:32,sm:36},height:{xs:32,sm:36},bgcolor:r("#9333EA",.12),color:"#9333EA",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:e.emoji||s.jsx(u,{})})}),s.jsx(h,{primary:s.jsx(x,{sx:{fontWeight:600,color:"text.primary",fontSize:{xs:"0.9rem",sm:"1rem"}},children:e.name}),secondary:s.jsx(x,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"},color:"text.secondary"},children:e.description||"暂无描述"})}),e.isSystem&&s.jsx(d,{label:"系统",size:"small",sx:{mr:1,bgcolor:r("#8b5cf6",.1),color:"#8b5cf6",fontWeight:500,fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24}}})]}),o<C.length-1&&s.jsx(a,{variant:"inset",component:"li",sx:{ml:0}})]},e.id)))})]}),s.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[s.jsxs(e,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[s.jsx(x,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"设置选项"}),s.jsx(x,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"配置选中助手的各项参数和功能"})]}),s.jsx(a,{}),s.jsxs(c,{disablePadding:!0,children:[s.jsxs(m,{onClick:()=>{w&&z("/settings/assistant-model-settings",{state:{assistant:w}})},disabled:!w,sx:{transition:"all 0.2s","&:hover":{bgcolor:s=>r(s.palette.primary.main,.05)}},children:[s.jsx(b,{children:s.jsx(n,{sx:{bgcolor:r("#06b6d4",.12),color:"#06b6d4",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:s.jsx(y,{})})}),s.jsx(h,{primary:s.jsx(x,{sx:{fontWeight:600,color:"text.primary",fontSize:{xs:"0.9rem",sm:"1rem"}},children:"模型设置"}),secondary:s.jsx(x,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"},color:"text.secondary"},children:"配置助手的模型参数和行为"})}),s.jsx(S,{sx:{color:"text.secondary"}})]}),s.jsx(a,{variant:"inset",component:"li",sx:{ml:0}}),s.jsxs(m,{disabled:!0,children:[s.jsx(b,{children:s.jsx(n,{sx:{bgcolor:"rgba(0,0,0,0.05)",color:"text.disabled"},children:s.jsx(y,{})})}),s.jsx(h,{primary:s.jsx(x,{sx:{fontWeight:600,color:"text.disabled",fontSize:{xs:"0.9rem",sm:"1rem"}},children:"提示词设置"}),secondary:s.jsx(x,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"},color:"text.disabled"},children:"即将推出"})}),s.jsx(S,{sx:{color:"text.disabled"}})]}),s.jsx(a,{variant:"inset",component:"li",sx:{ml:0}}),s.jsxs(m,{disabled:!0,children:[s.jsx(b,{children:s.jsx(n,{sx:{bgcolor:"rgba(0,0,0,0.05)",color:"text.disabled"},children:s.jsx(y,{})})}),s.jsx(h,{primary:s.jsx(x,{sx:{fontWeight:600,color:"text.disabled",fontSize:{xs:"0.9rem",sm:"1rem"}},children:"知识库设置"}),secondary:s.jsx(x,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"},color:"text.disabled"},children:"即将推出"})}),s.jsx(S,{sx:{color:"text.disabled"}})]}),s.jsx(a,{variant:"inset",component:"li",sx:{ml:0}}),s.jsxs(m,{disabled:!0,children:[s.jsx(b,{children:s.jsx(n,{sx:{bgcolor:"rgba(0,0,0,0.05)",color:"text.disabled"},children:s.jsx(y,{})})}),s.jsx(h,{primary:s.jsx(x,{sx:{fontWeight:600,color:"text.disabled",fontSize:{xs:"0.9rem",sm:"1rem"}},children:"MCP设置"}),secondary:s.jsx(x,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"},color:"text.disabled"},children:"即将推出"})}),s.jsx(S,{sx:{color:"text.disabled"}})]})]})]})]})]})};export{z as default};
