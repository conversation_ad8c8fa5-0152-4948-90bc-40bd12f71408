[{"pkg": "@capacitor-community/file-opener", "classpath": "com.ryltsov.alex.plugins.file.opener.FileOpenerPlugin"}, {"pkg": "@capacitor-community/native-audio", "classpath": "com.getcapacitor.community.audio.NativeAudio"}, {"pkg": "@capacitor-community/safe-area", "classpath": "com.getcapacitor.community.safearea.SafeAreaPlugin"}, {"pkg": "@capacitor-community/speech-recognition", "classpath": "com.getcapacitor.community.speechrecognition.SpeechRecognition"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/browser", "classpath": "com.capacitorjs.plugins.browser.BrowserPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/clipboard", "classpath": "com.capacitorjs.plugins.clipboard.ClipboardPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/preferences", "classpath": "com.capacitorjs.plugins.preferences.PreferencesPlugin"}, {"pkg": "@capacitor/share", "classpath": "com.capacitorjs.plugins.share.SharePlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@capacitor/toast", "classpath": "com.capacitorjs.plugins.toast.ToastPlugin"}, {"pkg": "@capawesome/capacitor-android-edge-to-edge-support", "classpath": "io.capawesome.capacitorjs.plugins.androidedgetoedgesupport.EdgeToEdgePlugin"}, {"pkg": "@capawesome/capacitor-file-picker", "classpath": "io.capawesome.capacitorjs.plugins.filepicker.FilePickerPlugin"}, {"pkg": "capacitor-cors-bypass-enhanced", "classpath": "com.capacitor.cors.CorsBypassPlugin"}]