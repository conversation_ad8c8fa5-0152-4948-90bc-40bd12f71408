import{c as e,j as r,o as s,p as i,q as a,B as t,a4 as n,a5 as l,a6 as o,r as d,m as c,b as x,k as m,l as h,J as u,n as p,I as j,a7 as g,t as v,y as f,F as b,e as y,S as C,M as w,g as S,h as z,z as k,a3 as I,a as W,A,T as q,K as M,P as D,a8 as B}from"./mui-vendor-hRDvsX89.js";import{r as E,u as H}from"./react-vendor-C9ilihHH.js";import{d as P,e as G,ar as N,as as T,A as V,aa as F,at as K}from"./index-BtK6VV6Z.js";import{A as L}from"./Add-B_Z45Y27.js";import{E as O}from"./Edit-D9CSXufJ.js";import{A as J}from"./AutoFixHigh-CmaJkOTk.js";import{C as Y}from"./CompareArrows-C1JIeSak.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const Q=e(r.jsx("path",{d:"M22 11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3z"})),R=({open:e,onClose:k,onSave:I,combo:W})=>{const[A,q]=E.useState(0),[M,D]=E.useState({name:"",description:"",strategy:"routing",enabled:!0,models:[]}),B=P((e=>e.settings.providers)).filter((e=>"model-combo"!==e.id&&e.isEnabled)).flatMap((e=>e.models.filter((e=>e.enabled)).map((r=>({...r,providerName:e.name,providerColor:e.color,providerAvatar:e.avatar}))))),H=["基本信息","选择策略","配置模型","完成设置"];E.useEffect((()=>{D(W?{name:W.name,description:W.description||"",strategy:W.strategy,enabled:W.enabled,models:W.models.map((e=>({modelId:e.modelId,role:e.role,weight:e.weight,priority:e.priority})))}:{name:"",description:"",strategy:"routing",enabled:!0,models:[]}),q(0)}),[W,e]);const N=()=>{D((e=>({...e,models:[...e.models,{modelId:"",role:"primary",weight:1,priority:1}]})))},T=(e,r,s)=>{D((i=>({...i,models:i.models.map(((i,a)=>a===e?{...i,[r]:s}:i))})))},V=e=>{switch(e){case"routing":return"智能路由 (开发中)";case"ensemble":return"模型集成 (开发中)";case"comparison":return"对比分析";case"cascade":return"级联调用 (开发中)";case"sequential":return"顺序执行";default:return e}},F=e=>{switch(e){case"routing":return"根据查询内容智能选择最适合的模型进行响应（基础功能可用，高级路由规则开发中）";case"ensemble":return"多个模型同时工作，综合它们的输出结果（基础功能可用，高级集成算法开发中）";case"comparison":return"同时使用多个模型，展示对比结果供用户选择（功能完整可用）";case"cascade":return"先使用便宜的模型，必要时升级到更强的模型（功能开发中，暂时使用路由策略）";case"sequential":return"按顺序使用模型，如先思考再生成（功能完整可用）";default:return""}},K=e=>{switch(e){case 0:return""!==M.name.trim();case 1:return Boolean(M.strategy);case 2:return M.models.length>0&&M.models.every((e=>""!==e.modelId.trim()));case 3:return!0;default:return!1}};return r.jsxs(s,{open:e,onClose:k,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{minHeight:"500px"}},children:[r.jsx(i,{children:W?"编辑模型组合":"创建模型组合"}),r.jsx(a,{children:r.jsxs(t,{sx:{width:"100%",mt:2},children:[r.jsx(n,{activeStep:A,alternativeLabel:!0,children:H.map((e=>r.jsx(l,{children:r.jsx(o,{children:e})},e)))}),r.jsx(t,{sx:{mt:3},children:(e=>{switch(e){case 0:return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2},children:[r.jsx(v,{fullWidth:!0,label:"组合名称",value:M.name,onChange:e=>D((r=>({...r,name:e.target.value}))),required:!0}),r.jsx(v,{fullWidth:!0,label:"描述",value:M.description,onChange:e=>D((r=>({...r,description:e.target.value}))),multiline:!0,rows:3}),r.jsx(S,{control:r.jsx(z,{checked:M.enabled,onChange:e=>D((r=>({...r,enabled:e.target.checked})))}),label:"启用此组合"})]});case 1:return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2},children:[r.jsxs(b,{fullWidth:!0,children:[r.jsx(y,{children:"组合策略"}),r.jsxs(C,{value:M.strategy,onChange:e=>D((r=>({...r,strategy:e.target.value}))),label:"组合策略",children:[r.jsx(w,{value:"routing",children:"智能路由 (开发中)"}),r.jsx(w,{value:"ensemble",children:"模型集成 (开发中)"}),r.jsx(w,{value:"comparison",children:"对比分析"}),r.jsx(w,{value:"cascade",children:"级联调用 (开发中)"}),r.jsx(w,{value:"sequential",children:"顺序执行"})]})]}),r.jsx(p,{severity:"info",children:F(M.strategy)})]});case 2:return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2},children:[r.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r.jsx(x,{variant:"h6",children:"配置模型"}),r.jsx(c,{startIcon:r.jsx(L,{}),onClick:N,variant:"outlined",size:"small",children:"添加模型"})]}),0===M.models.length?r.jsx(p,{severity:"warning",children:"请至少添加一个模型"}):M.models.map(((e,s)=>r.jsx(m,{variant:"outlined",children:r.jsxs(h,{children:[r.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[r.jsxs(x,{variant:"subtitle1",children:["模型 ",s+1]}),r.jsx(j,{size:"small",onClick:()=>(e=>{D((r=>({...r,models:r.models.filter(((r,s)=>s!==e))})))})(s),color:"error",children:r.jsx(G,{})})]}),r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2},children:[r.jsx(g,{fullWidth:!0,options:B,getOptionLabel:e=>e.name,value:B.find((r=>r.id===e.modelId))||null,onChange:(e,r)=>{T(s,"modelId",(null==r?void 0:r.id)||"")},renderOption:(e,s)=>{var i;return r.jsxs(t,{component:"li",...e,sx:{display:"flex",alignItems:"center",gap:1},children:[r.jsx(f,{sx:{width:24,height:24,bgcolor:s.providerColor||"#666",fontSize:"0.75rem"},children:s.providerAvatar||(null==(i=s.providerName)?void 0:i[0])||"M"}),r.jsxs(t,{children:[r.jsx(x,{variant:"body2",children:s.name}),r.jsx(x,{variant:"caption",color:"text.secondary",children:s.providerName})]})]})},renderInput:e=>r.jsx(v,{...e,label:"选择模型",placeholder:"从已配置的模型中选择",required:!0})}),r.jsxs(b,{fullWidth:!0,children:[r.jsx(y,{children:"角色"}),r.jsxs(C,{value:e.role,onChange:e=>T(s,"role",e.target.value),label:"角色",children:[r.jsx(w,{value:"primary",children:"主要"}),r.jsx(w,{value:"secondary",children:"次要"}),r.jsx(w,{value:"fallback",children:"备用"}),r.jsx(w,{value:"thinking",children:"思考"}),r.jsx(w,{value:"generating",children:"生成"})]})]}),("ensemble"===M.strategy||"comparison"===M.strategy)&&r.jsx(v,{fullWidth:!0,label:"权重",type:"number",value:e.weight||1,onChange:e=>T(s,"weight",Number(e.target.value)),inputProps:{min:0,max:1,step:.1}}),("cascade"===M.strategy||"sequential"===M.strategy)&&r.jsx(v,{fullWidth:!0,label:"优先级",type:"number",value:e.priority||1,onChange:e=>T(s,"priority",Number(e.target.value)),inputProps:{min:1}})]})]})},s)))]});case 3:return r.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2},children:[r.jsx(x,{variant:"h6",children:"确认配置"}),r.jsx(m,{variant:"outlined",children:r.jsxs(h,{children:[r.jsx(x,{variant:"subtitle1",gutterBottom:!0,children:M.name}),r.jsx(x,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:M.description}),r.jsxs(t,{sx:{display:"flex",gap:1,mb:2},children:[r.jsx(u,{label:V(M.strategy),size:"small"}),r.jsx(u,{label:M.enabled?"已启用":"已禁用",size:"small",color:M.enabled?"success":"default"})]}),r.jsxs(x,{variant:"body2",children:["包含 ",M.models.length," 个模型"]})]})})]});default:return null}})(A)})]})}),r.jsxs(d,{children:[r.jsx(c,{onClick:k,children:"取消"}),r.jsx(t,{sx:{flex:"1 1 auto"}}),r.jsx(c,{disabled:0===A,onClick:()=>{q((e=>e-1))},children:"上一步"}),A===H.length-1?r.jsx(c,{variant:"contained",onClick:()=>{I(M)},disabled:!K(A),children:"保存"}):r.jsx(c,{variant:"contained",onClick:()=>{q((e=>e+1))},disabled:!K(A),children:"下一步"})]})]})},U=()=>{const e=H(),n=k(),l=I(n.breakpoints.down("md")),o=I(n.breakpoints.down("sm")),[p,g]=E.useState([]),[v,f]=E.useState([]),[b,y]=E.useState(!1),[C,w]=E.useState(null),[S,z]=E.useState(!1),[P,U]=E.useState(null),{syncModelCombos:X}=N();E.useEffect((()=>{Z()}),[]);const Z=async()=>{try{const[e,r]=await Promise.all([T.getAllCombos(),Promise.resolve(T.getTemplates())]);g(e),f(r)}catch(e){console.error("加载模型组合数据失败:",e)}},$=()=>{w(null),y(!0)},_=e=>{switch(e){case"routing":default:return r.jsx(F,{});case"ensemble":return r.jsx(Q,{});case"comparison":return r.jsx(Y,{});case"cascade":return r.jsx(J,{});case"sequential":return r.jsx(K,{})}},ee=e=>{switch(e){case"routing":return"智能路由 (开发中)";case"ensemble":return"模型集成 (开发中)";case"comparison":return"对比分析";case"cascade":return"级联调用 (开发中)";case"sequential":return"顺序执行";default:return e}},re=e=>{switch(e){case"routing":return"#2196f3";case"ensemble":return"#4caf50";case"comparison":return"#ff9800";case"cascade":return"#9c27b0";case"sequential":return"#f44336";default:return"#757575"}};return r.jsxs(t,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?W(e.palette.primary.main,.02):W(e.palette.background.default,.9)},children:[r.jsx(A,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(q,{children:[r.jsx(j,{edge:"start",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(V,{})}),r.jsx(x,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"模型组合"})]})}),r.jsxs(t,{sx:{flexGrow:1,overflow:"auto",px:l?1:2,py:l?1:2,mt:8},children:[v.length>0&&r.jsxs(t,{sx:{mb:l?2:4},children:[r.jsx(x,{variant:l?"subtitle1":"h6",sx:{mb:l?1:2,fontWeight:600,fontSize:l?"1.1rem":void 0},children:"预设模板"}),r.jsx(t,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:o?"1fr":"repeat(2, 1fr)",md:"repeat(2, 1fr)",lg:"repeat(3, 1fr)"},gap:l?1:2},children:v.map((e=>r.jsx(t,{children:r.jsxs(m,{sx:{height:"100%",display:"flex",flexDirection:"column",border:"1px solid",borderColor:"divider","&:hover":{borderColor:"primary.main",transform:l?"none":"translateY(-2px)",boxShadow:l?1:2},transition:"all 0.2s ease-in-out"},children:[r.jsxs(h,{sx:{flexGrow:1,p:l?1.5:2,"&:last-child":{pb:l?1.5:2}},children:[r.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:l?.5:1},children:[r.jsx(t,{sx:{mr:1,fontSize:l?"1.2rem":"1.5rem"},children:e.icon}),r.jsx(x,{variant:l?"subtitle1":"h6",component:"div",sx:{fontWeight:600,fontSize:l?"1rem":void 0},children:e.name})]}),r.jsx(x,{variant:"body2",color:"text.secondary",sx:{mb:l?1:2,fontSize:l?"0.8rem":void 0,lineHeight:l?1.3:void 0},children:e.description}),r.jsx(u,{icon:_(e.strategy),label:ee(e.strategy),size:"small",sx:{bgcolor:W(re(e.strategy),.1),color:re(e.strategy),fontSize:l?"0.7rem":void 0,height:l?24:void 0,"& .MuiChip-icon":{fontSize:l?"0.9rem":void 0}}})]}),r.jsx(M,{sx:{p:l?1:2,pt:0},children:r.jsx(c,{size:"small",startIcon:r.jsx(L,{}),onClick:()=>(async e=>{try{const r={...e.template,name:e.name,description:e.description};await T.createCombo(r),await Z(),await X()}catch(r){console.error("从模板创建模型组合失败:",r)}})(e),sx:{ml:"auto",fontSize:l?"0.75rem":void 0,px:l?1:void 0},children:"使用模板"})})]})},e.id)))})]}),r.jsxs(t,{children:[r.jsx(x,{variant:l?"subtitle1":"h6",sx:{mb:l?1:2,fontWeight:600,fontSize:l?"1.1rem":void 0},children:"我的组合"}),0===p.length?r.jsxs(D,{sx:{p:l?2:4,textAlign:"center",border:"2px dashed",borderColor:"divider"},children:[r.jsx(F,{sx:{fontSize:l?36:48,color:"text.secondary",mb:l?1:2}}),r.jsx(x,{variant:l?"subtitle1":"h6",color:"text.secondary",gutterBottom:!0,sx:{fontSize:l?"1rem":void 0},children:"还没有创建任何模型组合"}),r.jsx(x,{variant:"body2",color:"text.secondary",sx:{mb:l?2:3,fontSize:l?"0.8rem":void 0},children:"创建模型组合来实现智能路由、模型集成等高级功能"}),r.jsx(c,{variant:"contained",startIcon:r.jsx(L,{}),onClick:$,size:l?"small":"medium",sx:{fontSize:l?"0.8rem":void 0},children:"创建第一个组合"})]}):r.jsx(t,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:o?"1fr":"repeat(2, 1fr)",md:"repeat(2, 1fr)",lg:"repeat(3, 1fr)"},gap:l?1:2},children:p.map((e=>r.jsx(t,{children:r.jsxs(m,{sx:{height:"100%",display:"flex",flexDirection:"column",border:"1px solid",borderColor:e.enabled?"success.main":"divider",opacity:e.enabled?1:.7},children:[r.jsxs(h,{sx:{flexGrow:1,p:l?1.5:2,"&:last-child":{pb:l?1.5:2}},children:[r.jsx(x,{variant:l?"subtitle1":"h6",component:"div",sx:{fontWeight:600,mb:l?.5:1,fontSize:l?"1rem":void 0},children:e.name}),r.jsx(x,{variant:"body2",color:"text.secondary",sx:{mb:l?1:2,fontSize:l?"0.8rem":void 0,lineHeight:l?1.3:void 0},children:e.description}),r.jsx(u,{icon:_(e.strategy),label:ee(e.strategy),size:"small",sx:{bgcolor:W(re(e.strategy),.1),color:re(e.strategy),mb:l?.5:1,fontSize:l?"0.7rem":void 0,height:l?24:void 0,"& .MuiChip-icon":{fontSize:l?"0.9rem":void 0}}}),r.jsxs(x,{variant:"caption",display:"block",color:"text.secondary",sx:{fontSize:l?"0.7rem":void 0},children:[e.models.length," 个模型"]})]}),r.jsxs(M,{sx:{p:l?1:2,pt:0,minHeight:l?"auto":void 0},children:[r.jsx(j,{size:"small",onClick:()=>(e=>{w(e),y(!0)})(e),color:"primary",sx:{p:l?.5:void 0,"& .MuiSvgIcon-root":{fontSize:l?"1rem":void 0}},children:r.jsx(O,{})}),r.jsx(j,{size:"small",onClick:()=>(e=>{U(e),z(!0)})(e),color:"error",sx:{p:l?.5:void 0,"& .MuiSvgIcon-root":{fontSize:l?"1rem":void 0}},children:r.jsx(G,{})})]})]})},e.id)))})]})]}),r.jsx(B,{color:"primary","aria-label":"add",size:l?"medium":"large",sx:{position:"fixed",bottom:l?12:16,right:l?12:16,width:l?48:void 0,height:l?48:void 0,"& .MuiSvgIcon-root":{fontSize:l?"1.2rem":void 0}},onClick:$,children:r.jsx(L,{})}),r.jsx(R,{open:b,onClose:()=>y(!1),onSave:async e=>{try{C?await T.updateCombo(C.id,e):await T.createCombo(e),await Z(),await X(),y(!1),w(null)}catch(r){console.error("保存模型组合失败:",r)}},combo:C,templates:v}),r.jsxs(s,{open:S,onClose:()=>z(!1),children:[r.jsx(i,{children:"确认删除"}),r.jsx(a,{children:r.jsxs(x,{children:['确定要删除模型组合 "',null==P?void 0:P.name,'" 吗？此操作无法撤销。']})}),r.jsxs(d,{children:[r.jsx(c,{onClick:()=>z(!1),children:"取消"}),r.jsx(c,{onClick:async()=>{if(P)try{await T.deleteCombo(P.id),await Z(),await X(),z(!1),U(null)}catch(e){console.error("删除模型组合失败:",e)}},color:"error",variant:"contained",children:"删除"})]})]})]})};export{U as default};
