const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/index-Dnlt-eWK.js","assets/js/mui-vendor-DsBXMegs.js","assets/js/react-vendor-Be-rfjCm.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/js/utils-vendor-D1FP9aB2.js","assets/css/index-BNwyRmd2.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,r=(r,s,t)=>((r,s,t)=>s in r?e(r,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[s]=t)(r,"symbol"!=typeof s?s+"":s,t);import{_ as s}from"./syntax-vendor-DfDNeb5M.js";import{j as t,B as a,b as i,a as o,A as n,T as c,I as l,m as d,n as x,Z as h,_ as m,P as b,D as y,L as g,w as j,x as p,h as v,i as u,F as f,S,M,J as w,t as C,aa as k,C as R,k as E,l as I}from"./mui-vendor-DsBXMegs.js";import{u as D,r as z}from"./react-vendor-Be-rfjCm.js";import{c as T,az as A,aA as O,aB as W,o as P,d as B,aC as L,aD as U,aE as _,l as N,aF as $,aG as F,aH as G,f as K,aI as H,aJ as q,aK as J,aL as V}from"./index-Dnlt-eWK.js";import{D as Y}from"./DialogModelSelector-BXMaaYok.js";import{D as Z}from"./DropdownModelSelector-Cblp38oH.js";import{M as Q,S as X,C as ee,a as re,B as se,b as te,D as ae,c as ie}from"./MemoryEnhancedMessageService-6SZNgCFE.js";import{A as oe,B as ne}from"./bot-xWxJFwPz.js";import{T as ce}from"./trash-2-WKKfG4Dh.js";import"./utils-vendor-D1FP9aB2.js";const le=T("bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),de=T("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var xe=(e=>(e.PREFERENCE="preference",e.BACKGROUND="background",e.SKILL="skill",e.HABIT="habit",e.PLAN="plan",e))(xe||{});class he{constructor(){r(this,"memoryService"),r(this,"storageService"),r(this,"embeddingService"),r(this,"searchService"),r(this,"contextBuilder"),r(this,"enhancedMessageService"),this.memoryService=A.getInstance(),this.storageService=Q.getInstance(),this.embeddingService=O.getInstance(),this.searchService=X.getInstance(),this.contextBuilder=ee.getInstance(),this.enhancedMessageService=re.getInstance()}async runBasicTests(){const e={},r=[],s="test-user-"+Date.now();try{W.log("INFO","Starting memory system basic tests",{testUserId:s}),e.serviceInitialization=this.testServiceInitialization(),e.createAndSaveMemory=await this.testCreateAndSaveMemory(s),e.retrieveMemories=await this.testRetrieveMemories(s),e.memoryStats=await this.testMemoryStats(s),e.textSearch=await this.testTextSearch(s),e.cleanup=await this.testCleanup(s);const t=Object.values(e).every((e=>!0===e));return W.log("INFO","Memory system basic tests completed",{testUserId:s,allPassed:t,results:e}),{success:t,results:e,errors:r}}catch(t){const a=t instanceof Error?t.message:String(t);return r.push(a),W.log("ERROR","Memory system tests failed",{error:t,testUserId:s}),{success:!1,results:e,errors:r}}}testServiceInitialization(){try{return!!(this.memoryService&&this.storageService&&this.embeddingService&&this.searchService&&this.contextBuilder&&this.enhancedMessageService)}catch(e){return W.log("ERROR","Service initialization test failed",{error:e}),!1}}async testCreateAndSaveMemory(e){try{const r=this.memoryService.createMemoryRecord(e,"用户喜欢喝咖啡，特别是拿铁",xe.PREFERENCE,8,{testData:!0});await this.memoryService.saveMemory(r);const s=await this.memoryService.getUserMemories(e);return s.length>0&&s.some((e=>e.id===r.id))}catch(r){return W.log("ERROR","Create and save memory test failed",{error:r,userId:e}),!1}}async testRetrieveMemories(e){try{const r=[{content:"用户是一名软件工程师",category:xe.BACKGROUND,importance:7},{content:"用户每天早上跑步",category:xe.HABIT,importance:6}];for(const a of r){const r=this.memoryService.createMemoryRecord(e,a.content,a.category,a.importance,{testData:!0});await this.memoryService.saveMemory(r)}const s=await this.memoryService.getMemoriesByCategory(e,xe.PREFERENCE),t=await this.memoryService.getMemoriesByCategory(e,xe.BACKGROUND);return s.length>0&&t.length>0}catch(r){return W.log("ERROR","Retrieve memories test failed",{error:r,userId:e}),!1}}async testMemoryStats(e){try{const r=await this.memoryService.getMemoryStats(e);return!!(r&&"number"==typeof r.totalMemories&&r.totalMemories>0&&r.categoryCounts&&"number"==typeof r.averageImportance)}catch(r){return W.log("ERROR","Memory stats test failed",{error:r,userId:e}),!1}}async testTextSearch(e){try{return(await this.storageService.searchMemories(e,"咖啡",{limit:5})).length>0}catch(r){return W.log("ERROR","Text search test failed",{error:r,userId:e}),!1}}async testCleanup(e){try{const r=await this.memoryService.getUserMemories(e);for(const e of r)await this.memoryService.deleteMemory(e.id);return 0===(await this.memoryService.getUserMemories(e)).length}catch(r){return W.log("ERROR","Cleanup test failed",{error:r,userId:e}),!1}}async runPerformanceTests(){const e={},r=[],s="perf-test-user-"+Date.now();try{W.log("INFO","Starting memory system performance tests",{testUserId:s});const t=Date.now();for(let e=0;e<10;e++){const r=this.memoryService.createMemoryRecord(s,`测试记忆 ${e}：用户喜欢${e%2==0?"咖啡":"茶"}`,xe.PREFERENCE,5+e%5,{testData:!0,index:e});await this.memoryService.saveMemory(r)}e.saveTime=Date.now()-t;const a=Date.now(),i=await this.memoryService.getUserMemories(s);e.retrieveTime=Date.now()-a,e.memoriesRetrieved=i.length;const o=Date.now(),n=await this.storageService.searchMemories(s,"咖啡",{limit:5});e.searchTime=Date.now()-o,e.searchResults=n.length;for(const e of i)await this.memoryService.deleteMemory(e.id);return W.log("INFO","Memory system performance tests completed",{testUserId:s,metrics:e}),{success:!0,metrics:e,errors:r}}catch(t){const a=t instanceof Error?t.message:String(t);return r.push(a),W.log("ERROR","Memory system performance tests failed",{error:t,testUserId:s}),{success:!1,metrics:e,errors:r}}}getSystemStatus(){return{enabled:this.memoryService.isEnabled(),services:{memoryService:!!this.memoryService,storageService:!!this.storageService,embeddingService:!!this.embeddingService,searchService:!!this.searchService,contextBuilder:!!this.contextBuilder,enhancedMessageService:!!this.enhancedMessageService},config:this.enhancedMessageService.getMemoryStatus()}}}function me(e){const{children:r,value:s,index:i,...o}=e;return t.jsx("div",{role:"tabpanel",hidden:s!==i,id:`memory-tabpanel-${i}`,"aria-labelledby":`memory-tab-${i}`,...o,children:s===i&&t.jsx(a,{sx:{py:3},children:r})})}const be=()=>{const e=D(),r=P(),T=B((e=>e.internalMemory.config)),A=B((e=>e.settings.providers||[])),O=B((e=>e.settings.modelSelectorStyle||"dialog")),[W,Q]=z.useState(T),[X,ee]=z.useState(!0),[re,xe]=z.useState(!1),[be,ye]=z.useState(0),[ge,je]=z.useState(!1),[pe,ve]=z.useState([]),[ue,fe]=z.useState([]),Se=B(L),Me=B(U),we=B(_),[Ce,ke]=z.useState(""),[Re,Ee]=z.useState(null),[Ie,De]=z.useState(!1),ze="demo-user";z.useEffect((()=>{const e=A.filter((e=>e.isEnabled)).flatMap((e=>e.models.filter((e=>e.enabled)).map((r=>({...r,providerName:e.name,providerColor:e.color,providerAvatar:e.avatar})))));ve(e)}),[A]),z.useEffect((()=>{const e=N();fe(e)}),[]),z.useEffect((()=>{W.enabled,W.enabled&&(r($(ze)),r(F(ze)))}),[r,ze,W.enabled]),z.useEffect((()=>{(async()=>{try{ee(!0);const e="memory_config_global",s=await K.getSetting(e);if(s)Q(s),r(H(s));else{const r=q();Q(r),await K.saveSetting(e,r)}}catch(e){console.error("加载配置失败:",e)}finally{ee(!1)}})()}),[r]);const Te=e=>{Q((r=>({...r,...e})))},Ae=e=>{Te({extraction:{...W.extraction,model:e.id}}),je(!1)},Oe=pe.find((e=>e.id===W.extraction.model))||null,We=Se.filter((e=>e.content.toLowerCase().includes(Ce.toLowerCase())));return X?t.jsx(a,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:t.jsx(i,{children:"正在加载记忆设置..."})}):t.jsxs(a,{sx:{height:"100vh",display:"flex",flexDirection:"column",bgcolor:e=>"light"===e.palette.mode?o(e.palette.primary.main,.02):o(e.palette.background.default,.9)},children:[t.jsx(n,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:t.jsxs(c,{children:[t.jsx(l,{edge:"start",color:"inherit",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:t.jsx(oe,{size:20})}),t.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #4f46e5, #8b5cf6)",backgroundClip:"text",color:"transparent"},children:"记忆功能设置"}),t.jsx(d,{variant:"contained",onClick:async()=>{try{r(H(W));const e="memory_config_global";await K.saveSetting(e,W);const{updateMemoryConfig:t}=await s((async()=>{const{updateMemoryConfig:e}=await import("./index-Dnlt-eWK.js").then((e=>e.dc));return{updateMemoryConfig:e}}),__vite__mapDeps([0,1,2,3,4,5]));t(W),W.search.embeddingModel,xe(!0),setTimeout((()=>xe(!1)),3e3)}catch(e){console.error("保存设置时出错:",e),alert("保存设置失败，请重试")}},sx:{bgcolor:"primary.main","&:hover":{bgcolor:"primary.dark"},borderRadius:2,textTransform:"none",fontWeight:600},children:"保存设置"})]})}),re&&t.jsx(x,{severity:"success",sx:{m:0,borderRadius:0,mt:8},children:"记忆设置已保存到数据库"}),t.jsx(a,{sx:{borderBottom:1,borderColor:"divider",bgcolor:"background.paper",mt:re?0:8},children:t.jsxs(h,{value:be,onChange:(e,r)=>{ye(r)},sx:{px:2},children:[t.jsx(m,{icon:t.jsx(se,{size:20}),label:"基础设置",sx:{textTransform:"none",fontWeight:600}}),t.jsx(m,{icon:t.jsx(ne,{size:20}),label:"记忆提取",sx:{textTransform:"none",fontWeight:600}}),t.jsx(m,{icon:t.jsx(te,{size:20}),label:"记忆搜索",sx:{textTransform:"none",fontWeight:600}}),t.jsx(m,{icon:t.jsx(ae,{size:20}),label:"存储设置",sx:{textTransform:"none",fontWeight:600}}),t.jsx(m,{icon:t.jsx(de,{size:20}),label:"我的记忆",sx:{textTransform:"none",fontWeight:600}}),t.jsx(m,{icon:t.jsx(ie,{size:20}),label:"统计分析",sx:{textTransform:"none",fontWeight:600}}),t.jsx(m,{icon:t.jsx(le,{size:20}),label:"系统测试",sx:{textTransform:"none",fontWeight:600}})]})}),t.jsxs(a,{sx:{flexGrow:1,overflowY:"auto",p:2,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[t.jsx(me,{value:be,index:0,children:t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"记忆功能开关"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"启用后，AI将能够记住对话中的重要信息，提供个性化体验"})]}),t.jsx(y,{}),t.jsx(g,{disablePadding:!0,children:t.jsxs(j,{children:[t.jsx(p,{primary:"启用记忆功能"}),t.jsx(v,{edge:"end",checked:W.enabled,onChange:e=>Te({enabled:e.target.checked})})]})})]})}),t.jsxs(me,{value:be,index:1,children:[t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"记忆提取模型"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"选择用于从对话中提取记忆信息的AI模型"})]}),t.jsx(y,{}),t.jsx(a,{sx:{p:2},children:"dropdown"===O?t.jsx(Z,{selectedModel:Oe,availableModels:pe,handleModelSelect:Ae}):t.jsx(Y,{selectedModel:Oe,availableModels:pe,handleModelSelect:Ae,handleMenuClick:()=>je(!0),handleMenuClose:()=>je(!1),menuOpen:ge})})]}),t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"提取参数设置"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"配置记忆提取的触发条件和数量限制"})]}),t.jsx(y,{}),t.jsxs(g,{disablePadding:!0,children:[t.jsx(j,{children:t.jsx(p,{primary:"最少对话轮数",secondary:`当前设置：${W.extraction.minConversationLength} 轮 - 达到此轮数后开始提取记忆`})}),t.jsx(j,{children:t.jsx(a,{sx:{width:"100%",px:2},children:t.jsx(u,{value:W.extraction.minConversationLength,onChange:(e,r)=>Te({extraction:{...W.extraction,minConversationLength:r}}),min:1,max:10,step:1,marks:!0,valueLabelDisplay:"auto",color:"primary"})})}),t.jsx(y,{}),t.jsx(j,{children:t.jsx(p,{primary:"每次最大提取记忆数",secondary:`当前设置：${W.extraction.maxMemoriesPerExtraction} 条 - 限制单次提取的记忆数量`})}),t.jsx(j,{children:t.jsx(a,{sx:{width:"100%",px:2},children:t.jsx(u,{value:W.extraction.maxMemoriesPerExtraction,onChange:(e,r)=>Te({extraction:{...W.extraction,maxMemoriesPerExtraction:r}}),min:1,max:20,step:1,marks:!0,valueLabelDisplay:"auto",color:"primary"})})})]})]})]}),t.jsxs(me,{value:be,index:2,children:[t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"嵌入模型设置"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"选择用于生成记忆向量的嵌入模型"})]}),t.jsx(y,{}),t.jsx(a,{sx:{p:2},children:t.jsx(f,{fullWidth:!0,children:t.jsx(S,{value:W.search.embeddingModel,onChange:e=>Te({search:{...W.search,embeddingModel:e.target.value}}),displayEmpty:!0,sx:{borderRadius:1},children:ue.map((e=>t.jsx(M,{value:e.id,children:t.jsxs(a,{sx:{display:"flex",alignItems:"center",gap:1},children:[t.jsx(w,{label:e.provider,size:"small",sx:{fontSize:"0.75rem"}}),e.name]})},e.id)))})})})]}),t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"搜索参数设置"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"配置记忆搜索的相似度阈值和结果数量"})]}),t.jsx(y,{}),t.jsxs(g,{disablePadding:!0,children:[t.jsx(j,{children:t.jsx(p,{primary:"相似度阈值",secondary:`当前设置：${W.search.similarityThreshold} - 控制搜索结果的相关性要求`})}),t.jsx(j,{children:t.jsx(a,{sx:{width:"100%",px:2},children:t.jsx(u,{value:W.search.similarityThreshold,onChange:(e,r)=>Te({search:{...W.search,similarityThreshold:r}}),min:.1,max:1,step:.1,marks:!0,valueLabelDisplay:"auto",color:"primary"})})}),t.jsx(y,{}),t.jsx(j,{children:t.jsx(p,{primary:"最大搜索结果数",secondary:`当前设置：${W.search.maxResults} 条 - 限制返回的记忆数量`})}),t.jsx(j,{children:t.jsx(a,{sx:{width:"100%",px:2},children:t.jsx(u,{value:W.search.maxResults,onChange:(e,r)=>Te({search:{...W.search,maxResults:r}}),min:1,max:20,step:1,marks:!0,valueLabelDisplay:"auto",color:"primary"})})})]})]})]}),t.jsx(me,{value:be,index:3,children:t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"存储容量设置"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"配置记忆存储的容量限制和清理策略"})]}),t.jsx(y,{}),t.jsxs(g,{disablePadding:!0,children:[t.jsx(j,{children:t.jsx(p,{primary:"每用户最大记忆数",secondary:`当前设置：${W.storage.maxMemoriesPerUser} 条 - 超出限制时自动清理旧记忆`})}),t.jsx(j,{children:t.jsx(a,{sx:{width:"100%",px:2},children:t.jsx(u,{value:W.storage.maxMemoriesPerUser,onChange:(e,r)=>Te({storage:{...W.storage,maxMemoriesPerUser:r}}),min:100,max:5e3,step:100,marks:!0,valueLabelDisplay:"auto",color:"primary"})})}),t.jsx(y,{}),t.jsx(j,{children:t.jsx(p,{primary:"记忆保留天数",secondary:`当前设置：${W.storage.retentionDays} 天 - 超过此天数的记忆将被自动清理`})}),t.jsx(j,{children:t.jsx(a,{sx:{width:"100%",px:2},children:t.jsx(u,{value:W.storage.retentionDays,onChange:(e,r)=>Te({storage:{...W.storage,retentionDays:r}}),min:7,max:365,step:7,marks:[{value:7,label:"7天"},{value:30,label:"30天"},{value:90,label:"90天"},{value:180,label:"180天"},{value:365,label:"365天"}],valueLabelDisplay:"auto",color:"primary"})})})]})]})}),t.jsx(me,{value:be,index:4,children:t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"记忆搜索"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"搜索和查看已保存的记忆信息"})]}),t.jsx(y,{}),t.jsxs(a,{sx:{p:2},children:[t.jsx(C,{fullWidth:!0,placeholder:"搜索记忆内容...",value:Ce,onChange:e=>ke(e.target.value),slotProps:{input:{startAdornment:t.jsx(k,{position:"start",children:t.jsx(te,{size:20})})}},sx:{mb:2}}),we.memories?t.jsxs(a,{sx:{display:"flex",justifyContent:"center",p:3},children:[t.jsx(R,{}),t.jsx(i,{variant:"body2",color:"text.secondary",sx:{ml:2},children:"正在加载记忆数据..."})]}):t.jsxs(a,{children:[t.jsxs(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:["共找到 ",We.length," 条记忆"]}),0===We.length?t.jsxs(a,{sx:{textAlign:"center",py:4},children:[t.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:Ce?"没有找到匹配的记忆":"暂无记忆数据"}),!W.enabled&&t.jsx(i,{variant:"body2",color:"warning.main",children:'记忆功能未启用，请在"基础设置"中开启'}),W.enabled&&0===Se.length&&t.jsx(i,{variant:"body2",color:"info.main",children:"开始对话后，AI将自动提取重要信息作为记忆"})]}):t.jsxs(g,{children:[We.slice(0,10).map(((e,s)=>t.jsx(j,{divider:!0,secondaryAction:t.jsx(l,{edge:"end","aria-label":"delete",onClick:()=>(async e=>{try{await r(J(e))}catch(s){console.error("❌ 删除记忆失败:",s),alert("删除记忆失败，请重试")}})(e.id),size:"small",color:"error",children:t.jsx(ce,{size:16})}),children:t.jsx(p,{primary:e.content,secondary:t.jsxs("span",{children:[t.jsxs("span",{style:{display:"block",fontSize:"0.75rem",color:"rgba(0, 0, 0, 0.6)"},children:["类别: ",G[e.category]||e.category]}),t.jsxs("span",{style:{display:"block",fontSize:"0.75rem",color:"rgba(0, 0, 0, 0.6)"},children:["创建时间: ",new Date(e.createdAt).toLocaleString()]})]})})},e.id||s))),We.length>10&&t.jsx(j,{children:t.jsx(p,{primary:t.jsxs(i,{variant:"body2",color:"text.secondary",sx:{textAlign:"center"},children:["还有 ",We.length-10," 条记忆..."]})})})]})]})]})]})}),t.jsx(me,{value:be,index:5,children:t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"记忆统计信息"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"查看记忆系统的使用统计和分析数据"})]}),t.jsx(y,{}),t.jsx(a,{sx:{p:2},children:we.stats?t.jsxs(a,{sx:{display:"flex",justifyContent:"center",p:3},children:[t.jsx(R,{}),t.jsx(i,{variant:"body2",color:"text.secondary",sx:{ml:2},children:"正在加载统计数据..."})]}):Me?t.jsxs(a,{children:[t.jsxs(a,{sx:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:2,mb:3},children:[t.jsx(E,{variant:"outlined",children:t.jsxs(I,{children:[t.jsx(i,{variant:"h4",color:"primary",children:Me.totalMemories||0}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"总记忆数"})]})}),t.jsx(E,{variant:"outlined",children:t.jsxs(I,{children:[t.jsx(i,{variant:"h4",color:"secondary",children:Object.keys(Me.categoryCounts||{}).length}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"记忆类别数"})]})}),t.jsx(E,{variant:"outlined",children:t.jsxs(I,{children:[t.jsx(i,{variant:"h4",color:"success.main",children:Me.recentMemories||0}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"近7天新增"})]})}),t.jsx(E,{variant:"outlined",children:t.jsxs(I,{children:[t.jsx(i,{variant:"h4",color:"warning.main",children:Me.averageImportance||0}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"平均重要性"})]})})]}),t.jsxs(a,{children:[t.jsx(i,{variant:"subtitle2",sx:{mb:2,fontWeight:600},children:"记忆分类分布"}),Object.entries(Me.categoryCounts||{}).map((([e,r])=>t.jsx(a,{sx:{mb:1},children:t.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",mb:.5},children:[t.jsx(i,{variant:"body2",children:G[e]||e}),t.jsxs(i,{variant:"body2",color:"text.secondary",children:[r," 条"]})]})},e)))]})]}):t.jsxs(a,{sx:{textAlign:"center",py:4},children:[t.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"暂无统计数据"}),!W.enabled&&t.jsx(i,{variant:"body2",color:"warning.main",children:'记忆功能未启用，请在"基础设置"中开启'}),W.enabled&&0===Se.length&&t.jsx(i,{variant:"body2",color:"info.main",children:"开始对话后，系统将自动生成统计数据"})]})})]})}),t.jsx(me,{value:be,index:6,children:t.jsxs(b,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[t.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"记忆系统测试"}),t.jsx(i,{variant:"body2",color:"text.secondary",children:"运行系统测试以验证记忆功能的正常工作"})]}),t.jsx(y,{}),t.jsxs(a,{sx:{p:2},children:[t.jsxs(a,{sx:{display:"flex",gap:2,mb:2},children:[t.jsx(d,{variant:"contained",onClick:async()=>{De(!0);try{const e=new he,r=await e.runBasicTests();Ee(r)}catch(e){console.error("Test failed:",e),Ee({success:!1,results:{},errors:[e instanceof Error?e.message:String(e)]})}finally{De(!1)}},disabled:Ie,startIcon:Ie?t.jsx(R,{size:20}):t.jsx(le,{size:20}),children:Ie?"正在测试...":"运行系统测试"}),t.jsx(d,{variant:"outlined",onClick:async()=>{De(!0),Ee(null);try{const t=Se||[];if(t.length,0===t.length)return void Ee({success:!0,results:{"记忆数量":0},errors:["没有需要重新生成嵌入向量的记忆"]});let a=0,i=0;const o=[],{MemoryEmbeddingService:n}=await s((async()=>{const{MemoryEmbeddingService:e}=await import("./index-Dnlt-eWK.js").then((e=>e.de));return{MemoryEmbeddingService:e}}),__vite__mapDeps([0,1,2,3,4,5])),c=n.getInstance();for(const s of t)try{s.id;const e=await c.generateMemoryEmbedding(s.content),t={...s,embedding:e,updatedAt:new Date};await r(V(t)),a++,s.id,e.length}catch(e){i++;const r=`记忆 ${s.id} 重新生成失败: ${e instanceof Error?e.message:"未知错误"}`;o.push(r),console.error(`❌ ${r}`)}await r($("demo-user")),await r(F("demo-user")),Ee({success:0===i,results:{"总记忆数":t.length,"成功重新生成":a,"失败数量":i,"嵌入向量更新":0===i},errors:o})}catch(e){console.error("❌ 重新生成嵌入向量失败:",e),Ee({success:!1,results:{},errors:[e instanceof Error?e.message:"未知错误"]})}finally{De(!1)}},disabled:Ie,startIcon:Ie?t.jsx(R,{size:20}):t.jsx(te,{size:20}),color:"secondary",children:Ie?"正在重新生成...":"重新生成嵌入向量"})]}),t.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"如果更换了嵌入模型，需要重新生成所有记忆的嵌入向量以确保记忆搜索正常工作"}),Re&&t.jsxs(a,{sx:{mt:2},children:[t.jsx(x,{severity:Re.success?"success":"error",sx:{mb:2},children:Re.success?"所有测试通过！":"测试失败，请检查系统配置"}),Re.results&&Object.keys(Re.results).length>0&&t.jsxs(a,{children:[t.jsx(i,{variant:"subtitle2",sx:{mb:1,fontWeight:600},children:"测试结果详情:"}),Object.entries(Re.results).map((([e,r])=>t.jsx(a,{sx:{mb:1},children:t.jsxs(i,{variant:"body2",children:[e,": ",r?"✅ 通过":"❌ 失败"]})},e)))]}),Re.errors&&Re.errors.length>0&&t.jsxs(a,{sx:{mt:2},children:[t.jsx(i,{variant:"subtitle2",sx:{mb:1,fontWeight:600},children:"错误信息:"}),Re.errors.map(((e,r)=>t.jsxs(i,{variant:"body2",color:"error",sx:{mb:.5},children:["• ",e]},r)))]})]})]})]})})]})]})};export{be as default};
