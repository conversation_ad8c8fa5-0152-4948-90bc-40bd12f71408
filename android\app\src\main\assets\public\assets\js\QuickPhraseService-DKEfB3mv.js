import{c as a,f as t}from"./index-BtK6VV6Z.js";const s=a("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);class e{static async getAll(){return await t.getAllQuickPhrases()}static async get(a){return await t.getQuickPhrase(a)}static async add(a){return await t.addQuickPhrase(a)}static async update(a,s){return await t.updateQuickPhrase(a,s)}static async delete(a){return await t.deleteQuickPhrase(a)}static async updateOrder(a){return await t.updateQuickPhrasesOrder(a)}static getAssistantPhrases(a){return(null==a?void 0:a.regularPhrases)||[]}static async updateAssistantPhrases(a,t,s){const e={...a,regularPhrases:t};await s(e)}static async addAssistantPhrase(a,t,s){const e=Date.now(),r={id:crypto.randomUUID(),title:t.title,content:t.content,createdAt:e,updatedAt:e,order:0},i=[r,...(null==a?void 0:a.regularPhrases)||[]];return await this.updateAssistantPhrases(a,i,s),r}static async deleteAssistantPhrase(a,t,s){const e=((null==a?void 0:a.regularPhrases)||[]).filter((a=>a.id!==t));await this.updateAssistantPhrases(a,e,s)}static async updateAssistantPhrase(a,t,s,e){const r=((null==a?void 0:a.regularPhrases)||[]).map((a=>a.id===t?{...a,...s,updatedAt:Date.now()}:a));await this.updateAssistantPhrases(a,r,e)}}export{e as Q,s as Z};
