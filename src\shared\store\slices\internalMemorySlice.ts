/**
 * 内置记忆系统状态管理
 */

import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import type { 
  MemoryRecord, 
  MemoryStats, 
  InternalMemoryConfig 
} from '../../types/internalMemory';
import { InternalMemoryService } from '../../services/memory/InternalMemoryService';
import { getMemoryConfig, updateMemoryConfig } from '../../config/internalMemoryConfig';

// 状态接口
interface InternalMemoryState {
  // 配置
  config: InternalMemoryConfig;
  
  // 记忆数据
  memories: MemoryRecord[];
  
  // 统计信息
  stats: MemoryStats | null;
  
  // 加载状态
  loading: {
    memories: boolean;
    stats: boolean;
    saving: boolean;
  };
  
  // 错误状态
  error: string | null;
  
  // 当前用户ID
  currentUserId: string | null;
}

// 初始状态
const initialState: InternalMemoryState = {
  config: getMemoryConfig(),
  memories: [],
  stats: null,
  loading: {
    memories: false,
    stats: false,
    saving: false
  },
  error: null,
  currentUserId: null
};

// 异步操作：加载用户记忆
export const loadUserMemories = createAsyncThunk(
  'internalMemory/loadUserMemories',
  async (userId: string, { rejectWithValue }) => {
    try {
      const memoryService = InternalMemoryService.getInstance();
      const memories = await memoryService.getUserMemories(userId);
      return { userId, memories };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load memories');
    }
  }
);

// 异步操作：加载记忆统计
export const loadMemoryStats = createAsyncThunk(
  'internalMemory/loadMemoryStats',
  async (userId: string, { rejectWithValue }) => {
    try {
      const memoryService = InternalMemoryService.getInstance();
      const stats = await memoryService.getMemoryStats(userId);
      return stats;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load memory stats');
    }
  }
);

// 异步操作：保存记忆
export const saveMemory = createAsyncThunk(
  'internalMemory/saveMemory',
  async (memory: MemoryRecord, { rejectWithValue }) => {
    try {
      const memoryService = InternalMemoryService.getInstance();
      await memoryService.saveMemory(memory);
      return memory;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to save memory');
    }
  }
);

// 异步操作：更新记忆
export const updateMemory = createAsyncThunk(
  'internalMemory/updateMemory',
  async ({ memoryId, content }: { memoryId: string; content: string }, { rejectWithValue }) => {
    try {
      const memoryService = InternalMemoryService.getInstance();
      await memoryService.updateMemory(memoryId, content);
      return { memoryId, content };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update memory');
    }
  }
);

// 异步操作：删除记忆
export const deleteMemory = createAsyncThunk(
  'internalMemory/deleteMemory',
  async (memoryId: string, { rejectWithValue }) => {
    try {
      const memoryService = InternalMemoryService.getInstance();
      await memoryService.deleteMemory(memoryId);
      return memoryId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete memory');
    }
  }
);

// 创建切片
const internalMemorySlice = createSlice({
  name: 'internalMemory',
  initialState,
  reducers: {
    // 设置当前用户ID
    setCurrentUserId: (state, action: PayloadAction<string | null>) => {
      state.currentUserId = action.payload;
      // 清空之前用户的数据
      if (state.currentUserId !== action.payload) {
        state.memories = [];
        state.stats = null;
      }
    },

    // 更新配置
    updateConfig: (state, action: PayloadAction<Partial<InternalMemoryConfig>>) => {
      state.config = { ...state.config, ...action.payload };
      // 同步更新全局配置
      updateMemoryConfig(action.payload);
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    },

    // 清除所有数据
    clearAllData: (state) => {
      state.memories = [];
      state.stats = null;
      state.error = null;
      state.currentUserId = null;
    }
  },
  extraReducers: (builder) => {
    // 加载用户记忆
    builder
      .addCase(loadUserMemories.pending, (state) => {
        state.loading.memories = true;
        state.error = null;
      })
      .addCase(loadUserMemories.fulfilled, (state, action) => {
        state.loading.memories = false;
        state.memories = action.payload.memories;
        state.currentUserId = action.payload.userId;
      })
      .addCase(loadUserMemories.rejected, (state, action) => {
        state.loading.memories = false;
        state.error = action.payload as string;
      });

    // 加载记忆统计
    builder
      .addCase(loadMemoryStats.pending, (state) => {
        state.loading.stats = true;
      })
      .addCase(loadMemoryStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.stats = action.payload;
      })
      .addCase(loadMemoryStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.error = action.payload as string;
      });

    // 保存记忆
    builder
      .addCase(saveMemory.pending, (state) => {
        state.loading.saving = true;
        state.error = null;
      })
      .addCase(saveMemory.fulfilled, (state, action) => {
        state.loading.saving = false;
        // 添加或更新记忆
        const existingIndex = state.memories.findIndex((m: any) => m.id === action.payload.id);
        if (existingIndex >= 0) {
          state.memories[existingIndex] = action.payload;
        } else {
          state.memories.push(action.payload);
        }
      })
      .addCase(saveMemory.rejected, (state, action) => {
        state.loading.saving = false;
        state.error = action.payload as string;
      });

    // 更新记忆
    builder
      .addCase(updateMemory.fulfilled, (state, action) => {
        const { memoryId, content } = action.payload;
        const memory = state.memories.find((m: any) => m.id === memoryId);
        if (memory) {
          memory.content = content;
          memory.updatedAt = new Date();
        }
      });

    // 删除记忆
    builder
      .addCase(deleteMemory.fulfilled, (state, action) => {
        state.memories = state.memories.filter((m: any) => m.id !== action.payload);
      });
  }
});

// 导出actions
export const { 
  setCurrentUserId, 
  updateConfig, 
  clearError, 
  clearAllData 
} = internalMemorySlice.actions;

// 导出reducer
export default internalMemorySlice.reducer;

// 选择器
export const selectMemoryConfig = (state: { internalMemory: InternalMemoryState }) => 
  state.internalMemory.config;

export const selectMemories = (state: { internalMemory: InternalMemoryState }) => 
  state.internalMemory.memories;

export const selectMemoryStats = (state: { internalMemory: InternalMemoryState }) => 
  state.internalMemory.stats;

export const selectMemoryLoading = (state: { internalMemory: InternalMemoryState }) => 
  state.internalMemory.loading;

export const selectMemoryError = (state: { internalMemory: InternalMemoryState }) => 
  state.internalMemory.error;

export const selectCurrentUserId = (state: { internalMemory: InternalMemoryState }) => 
  state.internalMemory.currentUserId;
