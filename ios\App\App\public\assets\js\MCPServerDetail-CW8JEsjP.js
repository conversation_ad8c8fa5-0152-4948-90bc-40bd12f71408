import{j as e,B as s,C as t,A as r,T as a,I as n,y as i,a as l,b as o,m as c,P as x,g as d,h,J as p,t as j,F as m,e as u,S as y,M as v,R as g,V as f,W as b,X as S,L as C,w as I,x as w,U as A,n as k}from"./mui-vendor-DsBXMegs.js";import{u as E,e as W,f as z,r as P}from"./react-vendor-Be-rfjCm.js";import{Z as T,A as B,_ as J,$ as D,E as N,S as O}from"./index-Dnlt-eWK.js";import{S as U}from"./Save-BEY1hRQb.js";import{T as H}from"./PlayArrow-DAmWqkSm.js";import{H as G}from"./Http-BYjiteRT.js";import{D as M}from"./Description-DN_ypUlF.js";import{F as R}from"./Folder-5Ry-HErD.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const F=()=>{const F=E(),{serverId:L}=W(),_=z(),[K,V]=P.useState(null),[X,Y]=P.useState([]),[Z,$]=P.useState([]),[q,Q]=P.useState([]),[ee,se]=P.useState(!1),[te,re]=P.useState(!1),[ae,ne]=P.useState({open:!1,message:"",severity:"success"});P.useEffect((()=>{var e;if(null==(e=_.state)?void 0:e.server)V(_.state.server),ie(_.state.server);else if(L){const e=T.getServerById(L);e&&(V(e),ie(e))}}),[L,_.state]);const ie=async e=>{if(e.isActive){se(!0);try{const[s,t,r]=await Promise.all([T.listTools(e),T.listPrompts(e),T.listResources(e)]);Y(s),$(t),Q(r)}catch(s){console.error("加载服务器数据失败:",s)}finally{se(!1)}}},le=e=>{switch(e){case"httpStream":return"#9c27b0";case"inMemory":return"#ff9800";default:return"#9e9e9e"}};return K?e.jsxs(s,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:"background.default"},children:[e.jsx(r,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider"},children:e.jsxs(a,{children:[e.jsx(n,{edge:"start",onClick:()=>{F("/settings/mcp-server")},"aria-label":"back",sx:{color:"primary.main"},children:e.jsx(B,{})}),e.jsx(i,{sx:{bgcolor:l(le(K.type),.1),color:le(K.type),mr:2,width:32,height:32},children:(s=>{switch(s){case"httpStream":return e.jsx(G,{});case"inMemory":return e.jsx(O,{});default:return e.jsx(J,{})}})(K.type)}),e.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600},children:K.name}),e.jsx(c,{startIcon:te?e.jsx(t,{size:16}):e.jsx(H,{}),onClick:async()=>{if(K){re(!0);try{const e=await T.testConnection(K);ne({open:!0,message:e?"连接测试成功":"连接测试失败",severity:e?"success":"error"}),e&&K.isActive&&await ie(K)}catch(e){ne({open:!0,message:"连接测试失败",severity:"error"})}finally{re(!1)}}},disabled:te,size:"small",sx:{mr:1},children:"测试"}),e.jsx(c,{startIcon:e.jsx(U,{}),onClick:async()=>{if(K)try{await T.updateServer(K),ne({open:!0,message:"保存成功",severity:"success"})}catch(e){ne({open:!0,message:"保存失败",severity:"error"})}},variant:"contained",size:"small",children:"保存"})]})}),e.jsxs(s,{sx:{flexGrow:1,overflow:"auto",mt:8,px:2,py:2},children:[e.jsxs(x,{sx:{p:3,mb:2},children:[e.jsxs(o,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(J,{}),"基本信息"]}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(d,{control:e.jsx(h,{checked:K.isActive,onChange:e=>(async e=>{if(K)try{await T.toggleServer(K.id,e),V({...K,isActive:e}),e?await ie({...K,isActive:e}):(Y([]),$([]),Q([])),ne({open:!0,message:e?"服务器已启用":"服务器已停用",severity:"success"})}catch(s){ne({open:!0,message:"操作失败",severity:"error"})}})(e.target.checked),color:"primary"}),label:"启用服务器"}),K.isActive&&e.jsx(p,{label:"运行中",size:"small",color:"success",variant:"outlined",sx:{ml:2}})]}),e.jsx(j,{fullWidth:!0,label:"服务器名称",value:K.name,onChange:e=>V({...K,name:e.target.value}),sx:{mb:2}}),e.jsxs(m,{fullWidth:!0,sx:{mb:2},children:[e.jsx(u,{children:"服务器类型"}),e.jsxs(y,{value:K.type,label:"服务器类型",onChange:e=>V({...K,type:e.target.value}),children:[e.jsx(v,{value:"httpStream",children:"HTTP Stream (支持SSE+HTTP)"}),e.jsx(v,{value:"inMemory",children:"内存服务器"})]})]}),"httpStream"===K.type&&e.jsx(j,{fullWidth:!0,label:"服务器 URL",value:K.baseUrl||"",onChange:e=>V({...K,baseUrl:e.target.value}),placeholder:"https://example.com/mcp",sx:{mb:2}}),e.jsx(j,{fullWidth:!0,label:"描述",value:K.description||"",onChange:e=>V({...K,description:e.target.value}),multiline:!0,rows:2,sx:{mb:2}}),e.jsx(j,{fullWidth:!0,label:"超时时间（秒）",type:"number",value:K.timeout||60,onChange:e=>V({...K,timeout:parseInt(e.target.value)||60}),inputProps:{min:1,max:300},sx:{mb:2}}),"httpStream"===K.type&&e.jsx(d,{control:e.jsx(g,{checked:!0===K.enableSSE,onChange:e=>V({...K,enableSSE:e.target.checked})}),label:"启用SSE流（Server-Sent Events）"})]}),e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(N,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(D,{}),"高级设置"]})}),e.jsxs(S,{children:[e.jsx(j,{fullWidth:!0,label:"请求头（JSON 格式）",value:JSON.stringify(K.headers||{},null,2),onChange:e=>{try{const s=JSON.parse(e.target.value);V({...K,headers:s})}catch(s){}},multiline:!0,rows:4,sx:{mb:2},placeholder:'{\\n  "Authorization": "Bearer token",\\n  "Content-Type": "application/json"\\n}'}),e.jsx(j,{fullWidth:!0,label:"环境变量（JSON 格式）",value:JSON.stringify(K.env||{},null,2),onChange:e=>{try{const s=JSON.parse(e.target.value);V({...K,env:s})}catch(s){}},multiline:!0,rows:4,sx:{mb:2},placeholder:'{\\n  "API_KEY": "your-api-key",\\n  "DEBUG": "true"\\n}'}),e.jsx(j,{fullWidth:!0,label:"参数（每行一个）",value:(K.args||[]).join("\n"),onChange:e=>{const s=e.target.value.split("\n").filter((e=>e.trim()));V({...K,args:s})},multiline:!0,rows:3,placeholder:"--verbose\\n--config=/path/to/config"})]})]}),K.isActive&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(N,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(D,{}),"可用工具 (",X.length,")"]})}),e.jsx(S,{children:ee?e.jsx(s,{sx:{display:"flex",justifyContent:"center",py:2},children:e.jsx(t,{})}):0===X.length?e.jsx(o,{color:"text.secondary",sx:{textAlign:"center",py:2},children:"暂无可用工具"}):e.jsx(C,{children:X.map(((s,t)=>e.jsx(I,{divider:!0,children:e.jsx(w,{primary:s.name,secondary:s.description||"无描述"})},t)))})})]}),K.isActive&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(N,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(M,{}),"可用提示词 (",Z.length,")"]})}),e.jsx(S,{children:ee?e.jsx(s,{sx:{display:"flex",justifyContent:"center",py:2},children:e.jsx(t,{})}):0===Z.length?e.jsx(o,{color:"text.secondary",sx:{textAlign:"center",py:2},children:"暂无可用提示词"}):e.jsx(C,{children:Z.map(((s,t)=>e.jsx(I,{divider:!0,children:e.jsx(w,{primary:s.name,secondary:s.description||"无描述"})},t)))})})]}),K.isActive&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(N,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(R,{}),"可用资源 (",q.length,")"]})}),e.jsx(S,{children:ee?e.jsx(s,{sx:{display:"flex",justifyContent:"center",py:2},children:e.jsx(t,{})}):0===q.length?e.jsx(o,{color:"text.secondary",sx:{textAlign:"center",py:2},children:"暂无可用资源"}):e.jsx(C,{children:q.map(((t,r)=>e.jsx(I,{divider:!0,children:e.jsx(w,{primary:t.name,secondary:e.jsxs(s,{children:[e.jsx(o,{variant:"body2",color:"text.secondary",children:t.description||"无描述"}),e.jsxs(o,{variant:"caption",color:"text.secondary",children:["URI: ",t.uri]})]})})},r)))})})]})]}),e.jsx(A,{open:ae.open,autoHideDuration:3e3,onClose:()=>ne({...ae,open:!1}),children:e.jsx(k,{severity:ae.severity,onClose:()=>ne({...ae,open:!1}),children:ae.message})})]}):e.jsx(s,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:e.jsx(t,{})})};export{F as default};
