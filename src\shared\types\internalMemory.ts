/**
 * 内置记忆系统类型定义
 * 基于 Mem0 架构设计的本地记忆系统
 */

// 记忆分类枚举
export enum MemoryCategory {
  PREFERENCE = 'preference',     // 个人偏好
  BACKGROUND = 'background',     // 背景信息
  SKILL = 'skill',              // 技能专长
  HABIT = 'habit',              // 使用习惯
  PLAN = 'plan'                 // 计划目标
}

// 记忆操作类型
export enum MemoryOperationType {
  ADD = 'ADD',
  UPDATE = 'UPDATE', 
  DELETE = 'DELETE',
  NONE = 'NONE'
}

// 记忆记录接口
export interface MemoryRecord {
  id: string;                    // UUID
  userId: string;                // 用户标识
  content: string;               // 记忆内容
  category: MemoryCategory;      // 记忆分类
  importance: number;            // 1-10 重要性评分
  embedding: number[];           // 向量表示
  hash: string;                  // 内容哈希，用于去重
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>; // 额外元数据
}

// 记忆操作接口
export interface MemoryOperation {
  id: string;
  text: string;
  event: MemoryOperationType;
  oldMemory?: string;            // 仅UPDATE时需要
}

// 记忆搜索结果
export interface MemorySearchResult {
  memory: MemoryRecord;
  similarity: number;
}

// 记忆提取结果
export interface MemoryExtractionResult {
  facts: string[];
  operations: MemoryOperation[];
}

// 记忆统计信息
export interface MemoryStats {
  totalMemories: number;
  categoryCounts: Record<string, number>;
  recentGrowth: number;
  averageImportance: number;
}

// 记忆配置接口
export interface InternalMemoryConfig {
  enabled: boolean;
  extraction: {
    model: string;
    minConversationLength: number;
    maxMemoriesPerExtraction: number;
  };
  search: {
    embeddingModel: string;
    similarityThreshold: number;
    maxResults: number;
  };
  storage: {
    maxMemoriesPerUser: number;
    retentionDays: number;
  };
}

// 记忆上下文构建结果
export interface MemoryContext {
  relevantMemories: MemoryRecord[];
  enhancedPrompt: string;
}
