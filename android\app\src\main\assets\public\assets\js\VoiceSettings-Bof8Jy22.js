import{c as e,j as a,B as s,b as l,n as t,t as i,aa as r,I as n,F as o,e as u,S as c,M as x,u as d,i as m,m as h,C as p,P as b,J as g,s as v,g as j,h as y,A as f,T as S,Z as w,_ as z}from"./mui-vendor-hRDvsX89.js";import{r as A,u as C}from"./react-vendor-C9ilihHH.js";import{b8 as _,ay as T,A as k,ax as M}from"./index-BtK6VV6Z.js";import{TTSService as I}from"./TTSService-B25iytT5.js";import{V as W}from"./Visibility-CbrDS_a-.js";import{o as N,u as K,v as V}from"./useVoiceRecognition-BEmiJSCO.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const O=e(a.jsx("path",{d:"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3m5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72z"})),F=e(a.jsx("path",{d:"M6 6h12v12H6z"})),L=e(a.jsx("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"})),R=e(a.jsx("path",{d:"M3 9v6h4l5 5V4L7 9zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02M14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77"})),P=({settings:e,onSettingsChange:h})=>{const p=(a,s)=>{h({...e,[a]:s})};return a.jsxs(s,{sx:{py:1},children:[a.jsx(l,{variant:"h6",sx:{mb:2},children:"OpenAI Whisper 语音识别设置"}),a.jsx(t,{severity:"info",sx:{mb:3},children:"OpenAI Whisper是一个强大的语音识别模型，支持多种语言。您需要有OpenAI的API密钥才能使用此功能。"}),a.jsx(i,{fullWidth:!0,label:"OpenAI API密钥",value:e.apiKey,onChange:e=>p("apiKey",e.target.value),type:e.showApiKey?"text":"password",margin:"normal",sx:{mb:3},helperText:"请输入您的OpenAI API密钥",slotProps:{input:{"aria-invalid":!1,"aria-describedby":"openai-api-key-helper-text"},formHelperText:{id:"openai-api-key-helper-text"}},InputProps:{endAdornment:a.jsx(r,{position:"end",children:a.jsx(n,{"aria-label":"切换API密钥可见性",onClick:()=>{p("showApiKey",!e.showApiKey)},edge:"end",children:e.showApiKey?a.jsx(L,{}):a.jsx(W,{})})})}}),a.jsxs(o,{fullWidth:!0,margin:"normal",sx:{mb:3},children:[a.jsx(u,{children:"Whisper模型"}),a.jsx(c,{value:e.model,onChange:e=>p("model",e.target.value),label:"Whisper模型",children:N.getAvailableModels().map((e=>a.jsx(x,{value:e,children:e},e)))}),a.jsx(d,{children:"目前只有whisper-1一个模型可用"})]}),a.jsxs(o,{fullWidth:!0,margin:"normal",sx:{mb:3},children:[a.jsx(u,{children:"语言"}),a.jsxs(c,{value:e.language||"",onChange:e=>p("language",e.target.value||void 0),label:"语言",children:[a.jsx(x,{value:"",children:"自动检测"}),a.jsx(x,{value:"zh",children:"中文"}),a.jsx(x,{value:"en",children:"英语"}),a.jsx(x,{value:"ja",children:"日语"}),a.jsx(x,{value:"ko",children:"韩语"}),a.jsx(x,{value:"fr",children:"法语"}),a.jsx(x,{value:"de",children:"德语"}),a.jsx(x,{value:"es",children:"西班牙语"}),a.jsx(x,{value:"ru",children:"俄语"})]}),a.jsx(d,{children:"指定音频的语言可以提高转录准确性，留空则自动检测"})]}),a.jsxs(s,{sx:{mb:3},children:[a.jsxs(l,{id:"temperature-slider",gutterBottom:!0,children:["温度: ",e.temperature]}),a.jsx(m,{"aria-labelledby":"temperature-slider",value:e.temperature||0,onChange:(e,a)=>p("temperature",a),step:.1,marks:!0,min:0,max:1,valueLabelDisplay:"auto"}),a.jsx(d,{children:"较高的值会使输出更加随机，较低的值则使输出更加确定。建议设置为0以获得最准确的结果。"})]}),a.jsxs(o,{fullWidth:!0,margin:"normal",sx:{mb:3},children:[a.jsx(u,{children:"响应格式"}),a.jsxs(c,{value:e.responseFormat||"json",onChange:e=>p("responseFormat",e.target.value),label:"响应格式",children:[a.jsx(x,{value:"json",children:"JSON"}),a.jsx(x,{value:"text",children:"纯文本"}),a.jsx(x,{value:"srt",children:"SRT"}),a.jsx(x,{value:"verbose_json",children:"详细JSON"}),a.jsx(x,{value:"vtt",children:"VTT"})]}),a.jsx(d,{children:"选择API返回的响应格式，一般使用默认的JSON格式即可"})]})]})},B=({settings:e,enabled:i})=>{const[r,n]=A.useState(!1),[o,u]=A.useState(!1),[c,x]=A.useState(null),[d,m]=A.useState(null),v=A.useRef(null),j=A.useRef([]),y=A.useRef(null);return a.jsxs(s,{sx:{mt:4,mb:2},children:[a.jsx(l,{variant:"h6",gutterBottom:!0,children:"测试OpenAI Whisper语音识别"}),!i&&a.jsx(t,{severity:"warning",sx:{mb:3},children:"请先启用语音识别功能"}),d&&a.jsx(t,{severity:"error",sx:{mb:3},children:d}),a.jsxs(s,{sx:{display:"flex",gap:2,mb:3,flexWrap:"wrap"},children:[a.jsx(h,{variant:"contained",color:r?"error":"primary",startIcon:r?a.jsx(F,{}):a.jsx(O,{}),onClick:r?()=>{v.current&&"inactive"!==v.current.state&&v.current.stop()}:async()=>{try{if(m(null),x(null),!e.apiKey)return void m("请先设置OpenAI API密钥");N.setApiKey(e.apiKey),N.setModel(e.model),e.language&&N.setLanguage(e.language),N.setTemperature(e.temperature||0),N.setResponseFormat(e.responseFormat||"json");const a=await navigator.mediaDevices.getUserMedia({audio:!0});y.current=a;const s=new MediaRecorder(a);v.current=s,j.current=[],s.addEventListener("dataavailable",(e=>{j.current.push(e.data)})),s.addEventListener("stop",(async()=>{try{n(!1),u(!0);const e=new Blob(j.current,{type:"audio/webm"}),a=await N.transcribeAudio(e);x(a)}catch(e){m(e.message||"转录失败")}finally{u(!1),y.current&&(y.current.getTracks().forEach((e=>e.stop())),y.current=null)}})),s.start(),n(!0),setTimeout((()=>{v.current&&"inactive"!==v.current.state&&v.current.stop()}),5e3)}catch(a){m(a.message||"无法访问麦克风"),n(!1)}},disabled:!i||o||!e.apiKey,children:r?"停止录制":"开始录制"}),a.jsx(h,{variant:"outlined",startIcon:a.jsx(_,{}),onClick:()=>{x(null),m(null)},disabled:r||o||!c&&!d,children:"清除结果"})]}),o&&a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:2,my:3},children:[a.jsx(p,{size:24}),a.jsx(l,{children:"正在处理音频..."})]}),c&&a.jsxs(b,{elevation:1,sx:{p:3,mb:3,bgcolor:"background.paper",border:"1px solid",borderColor:"divider",borderRadius:2},children:[a.jsx(l,{variant:"subtitle1",gutterBottom:!0,fontWeight:500,children:"识别结果:"}),a.jsx(l,{variant:"body1",sx:{whiteSpace:"pre-wrap"},children:c.text}),c.language&&a.jsx(g,{label:`检测到语言: ${c.language}`,color:"primary",size:"small",sx:{mt:2,mr:1}}),c.duration&&a.jsx(g,{label:`音频时长: ${c.duration.toFixed(2)}秒`,color:"secondary",size:"small",sx:{mt:2}})]}),a.jsx(t,{severity:"info",sx:{mb:2},children:'提示: 点击"开始录制"按钮，系统将录制5秒的音频，然后自动使用OpenAI Whisper API进行转录。'})]})},D=[{value:"FunAudioLLM/CosyVoice2-0.5B",label:"CosyVoice2-0.5B - 多语言语音合成"}],E={"FunAudioLLM/CosyVoice2-0.5B":[{value:"FunAudioLLM/CosyVoice2-0.5B:alex",label:"沉稳男声 (Alex)"},{value:"FunAudioLLM/CosyVoice2-0.5B:benjamin",label:"低沉男声 (Benjamin)"},{value:"FunAudioLLM/CosyVoice2-0.5B:charles",label:"磁性男声 (Charles)"},{value:"FunAudioLLM/CosyVoice2-0.5B:david",label:"欢快男声 (David)"},{value:"FunAudioLLM/CosyVoice2-0.5B:anna",label:"沉稳女声 (Anna)"},{value:"FunAudioLLM/CosyVoice2-0.5B:bella",label:"激情女声 (Bella)"},{value:"FunAudioLLM/CosyVoice2-0.5B:claire",label:"温柔女声 (Claire)"},{value:"FunAudioLLM/CosyVoice2-0.5B:diana",label:"欢快女声 (Diana)"}]},H=({settings:e,onSettingsChange:s})=>{const t=A.useCallback((a=>{s({...e,apiKey:a})}),[e,s]),r=A.useCallback((()=>{s({...e,showApiKey:!e.showApiKey})}),[e,s]),m=A.useCallback((a=>{var l,t;const i=(null==(t=null==(l=E[a])?void 0:l[0])?void 0:t.value)||"";s({...e,selectedModel:a,selectedVoice:i})}),[e,s]),h=A.useCallback((a=>{s({...e,selectedVoice:a})}),[e,s]),p=E[e.selectedModel]||[];return a.jsxs(a.Fragment,{children:[a.jsx(l,{variant:"subtitle1",sx:{mb:{xs:2,sm:3},fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem",md:"1.25rem"},color:"text.primary"},children:"硅基流动 TTS API 设置"}),a.jsxs(v,{spacing:{xs:2,sm:3},children:[a.jsx(o,{fullWidth:!0,variant:"outlined",children:a.jsx(i,{label:"API密钥",variant:"outlined",value:e.apiKey,onChange:e=>t(e.target.value),type:e.showApiKey?"text":"password",placeholder:"请输入硅基流动API密钥",helperText:"获取API密钥请访问：https://siliconflow.cn/",slotProps:{input:{"aria-invalid":!1,"aria-describedby":"siliconflow-api-key-helper-text",endAdornment:a.jsx(n,{onClick:r,edge:"end",size:window.innerWidth<600?"small":"medium",sx:{"&:hover":{bgcolor:"action.hover",transform:"scale(1.1)"},transition:"all 0.2s ease-in-out"},children:e.showApiKey?a.jsx(L,{}):a.jsx(W,{})})},formHelperText:{id:"siliconflow-api-key-helper-text"}},sx:{mb:{xs:1.5,sm:2},"& .MuiInputBase-root":{fontSize:{xs:"0.9rem",sm:"1rem"}},"& .MuiInputLabel-root":{fontSize:{xs:"0.9rem",sm:"1rem"}},"& .MuiFormHelperText-root":{fontSize:{xs:"0.75rem",sm:"0.875rem"},mt:{xs:.5,sm:1}},"& .MuiOutlinedInput-notchedOutline":{borderRadius:{xs:1.5,sm:2}}}})}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"TTS模型"}),a.jsx(c,{value:e.selectedModel,onChange:e=>m(e.target.value),label:"TTS模型",children:D.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"CosyVoice2-0.5B支持跨语言语音合成（中文、英文、日语、韩语、中国方言）和情感控制"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"语音选择"}),a.jsx(c,{value:e.selectedVoice,onChange:e=>h(e.target.value),label:"语音选择",disabled:!e.selectedModel,children:p.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:e.selectedModel?"8种高质量预置音色，支持情感控制和多语言合成":"请先选择TTS模型"})]})]})]})},U=[{value:"tts-1",label:"TTS-1 - 标准质量，速度快"},{value:"tts-1-hd",label:"TTS-1-HD - 高清质量，更自然"}],X=[{value:"alloy",label:"Alloy - 中性，平衡"},{value:"echo",label:"Echo - 男性，深沉"},{value:"fable",label:"Fable - 英式，优雅"},{value:"onyx",label:"Onyx - 男性，深沉有力"},{value:"nova",label:"Nova - 女性，年轻活泼"},{value:"shimmer",label:"Shimmer - 女性，温柔"}],Y=[{value:"mp3",label:"MP3 - 通用格式，兼容性好"},{value:"opus",label:"Opus - 高压缩比，适合网络传输"},{value:"aac",label:"AAC - 高质量，适合移动设备"},{value:"flac",label:"FLAC - 无损压缩，最高质量"},{value:"wav",label:"WAV - 无压缩，最大兼容性"},{value:"pcm",label:"PCM - 原始音频数据"}],J=({settings:e,onSettingsChange:s})=>{const t=A.useCallback((a=>{s({...e,apiKey:a})}),[e,s]),r=A.useCallback((()=>{s({...e,showApiKey:!e.showApiKey})}),[e,s]),h=A.useCallback((a=>{s({...e,selectedModel:a})}),[e,s]),p=A.useCallback((a=>{s({...e,selectedVoice:a})}),[e,s]),b=A.useCallback((a=>{s({...e,selectedFormat:a})}),[e,s]),g=A.useCallback((a=>{s({...e,speed:a})}),[e,s]),f=A.useCallback((a=>{s({...e,useStream:a})}),[e,s]);return a.jsxs(a.Fragment,{children:[a.jsx(l,{variant:"subtitle1",sx:{mb:2,fontWeight:600},children:"OpenAI TTS API 设置"}),a.jsxs(v,{spacing:3,children:[a.jsx(o,{fullWidth:!0,variant:"outlined",children:a.jsx(i,{label:"OpenAI API密钥",variant:"outlined",value:e.apiKey,onChange:e=>t(e.target.value),type:e.showApiKey?"text":"password",placeholder:"请输入OpenAI API密钥",helperText:"获取API密钥请访问：https://platform.openai.com/api-keys",slotProps:{input:{"aria-invalid":!1,"aria-describedby":"openai-tts-api-key-helper-text",endAdornment:a.jsx(n,{onClick:r,edge:"end",children:e.showApiKey?a.jsx(L,{}):a.jsx(W,{})})},formHelperText:{id:"openai-tts-api-key-helper-text"}},sx:{mb:2}})}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"TTS模型"}),a.jsx(c,{value:e.selectedModel,onChange:e=>h(e.target.value),label:"TTS模型",children:U.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"TTS-1适合快速生成，TTS-1-HD质量更高但速度较慢"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"语音选择"}),a.jsx(c,{value:e.selectedVoice,onChange:e=>p(e.target.value),label:"语音选择",children:X.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"选择您喜欢的语音风格，每种语音都有独特的特色"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"输出格式"}),a.jsx(c,{value:e.selectedFormat,onChange:e=>b(e.target.value),label:"输出格式",children:Y.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"推荐使用MP3格式，兼容性最好"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(l,{gutterBottom:!0,children:"语速控制"}),a.jsx(m,{value:e.speed,min:.25,max:4,step:.05,onChange:(e,a)=>g(a),valueLabelDisplay:"auto",marks:[{value:.25,label:"0.25x"},{value:1,label:"1.0x"},{value:4,label:"4.0x"}]}),a.jsx(d,{children:"调整语音播放速度 (0.25x - 4.0x，默认1.0x)"})]}),a.jsx(j,{control:a.jsx(y,{checked:e.useStream,onChange:e=>f(e.target.checked),color:"primary"}),label:"启用流式传输"}),a.jsx(d,{children:"流式传输可以更快地开始播放，但可能影响音质"})]})]})},G=[{value:"eastus",label:"美国东部 (East US)"},{value:"westus",label:"美国西部 (West US)"},{value:"westus2",label:"美国西部2 (West US 2)"},{value:"eastus2",label:"美国东部2 (East US 2)"},{value:"southeastasia",label:"东南亚 (Southeast Asia)"},{value:"westeurope",label:"西欧 (West Europe)"},{value:"northeurope",label:"北欧 (North Europe)"},{value:"japaneast",label:"日本东部 (Japan East)"},{value:"australiaeast",label:"澳大利亚东部 (Australia East)"},{value:"centralindia",label:"印度中部 (Central India)"}],$=[{value:"zh-CN-XiaoxiaoNeural",label:"晓晓 (女性，温柔甜美)"},{value:"zh-CN-YunxiNeural",label:"云希 (男性，成熟稳重)"},{value:"zh-CN-YunjianNeural",label:"云健 (男性，年轻活力)"},{value:"zh-CN-XiaoyiNeural",label:"晓伊 (女性，亲切自然)"},{value:"zh-CN-YunyangNeural",label:"云扬 (男性，新闻播报)"},{value:"zh-CN-XiaochenNeural",label:"晓辰 (女性，温暖治愈)"},{value:"zh-CN-XiaohanNeural",label:"晓涵 (女性，严肃正式)"},{value:"zh-CN-XiaomengNeural",label:"晓梦 (女性，可爱活泼)"},{value:"zh-CN-XiaomoNeural",label:"晓墨 (女性，知性优雅)"},{value:"zh-CN-XiaoqiuNeural",label:"晓秋 (女性，成熟知性)"},{value:"zh-CN-XiaoruiNeural",label:"晓睿 (女性，亲和力强)"},{value:"zh-CN-XiaoshuangNeural",label:"晓双 (女性，年轻甜美)"},{value:"zh-CN-XiaoxuanNeural",label:"晓萱 (女性，温柔体贴)"},{value:"zh-CN-XiaoyanNeural",label:"晓颜 (女性，清新自然)"},{value:"zh-CN-XiaoyouNeural",label:"晓悠 (女性，悠扬动听)"},{value:"zh-CN-XiaozhenNeural",label:"晓甄 (女性，专业严谨)"},{value:"zh-CN-YunfengNeural",label:"云枫 (男性，磁性深沉)"},{value:"zh-CN-YunhaoNeural",label:"云皓 (男性，阳光开朗)"},{value:"zh-CN-YunxiaNeural",label:"云夏 (男性，清新自然)"},{value:"zh-CN-YunyeNeural",label:"云野 (男性，豪放不羁)"},{value:"zh-CN-YunzeNeural",label:"云泽 (男性，温润如玉)"},{value:"en-US-JennyNeural",label:"Jenny (女性，美式英语)"},{value:"en-US-GuyNeural",label:"Guy (男性，美式英语)"},{value:"en-US-AriaNeural",label:"Aria (女性，美式英语)"},{value:"en-US-DavisNeural",label:"Davis (男性，美式英语)"}],q=[{value:"audio-24khz-160kbitrate-mono-mp3",label:"MP3 24kHz 160kbps (推荐)"},{value:"audio-24khz-96kbitrate-mono-mp3",label:"MP3 24kHz 96kbps"},{value:"audio-16khz-128kbitrate-mono-mp3",label:"MP3 16kHz 128kbps"},{value:"webm-24khz-16bit-mono-opus",label:"WebM Opus 24kHz"},{value:"ogg-24khz-16bit-mono-opus",label:"OGG Opus 24kHz"},{value:"riff-24khz-16bit-mono-pcm",label:"WAV 24kHz 16bit"},{value:"riff-16khz-16bit-mono-pcm",label:"WAV 16kHz 16bit"}],Z=[{value:"general",label:"通用 (默认)"},{value:"cheerful",label:"愉快开心"},{value:"sad",label:"悲伤沮丧"},{value:"angry",label:"愤怒生气"},{value:"fearful",label:"恐惧害怕"},{value:"disgruntled",label:"不满抱怨"},{value:"serious",label:"严肃正经"},{value:"affectionate",label:"亲切关爱"},{value:"gentle",label:"温柔体贴"},{value:"embarrassed",label:"尴尬羞涩"},{value:"calm",label:"平静冷静"}],Q=[{value:"default",label:"默认"},{value:"Girl",label:"女孩"},{value:"Boy",label:"男孩"},{value:"YoungAdultFemale",label:"年轻女性"},{value:"YoungAdultMale",label:"年轻男性"},{value:"OlderAdultFemale",label:"成年女性"},{value:"OlderAdultMale",label:"成年男性"},{value:"SeniorFemale",label:"老年女性"},{value:"SeniorMale",label:"老年男性"}],ee=({settings:e,onSettingsChange:t})=>{const r=A.useCallback((a=>{t({...e,apiKey:a})}),[e,t]),h=A.useCallback((()=>{t({...e,showApiKey:!e.showApiKey})}),[e,t]),p=A.useCallback((a=>{t({...e,region:a})}),[e,t]),b=A.useCallback((a=>{t({...e,voiceName:a})}),[e,t]),g=A.useCallback((a=>{t({...e,outputFormat:a})}),[e,t]),f=A.useCallback((a=>{t({...e,rate:a})}),[e,t]),S=A.useCallback((a=>{t({...e,pitch:a})}),[e,t]),w=A.useCallback((a=>{t({...e,volume:a})}),[e,t]),z=A.useCallback((a=>{t({...e,style:a})}),[e,t]),C=A.useCallback((a=>{t({...e,styleDegree:a})}),[e,t]),_=A.useCallback((a=>{t({...e,role:a})}),[e,t]),T=A.useCallback((a=>{t({...e,useSSML:a})}),[e,t]);return a.jsxs(a.Fragment,{children:[a.jsx(l,{variant:"subtitle1",sx:{mb:2,fontWeight:600},children:"微软Azure TTS API 设置"}),a.jsxs(v,{spacing:3,children:[a.jsx(o,{fullWidth:!0,variant:"outlined",children:a.jsx(i,{label:"Azure API密钥",variant:"outlined",value:e.apiKey,onChange:e=>r(e.target.value),type:e.showApiKey?"text":"password",placeholder:"请输入Azure Speech Services API密钥",helperText:"获取API密钥请访问：https://portal.azure.com/",slotProps:{input:{"aria-invalid":!1,"aria-describedby":"azure-api-key-helper-text",endAdornment:a.jsx(n,{onClick:h,edge:"end",children:e.showApiKey?a.jsx(L,{}):a.jsx(W,{})})},formHelperText:{id:"azure-api-key-helper-text"}},sx:{mb:2}})}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"服务区域"}),a.jsx(c,{value:e.region,onChange:e=>p(e.target.value),label:"服务区域",children:G.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"选择离您最近的Azure服务区域以获得最佳性能"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"语音选择"}),a.jsx(c,{value:e.voiceName,onChange:e=>b(e.target.value),label:"语音选择",children:$.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"选择您喜欢的语音风格，Neural语音支持更多表达风格"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"输出格式"}),a.jsx(c,{value:e.outputFormat,onChange:e=>g(e.target.value),label:"输出格式",children:q.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"推荐使用MP3格式，兼容性最好"})]}),a.jsxs(s,{sx:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:2},children:[a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"语速"}),a.jsxs(c,{value:e.rate,onChange:e=>f(e.target.value),label:"语速",children:[a.jsx(x,{value:"x-slow",children:"很慢"}),a.jsx(x,{value:"slow",children:"慢"}),a.jsx(x,{value:"medium",children:"正常"}),a.jsx(x,{value:"fast",children:"快"}),a.jsx(x,{value:"x-fast",children:"很快"})]})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"音调"}),a.jsxs(c,{value:e.pitch,onChange:e=>S(e.target.value),label:"音调",children:[a.jsx(x,{value:"x-low",children:"很低"}),a.jsx(x,{value:"low",children:"低"}),a.jsx(x,{value:"medium",children:"正常"}),a.jsx(x,{value:"high",children:"高"}),a.jsx(x,{value:"x-high",children:"很高"})]})]})]}),a.jsxs(s,{sx:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:2},children:[a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"音量"}),a.jsxs(c,{value:e.volume,onChange:e=>w(e.target.value),label:"音量",children:[a.jsx(x,{value:"silent",children:"静音"}),a.jsx(x,{value:"x-soft",children:"很轻"}),a.jsx(x,{value:"soft",children:"轻"}),a.jsx(x,{value:"medium",children:"正常"}),a.jsx(x,{value:"loud",children:"响"}),a.jsx(x,{value:"x-loud",children:"很响"})]})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"说话风格"}),a.jsx(c,{value:e.style,onChange:e=>z(e.target.value),label:"说话风格",children:Z.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))})]})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(l,{gutterBottom:!0,children:"风格强度"}),a.jsx(m,{value:e.styleDegree,min:.01,max:2,step:.01,onChange:(e,a)=>C(a),valueLabelDisplay:"auto",marks:[{value:.01,label:"0.01"},{value:1,label:"1.0"},{value:2,label:"2.0"}]}),a.jsx(d,{children:"调整说话风格的强度 (0.01-2.0，默认1.0)"})]}),a.jsxs(o,{fullWidth:!0,children:[a.jsx(u,{children:"角色扮演"}),a.jsx(c,{value:e.role,onChange:e=>_(e.target.value),label:"角色扮演",children:Q.map((e=>a.jsx(x,{value:e.value,children:e.label},e.value)))}),a.jsx(d,{children:"选择语音的角色扮演风格"})]}),a.jsx(j,{control:a.jsx(y,{checked:e.useSSML,onChange:e=>T(e.target.checked),color:"primary"}),label:"使用SSML标记语言"}),a.jsx(d,{children:"启用SSML可以获得更精细的语音控制，包括语速、音调、停顿等效果"})]})]})},ae=({testText:e,setTestText:t,handleTestTTS:r,isTestPlaying:n,enableTTS:o,selectedTTSService:u,openaiApiKey:c,azureApiKey:x,siliconFlowApiKey:d})=>a.jsxs(s,{sx:{mt:{xs:2,sm:3}},children:[a.jsx(l,{variant:"h6",sx:{mb:{xs:2,sm:3},fontWeight:600,fontSize:{xs:"1.1rem",sm:"1.25rem",md:"1.5rem"},color:"text.primary"},children:"测试语音效果"}),a.jsx(i,{fullWidth:!0,multiline:!0,rows:3,label:"测试文本",value:e,onChange:e=>t(e.target.value),variant:"outlined",sx:{mb:{xs:2,sm:3},"& .MuiInputBase-root":{fontSize:{xs:"0.9rem",sm:"1rem"},minHeight:{xs:"80px",sm:"100px"}},"& .MuiInputLabel-root":{fontSize:{xs:"0.9rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderRadius:{xs:1.5,sm:2}}}}),a.jsx(s,{sx:{display:"flex",justifyContent:"flex-start",flexDirection:{xs:"column",sm:"row"},gap:{xs:2,sm:0}},children:a.jsx(h,{variant:"contained",color:n?"error":"primary",startIcon:a.jsx(R,{}),onClick:r,disabled:!o||"openai"===u&&!c||"azure"===u&&!x||"siliconflow"===u&&!d,size:window.innerWidth<600?"large":"medium",sx:{minHeight:{xs:48,sm:40},fontSize:{xs:"0.9rem",sm:"1rem"},fontWeight:600,borderRadius:{xs:2,sm:1.5},px:{xs:3,sm:2},"&:hover":{transform:"translateY(-1px)",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},transition:"all 0.2s ease-in-out"},children:n?"停止播放":"播放测试"})})]}),se=()=>{const e=C(),r=A.useMemo((()=>I.getInstance()),[]),m=A.useRef(null),p=A.useRef(null),[v,_]=A.useState({apiKey:"",showApiKey:!1,selectedModel:"FunAudioLLM/CosyVoice2-0.5B",selectedVoice:"FunAudioLLM/CosyVoice2-0.5B:alex"}),[W,N]=A.useState({apiKey:"",showApiKey:!1,selectedModel:"tts-1",selectedVoice:"alloy",selectedFormat:"mp3",speed:1,useStream:!1}),[L,D]=A.useState({mainTabValue:0,ttsSubTabValue:0,sttSubTabValue:0,isSaved:!1,saveError:"",isTestPlaying:!1}),[E,U]=A.useState({enabled:!0,language:"zh-CN",autoStart:!1,silenceTimeout:2e3,maxResults:5,partialResults:!0,permissionStatus:"unknown",provider:"capacitor"}),[X,Y]=A.useState({apiKey:"",showApiKey:!1,model:"whisper-1",language:"zh",temperature:0,responseFormat:"json"}),[G,$]=A.useState({apiKey:"",showApiKey:!1,region:"eastus",voiceName:"zh-CN-XiaoxiaoNeural",language:"zh-CN",outputFormat:"audio-24khz-160kbitrate-mono-mp3",rate:"medium",pitch:"medium",volume:"medium",style:"general",styleDegree:1,role:"default",useSSML:!0}),[q,Z]=A.useState("你好，我是语音合成服务，感谢你的使用！"),[Q,se]=A.useState(!0),[le,te]=A.useState("siliconflow"),[ie,re]=A.useState(!1),[ne,oe]=A.useState(!1),{isListening:ue,recognitionText:ce,permissionStatus:xe,error:de,startRecognition:me,stopRecognition:he}=K();A.useEffect((()=>{(async()=>{try{const e=await M("siliconflow_api_key")||"",a=await M("tts_model")||"FishSpeech",s=await M("tts_voice")||"fishaudio_fish_speech_1",l="false"!==await M("enable_tts"),t=await M("openai_tts_api_key")||"",i=await M("openai_tts_model")||"tts-1",n=await M("openai_tts_voice")||"alloy",o=await M("openai_tts_format")||"mp3",u=Number(await M("openai_tts_speed")||"1.0"),c="true"===await M("openai_tts_stream"),x="true"===await M("use_openai_tts"),d=await M("azure_tts_api_key")||"",m=await M("azure_tts_region")||"eastus",h=await M("azure_tts_voice")||"zh-CN-XiaoxiaoNeural",p=await M("azure_tts_language")||"zh-CN",b=await M("azure_tts_format")||"audio-24khz-160kbitrate-mono-mp3",g=await M("azure_tts_rate")||"medium",v=await M("azure_tts_pitch")||"medium",j=await M("azure_tts_volume")||"medium",y=await M("azure_tts_style")||"general",f=parseFloat(await M("azure_tts_style_degree")||"1.0"),S=await M("azure_tts_role")||"default",w="false"!==await M("azure_tts_use_ssml"),z="true"===await M("use_azure_tts"),A=await M("selected_tts_service")||"siliconflow",C="false"!==await M("speech_recognition_enabled"),T=await M("speech_recognition_language")||"zh-CN",k="true"===await M("speech_recognition_auto_start"),I=Number(await M("speech_recognition_silence_timeout")||"2000"),W=Number(await M("speech_recognition_max_results")||"5"),K="false"!==await M("speech_recognition_partial_results"),V=await M("speech_recognition_provider")||"capacitor",O=await M("whisper_api_key")||"",F=await M("whisper_model")||"whisper-1",L=await M("whisper_language")||"zh",R=Number(await M("whisper_temperature")||"0"),P=await M("whisper_response_format")||"json";_({apiKey:e,showApiKey:!1,selectedModel:a,selectedVoice:s}),N({apiKey:t,showApiKey:!1,selectedModel:i,selectedVoice:n,selectedFormat:o,speed:u,useStream:c});let B=0;"openai"===A?B=1:"azure"===A&&(B=2),D((e=>({...e,ttsSubTabValue:B}))),$({apiKey:d,showApiKey:!1,region:m,voiceName:h,language:p,outputFormat:b,rate:g,pitch:v,volume:j,style:y,styleDegree:f,role:S,useSSML:w}),se(l),re(x),oe(z),te(A),r.setApiKey(e),r.setOpenAIApiKey(t),r.setOpenAIModel(i),r.setOpenAIVoice(n),r.setOpenAIResponseFormat(o),r.setOpenAISpeed(u),r.setUseOpenAIStream(c),r.setUseOpenAI(x),r.setAzureApiKey(d),r.setAzureRegion(m),r.setAzureVoiceName(h),r.setAzureLanguage(p),r.setAzureOutputFormat(b),r.setAzureRate(g),r.setAzurePitch(v),r.setAzureVolume(j),r.setAzureStyle(y),r.setAzureStyleDegree(f),r.setAzureRole(S),r.setAzureUseSSML(w),r.setUseAzure(z),a&&s&&r.setDefaultVoice(a,`${a}:${s}`),U({enabled:C,language:T,autoStart:k,silenceTimeout:I,maxResults:W,partialResults:K,permissionStatus:"unknown",provider:V}),Y({apiKey:O,showApiKey:!1,model:F,language:L,temperature:R,responseFormat:P})}catch(e){console.error("加载语音设置失败:",e)}})()}),[]);const pe=A.useCallback((()=>{e("/settings")}),[e]),be=A.useCallback((async()=>{var e;try{await T("siliconflow_api_key",v.apiKey),await T("tts_model",v.selectedModel),await T("tts_voice",v.selectedVoice),await T("enable_tts",Q.toString()),await T("openai_tts_api_key",W.apiKey),await T("openai_tts_model",W.selectedModel),await T("openai_tts_voice",W.selectedVoice),await T("openai_tts_format",W.selectedFormat),await T("openai_tts_speed",W.speed.toString()),await T("openai_tts_stream",W.useStream.toString()),await T("use_openai_tts",ie.toString()),await T("azure_tts_api_key",G.apiKey),await T("azure_tts_region",G.region),await T("azure_tts_voice",G.voiceName),await T("azure_tts_language",G.language),await T("azure_tts_format",G.outputFormat),await T("azure_tts_rate",G.rate),await T("azure_tts_pitch",G.pitch),await T("azure_tts_volume",G.volume),await T("azure_tts_style",G.style),await T("azure_tts_style_degree",G.styleDegree.toString()),await T("azure_tts_role",G.role),await T("azure_tts_use_ssml",G.useSSML.toString()),await T("use_azure_tts",ne.toString()),await T("selected_tts_service",le),await T("speech_recognition_enabled",E.enabled.toString()),await T("speech_recognition_language",E.language),await T("speech_recognition_auto_start",E.autoStart.toString()),await T("speech_recognition_silence_timeout",E.silenceTimeout.toString()),await T("speech_recognition_max_results",E.maxResults.toString()),await T("speech_recognition_partial_results",E.partialResults.toString()),await T("speech_recognition_provider",E.provider),await T("whisper_api_key",X.apiKey),await T("whisper_model",X.model),await T("whisper_language",X.language||""),await T("whisper_temperature",(null==(e=X.temperature)?void 0:e.toString())||"0"),await T("whisper_response_format",X.responseFormat||"json"),r.setApiKey(v.apiKey),r.setDefaultVoice(v.selectedModel,v.selectedVoice),r.setOpenAIApiKey(W.apiKey),r.setOpenAIModel(W.selectedModel),r.setOpenAIVoice(W.selectedVoice),r.setOpenAIResponseFormat(W.selectedFormat),r.setOpenAISpeed(W.speed),r.setUseOpenAIStream(W.useStream),r.setUseOpenAI(ie),r.setAzureApiKey(G.apiKey),r.setAzureRegion(G.region),r.setAzureVoiceName(G.voiceName),r.setAzureLanguage(G.language),r.setAzureOutputFormat(G.outputFormat),r.setAzureRate(G.rate),r.setAzurePitch(G.pitch),r.setAzureVolume(G.volume),r.setAzureStyle(G.style),r.setAzureStyleDegree(G.styleDegree),r.setAzureRole(G.role),r.setAzureUseSSML(G.useSSML),r.setUseAzure(ne),m.current&&clearTimeout(m.current),D((e=>({...e,isSaved:!0,saveError:""}))),m.current=setTimeout((()=>{D((e=>({...e,isSaved:!1})))}),3e3)}catch(a){console.error("保存设置失败:",a),D((e=>({...e,saveError:"保存设置失败，请重试"})))}}),[v,W,G,Q,ie,ne,le,r,E,X]),ge=A.useCallback((async()=>{if(L.isTestPlaying)return r.stop(),p.current&&clearInterval(p.current),void D((e=>({...e,isTestPlaying:!1})));D((e=>({...e,isTestPlaying:!0}))),r.setUseOpenAI("openai"===le),r.setUseAzure("azure"===le),"azure"===le?(r.setAzureApiKey(G.apiKey),r.setAzureRegion(G.region),r.setAzureVoiceName(G.voiceName),r.setAzureLanguage(G.language),r.setAzureOutputFormat(G.outputFormat),r.setAzureRate(G.rate),r.setAzurePitch(G.pitch),r.setAzureVolume(G.volume),r.setAzureStyle(G.style),r.setAzureStyleDegree(G.styleDegree),r.setAzureRole(G.role),r.setAzureUseSSML(G.useSSML)):"openai"===le?(r.setOpenAIApiKey(W.apiKey),r.setOpenAIModel(W.selectedModel),r.setOpenAIVoice(W.selectedVoice),r.setOpenAIResponseFormat(W.selectedFormat),r.setOpenAISpeed(W.speed),r.setUseOpenAIStream(W.useStream)):(r.setApiKey(v.apiKey),r.setDefaultVoice(v.selectedModel,v.selectedVoice));await r.speak(q)||D((e=>({...e,isTestPlaying:!1}))),p.current&&clearInterval(p.current),p.current=setInterval((()=>{r.getIsPlaying()||(D((e=>({...e,isTestPlaying:!1}))),p.current&&clearInterval(p.current))}),500)}),[L.isTestPlaying,le,G,W,v,q,r]),ve=A.useCallback(((e,a)=>{D((e=>({...e,mainTabValue:a})))}),[]),je=A.useCallback(((e,a)=>{D((e=>({...e,ttsSubTabValue:a})));let s="siliconflow";1===a?s="openai":2===a&&(s="azure"),te(s),re("openai"===s),oe("azure"===s)}),[]),ye=A.useCallback(((e,a)=>{D((e=>({...e,sttSubTabValue:a})));const s=0===a?"capacitor":"openai";U((e=>({...e,provider:s})))}),[]),fe=A.useCallback((e=>{te(e);const a="azure"===e;re("openai"===e),oe(a);let s=0;"openai"===e?s=1:"azure"===e&&(s=2),D((e=>({...e,ttsSubTabValue:s})))}),[]);A.useEffect((()=>()=>{m.current&&clearTimeout(m.current),p.current&&clearInterval(p.current)}),[]);return a.jsxs(s,{sx:{display:"flex",flexDirection:"column",height:"100vh",width:"100vw",overflow:"hidden",bgcolor:"background.default"},children:[a.jsx(f,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(20px)",WebkitBackdropFilter:"blur(20px)",background:"rgba(255, 255, 255, 0.8)","@media (prefers-color-scheme: dark)":{background:"rgba(18, 18, 18, 0.8)"}},children:a.jsxs(S,{sx:{minHeight:{xs:56,sm:64},px:{xs:1,sm:2,md:3}},children:[a.jsx(n,{edge:"start",onClick:pe,"aria-label":"返回",size:"large",sx:{color:"primary.main",mr:{xs:1,sm:2},"&:hover":{bgcolor:"primary.main",color:"primary.contrastText",transform:"scale(1.05)"},transition:"all 0.2s ease-in-out"},children:a.jsx(k,{})}),a.jsx(l,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,fontSize:{xs:"1.1rem",sm:"1.25rem",md:"1.5rem"},backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",WebkitBackgroundClip:"text",color:"transparent",textAlign:{xs:"left",sm:"left"}},children:"语音功能设置"})]})}),a.jsx(s,{sx:{flex:1,overflow:"auto",overflowX:"hidden",pt:{xs:7,sm:8},pb:{xs:2,sm:3},px:{xs:1,sm:2,md:3},WebkitOverflowScrolling:"touch",scrollBehavior:"smooth","&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-track":{background:"transparent"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"10px","&:hover":{backgroundColor:"rgba(0,0,0,0.2)"}}},children:a.jsxs(s,{sx:{maxWidth:{xs:"100%",sm:"100%",md:"1200px",lg:"1400px"},mx:"auto",width:"100%"},children:[L.isSaved&&a.jsx(t,{severity:"success",sx:{mb:{xs:1.5,sm:2},borderRadius:{xs:1,sm:2},fontSize:{xs:"0.875rem",sm:"1rem"},"& .MuiAlert-icon":{fontSize:{xs:"1.2rem",sm:"1.5rem"}}},children:"设置已保存成功"}),L.saveError&&a.jsx(t,{severity:"error",sx:{mb:{xs:1.5,sm:2},borderRadius:{xs:1,sm:2},fontSize:{xs:"0.875rem",sm:"1rem"},"& .MuiAlert-icon":{fontSize:{xs:"1.2rem",sm:"1.5rem"}}},children:L.saveError}),a.jsxs(w,{value:L.mainTabValue,onChange:ve,variant:"fullWidth",sx:{mb:{xs:2,sm:3},borderBottom:1,borderColor:"divider","& .MuiTabs-indicator":{height:{xs:3,sm:4},borderRadius:"2px 2px 0 0",background:"linear-gradient(90deg, #9333EA, #754AB4)"},"& .MuiTab-root":{minHeight:{xs:56,sm:64},fontSize:{xs:"0.9rem",sm:"1rem",md:"1.1rem"},fontWeight:600,textTransform:"none","&.Mui-selected":{color:"primary.main"}}},children:[a.jsx(z,{label:"文本转语音 (TTS)",icon:a.jsx(R,{}),iconPosition:"start"}),a.jsx(z,{label:"语音识别 (STT)",icon:a.jsx(O,{}),iconPosition:"start"})]}),0===L.mainTabValue&&a.jsxs(b,{elevation:0,sx:{p:{xs:2,sm:3,md:4},mb:{xs:2,sm:3},borderRadius:{xs:2,sm:3},border:"1px solid",borderColor:"divider",background:"background.paper",boxShadow:{xs:"0 2px 8px rgba(0,0,0,0.04)",sm:"0 4px 12px rgba(0,0,0,0.08)"},transition:"all 0.2s ease-in-out","&:hover":{boxShadow:{xs:"0 4px 12px rgba(0,0,0,0.08)",sm:"0 8px 24px rgba(0,0,0,0.12)"}}},children:[a.jsx(l,{variant:"h6",sx:{mb:{xs:2,sm:3},fontWeight:600,fontSize:{xs:"1.1rem",sm:"1.25rem",md:"1.5rem"},color:"text.primary"},children:"文本转语音 (TTS) 功能"}),a.jsx(s,{sx:{mb:{xs:2,sm:3}},children:a.jsx(j,{control:a.jsx(y,{checked:Q,onChange:e=>se(e.target.checked),color:"primary",size:"medium",sx:{"& .MuiSwitch-thumb":{width:{xs:20,sm:24},height:{xs:20,sm:24}},"& .MuiSwitch-track":{borderRadius:{xs:10,sm:12}}}}),label:a.jsx(l,{sx:{fontSize:{xs:"0.9rem",sm:"1rem"},fontWeight:500},children:"启用语音转换功能"}),sx:{"& .MuiFormControlLabel-label":{ml:{xs:1,sm:1.5}}}})}),a.jsx(l,{variant:"body2",sx:{mb:{xs:2,sm:3},color:"text.secondary",fontSize:{xs:"0.875rem",sm:"1rem"},lineHeight:{xs:1.4,sm:1.6},px:{xs:0,sm:1}},children:"启用后，在聊天界面可以将AI回复内容转换为语音播放。本应用支持硅基流动TTS、OpenAI TTS和微软Azure TTS服务，如API无效则会自动降级使用浏览器内置的Web Speech API功能。"}),a.jsxs(o,{fullWidth:!0,sx:{mb:{xs:2,sm:3},"& .MuiInputLabel-root":{fontSize:{xs:"0.9rem",sm:"1rem"}}},children:[a.jsx(u,{children:"选择TTS服务"}),a.jsxs(c,{value:le,onChange:e=>fe(e.target.value),label:"选择TTS服务",sx:{"& .MuiSelect-select":{py:{xs:1.5,sm:2},fontSize:{xs:"0.9rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderRadius:{xs:1.5,sm:2}}},children:[a.jsx(x,{value:"siliconflow",sx:{py:{xs:1,sm:1.5},px:{xs:2,sm:3}},children:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},width:"100%"},children:[a.jsx(g,{size:"small",label:"推荐",color:"primary",variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24},"& .MuiChip-label":{px:{xs:.5,sm:1}}}}),a.jsx(l,{sx:{fontSize:{xs:"0.85rem",sm:"0.95rem"},ml:{xs:.5,sm:1}},children:"硅基流动 TTS (免费额度)"})]})}),a.jsx(x,{value:"openai",sx:{py:{xs:1,sm:1.5},px:{xs:2,sm:3}},children:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},width:"100%"},children:[a.jsx(g,{size:"small",label:"付费",color:"warning",variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24},"& .MuiChip-label":{px:{xs:.5,sm:1}}}}),a.jsx(l,{sx:{fontSize:{xs:"0.85rem",sm:"0.95rem"},ml:{xs:.5,sm:1}},children:"OpenAI TTS (高音质)"})]})}),a.jsx(x,{value:"azure",sx:{py:{xs:1,sm:1.5},px:{xs:2,sm:3}},children:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},width:"100%"},children:[a.jsx(g,{size:"small",label:"企业级",color:"info",variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24},"& .MuiChip-label":{px:{xs:.5,sm:1}}}}),a.jsx(l,{sx:{fontSize:{xs:"0.85rem",sm:"0.95rem"},ml:{xs:.5,sm:1}},children:"微软Azure TTS (免费额度+付费)"})]})})]}),a.jsx(d,{sx:{fontSize:{xs:"0.75rem",sm:"0.875rem"},mt:{xs:.5,sm:1},px:{xs:0,sm:1}},children:"选择您要使用的文本转语音服务。硅基流动提供免费额度，OpenAI音质优秀，Azure提供企业级服务。"})]}),a.jsxs(w,{value:L.ttsSubTabValue,onChange:je,variant:"scrollable",scrollButtons:"auto",allowScrollButtonsMobile:!0,sx:{mb:{xs:2,sm:3},borderBottom:1,borderColor:"divider","& .MuiTabs-scroller":{overflow:"auto !important",scrollBehavior:"smooth",WebkitOverflowScrolling:"touch","&::-webkit-scrollbar":{display:"none"},scrollbarWidth:"none"},"& .MuiTabs-flexContainer":{gap:{xs:.5,sm:1},minWidth:"fit-content"},"& .MuiTab-root":{minHeight:{xs:56,sm:64},fontSize:{xs:"0.7rem",sm:"0.875rem",md:"1rem"},fontWeight:500,textTransform:"none",px:{xs:1.5,sm:2,md:3},py:{xs:1,sm:1.5},minWidth:{xs:"auto",sm:120,md:160},maxWidth:{xs:200,sm:250,md:300},whiteSpace:"nowrap","&.Mui-selected":{fontWeight:600,color:"primary.main"},"&:hover":{backgroundColor:"action.hover",transition:"background-color 0.2s ease-in-out"}},"& .MuiTabs-indicator":{height:{xs:3,sm:4},borderRadius:"2px 2px 0 0",background:"linear-gradient(90deg, #9333EA, #754AB4)"},"& .MuiTabs-scrollButtons":{"&.Mui-disabled":{opacity:.3},"& .MuiSvgIcon-root":{fontSize:{xs:"1.2rem",sm:"1.5rem"}}}},children:[a.jsx(z,{label:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},flexDirection:"row",textAlign:"center",justifyContent:"center",width:"100%"},children:[a.jsx(l,{sx:{fontSize:"inherit",fontWeight:"inherit",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:"硅基流动"}),a.jsxs(s,{sx:{display:"flex",gap:.25,alignItems:"center"},children:["siliconflow"===le&&a.jsx(g,{size:"small",label:"使用中",color:"success",variant:"filled",sx:{fontSize:{xs:"0.55rem",sm:"0.65rem"},height:{xs:16,sm:20},minWidth:"auto","& .MuiChip-label":{px:{xs:.5,sm:.75},py:0}}}),v.apiKey&&"siliconflow"!==le&&a.jsx(g,{size:"small",label:"已配置",color:"info",variant:"outlined",sx:{fontSize:{xs:"0.55rem",sm:"0.65rem"},height:{xs:16,sm:20},minWidth:"auto","& .MuiChip-label":{px:{xs:.5,sm:.75},py:0}}})]})]})}),a.jsx(z,{label:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},flexDirection:{xs:"column",sm:"row"},textAlign:"center"},children:[a.jsx(l,{sx:{fontSize:{xs:"0.7rem",sm:"0.85rem"},fontWeight:"inherit",whiteSpace:{xs:"normal",sm:"nowrap"}},children:"OpenAI TTS"}),a.jsxs(s,{sx:{display:"flex",gap:.5,flexWrap:"wrap",justifyContent:"center"},children:["openai"===le&&a.jsx(g,{size:"small",label:"当前使用",color:"success",variant:"filled",sx:{fontSize:{xs:"0.6rem",sm:"0.7rem"},height:{xs:14,sm:18},"& .MuiChip-label":{px:{xs:.5,sm:.75}}}}),W.apiKey&&a.jsx(g,{size:"small",label:"已配置",color:"info",variant:"outlined",sx:{fontSize:{xs:"0.6rem",sm:"0.7rem"},height:{xs:14,sm:18},"& .MuiChip-label":{px:{xs:.5,sm:.75}}}})]})]})}),a.jsx(z,{label:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},flexDirection:{xs:"column",sm:"row"},textAlign:"center"},children:[a.jsx(l,{sx:{fontSize:{xs:"0.7rem",sm:"0.85rem"},fontWeight:"inherit",whiteSpace:{xs:"normal",sm:"nowrap"}},children:"微软Azure TTS"}),a.jsxs(s,{sx:{display:"flex",gap:.5,flexWrap:"wrap",justifyContent:"center"},children:["azure"===le&&a.jsx(g,{size:"small",label:"当前使用",color:"success",variant:"filled",sx:{fontSize:{xs:"0.6rem",sm:"0.7rem"},height:{xs:14,sm:18},"& .MuiChip-label":{px:{xs:.5,sm:.75}}}}),G.apiKey&&a.jsx(g,{size:"small",label:"已配置",color:"info",variant:"outlined",sx:{fontSize:{xs:"0.6rem",sm:"0.7rem"},height:{xs:14,sm:18},"& .MuiChip-label":{px:{xs:.5,sm:.75}}}})]})]})})]}),0===L.ttsSubTabValue&&a.jsx(H,{settings:v,onSettingsChange:_}),1===L.ttsSubTabValue&&a.jsx(J,{settings:W,onSettingsChange:N}),2===L.ttsSubTabValue&&a.jsx(ee,{settings:G,onSettingsChange:$})]}),0===L.mainTabValue&&a.jsx(ae,{testText:q,setTestText:Z,handleTestTTS:ge,isTestPlaying:L.isTestPlaying,enableTTS:Q,selectedTTSService:le,openaiApiKey:W.apiKey,azureApiKey:G.apiKey,siliconFlowApiKey:v.apiKey}),1===L.mainTabValue&&a.jsxs(b,{elevation:0,sx:{p:{xs:2,sm:3,md:4},mb:{xs:2,sm:3},borderRadius:{xs:2,sm:3},border:"1px solid",borderColor:"divider",background:"background.paper",boxShadow:{xs:"0 2px 8px rgba(0,0,0,0.04)",sm:"0 4px 12px rgba(0,0,0,0.08)"},transition:"all 0.2s ease-in-out","&:hover":{boxShadow:{xs:"0 4px 12px rgba(0,0,0,0.08)",sm:"0 8px 24px rgba(0,0,0,0.12)"}}},children:[a.jsx(l,{variant:"h6",sx:{mb:{xs:2,sm:3},fontWeight:600,fontSize:{xs:"1.1rem",sm:"1.25rem",md:"1.5rem"},color:"text.primary"},children:"语音识别 (STT) 功能"}),a.jsx(s,{sx:{mb:{xs:2,sm:3}},children:a.jsx(j,{control:a.jsx(y,{checked:E.enabled,onChange:e=>U((a=>({...a,enabled:e.target.checked}))),color:"primary",size:"medium",sx:{"& .MuiSwitch-thumb":{width:{xs:20,sm:24},height:{xs:20,sm:24}},"& .MuiSwitch-track":{borderRadius:{xs:10,sm:12}}}}),label:a.jsx(l,{sx:{fontSize:{xs:"0.9rem",sm:"1rem"},fontWeight:500},children:"启用语音识别功能"}),sx:{"& .MuiFormControlLabel-label":{ml:{xs:1,sm:1.5}}}})}),a.jsxs(w,{value:L.sttSubTabValue,onChange:ye,variant:"scrollable",scrollButtons:"auto",allowScrollButtonsMobile:!0,sx:{mb:{xs:2,sm:3},borderBottom:1,borderColor:"divider","& .MuiTabs-scroller":{overflow:"auto !important",scrollBehavior:"smooth",WebkitOverflowScrolling:"touch","&::-webkit-scrollbar":{display:"none"},scrollbarWidth:"none"},"& .MuiTabs-flexContainer":{gap:{xs:.5,sm:1},minWidth:"fit-content"},"& .MuiTab-root":{minHeight:{xs:56,sm:64},fontSize:{xs:"0.7rem",sm:"0.875rem",md:"1rem"},fontWeight:500,textTransform:"none",px:{xs:1.5,sm:2,md:3},py:{xs:1,sm:1.5},minWidth:{xs:"auto",sm:120,md:160},maxWidth:{xs:200,sm:250,md:300},whiteSpace:"nowrap","&.Mui-selected":{fontWeight:600,color:"primary.main"},"&:hover":{backgroundColor:"action.hover",transition:"background-color 0.2s ease-in-out"}},"& .MuiTabs-indicator":{height:{xs:3,sm:4},borderRadius:"2px 2px 0 0",background:"linear-gradient(90deg, #9333EA, #754AB4)"},"& .MuiTabs-scrollButtons":{"&.Mui-disabled":{opacity:.3},"& .MuiSvgIcon-root":{fontSize:{xs:"1.2rem",sm:"1.5rem"}}}},children:[a.jsx(z,{label:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},flexDirection:"row",textAlign:"center",justifyContent:"center",width:"100%"},children:[a.jsx(l,{sx:{fontSize:"inherit",fontWeight:"inherit",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:"本地识别"}),a.jsx(s,{sx:{display:"flex",gap:.25,alignItems:"center"},children:"capacitor"===E.provider&&a.jsx(g,{size:"small",label:"使用中",color:"success",variant:"filled",sx:{fontSize:{xs:"0.55rem",sm:"0.65rem"},height:{xs:16,sm:20},minWidth:"auto","& .MuiChip-label":{px:{xs:.5,sm:.75},py:0}}})})]})}),a.jsx(z,{label:a.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},flexDirection:"row",textAlign:"center",justifyContent:"center",width:"100%"},children:[a.jsx(l,{sx:{fontSize:"inherit",fontWeight:"inherit",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:"OpenAI Whisper"}),a.jsxs(s,{sx:{display:"flex",gap:.25,alignItems:"center"},children:["openai"===E.provider&&a.jsx(g,{size:"small",label:"使用中",color:"success",variant:"filled",sx:{fontSize:{xs:"0.55rem",sm:"0.65rem"},height:{xs:16,sm:20},minWidth:"auto","& .MuiChip-label":{px:{xs:.5,sm:.75}}}}),X.apiKey&&"openai"!==E.provider&&a.jsx(g,{size:"small",label:"已配置",color:"info",variant:"outlined",sx:{fontSize:{xs:"0.55rem",sm:"0.65rem"},height:{xs:16,sm:20},minWidth:"auto","& .MuiChip-label":{px:{xs:.5,sm:.75},py:0}}})]})]})})]}),0===L.sttSubTabValue&&a.jsxs(a.Fragment,{children:[a.jsxs(o,{fullWidth:!0,sx:{mb:{xs:2,sm:3}},children:[a.jsx(u,{children:"默认语言"}),a.jsxs(c,{value:E.language,onChange:e=>U((a=>({...a,language:e.target.value}))),label:"默认语言",children:[a.jsx(x,{value:"zh-CN",children:"中文 (普通话)"}),a.jsx(x,{value:"en-US",children:"English (US)"})]}),a.jsx(d,{children:"选择语音识别的默认语言。"})]}),a.jsxs(l,{variant:"body2",color:"text.secondary",sx:{mb:{xs:2,sm:3}},children:["权限状态: ",xe]}),a.jsx(l,{variant:"h6",sx:{mb:{xs:2,sm:3},fontWeight:600},children:"识别参数"}),a.jsx(i,{fullWidth:!0,label:"静音超时时间 (毫秒)",type:"number",value:E.silenceTimeout,onChange:e=>U((a=>({...a,silenceTimeout:Number(e.target.value)}))),sx:{mb:{xs:2,sm:3}}}),a.jsx(i,{fullWidth:!0,label:"最大结果数量",type:"number",value:E.maxResults,onChange:e=>U((a=>({...a,maxResults:Number(e.target.value)}))),sx:{mb:{xs:2,sm:3}}}),a.jsx(j,{control:a.jsx(y,{checked:E.partialResults,onChange:e=>U((a=>({...a,partialResults:e.target.checked}))),color:"primary"}),label:"显示部分结果",sx:{mb:{xs:2,sm:3}}}),a.jsx(j,{control:a.jsx(y,{checked:E.autoStart,onChange:e=>U((a=>({...a,autoStart:e.target.checked}))),color:"primary"}),label:"自动开始识别",sx:{mb:{xs:2,sm:3}}}),a.jsx(l,{variant:"h6",sx:{mb:{xs:2,sm:3},fontWeight:600},children:"测试语音识别"}),a.jsxs(s,{sx:{display:"flex",gap:{xs:1,sm:2},mb:{xs:2,sm:3},flexDirection:{xs:"column",sm:"row"}},children:[a.jsx(h,{variant:"outlined",onClick:async()=>{try{await V.requestPermissions()}catch(e){console.error("请求权限失败:",e)}},disabled:"granted"===xe,sx:{flex:1},children:"检查并请求权限"}),a.jsx(h,{variant:"contained",color:ue?"error":"primary",onClick:ue?he:()=>me({language:E.language,maxResults:E.maxResults,partialResults:E.partialResults,popup:!1}),startIcon:ue?a.jsx(F,{}):a.jsx(O,{}),sx:{flex:1},children:ue?"停止识别":"开始识别"})]}),ce&&a.jsxs(t,{severity:"info",sx:{mb:{xs:2,sm:3}},children:["实时识别结果: ",ce]}),de&&a.jsxs(t,{severity:"error",sx:{mb:{xs:2,sm:3}},children:["语音识别错误: ",de.message||"未知错误"]})]}),1===L.sttSubTabValue&&a.jsxs(a.Fragment,{children:[a.jsx(P,{settings:X,onSettingsChange:Y}),a.jsx(B,{settings:X,enabled:E.enabled})]})]}),a.jsx(s,{sx:{display:"flex",justifyContent:"flex-end",mt:{xs:2,sm:3}},children:a.jsx(h,{variant:"contained",color:"primary",onClick:be,size:window.innerWidth<600?"large":"medium",sx:{minHeight:{xs:48,sm:40},fontSize:{xs:"0.9rem",sm:"1rem"},fontWeight:600,borderRadius:{xs:2,sm:1.5},px:{xs:3,sm:2},background:"linear-gradient(45deg, #9333EA, #754AB4)","&:hover":{background:"linear-gradient(45deg, #7C3AED, #6D28D9)",transform:"translateY(-1px)",boxShadow:"0 4px 12px rgba(147, 51, 234, 0.3)"},transition:"all 0.2s ease-in-out"},children:"保存设置"})})]})})]})};export{se as default};
