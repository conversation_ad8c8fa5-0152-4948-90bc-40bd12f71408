import{c as e,j as r,B as a,a as o,A as s,T as l,I as i,b as n,m as t,P as d,D as c,L as x,N as p,Q as h,y as m,x as g,w as b,V as j,o as u,p as v,q as y,t as f,F as k,e as C,S as w,M as S,r as A}from"./mui-vendor-hRDvsX89.js";import{u as I,r as z}from"./react-vendor-C9ilihHH.js";import{u as P,a as W,A as D,$ as R,aa as H,aA as M,aB as B,aC as E}from"./index-BtK6VV6Z.js";import{A as G}from"./Add-B_Z45Y27.js";import{C as F}from"./ChevronRight-D9zZwpxn.js";import{D as T,C as V,P as O}from"./dnd.esm-sjuiTR8o.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const $=e(r.jsx("path",{d:"M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2m-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2"})),q=e(r.jsx("path",{d:"M15 15H3v2h12zm0-8H3v2h12zM3 13h18v-2H3zm0 8h18v-2H3zM3 3v2h18V3z"})),L=e(r.jsx("path",{d:"M19 13H5c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4c0-1.1-.9-2-2-2m0-10H5c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2"}));const N=()=>{const e=I(),N=P(),Q=W((e=>e.settings.providers)),U=W((e=>e.settings.modelSelectorStyle)),[Y,J]=z.useState(!1),[K,X]=z.useState(!1),[Z,_]=z.useState(null),[ee,re]=z.useState(""),[ae,oe]=z.useState(""),se=()=>{X(!1),_(null),re(""),oe("")};return r.jsxs(a,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?o(e.palette.primary.main,.02):o(e.palette.background.default,.9)},children:[r.jsx(s,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(l,{children:[r.jsx(i,{edge:"start",color:"inherit",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(D,{})}),r.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"模型设置"}),r.jsx(t,{startIcon:r.jsx(G,{}),onClick:()=>{e("/settings/add-provider")},sx:{bgcolor:e=>o(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>o(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})}),r.jsxs(a,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[r.jsxs(d,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[r.jsx(n,{variant:"subtitle1",sx:{fontWeight:600},children:"模型服务商"}),r.jsx(n,{variant:"body2",color:"text.secondary",children:"您可以配置多个模型服务商，点击对应的服务商进行设置和管理"})]}),r.jsx(c,{}),r.jsx(T,{onDragStart:()=>{J(!0)},onDragEnd:e=>{if(J(!1),!e.destination)return;const r=e.source.index,a=e.destination.index;if(r===a)return;const o=function(e,r,a){const o=Array.from(e),[s]=o.splice(r,1);return o.splice(a,0,s),o}(Q,r,a);N(M(o))},children:r.jsx(V,{droppableId:"providers-list",children:s=>r.jsxs(x,{disablePadding:!0,ref:s.innerRef,...s.droppableProps,children:[Q.map(((s,l)=>r.jsx(O,{draggableId:s.id,index:l,children:(l,t)=>r.jsxs(p,{ref:l.innerRef,...l.draggableProps,onClick:()=>{return r=s.id,void(Y||e(`/settings/model-provider/${r}`));var r},sx:{transition:"all 0.2s",transform:t.isDragging?"rotate(5deg)":"none",boxShadow:t.isDragging?"0 8px 24px rgba(0,0,0,0.15)":"none",bgcolor:t.isDragging?"background.paper":"transparent","&:hover":{bgcolor:e=>o(e.palette.primary.main,.05)},...l.draggableProps.style},children:[r.jsx(a,{...l.dragHandleProps,sx:{display:"flex",alignItems:"center",mr:1,cursor:"grab","&:active":{cursor:"grabbing"},opacity:.6,"&:hover":{opacity:1}},children:r.jsx($,{fontSize:"small"})}),r.jsx(h,{children:r.jsx(m,{sx:{bgcolor:s.color||"#8e24aa",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:s.avatar||s.name.charAt(0).toUpperCase()})}),r.jsx(g,{primary:r.jsx(n,{sx:{fontWeight:600,color:"text.primary"},children:s.name}),secondary:r.jsxs(a,{component:"span",sx:{display:"flex",alignItems:"center"},children:[r.jsx(n,{component:"span",variant:"body2",sx:{mr:1,color:s.isEnabled?"success.main":"text.disabled",fontWeight:500},children:s.isEnabled?"已启用":"已禁用"}),s.models.length>0&&r.jsxs(n,{component:"span",variant:"body2",color:"text.secondary",children:[s.models.length," 个模型"]})]})}),r.jsx(i,{size:"small",onClick:e=>((e,r)=>{r.stopPropagation(),_(e),re(e.name),oe(e.providerType||""),X(!0)})(s,e),sx:{mr:1,color:"text.secondary","&:hover":{color:"primary.main",bgcolor:e=>o(e.palette.primary.main,.1)}},children:r.jsx(R,{fontSize:"small"})}),r.jsx(F,{sx:{color:e=>o(e.palette.primary.main,.5)}})]})},s.id))),s.placeholder]})})})]}),r.jsx(d,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:r.jsxs(x,{subheader:r.jsx(j,{component:"div",sx:{bgcolor:"rgba(0,0,0,0.01)",py:1,fontWeight:600,color:"text.primary"},children:"推荐操作"}),children:[r.jsx(b,{disablePadding:!0,children:r.jsxs(p,{onClick:()=>e("/settings/default-model-settings"),sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>o(e.palette.primary.main,.05)}},children:[r.jsx(h,{children:r.jsx(m,{sx:{bgcolor:o("#4f46e5",.12),color:"#4f46e5",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:r.jsx(H,{})})}),r.jsx(g,{primary:r.jsx(n,{sx:{fontWeight:600,color:"text.primary"},children:"默认模型设置"}),secondary:"设置默认使用的模型和自动化选项"}),r.jsx(F,{sx:{color:"text.secondary"}})]})}),r.jsx(c,{variant:"inset",component:"li",sx:{ml:0}}),r.jsx(b,{disablePadding:!0,children:r.jsxs(p,{onClick:()=>{N(B("dialog"===U?"dropdown":"dialog"))},sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>o(e.palette.primary.main,.05)}},children:[r.jsx(h,{children:r.jsx(m,{sx:{bgcolor:o("#06b6d4",.12),color:"#06b6d4",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:"dialog"===U?r.jsx(L,{}):r.jsx(q,{})})}),r.jsx(g,{primary:r.jsx(n,{sx:{fontWeight:600,color:"text.primary"},children:"模型选择器样式"}),secondary:"dialog"===U?"当前：弹窗式选择器（点击切换为下拉式）":"当前：下拉式选择器（点击切换为弹窗式）"})]})}),r.jsx(c,{variant:"inset",component:"li",sx:{ml:0}}),r.jsx(b,{disablePadding:!0,children:r.jsxs(p,{onClick:()=>e("/settings/add-provider"),sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>o(e.palette.primary.main,.05)}},children:[r.jsx(h,{children:r.jsx(m,{sx:{bgcolor:o("#9333ea",.12),color:"#9333ea",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:r.jsx(G,{})})}),r.jsx(g,{primary:r.jsx(n,{sx:{fontWeight:600,color:"text.primary"},children:"添加模型服务商"}),secondary:"设置新的模型服务商"}),r.jsx(F,{sx:{color:"text.secondary"}})]})})]})})]}),r.jsxs(u,{open:K,onClose:se,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2}},children:[r.jsx(v,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"编辑供应商"}),r.jsxs(y,{sx:{pt:2},children:[r.jsx(f,{autoFocus:!0,margin:"dense",label:"供应商名称",placeholder:"例如: 我的智谱AI",type:"text",fullWidth:!0,variant:"outlined",value:ee,onChange:e=>re(e.target.value),sx:{mb:2}}),r.jsxs(k,{fullWidth:!0,variant:"outlined",children:[r.jsx(C,{children:"供应商类型"}),r.jsx(w,{value:ae,onChange:e=>oe(e.target.value),label:"供应商类型",children:[{value:"openai",label:"OpenAI"},{value:"anthropic",label:"Anthropic"},{value:"deepseek",label:"DeepSeek"},{value:"zhipu",label:"智谱AI"},{value:"google",label:"Google"},{value:"azure-openai",label:"Azure OpenAI"},{value:"siliconflow",label:"SiliconFlow"},{value:"volcengine",label:"火山引擎"},{value:"grok",label:"Grok"},{value:"custom",label:"自定义"}].map((e=>r.jsx(S,{value:e.value,children:e.label},e.value)))})]})]}),r.jsxs(A,{sx:{p:2},children:[r.jsx(t,{onClick:se,children:"取消"}),r.jsx(t,{onClick:()=>{Z&&ee.trim()&&(N(E({id:Z.id,updates:{name:ee.trim(),providerType:ae}})),se())},disabled:!ee.trim(),sx:{bgcolor:e=>o(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>o(e.palette.primary.main,.2)},borderRadius:2},children:"保存"})]})]})]})};export{N as default};
