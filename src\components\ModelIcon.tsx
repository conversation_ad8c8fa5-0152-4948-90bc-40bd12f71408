import React from 'react';
import { Box, Avatar } from '@mui/material';
import { getModelIcon } from '../shared/utils/modelIcons';
import type { ModelIconProps } from '../shared/utils/modelIcons';

interface ModelIconComponentProps extends ModelIconProps {
  /** 供应商类型 */
  providerType?: string;
  /** 模型ID */
  modelId?: string;
  /** 图标大小，默认24 */
  size?: number;
  /** 是否显示为圆形头像 */
  variant?: 'default' | 'avatar';
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否禁用 */
  disabled?: boolean;
  /** 点击事件 */
  onClick?: () => void;
}

/**
 * 模型图标组件
 * 根据供应商类型和模型ID自动显示对应的图标
 */
export const ModelIcon: React.FC<ModelIconComponentProps> = ({
  providerType,
  modelId,
  size = 24,
  variant = 'default',
  className,
  style,
  disabled = false,
  onClick,
  ...props
}) => {
  const IconComponent = getModelIcon(providerType, modelId);

  const iconStyle: React.CSSProperties = {
    width: size,
    height: size,
    opacity: disabled ? 0.5 : 1,
    cursor: onClick ? 'pointer' : 'default',
    ...style,
  };

  const iconElement = (
    <IconComponent
      size={size}
      style={iconStyle}
      className={className}
      {...props}
    />
  );

  // 如果是头像变体，使用 Avatar 包装
  if (variant === 'avatar') {
    return (
      <Avatar
        sx={{
          width: size,
          height: size,
          bgcolor: 'transparent',
          cursor: onClick ? 'pointer' : 'default',
          opacity: disabled ? 0.5 : 1,
          ...style,
        }}
        className={className}
        onClick={onClick}
      >
        <IconComponent size={size * 0.7} {...props} />
      </Avatar>
    );
  }

  // 如果有点击事件，用 Box 包装
  if (onClick) {
    return (
      <Box
        component="span"
        onClick={onClick}
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          opacity: disabled ? 0.5 : 1,
          '&:hover': {
            opacity: disabled ? 0.5 : 0.8,
          },
        }}
        className={className}
        style={style}
      >
        <IconComponent size={size} {...props} />
      </Box>
    );
  }

  return iconElement;
};

export default ModelIcon;
