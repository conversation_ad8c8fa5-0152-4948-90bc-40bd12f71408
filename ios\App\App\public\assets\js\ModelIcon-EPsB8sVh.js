import{j as e,y as s,B as n}from"./mui-vendor-hRDvsX89.js";import"./react-vendor-C9ilihHH.js";import{af as a,ag as i,ah as t,ai as r}from"./index-BtK6VV6Z.js";const o={Chat:"chat",Vision:"vision",Audio:"audio",Embedding:"embedding",Tool:"tool",Reasoning:"reasoning",ImageGen:"image_gen",FunctionCalling:"function_calling",WebSearch:"web_search",Rerank:"rerank",CodeGen:"code_gen",Translation:"translation",Transcription:"transcription"},c=({providerType:o,modelId:c,size:l=24,variant:d="default",className:u,style:p,disabled:f=!1,onClick:g,...m})=>{const h=((e,s)=>{if(!e)return a;const n=e.toLowerCase().replace(/[-_\s]/g,"");if(s){const e=s.toLowerCase().replace(/[-_\s]/g,"");if(i(e))return t(e);for(const s of Object.keys(r))if(e.includes(s)||e.startsWith(s))return t(s)}if(i(n))return t(n);for(const a of Object.keys(r))if(n.includes(a)||a.includes(n))return t(a);return a})(o,c),y={width:l,height:l,opacity:f?.5:1,cursor:g?"pointer":"default",...p},j=e.jsx(h,{size:l,style:y,className:u,...m});return"avatar"===d?e.jsx(s,{sx:{width:l,height:l,bgcolor:"transparent",cursor:g?"pointer":"default",opacity:f?.5:1,...p},className:u,onClick:g,children:e.jsx(h,{size:.7*l,...m})}):g?e.jsx(n,{component:"span",onClick:g,sx:{display:"inline-flex",alignItems:"center",justifyContent:"center",cursor:"pointer",opacity:f?.5:1,"&:hover":{opacity:f?.5:.8}},className:u,style:p,children:e.jsx(h,{size:l,...m})}):j};export{o as M,c as a};
