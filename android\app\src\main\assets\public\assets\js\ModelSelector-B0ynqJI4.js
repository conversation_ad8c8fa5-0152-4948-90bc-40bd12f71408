import{j as e}from"./mui-vendor-hRDvsX89.js";import"./react-vendor-C9ilihHH.js";import{d as l}from"./index-BtK6VV6Z.js";import{D as o}from"./DialogModelSelector-BK--n_cz.js";import{D as d}from"./DropdownModelSelector-DlmZp9jO.js";const a=a=>"dropdown"===l((e=>e.settings.modelSelectorStyle))?e.jsx(d,{selectedModel:a.selectedModel,availableModels:a.availableModels,handleModelSelect:a.handleModelSelect}):e.jsx(o,{selectedModel:a.selectedModel,availableModels:a.availableModels,handleModelSelect:a.handleModelSelect,handleMenuClick:a.handleMenuClick,handleMenuClose:a.handleMenuClose,menuOpen:a.menuOpen});export{a as M};
