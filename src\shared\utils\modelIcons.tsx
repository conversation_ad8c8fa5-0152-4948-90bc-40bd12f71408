import React from 'react';
import { OpenAI } from '@lobehub/icons';
import { CORE_ICONS, getCoreIcon, isCoreIcon } from './iconPreloader';

// 模型图标映射类型
export interface ModelIconProps {
  size?: number;
  className?: string;
  style?: React.CSSProperties;
}

// 延迟加载的图标映射 - 只在需要时才导入
const LAZY_ICON_MAP: Record<string, () => Promise<any>> = {
  // 不常用的图标使用动态导入
  'dalle': () => import('@lobehub/icons').then(m => m.<PERSON>),
  'gemma': () => import('@lobehub/icons').then(m => m.Gemma),
  'palm': () => import('@lobehub/icons').then(m => m.PaLM),
  'vertex': () => import('@lobehub/icons').then(m => m.VertexAI),
  'vertexai': () => import('@lobehub/icons').then(m => m.VertexAI),
  'zhipuai': () => import('@lobehub/icons').then(m => m.Zhipu),
  'alibabacloud': () => import('@lobehub/icons').then(m => m.AlibabaCloud),
  'aliyun': () => import('@lobehub/icons').then(m => m.AlibabaCloud),
  'wenxin': () => import('@lobehub/icons').then(m => m.Wenxin),
  'ernie': () => import('@lobehub/icons').then(m => m.Wenxin),
  'baiducloud': () => import('@lobehub/icons').then(m => m.BaiduCloud),
  'hunyuan': () => import('@lobehub/icons').then(m => m.Hunyuan),
  'tencentcloud': () => import('@lobehub/icons').then(m => m.TencentCloud),
  'doubao': () => import('@lobehub/icons').then(m => m.Doubao),
  'volcengine': () => import('@lobehub/icons').then(m => m.Volcengine),
  'volcano': () => import('@lobehub/icons').then(m => m.Volcengine),
  'spark': () => import('@lobehub/icons').then(m => m.Spark),
  'xinghuo': () => import('@lobehub/icons').then(m => m.Spark),
  'iflytek': () => import('@lobehub/icons').then(m => m.IFlyTekCloud),
  'iflytekcloud': () => import('@lobehub/icons').then(m => m.IFlyTekCloud),
  'baichuan': () => import('@lobehub/icons').then(m => m.Baichuan),
  'sensenova': () => import('@lobehub/icons').then(m => m.SenseNova),
  'sensetime': () => import('@lobehub/icons').then(m => m.SenseNova),
  'stepfun': () => import('@lobehub/icons').then(m => m.Stepfun),
  'step': () => import('@lobehub/icons').then(m => m.Stepfun),
  'yi': () => import('@lobehub/icons').then(m => m.Yi),
  '01ai': () => import('@lobehub/icons').then(m => m.ZeroOne),
  'zeroone': () => import('@lobehub/icons').then(m => m.ZeroOne),
  'kimi': () => import('@lobehub/icons').then(m => m.Kimi),
  'moonshot': () => import('@lobehub/icons').then(m => m.Moonshot),
  'moonshotai': () => import('@lobehub/icons').then(m => m.Moonshot),
  'minimax': () => import('@lobehub/icons').then(m => m.Minimax),
  'cohere': () => import('@lobehub/icons').then(m => m.Cohere),
  'fireworks': () => import('@lobehub/icons').then(m => m.Fireworks),
  'fireworksai': () => import('@lobehub/icons').then(m => m.Fireworks),
  'anyscale': () => import('@lobehub/icons').then(m => m.Anyscale),
  'openrouter': () => import('@lobehub/icons').then(m => m.OpenRouter),
  'ollama': () => import('@lobehub/icons').then(m => m.Ollama),
  'lmstudio': () => import('@lobehub/icons').then(m => m.LmStudio),
  'lm-studio': () => import('@lobehub/icons').then(m => m.LmStudio),
  'xinference': () => import('@lobehub/icons').then(m => m.Xinference),
  'aws': () => import('@lobehub/icons').then(m => m.Aws),
  'bedrock': () => import('@lobehub/icons').then(m => m.Bedrock),
  'azure': () => import('@lobehub/icons').then(m => m.AzureAI),
  'azureai': () => import('@lobehub/icons').then(m => m.AzureAI),
  'azureopenai': () => import('@lobehub/icons').then(m => m.AzureAI),
  'perplexity': () => import('@lobehub/icons').then(m => m.Perplexity),
  'poe': () => import('@lobehub/icons').then(m => m.Poe),
};

/**
 * 根据供应商类型和模型ID获取对应的图标组件
 * 优先使用预加载的核心图标，提高缓存效率
 * @param providerType 供应商类型
 * @param modelId 模型ID（可选）
 * @returns React图标组件
 */
export const getModelIcon = (providerType?: string, modelId?: string) => {
  if (!providerType) {
    return OpenAI; // 默认图标
  }

  const normalizedProviderType = providerType.toLowerCase().replace(/[-_\s]/g, '');

  // 首先尝试根据模型ID匹配核心图标
  if (modelId) {
    const normalizedModelId = modelId.toLowerCase().replace(/[-_\s]/g, '');

    // 优先检查核心图标
    if (isCoreIcon(normalizedModelId)) {
      return getCoreIcon(normalizedModelId);
    }

    // 检查模型ID中是否包含核心图标的关键词
    for (const key of Object.keys(CORE_ICONS)) {
      if (normalizedModelId.includes(key) || normalizedModelId.startsWith(key)) {
        return getCoreIcon(key);
      }
    }
  }

  // 然后根据供应商类型匹配核心图标
  if (isCoreIcon(normalizedProviderType)) {
    return getCoreIcon(normalizedProviderType);
  }

  // 模糊匹配核心图标
  for (const key of Object.keys(CORE_ICONS)) {
    if (normalizedProviderType.includes(key) || key.includes(normalizedProviderType)) {
      return getCoreIcon(key);
    }
  }

  // 如果没有匹配到核心图标，返回默认的OpenAI图标
  // 注意：延迟加载的图标暂时不在这里处理，以保持同步性
  return OpenAI;
};

/**
 * 获取模型图标的React元素
 * @param providerType 供应商类型
 * @param modelId 模型ID（可选）
 * @param props 图标属性
 * @returns React元素
 */
export const getModelIconElement = (
  providerType?: string,
  modelId?: string,
  props: ModelIconProps = {}
) => {
  const IconComponent = getModelIcon(providerType, modelId);
  return <IconComponent {...props} />;
};

/**
 * 检查是否有对应的模型图标
 * @param providerType 供应商类型
 * @param modelId 模型ID（可选）
 * @returns 是否有对应图标
 */
export const hasModelIcon = (providerType?: string, modelId?: string): boolean => {
  if (!providerType) return false;

  const normalizedProviderType = providerType.toLowerCase().replace(/[-_\s]/g, '');

  if (modelId) {
    const normalizedModelId = modelId.toLowerCase().replace(/[-_\s]/g, '');
    // 检查核心图标
    if (isCoreIcon(normalizedModelId)) {
      return true;
    }
    // 检查核心图标的关键词
    for (const key of Object.keys(CORE_ICONS)) {
      if (normalizedModelId.includes(key) || normalizedModelId.startsWith(key)) {
        return true;
      }
    }
    // 检查延迟加载图标
    for (const key of Object.keys(LAZY_ICON_MAP)) {
      if (normalizedModelId.includes(key) || normalizedModelId.startsWith(key)) {
        return true;
      }
    }
  }

  // 检查供应商类型
  return isCoreIcon(normalizedProviderType) || normalizedProviderType in LAZY_ICON_MAP;
};

/**
 * 获取所有支持的模型图标列表
 * @returns 支持的模型类型列表
 */
export const getSupportedModelTypes = (): string[] => {
  return [...Object.keys(CORE_ICONS), ...Object.keys(LAZY_ICON_MAP)];
};
