/**
 * 内置记忆服务核心实现
 * 基于 Mem0 架构设计的本地记忆系统
 */

import type {
  MemoryRecord,
  MemoryStats,
  MemoryCategory
} from '../../types/internalMemory';
import { 
  dbRecordToMemoryRecord, 
  memoryRecordToDBRecord, 
  generateContentHash, 
  generateUUID 
} from '../../database/memorySchema';
import { getMemoryConfig } from '../../config/internalMemoryConfig';
import { DexieStorageService } from '../DexieStorageService';
import LoggerService from '../LoggerService';
import { MemoryEmbeddingService } from './EmbeddingService';

/**
 * 内置记忆服务类
 * 提供完整的记忆管理功能
 */
export class InternalMemoryService {
  private static instance: InternalMemoryService;
  private db: DexieStorageService;
  private embeddingService: MemoryEmbeddingService;
  private config = getMemoryConfig();

  private constructor() {
    this.db = DexieStorageService.getInstance();
    this.embeddingService = MemoryEmbeddingService.getInstance();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): InternalMemoryService {
    if (!InternalMemoryService.instance) {
      InternalMemoryService.instance = new InternalMemoryService();
    }
    return InternalMemoryService.instance;
  }

  /**
   * 检查记忆功能是否启用
   */
  public isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * 保存记忆记录
   */
  public async saveMemory(memory: MemoryRecord): Promise<void> {
    try {
      if (!this.isEnabled()) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return;
      }

      const dbRecord = memoryRecordToDBRecord(memory);
      await this.db.memories.put(dbRecord);

      LoggerService.log('INFO', 'Memory saved successfully', {
        memoryId: memory.id,
        userId: memory.userId,
        category: memory.category
      });
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to save memory', {
        error,
        memoryId: memory.id
      });
      throw error;
    }
  }

  /**
   * 保存带有嵌入向量的记忆
   */
  public async saveMemoryWithEmbedding(memory: MemoryRecord): Promise<void> {
    try {
      if (!this.isEnabled()) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return;
      }

      // 如果没有嵌入向量，生成一个
      if (!memory.embedding || memory.embedding.length === 0) {
        try {
          memory.embedding = await this.embeddingService.generateMemoryEmbedding(memory.content);
        } catch (error) {
          LoggerService.log('ERROR', 'Failed to generate embedding, saving without it', {
            error,
            memoryId: memory.id
          });
          memory.embedding = []; // 保存空向量作为降级
        }
      }

      await this.saveMemory(memory);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to save memory with embedding', {
        error,
        memoryId: memory.id
      });
      throw error;
    }
  }

  /**
   * 获取用户的所有记忆
   */
  public async getUserMemories(userId: string): Promise<MemoryRecord[]> {
    try {
      if (!this.isEnabled()) {
        return [];
      }

      const dbRecords = await this.db.memories
        .where('userId')
        .equals(userId)
        .toArray();

      return dbRecords.map(dbRecordToMemoryRecord);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get user memories', { error, userId });
      return [];
    }
  }

  /**
   * 根据分类获取记忆
   */
  public async getMemoriesByCategory(
    userId: string, 
    category: MemoryCategory
  ): Promise<MemoryRecord[]> {
    try {
      if (!this.isEnabled()) {
        return [];
      }

      const dbRecords = await this.db.memories
        .where('[userId+category]')
        .equals([userId, category])
        .toArray();

      return dbRecords.map(dbRecordToMemoryRecord);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get memories by category', { 
        error, 
        userId, 
        category 
      });
      return [];
    }
  }

  /**
   * 更新记忆
   */
  public async updateMemory(memoryId: string, content: string): Promise<void> {
    try {
      if (!this.isEnabled()) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return;
      }

      const existingRecord = await this.db.memories.get(memoryId);
      if (!existingRecord) {
        throw new Error(`Memory ${memoryId} not found`);
      }

      const memory = dbRecordToMemoryRecord(existingRecord);
      memory.content = content;
      memory.hash = generateContentHash(content);
      memory.updatedAt = new Date();

      await this.saveMemory(memory);
      
      LoggerService.log('INFO', 'Memory updated successfully', { memoryId });
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to update memory', { error, memoryId });
      throw error;
    }
  }

  /**
   * 删除记忆
   */
  public async deleteMemory(memoryId: string): Promise<void> {
    try {
      if (!this.isEnabled()) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return;
      }

      await this.db.memories.delete(memoryId);
      
      LoggerService.log('INFO', 'Memory deleted successfully', { memoryId });
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to delete memory', { error, memoryId });
      throw error;
    }
  }

  /**
   * 检查记忆是否已存在（基于哈希去重）
   */
  public async isMemoryExists(userId: string, contentHash: string): Promise<boolean> {
    try {
      if (!this.isEnabled()) {
        return false;
      }

      const existingMemory = await this.db.memories
        .where('hash')
        .equals(contentHash)
        .and(record => record.userId === userId)
        .first();

      return !!existingMemory;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to check memory existence', { 
        error, 
        userId, 
        contentHash 
      });
      return false;
    }
  }

  /**
   * 获取记忆统计信息
   */
  public async getMemoryStats(userId: string): Promise<MemoryStats> {
    try {
      if (!this.isEnabled()) {
        return {
          totalMemories: 0,
          categoryCounts: {},
          recentGrowth: 0,
          averageImportance: 0
        };
      }

      const memories = await this.getUserMemories(userId);
      
      // 计算分类统计
      const categoryCounts: Record<string, number> = {};
      let totalImportance = 0;
      
      memories.forEach(memory => {
        categoryCounts[memory.category] = (categoryCounts[memory.category] || 0) + 1;
        totalImportance += memory.importance;
      });

      // 计算最近增长（过去7天）
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const recentMemories = memories.filter(
        memory => memory.createdAt > sevenDaysAgo
      );

      return {
        totalMemories: memories.length,
        categoryCounts,
        recentGrowth: recentMemories.length,
        averageImportance: memories.length > 0 ? totalImportance / memories.length : 0
      };
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get memory stats', { error, userId });
      return {
        totalMemories: 0,
        categoryCounts: {},
        recentGrowth: 0,
        averageImportance: 0
      };
    }
  }

  /**
   * 清理过期记忆
   */
  public async cleanupExpiredMemories(userId: string): Promise<number> {
    try {
      if (!this.isEnabled() || this.config.storage.retentionDays <= 0) {
        return 0;
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.storage.retentionDays);
      const cutoffTimestamp = cutoffDate.getTime();

      const expiredMemories = await this.db.memories
        .where('[userId+createdAt]')
        .below([userId, cutoffTimestamp])
        .toArray();

      if (expiredMemories.length > 0) {
        const expiredIds = expiredMemories.map(memory => memory.id);
        await this.db.memories.bulkDelete(expiredIds);
        
        LoggerService.log('INFO', 'Cleaned up expired memories', { 
          userId, 
          count: expiredMemories.length 
        });
      }

      return expiredMemories.length;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to cleanup expired memories', { error, userId });
      return 0;
    }
  }

  /**
   * 创建新的记忆记录
   */
  public createMemoryRecord(
    userId: string,
    content: string,
    category: MemoryCategory,
    importance: number = 5,
    metadata: Record<string, any> = {}
  ): MemoryRecord {
    return {
      id: generateUUID(),
      userId,
      content,
      category,
      importance: Math.max(1, Math.min(10, importance)), // 确保在1-10范围内
      embedding: [], // 将在后续步骤中填充
      hash: generateContentHash(content),
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata
    };
  }
}
