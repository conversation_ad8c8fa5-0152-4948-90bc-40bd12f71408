import{c as e,j as r,z as s,B as a,b as o,a as i,k as l,ab as t,l as n,J as x,A as d,T as c,I as h,P as p,D as b,F as m,e as g,S as j,M as u,u as y,i as f,L as v,w as S,N as k,Q as w,y as C,x as z}from"./mui-vendor-hRDvsX89.js";import{u as R}from"./react-vendor-C9ilihHH.js";import{o as W,d as M,aQ as I,aR as A,aS as H,aT as L,aU as B,u as P,a as V,A as D,aV as E,C as F,a0 as G,aW as O}from"./index-BtK6VV6Z.js";import{C as $}from"./ChevronRight-D9zZwpxn.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const Q=e(r.jsx("path",{d:"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8m-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12m3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8m5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8m3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5"})),T=e(r.jsx("path",{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m-2 12H6v-2h12zm0-3H6V9h12zm0-3H6V6h12z"})),Y=e(r.jsx("path",{d:"M6 19h12v2H6z"})),J=e(r.jsx("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10c1.38 0 2.5-1.12 2.5-2.5 0-.61-.23-1.2-.64-1.67-.08-.1-.13-.21-.13-.33 0-.28.22-.5.5-.5H16c3.31 0 6-2.69 6-6 0-4.96-4.49-9-10-9m5.5 11c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m-3-4c-.83 0-1.5-.67-1.5-1.5S13.67 6 14.5 6s1.5.67 1.5 1.5S15.33 9 14.5 9M5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S7.33 13 6.5 13 5 12.33 5 11.5m6-4c0 .83-.67 1.5-1.5 1.5S8 8.33 8 7.5 8.67 6 9.5 6s1.5.67 1.5 1.5"})),N={default:r.jsx(J,{}),claude:r.jsx(B,{}),minimal:r.jsx(Y,{}),vibrant:r.jsx(Q,{})},U=({compact:e=!1})=>{const d=W(),c=s(),h=M((e=>e.settings.themeStyle))||"default",p=({themeStyle:s})=>{var p;const b=I[s],m=A(s),g=h===s;return r.jsx(l,{elevation:0,sx:{position:"relative",border:"2px solid",borderColor:g?c.palette.primary.main:"divider",borderRadius:2,overflow:"hidden",transition:"all 0.2s ease-in-out",aspectRatio:e?"1.2/1":"1.1/1",minHeight:e?"120px":"140px",maxWidth:"200px","&:hover":{borderColor:g?c.palette.primary.main:i(c.palette.primary.main,.5),transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(0,0,0,0.15)"}},children:r.jsx(t,{onClick:()=>(e=>{d(L(e))})(s),children:r.jsxs(n,{sx:{p:e?1.5:2},children:[g&&r.jsx(a,{sx:{position:"absolute",top:8,right:8,zIndex:1},children:r.jsx(H,{sx:{color:c.palette.primary.main,fontSize:20}})}),r.jsxs(a,{sx:{height:e?60:80,borderRadius:1,mb:1.5,position:"relative",overflow:"hidden",background:(null==(p=b.gradients)?void 0:p.primary)||m.primary},children:[r.jsxs(a,{sx:{position:"absolute",top:8,left:8,right:8,height:20,bgcolor:m.paper,borderRadius:.5,display:"flex",alignItems:"center",px:1},children:[r.jsx(a,{sx:{width:8,height:8,borderRadius:"50%",bgcolor:m.primary,mr:.5}}),r.jsx(a,{sx:{flex:1,height:4,bgcolor:i(m.primary,.3),borderRadius:.5}})]}),r.jsxs(a,{sx:{position:"absolute",bottom:8,left:8,right:8,height:16,bgcolor:i(m.paper,.9),borderRadius:.5,display:"flex",alignItems:"center",px:1,gap:.5},children:[r.jsx(a,{sx:{width:12,height:8,bgcolor:m.secondary,borderRadius:.5}}),r.jsx(a,{sx:{width:20,height:8,bgcolor:i(m.primary,.5),borderRadius:.5}})]})]}),r.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:1},children:[r.jsx(a,{sx:{color:m.primary,mr:1,display:"flex",alignItems:"center"},children:N[s]}),r.jsx(o,{variant:e?"body2":"subtitle2",sx:{fontWeight:600,color:"text.primary"},children:b.name})]}),!e&&r.jsx(o,{variant:"caption",color:"text.secondary",sx:{display:"block",mb:1,lineHeight:1.3},children:b.description}),r.jsxs(a,{sx:{display:"flex",gap:.5,alignItems:"center"},children:[r.jsx(a,{sx:{width:16,height:16,borderRadius:"50%",bgcolor:m.primary,border:"2px solid",borderColor:"background.paper",boxShadow:"0 1px 3px rgba(0,0,0,0.2)"}}),r.jsx(a,{sx:{width:16,height:16,borderRadius:"50%",bgcolor:m.secondary,border:"2px solid",borderColor:"background.paper",boxShadow:"0 1px 3px rgba(0,0,0,0.2)"}}),b.colors.accent&&r.jsx(a,{sx:{width:16,height:16,borderRadius:"50%",bgcolor:b.colors.accent,border:"2px solid",borderColor:"background.paper",boxShadow:"0 1px 3px rgba(0,0,0,0.2)"}}),g&&r.jsx(x,{label:"当前",size:"small",sx:{ml:"auto",height:20,fontSize:"0.7rem",bgcolor:i(c.palette.primary.main,.1),color:c.palette.primary.main,fontWeight:600}})]})]})})})};return r.jsxs(a,{children:[r.jsx(o,{variant:"subtitle2",sx:{fontWeight:600,mb:2,color:"text.primary"},children:"主题风格"}),r.jsx(o,{variant:"body2",color:"text.secondary",sx:{mb:2,fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"选择您喜欢的界面设计风格，每种风格都有独特的色彩搭配和视觉效果"}),r.jsx(a,{sx:{display:"flex",flexWrap:"wrap",gap:e?1:2,justifyContent:e?"flex-start":"center","& > *":{flex:e?"1 1 calc(50% - 8px)":"0 0 auto",minWidth:e?"140px":"160px",maxWidth:e?"180px":"200px"}},children:Object.keys(I).map((e=>r.jsx(a,{children:r.jsx(p,{themeStyle:e})},e)))}),r.jsx(a,{sx:{mt:3,p:2,bgcolor:i(c.palette.primary.main,.05),borderRadius:2},children:r.jsxs(o,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.8rem"},children:["💡 ",r.jsx("strong",{children:"提示："}),"主题风格会影响整个应用的色彩搭配、按钮样式和视觉效果。 您可以随时在设置中更改主题风格，更改会立即生效。"]})})]})},X=()=>{const e=R(),s=P(),l=V((e=>e.settings)),t=[{value:12,label:"极小"},{value:14,label:"小"},{value:16,label:"标准"},{value:18,label:"大"},{value:20,label:"极大"},{value:24,label:"超大"}];return r.jsxs(a,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?i(e.palette.primary.main,.02):i(e.palette.background.default,.9)},children:[r.jsx(d,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(c,{children:[r.jsx(h,{edge:"start",color:"inherit",onClick:()=>{e("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(D,{})}),r.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"外观设置"})]})}),r.jsxs(a,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[r.jsxs(p,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[r.jsx(o,{variant:"subtitle1",sx:{fontWeight:600},children:"主题和字体"}),r.jsx(o,{variant:"body2",color:"text.secondary",children:"自定义应用的外观主题和全局字体大小设置"})]}),r.jsx(b,{}),r.jsxs(a,{sx:{p:2},children:[r.jsxs(m,{fullWidth:!0,variant:"outlined",sx:{mb:3},children:[r.jsx(g,{children:"主题"}),r.jsxs(j,{value:l.theme,onChange:e=>s(E(e.target.value)),label:"主题",sx:{"& .MuiSelect-select":{fontSize:{xs:"0.9rem",sm:"1rem"}},"& .MuiOutlinedInput-notchedOutline":{borderRadius:2}},children:[r.jsx(u,{value:"light",children:"浅色"}),r.jsx(u,{value:"dark",children:"深色"}),r.jsx(u,{value:"system",children:"跟随系统"})]}),r.jsx(y,{children:"选择应用的外观主题，跟随系统将自动适配设备的深色/浅色模式"})]}),r.jsx(a,{sx:{mb:3},children:r.jsx(U,{compact:!0})}),r.jsxs(a,{sx:{mb:2},children:[r.jsxs(a,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2},children:[r.jsx(o,{variant:"body1",sx:{fontWeight:500,fontSize:{xs:"0.9rem",sm:"1rem"}},children:"全局字体大小"}),r.jsx(x,{label:`${l.fontSize}px (${(e=>{const r=t.find((r=>r.value===e));return r?r.label:"自定义"})(l.fontSize)})`,size:"small",color:"primary",variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},fontWeight:500}})]}),r.jsx(f,{value:l.fontSize,min:12,max:24,step:1,onChange:(e,r)=>{s(O(r))},valueLabelDisplay:"auto",valueLabelFormat:e=>`${e}px`,marks:t.map((e=>({value:e.value,label:e.label}))),sx:{"& .MuiSlider-thumb":{width:{xs:20,sm:24},height:{xs:20,sm:24},"&:hover":{boxShadow:"0 0 0 8px rgba(147, 51, 234, 0.16)"}},"& .MuiSlider-track":{background:"linear-gradient(90deg, #9333EA, #754AB4)"},"& .MuiSlider-rail":{opacity:.3},"& .MuiSlider-mark":{backgroundColor:"currentColor",height:8,width:2,"&.MuiSlider-markActive":{backgroundColor:"currentColor"}},"& .MuiSlider-markLabel":{fontSize:{xs:"0.65rem",sm:"0.75rem"},color:"text.secondary",transform:"translateX(-50%)",top:{xs:28,sm:32}},"& .MuiSlider-valueLabel":{fontSize:"0.75rem",fontWeight:600,background:"linear-gradient(45deg, #9333EA, #754AB4)"}}}),r.jsx(y,{sx:{mt:1,fontSize:{xs:"0.75rem",sm:"0.875rem"}},children:"调整应用中所有文本的基础字体大小，影响聊天消息、界面文字等全局显示效果"})]})]})]}),r.jsxs(p,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(a,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[r.jsx(o,{variant:"subtitle1",sx:{fontWeight:600},children:"界面定制"}),r.jsx(o,{variant:"body2",color:"text.secondary",children:"自定义聊天界面、消息气泡和工具栏的外观设置"})]}),r.jsx(b,{}),r.jsxs(v,{disablePadding:!0,children:[r.jsx(S,{disablePadding:!0,children:r.jsxs(k,{onClick:()=>{e("/settings/appearance/chat-interface")},sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>i(e.palette.primary.main,.05)}},children:[r.jsx(w,{children:r.jsx(C,{sx:{bgcolor:i("#6366f1",.12),color:"#6366f1",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:r.jsx(F,{})})}),r.jsx(z,{primary:r.jsx(o,{sx:{fontWeight:600,color:"text.primary"},children:"聊天界面设置"}),secondary:"自定义聊天界面布局和显示选项"}),r.jsx($,{sx:{color:"text.secondary"}})]})}),r.jsx(b,{variant:"inset",component:"li",sx:{ml:0}}),r.jsx(S,{disablePadding:!0,children:r.jsxs(k,{onClick:()=>{e("/settings/appearance/message-bubble")},sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>i(e.palette.primary.main,.05)}},children:[r.jsx(w,{children:r.jsx(C,{sx:{bgcolor:i("#8b5cf6",.12),color:"#8b5cf6",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:r.jsx(T,{})})}),r.jsx(z,{primary:r.jsx(o,{sx:{fontWeight:600,color:"text.primary"},children:"信息气泡管理"}),secondary:"调整消息气泡的样式和宽度设置"}),r.jsx($,{sx:{color:"text.secondary"}})]})}),r.jsx(b,{variant:"inset",component:"li",sx:{ml:0}}),r.jsx(S,{disablePadding:!0,children:r.jsxs(k,{onClick:()=>{e("/settings/appearance/top-toolbar")},sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>i(e.palette.primary.main,.05)}},children:[r.jsx(w,{children:r.jsx(C,{sx:{bgcolor:i("#06b6d4",.12),color:"#06b6d4",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:r.jsx(G,{})})}),r.jsx(z,{primary:r.jsx(o,{sx:{fontWeight:600,color:"text.primary"},children:"顶部工具栏设置"}),secondary:"自定义顶部工具栏的组件和布局"}),r.jsx($,{sx:{color:"text.secondary"}})]})})]})]})]})]})};export{X as default};
