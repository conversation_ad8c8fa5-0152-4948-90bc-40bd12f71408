import{j as t}from"./mui-vendor-DsBXMegs.js";import{r as e}from"./react-vendor-Be-rfjCm.js";const n=e.createContext({});function i(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const s="undefined"!=typeof window,o=s?e.useLayoutEffect:e.useEffect,r=e.createContext(null);function a(t,e){-1===t.indexOf(e)&&t.push(e)}function l(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const u=(t,e,n)=>n>e?e:n<t?t:n;const h={},c=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function d(t){return"object"==typeof t&&null!==t}const p=t=>/^0[^.\s]+$/u.test(t);function m(t){let e;return()=>(void 0===e&&(e=t()),e)}const f=t=>t,g=(t,e)=>n=>e(t(n)),y=(...t)=>t.reduce(g),v=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class x{constructor(){this.subscriptions=[]}add(t){return a(this.subscriptions,t),()=>l(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const T=t=>1e3*t,w=t=>t/1e3;function P(t,e){return e?t*(1e3/e):0}const S=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function b(t,e,n,i){if(t===e&&n===i)return f;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=S(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:S(s(t),e,i)}const A=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,E=t=>e=>1-t(1-e),V=b(.33,1.53,.69,.99),M=E(V),C=A(M),D=t=>(t*=2)<1?.5*M(t):.5*(2-Math.pow(2,-10*(t-1))),k=t=>1-Math.sin(Math.acos(t)),R=E(k),L=A(k),j=b(.42,0,1,1),B=b(0,0,.58,1),F=b(.42,0,.58,1),O=t=>Array.isArray(t)&&"number"==typeof t[0],I={linear:f,easeIn:j,easeInOut:F,easeOut:B,circIn:k,circInOut:L,circOut:R,backIn:M,backInOut:C,backOut:V,anticipate:D},U=t=>{if(O(t)){t.length;const[e,n,i,s]=t;return b(e,n,i,s)}return"string"==typeof t?I[t]:t},N=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],W={value:null};function $(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=N.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(h.schedule(e),t()),l++,e(a)}const h={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&W.value&&W.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,h.process(t)))}};return h}(o,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:p,render:m,postRender:f}=r,g=()=>{const o=h.useManualTiming?s.timestamp:performance.now();n=!1,h.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),d.process(s),p.process(s),m.process(s),f.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(g))};return{schedule:N.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(g)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<N.length;e++)r[N[e]].cancel(t)},state:s,steps:r}}const{schedule:X,cancel:Y,state:z,steps:K}=$("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:f,!0);let H;function q(){H=void 0}const G={now:()=>(void 0===H&&G.set(z.isProcessing||h.useManualTiming?z.timestamp:performance.now()),H),set:t=>{H=t,queueMicrotask(q)}},Z=t=>e=>"string"==typeof e&&e.startsWith(t),_=Z("--"),J=Z("var(--"),Q=t=>!!J(t)&&tt.test(t.split("/*")[0].trim()),tt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,et={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},nt={...et,transform:t=>u(0,1,t)},it={...et,default:1},st=t=>Math.round(1e5*t)/1e5,ot=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const rt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,at=(t,e)=>n=>Boolean("string"==typeof n&&rt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),lt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(ot);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},ut={...et,transform:t=>Math.round((t=>u(0,255,t))(t))},ht={test:at("rgb","red"),parse:lt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+ut.transform(t)+", "+ut.transform(e)+", "+ut.transform(n)+", "+st(nt.transform(i))+")"};const ct={test:at("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:ht.transform},dt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),pt=dt("deg"),mt=dt("%"),ft=dt("px"),gt=dt("vh"),yt=dt("vw"),vt=(()=>({...mt,parse:t=>mt.parse(t)/100,transform:t=>mt.transform(100*t)}))(),xt={test:at("hsl","hue"),parse:lt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+mt.transform(st(e))+", "+mt.transform(st(n))+", "+st(nt.transform(i))+")"},Tt={test:t=>ht.test(t)||ct.test(t)||xt.test(t),parse:t=>ht.test(t)?ht.parse(t):xt.test(t)?xt.parse(t):ct.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ht.transform(t):xt.transform(t)},wt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Pt="number",St="color",bt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function At(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(bt,(t=>(Tt.test(t)?(i.color.push(o),s.push(St),n.push(Tt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Pt),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function Et(t){return At(t).values}function Vt(t){const{split:e,types:n}=At(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Pt?st(t[o]):e===St?Tt.transform(t[o]):t[o]}return s}}const Mt=t=>"number"==typeof t?0:t;const Ct={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(ot))?void 0:e.length)||0)+((null==(n=t.match(wt))?void 0:n.length)||0)>0},parse:Et,createTransformer:Vt,getAnimatableNone:function(t){const e=Et(t);return Vt(t)(e.map(Mt))}};function Dt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function kt(t,e){return n=>n>0?e:t}const Rt=(t,e,n)=>t+(e-t)*n,Lt=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},jt=[ct,ht,xt];function Bt(t){const e=(n=t,jt.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===xt&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Dt(a,i,t+1/3),o=Dt(a,i,t),r=Dt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Ft=(t,e)=>{const n=Bt(t),i=Bt(e);if(!n||!i)return kt(t,e);const s={...n};return t=>(s.red=Lt(n.red,i.red,t),s.green=Lt(n.green,i.green,t),s.blue=Lt(n.blue,i.blue,t),s.alpha=Rt(n.alpha,i.alpha,t),ht.transform(s))},Ot=new Set(["none","hidden"]);function It(t,e){return n=>Rt(t,e,n)}function Ut(t){return"number"==typeof t?It:"string"==typeof t?Q(t)?kt:Tt.test(t)?Ft:$t:Array.isArray(t)?Nt:"object"==typeof t?Tt.test(t)?Ft:Wt:kt}function Nt(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>Ut(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Wt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=Ut(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const $t=(t,e)=>{const n=Ct.createTransformer(e),i=At(t),s=At(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Ot.has(t)&&!s.values.length||Ot.has(e)&&!i.values.length?function(t,e){return Ot.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):y(Nt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):kt(t,e)};function Xt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Rt(t,e,n);return Ut(t)(t,e)}const Yt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>X.update(e,t),stop:()=>Y(e),now:()=>z.isProcessing?z.timestamp:G.now()}},zt=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=t(o/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},Kt=2e4;function Ht(t){let e=0;let n=t.next(e);for(;!n.done&&e<Kt;)e+=50,n=t.next(e);return e>=Kt?1/0:e}function qt(t,e,n){const i=Math.max(e-5,0);return P(n-t(i),e-i)}const Gt=100,Zt=10,_t=1,Jt=0,Qt=800,te=.3,ee=.3,ne={granular:.01,default:2},ie={granular:.005,default:.5},se=.01,oe=10,re=.05,ae=1;function le({duration:t=Qt,bounce:e=te,velocity:n=Jt,mass:i=_t}){let s,o,r=1-e;r=u(re,ae,r),t=u(se,oe,w(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/he(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=he(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<ue;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=T(t),isNaN(a))return{stiffness:Gt,damping:Zt,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const ue=12;function he(t,e){return t*Math.sqrt(1-e*e)}const ce=["duration","bounce"],de=["stiffness","damping","mass"];function pe(t,e){return e.some((e=>void 0!==t[e]))}function me(t=ee,e=te){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:Jt,stiffness:Gt,damping:Zt,mass:_t,isResolvedFromDuration:!1,...t};if(!pe(t,de)&&pe(t,ce))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*u(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:_t,stiffness:s,damping:o}}else{const n=le(t);e={...e,...n,mass:_t},e.isResolvedFromDuration=!0}return e}({...n,velocity:-w(n.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(l*c)),y=r-o,v=w(Math.sqrt(l/c)),x=Math.abs(y)<5;let P;if(i||(i=x?ne.granular:ne.default),s||(s=x?ie.granular:ie.default),g<1){const t=he(v,g);P=e=>{const n=Math.exp(-g*v*e);return r-n*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}}else if(1===g)P=t=>r-Math.exp(-v*t)*(y+(f+v*y)*t);else{const t=v*Math.sqrt(g*g-1);P=e=>{const n=Math.exp(-g*v*e),i=Math.min(t*e,300);return r-n*((f+g*v*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}const S={calculatedDuration:m&&d||null,next:t=>{const e=P(t);if(m)a.done=t>=d;else{let n=0===t?f:0;g<1&&(n=0===t?T(f):qt(P,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(Ht(S),Kt),e=zt((e=>S.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return S}function fe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,g=void 0===r?f:r(f);g!==f&&(m=g-c);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=me({keyframes:[d.value,p(d.value)],velocity:qt(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function ge(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||h.mix||Xt,o=t.length-1;for(let r=0;r<o;r++){let n=s(t[r],t[r+1]);if(e){const t=Array.isArray(e)?e[r]||f:e;n=y(t,n)}i.push(n)}return i}(e,i,s),l=a.length,c=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=v(t[i],t[i+1],n);return a[i](s)};return n?e=>c(u(t[0],t[o-1],e)):c}function ye(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=v(0,e,i);t.push(Rt(n,1,s))}}(e,t.length-1),e}function ve({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(U):U(i),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:ye(e),t),a=ge(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map((()=>u||F)).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}me.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(Ht(i),Kt);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:w(s)}}(t,100,me);return t.ease=e.ease,t.duration=T(e.duration),t.type="keyframes",t};const xe=t=>null!==t;function Te(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(xe),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const we={decay:fe,inertia:fe,tween:ve,keyframes:ve,spring:me};function Pe(t){"string"==typeof t.type&&(t.type=we[t.type])}class Se{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const be=t=>t/100;class Ae extends Se{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{var e,n;if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==G.now()&&this.tick(G.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(n=(e=this.options).onStop)||n.call(e))},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Pe(t);const{type:e=ve,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||ve;a!==ve&&"number"!=typeof r[0]&&(this.mixKeyframes=y(be,Xt(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=Ht(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:h,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let x=this.currentTime,T=n;if(c){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/r)):"mirror"===d&&(T=o)),x=u(0,1,n)*r}const w=v?{done:!1,value:h[0]}:T.next(x);s&&(w.value=s(w.value));let{done:P}=w;v||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&m!==fe&&(w.value=Te(h,this.options,g,this.speed)),f&&f(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return w(this.calculatedDuration)}get time(){return w(this.currentTime)}set time(t){var e;t=T(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),null==(e=this.driver)||e.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(G.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=w(this.currentTime))}play(){var t,e;if(this.isStopped)return;const{driver:n=Yt,startTime:i}=this.options;this.driver||(this.driver=n((t=>this.tick(t)))),null==(e=(t=this.options).onPlay)||e.call(t);const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(G.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,e;this.notifyFinished(),this.teardown(),this.state="finished",null==(e=(t=this.options).onComplete)||e.call(t)}cancel(){var t,e;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(e=(t=this.options).onCancel)||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var e;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(e=this.driver)||e.stop(),t.observe(this)}}const Ee=t=>180*t/Math.PI,Ve=t=>{const e=Ee(Math.atan2(t[1],t[0]));return Ce(e)},Me={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ve,rotateZ:Ve,skewX:t=>Ee(Math.atan(t[1])),skewY:t=>Ee(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ce=t=>((t%=360)<0&&(t+=360),t),De=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ke=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Re={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:De,scaleY:ke,scale:t=>(De(t)+ke(t))/2,rotateX:t=>Ce(Ee(Math.atan2(t[6],t[5]))),rotateY:t=>Ce(Ee(Math.atan2(-t[2],t[0]))),rotateZ:Ve,rotate:Ve,skewX:t=>Ee(Math.atan(t[4])),skewY:t=>Ee(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Le(t){return t.includes("scale")?1:0}function je(t,e){if(!t||"none"===t)return Le(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Re,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Me,s=e}if(!s)return Le(e);const o=i[e],r=s[1].split(",").map(Be);return"function"==typeof o?o(r):r[o]}function Be(t){return parseFloat(t.trim())}const Fe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Oe=(()=>new Set(Fe))(),Ie=t=>t===et||t===ft,Ue=new Set(["x","y","z"]),Ne=Fe.filter((t=>!Ue.has(t)));const We={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>je(e,"x"),y:(t,{transform:e})=>je(e,"y")};We.translateX=We.x,We.translateY=We.y;const $e=new Set;let Xe=!1,Ye=!1,ze=!1;function Ke(){if(Ye){const t=Array.from($e).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return Ne.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{var i;null==(i=t.getValue(e))||i.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}Ye=!1,Xe=!1,$e.forEach((t=>t.complete(ze))),$e.clear()}function He(){$e.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(Ye=!0)}))}class qe{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?($e.add(this),Xe||(Xe=!0,X.read(He),X.resolveKeyframes(Ke))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),$e.delete(this)}cancel(){"scheduled"===this.state&&($e.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const Ge=m((()=>void 0!==window.ScrollTimeline)),Ze={};function _e(t,e){const n=m(t);return()=>Ze[e]??n()}const Je=_e((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Qe=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,tn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Qe([0,.65,.55,1]),circOut:Qe([.55,0,1,.45]),backIn:Qe([.31,.01,.66,-.59]),backOut:Qe([.33,1.53,.69,.99])};function en(t,e){return t?"function"==typeof t?Je()?zt(t,e):"ease-out":O(t)?Qe(t):Array.isArray(t)?t.map((t=>en(t,e)||tn.easeOut)):tn[t]:void 0}function nn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const h={[e]:n};l&&(h.offset=l);const c=en(a,s);Array.isArray(c)&&(h.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);return t.animate(h,d)}function sn(t){return"function"==typeof t&&"applyToOptions"in t}class on extends Se{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return sn(t)&&Je()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=nn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Te(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}null==a||a(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,e;null==(e=(t=this.animation).finish)||e.call(t)}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,e;this.isPseudoElement||null==(e=(t=this.animation).commitStyles)||e.call(t)}get duration(){var t,e;const n=(null==(e=null==(t=this.animation.effect)?void 0:t.getComputedTiming)?void 0:e.call(t).duration)||0;return w(Number(n))}get time(){return w(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=T(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){var n;return this.allowFlatten&&(null==(n=this.animation.effect)||n.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&Ge()?(this.animation.timeline=t,f):e(this)}}const rn={anticipate:D,backInOut:C,circInOut:L};function an(t){"string"==typeof t.ease&&t.ease in rn&&(t.ease=rn[t.ease])}class ln extends on{constructor(t){an(t),Pe(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Ae({...o,autoplay:!1}),a=T(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const un=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Ct.test(t)&&"0"!==t||t.startsWith("url(")));function hn(t){return d(t)&&"offsetHeight"in t}const cn=new Set(["opacity","clipPath","filter","transform"]),dn=m((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class pn extends Se{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...h}){var c;super(),this.stop=()=>{var t,e;this._animation&&(this._animation.stop(),null==(t=this.stopTimeline)||t.call(this)),null==(e=this.keyframeResolver)||e.cancel()},this.createdAt=G.now();const d={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...h},p=(null==u?void 0:u.KeyframeResolver)||qe;this.keyframeResolver=new p(r,((t,e,n)=>this.onKeyframesResolved(t,e,d,!n)),a,l,u),null==(c=this.keyframeResolver)||c.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=G.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=un(s,e),a=un(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||sn(n))&&i)}(t,s,o,r)||(!h.instantAnimations&&a||null==u||u(Te(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const c={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},d=!l&&function(t){var e;const{motionValue:n,name:i,repeatDelay:s,repeatType:o,damping:r,type:a}=t;if(!hn(null==(e=null==n?void 0:n.owner)?void 0:e.current))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return dn()&&i&&cn.has(i)&&("transform"!==i||!u)&&!l&&!s&&"mirror"!==o&&0!==r&&"inertia"!==a}(c)?new ln({...c,element:c.motionValue.owner.current}):new Ae(c);d.finished.then((()=>this.notifyFinished())).catch(f),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){var t;return this._animation||(null==(t=this.keyframeResolver)||t.resume(),ze=!0,He(),Ke(),ze=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),null==(t=this.keyframeResolver)||t.cancel()}}const mn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function fn(t,e,n=1){const[i,s]=function(t){const e=mn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return c(t)?parseFloat(t):t}return Q(s)?fn(s,e,n+1):s}function gn(t,e){return(null==t?void 0:t[e])??(null==t?void 0:t.default)??t}const yn=new Set(["width","height","top","left","right","bottom",...Fe]),vn=t=>e=>e.test(t),xn=[et,ft,mt,pt,yt,gt,{test:t=>"auto"===t,parse:t=>t}],Tn=t=>xn.find(vn(t));const wn=new Set(["brightness","contrast","saturate","opacity"]);function Pn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(ot)||[];if(!i)return t;const s=n.replace(i,"");let o=wn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Sn=/\b([a-z-]*)\(.*?\)/gu,bn={...Ct,getAnimatableNone:t=>{const e=t.match(Sn);return e?e.map(Pn).join(" "):t}},An={...et,transform:Math.round},En={borderWidth:ft,borderTopWidth:ft,borderRightWidth:ft,borderBottomWidth:ft,borderLeftWidth:ft,borderRadius:ft,radius:ft,borderTopLeftRadius:ft,borderTopRightRadius:ft,borderBottomRightRadius:ft,borderBottomLeftRadius:ft,width:ft,maxWidth:ft,height:ft,maxHeight:ft,top:ft,right:ft,bottom:ft,left:ft,padding:ft,paddingTop:ft,paddingRight:ft,paddingBottom:ft,paddingLeft:ft,margin:ft,marginTop:ft,marginRight:ft,marginBottom:ft,marginLeft:ft,backgroundPositionX:ft,backgroundPositionY:ft,...{rotate:pt,rotateX:pt,rotateY:pt,rotateZ:pt,scale:it,scaleX:it,scaleY:it,scaleZ:it,skew:pt,skewX:pt,skewY:pt,distance:ft,translateX:ft,translateY:ft,translateZ:ft,x:ft,y:ft,z:ft,perspective:ft,transformPerspective:ft,opacity:nt,originX:vt,originY:vt,originZ:ft},zIndex:An,fillOpacity:nt,strokeOpacity:nt,numOctaves:An},Vn={...En,color:Tt,backgroundColor:Tt,outlineColor:Tt,fill:Tt,stroke:Tt,borderColor:Tt,borderTopColor:Tt,borderRightColor:Tt,borderBottomColor:Tt,borderLeftColor:Tt,filter:bn,WebkitFilter:bn},Mn=t=>Vn[t];function Cn(t,e){let n=Mn(t);return n!==bn&&(n=Ct),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Dn=new Set(["auto","none","0"]);class kn extends qe{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),Q(n))){const i=fn(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!yn.has(n)||2!==t.length)return;const[i,s]=t,o=Tn(i),r=Tn(s);if(o!==r)if(Ie(o)&&Ie(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else We[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let s=0;s<t.length;s++)(null===t[s]||("number"==typeof(i=t[s])?0===i:null===i||"none"===i||"0"===i||p(i)))&&n.push(s);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Dn.has(e)&&At(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Cn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=We[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=We[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach((([t,n])=>{e.getValue(t).set(n)})),this.resolveNoneKeyframes()}}class Rn{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{var n,i;const s=G.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(null==(n=this.events.change)||n.notify(this.current),this.dependents))for(const o of this.dependents)o.dirty();e&&(null==(i=this.events.renderRequest)||i.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=G.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new x);const n=this.events[t].add(e);return"change"===t?()=>{n(),X.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;null==(t=this.events.change)||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=G.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return P(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,e;null==(t=this.dependents)||t.clear(),null==(e=this.events.destroy)||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ln(t,e){return new Rn(t,e)}const jn=(t,e)=>e&&"number"==typeof t?e.transform(t):t,{schedule:Bn}=$(queueMicrotask,!1),Fn={x:!1,y:!1};function On(){return Fn.x||Fn.y}function In(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document;const i=(null==n?void 0:n[t])??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Un(t){return!("touch"===t.pointerType||On())}const Nn=(t,e)=>!!e&&(t===e||Nn(t,e.parentElement)),Wn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,$n=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Xn=new WeakSet;function Yn(t){return e=>{"Enter"===e.key&&t(e)}}function zn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Kn(t){return Wn(t)&&!On()}function Hn(t,e,n={}){const[i,s,o]=In(t,n),r=t=>{const i=t.currentTarget;if(!Kn(t))return;Xn.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Xn.has(i)&&Xn.delete(i),Kn(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||Nn(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),hn(t)&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=Yn((()=>{if(Xn.has(n))return;zn(n,"down");const t=Yn((()=>{zn(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>zn(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,$n.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}function qn(t){return d(t)&&"ownerSVGElement"in t}const Gn=t=>Boolean(t&&t.getVelocity),Zn=[...xn,Tt,Ct],_n=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class Jn extends e.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=hn(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Qn({children:n,isPresent:i,anchorX:s}){const o=e.useId(),r=e.useRef(null),a=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(_n);return e.useInsertionEffect((()=>{const{width:t,height:e,top:n,left:u,right:h}=a.current;if(i||!r.current||!t||!e)return;const c="left"===s?`left: ${u}`:`right: ${h}`;r.current.dataset.motionPopId=o;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${c}px !important;\n            top: ${n}px !important;\n          }\n        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}}),[i]),t.jsx(Jn,{isPresent:i,childRef:r,sizeRef:a,children:e.cloneElement(n,{ref:r})})}const ti=({children:n,initial:s,isPresent:o,onExitComplete:a,custom:l,presenceAffectsLayout:u,mode:h,anchorX:c})=>{const d=i(ei),p=e.useId();let m=!0,f=e.useMemo((()=>(m=!1,{id:p,initial:s,isPresent:o,custom:l,onExitComplete:t=>{d.set(t,!0);for(const e of d.values())if(!e)return;a&&a()},register:t=>(d.set(t,!1),()=>d.delete(t))})),[o,d,a]);return u&&m&&(f={...f}),e.useMemo((()=>{d.forEach(((t,e)=>d.set(e,!1)))}),[o]),e.useEffect((()=>{!o&&!d.size&&a&&a()}),[o]),"popLayout"===h&&(n=t.jsx(Qn,{isPresent:o,anchorX:c,children:n})),t.jsx(r.Provider,{value:f,children:n})};function ei(){return new Map}function ni(t=!0){const n=e.useContext(r);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,a=e.useId();e.useEffect((()=>{if(t)return o(a)}),[t]);const l=e.useCallback((()=>t&&s&&s(a)),[a,s,t]);return!i&&s?[!1,l]:[!0]}const ii=t=>t.key||"";function si(t){const n=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&n.push(t)})),n}const oi=({children:s,custom:r,initial:a=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:h="sync",propagate:c=!1,anchorX:d="left"})=>{const[p,m]=ni(c),f=e.useMemo((()=>si(s)),[s]),g=c&&!p?[]:f.map(ii),y=e.useRef(!0),v=e.useRef(f),x=i((()=>new Map)),[T,w]=e.useState(f),[P,S]=e.useState(f);o((()=>{y.current=!1,v.current=f;for(let t=0;t<P.length;t++){const e=ii(P[t]);g.includes(e)?x.delete(e):!0!==x.get(e)&&x.set(e,!1)}}),[P,g.length,g.join("-")]);const b=[];if(f!==T){let t=[...f];for(let e=0;e<P.length;e++){const n=P[e],i=ii(n);g.includes(i)||(t.splice(e,0,n),b.push(n))}return"wait"===h&&b.length&&(t=b),S(si(t)),w(f),null}const{forceRender:A}=e.useContext(n);return t.jsx(t.Fragment,{children:P.map((e=>{const n=ii(e),i=!(c&&!p)&&(f===P||g.includes(n));return t.jsx(ti,{isPresent:i,initial:!(y.current&&!a)&&void 0,custom:r,presenceAffectsLayout:u,mode:h,onExitComplete:i?void 0:()=>{if(!x.has(n))return;x.set(n,!0);let t=!0;x.forEach((e=>{e||(t=!1)})),t&&(null==A||A(),S(v.current),c&&(null==m||m()),l&&l())},anchorX:d,children:e},n)}))})},ri=e.createContext({strict:!1}),ai={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},li={};for(const oa in ai)li[oa]={isEnabled:t=>ai[oa].some((e=>!!t[e]))};const ui=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function hi(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ui.has(t)}let ci=t=>!hi(t);try{(di=require("@emotion/is-prop-valid").default)&&(ci=t=>t.startsWith("on")?!hi(t):di(t))}catch{}var di;function pi(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const mi=e.createContext({});function fi(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function gi(t){return"string"==typeof t||Array.isArray(t)}const yi=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],vi=["initial",...yi];function xi(t){return fi(t.animate)||vi.some((e=>gi(t[e])))}function Ti(t){return Boolean(xi(t)||t.variants)}function wi(t){const{initial:n,animate:i}=function(t,e){if(xi(t)){const{initial:e,animate:n}=t;return{initial:!1===e||gi(e)?e:void 0,animate:gi(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(mi));return e.useMemo((()=>({initial:n,animate:i})),[Pi(n),Pi(i)])}function Pi(t){return Array.isArray(t)?t.join(" "):t}const Si=Symbol.for("motionComponentSymbol");function bi(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function Ai(t,n,i){return e.useCallback((e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):bi(i)&&(i.current=e))}),[n])}const Ei=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Vi="data-"+Ei("framerAppearId"),Mi=e.createContext({});function Ci(t,n,i,s,a){var l,u;const{visualElement:h}=e.useContext(mi),c=e.useContext(ri),d=e.useContext(r),p=e.useContext(_n).reducedMotion,m=e.useRef(null);s=s||c.renderer,!m.current&&s&&(m.current=s(t,{visualState:n,parent:h,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:p}));const f=m.current,g=e.useContext(Mi);!f||f.projection||!a||"html"!==f.type&&"svg"!==f.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Di(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&bi(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:h,layoutScroll:l,layoutRoot:u})}(m.current,i,a,g);const y=e.useRef(!1);e.useInsertionEffect((()=>{f&&y.current&&f.update(i,d)}));const v=i[Vi],x=e.useRef(Boolean(v)&&!(null==(l=window.MotionHandoffIsComplete)?void 0:l.call(window,v))&&(null==(u=window.MotionHasOptimisedAnimation)?void 0:u.call(window,v)));return o((()=>{f&&(y.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Bn.render(f.render),x.current&&f.animationState&&f.animationState.animateChanges())})),e.useEffect((()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask((()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,v)})),x.current=!1))})),f}function Di(t){if(t)return!1!==t.options.allowProjection?t.projection:Di(t.parent)}function ki({preloadedFeatures:n,createVisualElement:i,useRender:o,useVisualState:r,Component:a}){function l(n,l){let u;const h={...e.useContext(_n),...n,layoutId:Ri(n)},{isStatic:c}=h,d=wi(n),p=r(n,c);if(!c&&s){e.useContext(ri).strict;const t=function(t){const{drag:e,layout:n}=li;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=Ci(a,p,h,i,t.ProjectionNode)}return t.jsxs(mi.Provider,{value:d,children:[u&&d.visualElement?t.jsx(u,{visualElement:d.visualElement,...h}):null,o(a,n,Ai(p,d.visualElement,l),p,c,d.visualElement)]})}n&&function(t){for(const e in t)li[e]={...li[e],...t[e]}}(n),l.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;const u=e.forwardRef(l);return u[Si]=a,u}function Ri({layoutId:t}){const i=e.useContext(n).id;return i&&void 0!==t?i+"-"+t:t}const Li={};function ji(t,{layout:e,layoutId:n}){return Oe.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Li[t]||"opacity"===t)}const Bi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Fi=Fe.length;function Oi(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if(Oe.has(l))r=!0;else if(_(l))s[l]=t;else{const e=jn(t,En[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Fi;o++){const r=Fe[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=jn(a,En[r]);l||(s=!1,i+=`${Bi[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Ii=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ui(t,e,n){for(const i in e)Gn(e[i])||ji(i,n)||(t[i]=e[i])}function Ni(t,n){const i={};return Ui(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Oi(e,n,t),Object.assign({},e.vars,e.style)}),[n])}(t,n)),i}function Wi(t,e){const n={},i=Ni(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const $i={offset:"stroke-dashoffset",array:"stroke-dasharray"},Xi={offset:"strokeDashoffset",array:"strokeDasharray"};function Yi(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,h){if(Oi(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=(null==h?void 0:h.transformBox)??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?$i:Xi;t[o.offset]=ft.transform(-i);const r=ft.transform(e),a=ft.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const zi=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Ki=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Hi(t,n,i,s){const o=e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Yi(e,n,Ki(s),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}}),[n]);if(t.style){const e={};Ui(e,t.style,t),o.style={...e,...o.style}}return o}const qi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Gi(t){return"string"==typeof t&&!t.includes("-")&&!!(qi.indexOf(t)>-1||/[A-Z]/u.test(t))}function Zi(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(Gi(n)?Hi:Wi)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(ci(s)||!0===n&&hi(s)||!e&&!hi(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:h}=i,c=e.useMemo((()=>Gn(h)?h.get():h),[h]);return e.createElement(n,{...u,children:c})}}function _i(t){const e=[{},{}];return null==t||t.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function Ji(t,e,n,i){if("function"==typeof e){const[s,o]=_i(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=_i(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function Qi(t){return Gn(t)?t.get():t}const ts=t=>(n,s)=>{const o=e.useContext(mi),a=e.useContext(r),l=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:es(n,i,s,t),renderState:e()}}(t,n,o,a);return s?l():i(l)};function es(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=Qi(o[d]);let{initial:r,animate:a}=t;const l=xi(t),u=Ti(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let h=!!n&&!1===n.initial;h=h||!1===r;const c=h?a:r;if(c&&"boolean"!=typeof c&&!fi(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=Ji(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[h?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}function ns(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(Gn(s[r])||e.style&&Gn(e.style[r])||ji(r,t)||void 0!==(null==(i=null==n?void 0:n.getValue(r))?void 0:i.liveStyle))&&(o[r]=s[r]);return o}const is={useVisualState:ts({scrapeMotionValuesFromProps:ns,createRenderState:Ii})};function ss(t,e,n){const i=ns(t,e,n);for(const s in t)if(Gn(t[s])||Gn(e[s])){i[-1!==Fe.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const os={useVisualState:ts({scrapeMotionValuesFromProps:ss,createRenderState:zi})};function rs(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return ki({...Gi(n)?os:is,preloadedFeatures:t,useRender:Zi(i),createVisualElement:e,Component:n})}}function as(t,e,n){const i=t.getProps();return Ji(i,e,void 0!==n?n:i.custom,t)}const ls=t=>Array.isArray(t);function us(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Ln(n))}function hs(t,e){const n=t.getValue("willChange");if(i=n,Boolean(Gn(i)&&i.add))return n.add(e);if(!n&&h.WillChange){const n=new h.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function cs(t){return t.props[Vi]}const ds=t=>null!==t;const ps={type:"spring",stiffness:500,damping:25,restSpeed:10},ms={type:"keyframes",duration:.8},fs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},gs=(t,{keyframes:e})=>e.length>2?ms:Oe.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:ps:fs;const ys=(t,e,n,i={},s,o)=>r=>{const a=gn(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=T(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length})(a)||Object.assign(c,gs(t,c)),c.duration&&(c.duration=T(c.duration)),c.repeatDelay&&(c.repeatDelay=T(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(d=!0)),(h.instantAnimations||h.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,d&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"}){const i=t.filter(ds);return i[e&&"loop"!==n&&e%2==1?0:i.length-1]}(c.keyframes,a);if(void 0!==t)return void X.update((()=>{c.onUpdate(t),c.onComplete()}))}return a.isSync?new Ae(c):new pn(c)};function vs({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function xs(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const h in a){const e=t.getValue(h,t.latestValues[h]??null),i=a[h];if(void 0===i||u&&vs(u,h))continue;const s={delay:n,...gn(o||{},h)},r=e.get();if(void 0!==r&&!e.isAnimating&&!Array.isArray(i)&&i===r&&!s.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const e=cs(t);if(e){const t=window.MotionHandoffAnimation(e,h,X);null!==t&&(s.startTime=t,c=!0)}}hs(t,h),e.start(ys(h,e,i,t.shouldReduceMotion&&yn.has(h)?{type:!1}:s,t,c));const d=e.animation;d&&l.push(d)}return r&&Promise.all(l).then((()=>{X.update((()=>{r&&function(t,e){const n=as(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const a in o)us(t,a,(r=o[a],ls(r)?r[r.length-1]||0:r));var r}(t,r)}))})),l}function Ts(t,e,n={}){var i;const s=as(t,e,"exit"===n.type?null==(i=t.presenceContext)?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(xs(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(ws).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(Ts(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then((()=>e()))}return Promise.all([r(),a(n.delay)])}function ws(t,e){return t.sortNodePosition(e)}function Ps(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const Ss=vi.length;function bs(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&bs(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Ss;n++){const i=vi[n],s=t.props[i];(gi(s)||!1===s)&&(e[i]=s)}return e}const As=[...yi].reverse(),Es=yi.length;function Vs(t){return e=>Promise.all(e.map((({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>Ts(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=Ts(t,e,n);else{const s="function"==typeof e?as(t,e,n.custom):e;i=Promise.all(xs(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,n))))}function Ms(t){let e=Vs(t),n=ks(),i=!0;const s=e=>(n,i)=>{var s;const o=as(t,i,"exit"===e?null==(s=t.presenceContext)?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=bs(t.parent)||{},l=[],u=new Set;let h={},c=1/0;for(let e=0;e<Es;e++){const d=As[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=gi(m),g=d===o?p.isActive:null;!1===g&&(c=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...h},!p.isActive&&null===g||!m&&!p.prevProp||fi(m)||"boolean"==typeof m)continue;const v=Cs(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>c&&f,T=!1;const w=Array.isArray(m)?m:[m];let P=w.reduce(s(d),{});!1===g&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},A=e=>{x=!0,u.has(e)&&(T=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(h.hasOwnProperty(t))continue;let i=!1;i=ls(e)&&ls(n)?!Ps(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(h={...h,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(y&&v)||T)&&l.push(...w.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=as(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null==(s=t.variantChildren)||s.forEach((t=>{var n;return null==(n=t.animationState)?void 0:n.setActive(e,i)})),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=ks(),i=!0}}}function Cs(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Ps(e,t)}function Ds(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ks(){return{animate:Ds(!0),whileInView:Ds(),whileHover:Ds(),whileTap:Ds(),whileDrag:Ds(),whileFocus:Ds(),exit:Ds()}}class Rs{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ls=0;const js={animation:{Feature:class extends Rs{constructor(t){super(t),t.animationState||(t.animationState=Ms(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();fi(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}},exit:{Feature:class extends Rs{constructor(){super(...arguments),this.id=Ls++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Bs(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Fs(t){return{point:{x:t.pageX,y:t.pageY}}}function Os(t,e,n,i){return Bs(t,e,(t=>e=>Wn(e)&&t(e,Fs(e)))(n),i)}function Is({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Us(t){return t.max-t.min}function Ns(t,e,n,i=.5){t.origin=i,t.originPoint=Rt(e.min,e.max,t.origin),t.scale=Us(n)/Us(e),t.translate=Rt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Ws(t,e,n,i){Ns(t.x,e.x,n.x,i?i.originX:void 0),Ns(t.y,e.y,n.y,i?i.originY:void 0)}function $s(t,e,n){t.min=n.min+e.min,t.max=t.min+Us(e)}function Xs(t,e,n){t.min=e.min-n.min,t.max=t.min+Us(e)}function Ys(t,e,n){Xs(t.x,e.x,n.x),Xs(t.y,e.y,n.y)}const zs=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Ks(t){return[t("x"),t("y")]}function Hs(t){return void 0===t||1===t}function qs({scale:t,scaleX:e,scaleY:n}){return!Hs(t)||!Hs(e)||!Hs(n)}function Gs(t){return qs(t)||Zs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Zs(t){return _s(t.x)||_s(t.y)}function _s(t){return t&&"0%"!==t}function Js(t,e,n){return n+e*(t-n)}function Qs(t,e,n,i,s){return void 0!==s&&(t=Js(t,s,i)),Js(t,n,i)+e}function to(t,e=0,n=1,i,s){t.min=Qs(t.min,e,n,i,s),t.max=Qs(t.max,e,n,i,s)}function eo(t,{x:e,y:n}){to(t.x,e.translate,e.scale,e.originPoint),to(t.y,n.translate,n.scale,n.originPoint)}const no=.999999999999,io=1.0000000000001;function so(t,e){t.min=t.min+e,t.max=t.max+e}function oo(t,e,n,i,s=.5){to(t,e,n,Rt(t.min,t.max,s),i)}function ro(t,e){oo(t.x,e.x,e.scaleX,e.scale,e.originX),oo(t.y,e.y,e.scaleY,e.scale,e.originY)}function ao(t,e){return Is(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const lo=({current:t})=>t?t.ownerDocument.defaultView:null,uo=(t,e)=>Math.abs(t-e);class ho{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=mo(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=uo(t.x,e.x),i=uo(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=z;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=co(e,this.transformPagePoint),X.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=mo("pointercancel"===t.type?this.lastMoveEventInfo:co(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Wn(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=co(Fs(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=z;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,mo(o,this.history)),this.removeListeners=y(Os(this.contextWindow,"pointermove",this.handlePointerMove),Os(this.contextWindow,"pointerup",this.handlePointerUp),Os(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Y(this.updatePoint)}}function co(t,e){return e?{point:e(t.point)}:t}function po(t,e){return{x:t.x-e.x,y:t.y-e.y}}function mo({point:t},e){return{point:t,delta:po(t,go(e)),offset:po(t,fo(e)),velocity:yo(e,.1)}}function fo(t){return t[0]}function go(t){return t[t.length-1]}function yo(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=go(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>T(e)));)n--;if(!i)return{x:0,y:0};const o=w(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function vo(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function xo(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const To=.35;function wo(t,e,n){return{min:Po(t,e),max:Po(t,n)}}function Po(t,e){return"number"==typeof t?t:t[e]||0}const So=new WeakMap;class bo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new ho(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Fs(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?Fn[o]?null:(Fn[o]=!0,()=>{Fn[o]=!1}):Fn.x||Fn.y?null:(Fn.x=Fn.y=!0,()=>{Fn.x=Fn.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ks((t=>{let e=this.getAxisMotionValue(t).get()||0;if(mt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Us(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&X.postRender((()=>s(t,e))),hs(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Ks((t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:lo(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&X.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Ao(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Rt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Rt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,s=this.constraints;e&&bi(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:vo(t.x,n,s),y:vo(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=To){return!1===t?t=0:!0===t&&(t=To),{x:wo(t,"left","right"),y:wo(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ks((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!bi(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=ao(t,n),{scroll:s}=e;return s&&(so(i.x,s.offset.x),so(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:xo(t.x,e.x),y:xo(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Is(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Ks((r=>{if(!Ao(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,h=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return hs(this.visualElement,t),n.start(ys(t,n,0,e,this.visualElement,!1))}stopAnimation(){Ks((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){Ks((t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()}))}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Ks((e=>{const{drag:n}=this.getProps();if(!Ao(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Rt(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!bi(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ks((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Us(t),s=Us(e);return s>i?n=v(e.min,e.max-i,t.min):i>s&&(n=v(t.min,t.max-s,e.min)),u(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Ks((e=>{if(!Ao(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Rt(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;So.set(this.visualElement,this);const t=Os(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();bi(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),X.read(e);const s=Bs(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Ks((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=To,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Ao(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Eo=t=>(e,n)=>{t&&X.postRender((()=>t(e,n)))};const Vo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Mo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Co={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!ft.test(t))return t;t=parseFloat(t)}return`${Mo(t,e.target.x)}% ${Mo(t,e.target.y)}%`}},Do={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Ct.parse(t);if(s.length>5)return i;const o=Ct.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Rt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class ko extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)Li[e]=t[e],_(e)&&(Li[e].isCSSVariable=!0)}(Lo),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Vo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||X.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Bn.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Ro(i){const[s,o]=ni(),r=e.useContext(n);return t.jsx(ko,{...i,layoutGroup:r,switchLayoutGroup:e.useContext(Mi),isPresent:s,safeToRemove:o})}const Lo={borderRadius:{...Co,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Co,borderTopRightRadius:Co,borderBottomLeftRadius:Co,borderBottomRightRadius:Co,boxShadow:Do};const jo=(t,e)=>t.depth-e.depth;class Bo{constructor(){this.children=[],this.isDirty=!1}add(t){a(this.children,t),this.isDirty=!0}remove(t){l(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(jo),this.isDirty=!1,this.children.forEach(t)}}const Fo=["TopLeft","TopRight","BottomLeft","BottomRight"],Oo=Fo.length,Io=t=>"string"==typeof t?parseFloat(t):t,Uo=t=>"number"==typeof t||ft.test(t);function No(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Wo=Xo(0,.5,R),$o=Xo(.5,.95,f);function Xo(t,e,n){return i=>i<t?0:i>e?1:n(v(t,e,i))}function Yo(t,e){t.min=e.min,t.max=e.max}function zo(t,e){Yo(t.x,e.x),Yo(t.y,e.y)}function Ko(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Ho(t,e,n,i,s){return t=Js(t-=e,1/n,i),void 0!==s&&(t=Js(t,1/s,i)),t}function qo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){mt.test(e)&&(e=parseFloat(e),e=Rt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Rt(o.min,o.max,i);t===o&&(a-=e),t.min=Ho(t.min,e,n,a,s),t.max=Ho(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Go=["x","scaleX","originX"],Zo=["y","scaleY","originY"];function _o(t,e,n,i){qo(t.x,e,Go,n?n.x:void 0,i?i.x:void 0),qo(t.y,e,Zo,n?n.y:void 0,i?i.y:void 0)}function Jo(t){return 0===t.translate&&1===t.scale}function Qo(t){return Jo(t.x)&&Jo(t.y)}function tr(t,e){return t.min===e.min&&t.max===e.max}function er(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nr(t,e){return er(t.x,e.x)&&er(t.y,e.y)}function ir(t){return Us(t.x)/Us(t.y)}function sr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class or{constructor(){this.members=[]}add(t){a(this.members,t),t.scheduleRender()}remove(t){if(l(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const rr=["","X","Y","Z"],ar={visibility:"hidden"};let lr=0;function ur(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function hr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=cs(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",X,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&hr(i)}function cr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=lr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(mr),this.nodes.forEach(wr),this.nodes.forEach(Pr),this.nodes.forEach(fr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Bo)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new x),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=qn(e)&&!(qn(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=G.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(Y(i),t(o-e))};return X.setup(i,!0),()=>Y(i)}(i,250),Vo.hasAnimatedSinceResize&&(Vo.hasAnimatedSinceResize=!1,this.nodes.forEach(Tr))}))}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Mr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!nr(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...gn(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||Tr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Y(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Sr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&hr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(yr);this.isUpdating||this.nodes.forEach(vr),this.isUpdating=!1,this.nodes.forEach(xr),this.nodes.forEach(dr),this.nodes.forEach(pr),this.clearAllSnapshots();const t=G.now();z.delta=u(0,1e3/60,t-z.timestamp),z.timestamp=t,z.isProcessing=!0,K.update.process(z),K.preRender.process(z),K.render.process(z),z.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Bn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(gr),this.sharedNodes.forEach(br)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,X.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){X.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Us(this.snapshot.measuredBox.x)||Us(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Qo(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||Gs(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),kr((i=n).x),kr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(Lr))){const{scroll:t}=this.root;t&&(so(n.x,t.offset.x),so(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(zo(n,t),null==(e=this.scroll)?void 0:e.wasRoot)return n;for(let i=0;i<this.path.length;i++){const e=this.path[i],{scroll:s,options:o}=e;e!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&zo(n,t),so(n.x,s.offset.x),so(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};zo(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&ro(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),Gs(t.latestValues)&&ro(n,t.latestValues)}return Gs(this.latestValues)&&ro(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};zo(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!Gs(t.latestValues))continue;qs(t.latestValues)&&t.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};zo(i,t.measurePageBox()),_o(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return Gs(this.latestValues)&&_o(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==z.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=z.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ys(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),zo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var r,a,l;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,$s(r.x,a.x,l.x),$s(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):zo(this.target,this.layout.layoutBox),eo(this.target,this.targetDelta)):zo(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ys(this.relativeTargetOrigin,this.target,t.target),zo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!qs(this.parent.latestValues)&&!Zs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===z.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;zo(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ro(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,eo(t,r)),i&&Gs(o.latestValues)&&ro(t,o.latestValues))}e.x<io&&e.x>no&&(e.x=1),e.y<io&&e.y>no&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(Ko(this.prevProjectionDelta.x,this.projectionDelta.x),Ko(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Ws(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sr(this.projectionDelta.x,this.prevProjectionDelta.x)&&sr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,h=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Vr));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,g;Ar(o.x,t.x,n),Ar(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ys(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,g=n,Er(p.x,m.x,f.x,g),Er(p.y,m.y,f.y,g),c&&(l=this.relativeTarget,d=c,tr(l.x,d.x)&&tr(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),zo(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Rt(0,n.opacity??1,Wo(i)),t.opacityExit=Rt(e.opacity??1,0,$o(i))):o&&(t.opacity=Rt(e.opacity??1,n.opacity??1,i));for(let r=0;r<Oo;r++){const s=`border${Fo[r]}Radius`;let o=No(e,s),a=No(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Uo(o)===Uo(a)?(t[s]=Math.max(Rt(Io(o),Io(a),i),0),(mt.test(a)||mt.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=Rt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){var e,n,i;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(!1),null==(i=null==(n=this.resumingFrom)?void 0:n.currentAnimation)||i.stop(!1),this.pendingAnimation&&(Y(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=X.update((()=>{Vo.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ln(0)),this.currentAnimation=function(t,e,n){const i=Gn(t)?t:Ln(t);return i.start(ys("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Rr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Us(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Us(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}zo(e,n),ro(e,s),Ws(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new or);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&ur("z",t,i,this.animationValues);for(let s=0;s<rr.length;s++)ur(`rotate${rr[s]}`,t,i,this.animationValues),ur(`skew${rr[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ar;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=Qi(null==t?void 0:t.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Qi(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!Gs(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const a in Li){if(void 0===s[a])continue;const{correct:t,applyTo:n,isCSSVariable:o}=Li[a],r="none"===e.transform?s[a]:t(s[a],i);if(n){const t=n.length;for(let i=0;i<t;i++)e[n[i]]=r}else o?this.options.visualElement.renderState.vars[a]=r:e[a]=r}return this.options.layoutId&&(e.pointerEvents=i===this?Qi(null==t?void 0:t.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop(!1)})),this.root.nodes.forEach(yr),this.root.sharedNodes.clear()}}}function dr(t){t.updateLayout()}function pr(t){var e;const n=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?Ks((t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Us(i);i.min=e[t].min,i.max=i.min+s})):Rr(s,n.layoutBox,e)&&Ks((i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Us(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Ws(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Ws(a,t.applyTransform(i,!0),n.measuredBox):Ws(a,e,n.layoutBox);const l=!Qo(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Ys(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Ys(a,e,o.layoutBox),nr(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function mr(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function fr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function gr(t){t.clearSnapshot()}function yr(t){t.clearMeasurements()}function vr(t){t.isLayoutDirty=!1}function xr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Tr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function wr(t){t.resolveTargetDelta()}function Pr(t){t.calcProjection()}function Sr(t){t.resetSkewAndRotation()}function br(t){t.removeLeadSnapshot()}function Ar(t,e,n){t.translate=Rt(e.translate,0,n),t.scale=Rt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Er(t,e,n,i){t.min=Rt(e.min,n.min,i),t.max=Rt(e.max,n.max,i)}function Vr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Mr={duration:.45,ease:[.4,0,.1,1]},Cr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Dr=Cr("applewebkit/")&&!Cr("chrome/")?Math.round:f;function kr(t){t.min=Dr(t.min),t.max=Dr(t.max)}function Rr(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=ir(e),s=ir(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Lr(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}const jr=cr({attachResizeListener:(t,e)=>Bs(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Br={current:void 0},Fr=cr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Br.current){const t=new jr({});t.mount(window),t.setOptions({layoutScroll:!0}),Br.current=t}return Br.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Or={pan:{Feature:class extends Rs{constructor(){super(...arguments),this.removePointerDownListener=f}onPointerDown(t){this.session=new ho(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:lo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Eo(t),onStart:Eo(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&X.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Os(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Rs{constructor(t){super(t),this.removeGroupControls=f,this.removeListeners=f,this.controls=new bo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||f}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Fr,MeasureLayout:Ro}};function Ir(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&X.postRender((()=>s(e,Fs(e))))}function Ur(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&X.postRender((()=>s(e,Fs(e))))}const Nr=new WeakMap,Wr=new WeakMap,$r=t=>{const e=Nr.get(t.target);e&&e(t)},Xr=t=>{t.forEach($r)};function Yr(t,e,n){const i=function({root:t,...e}){const n=t||document;Wr.has(n)||Wr.set(n,{});const i=Wr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Xr,{root:t,...e})),i[s]}(e);return Nr.set(t,n),i.observe(t),()=>{Nr.delete(t),i.unobserve(t)}}const zr={some:0,all:1};const Kr={inView:{Feature:class extends Rs{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:zr[i]};return Yr(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Rs{mount(){const{current:t}=this.node;t&&(this.unmount=Hn(t,((t,e)=>(Ur(this.node,e,"Start"),(t,{success:e})=>Ur(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Rs{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=y(Bs(this.node.current,"focus",(()=>this.onFocus())),Bs(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Rs{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=In(t,n),r=t=>{if(!Un(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Un(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}(t,((t,e)=>(Ir(this.node,e,"Start"),t=>Ir(this.node,t,"End")))))}unmount(){}}}},Hr={layout:{ProjectionNode:Fr,MeasureLayout:Ro}},qr={current:null},Gr={current:!1};const Zr=new WeakMap;const _r=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Jr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=qe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=G.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,X.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=xi(e),this.isVariantNode=Ti(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const c in h){const t=h[c];void 0!==a[c]&&Gn(t)&&t.set(a[c],!1)}}mount(t){this.current=t,Zr.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Gr.current||function(){if(Gr.current=!0,s)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>qr.current=t.matches;t.addListener(e),e()}else qr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||qr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Y(this.notifyUpdate),Y(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Oe.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&X.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in li){const e=li[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<_r.length;n++){const e=_r[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(Gn(s))t.addValue(i,s);else if(Gn(o))t.addValue(i,Ln(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Ln(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Ln(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(c(n)||p(n))?n=parseFloat(n):(i=n,!Zn.find(vn(i))&&Ct.test(e)&&(n=Cn(t,e))),this.setBaseTarget(t,Gn(n)?n.get():n)),Gn(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=Ji(this.props,n,null==(e=this.presenceContext)?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Gn(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new x),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Qr extends Jr{constructor(){super(...arguments),this.KeyframeResolver=kn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Gn(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}function ta(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}class ea extends Qr{constructor(){super(...arguments),this.type="html",this.renderInstance=ta}readValueFromInstance(t,e){var n,i;if(Oe.has(e))return(null==(n=this.projection)?void 0:n.isProjecting)?Le(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return je(n,e)})(t,e);{const n=(i=t,window.getComputedStyle(i)),s=(_(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ao(t,e)}build(t,e,n){Oi(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return ns(t,e,n)}}const na=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ia extends Qr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=zs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Oe.has(e)){const t=Mn(e);return t&&t.default||0}return e=na.has(e)?e:Ei(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return ss(t,e,n)}build(t,e,n){Yi(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){ta(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(na.has(s)?s:Ei(s),e.attrs[s])}(t,e,0,i)}mount(t){this.isSVGTag=Ki(t.tagName),super.mount(t)}}const sa=pi(rs({...js,...Kr,...Or,...Hr},((t,n)=>Gi(t)?new ia(n):new ea(n,{allowProjection:t!==e.Fragment}))));export{oi as A,sa as m};
