import{a6 as a}from"./index-Dnlt-eWK.js";import"./mui-vendor-DsBXMegs.js";import"./react-vendor-Be-rfjCm.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";class e extends a{async canShare(){return"undefined"!=typeof navigator&&navigator.share?{value:!0}:{value:!1}}async share(a){if("undefined"==typeof navigator||!navigator.share)throw this.unavailable("Share API not available in this browser");return await navigator.share({title:a.title,text:a.text,url:a.url}),{}}}export{e as ShareWeb};
