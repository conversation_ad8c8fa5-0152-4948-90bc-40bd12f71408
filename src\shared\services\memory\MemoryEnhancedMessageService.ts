/**
 * 记忆增强消息服务
 * 集成记忆功能到消息处理流程
 */

import type { Message, Model } from '../../types';
import type { MemoryCategory } from '../../types/internalMemory';
import { MemoryExtractor } from './MemoryExtractor';
import { MemoryStorageService } from './MemoryStorageService';
import { ContextBuilder } from './ContextBuilder';
import { InternalMemoryService } from './InternalMemoryService';
import { getMemoryConfig } from '../../config/internalMemoryConfig';
import LoggerService from '../LoggerService';

/**
 * 记忆增强消息服务类
 * 在消息处理过程中集成记忆功能
 */
export class MemoryEnhancedMessageService {
  private static instance: MemoryEnhancedMessageService;
  private memoryExtractor: MemoryExtractor;
  private memoryStorage: MemoryStorageService;
  private contextBuilder: ContextBuilder;
  private memoryService: InternalMemoryService;
  private config = getMemoryConfig();

  private constructor() {
    this.memoryExtractor = new MemoryExtractor();
    this.memoryStorage = MemoryStorageService.getInstance();
    this.contextBuilder = ContextBuilder.getInstance();
    this.memoryService = InternalMemoryService.getInstance();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): MemoryEnhancedMessageService {
    if (!MemoryEnhancedMessageService.instance) {
      MemoryEnhancedMessageService.instance = new MemoryEnhancedMessageService();
    }
    return MemoryEnhancedMessageService.instance;
  }

  /**
   * 处理带记忆增强的消息
   */
  public async processMessageWithMemory(
    message: string,
    userId: string,
    _model: Model,
    options: {
      assistantId?: string;
      topicId?: string;
      conversationHistory?: Message[];
      onChunk?: (chunk: string, reasoning?: string) => void;
    } = {}
  ): Promise<{
    response: any;
    memoriesUsed: number;
    memoriesExtracted: number;
  }> {
    try {
      const { assistantId, topicId, conversationHistory = [] } = options;

      let enhancedMessage = message;
      let memoriesUsed = 0;

      // 1. 检查是否需要记忆增强
      if (this.config.enabled && this.contextBuilder.shouldEnhanceWithMemory(message, userId)) {
        try {
          // 🔧 修复：为记忆上下文构建添加错误处理，防止阻塞消息发送
          const memoryContext = await this.contextBuilder.buildEnhancedContext(
            message,
            userId,
            {
              assistantId,
              contextStyle: 'detailed',
              maxMemories: this.config.search.maxResults
            }
          );

          if (memoryContext.relevantMemories.length > 0) {
            enhancedMessage = memoryContext.enhancedPrompt;
            memoriesUsed = memoryContext.relevantMemories.length;

            LoggerService.log('INFO', 'Message enhanced with memory context', {
              userId,
              memoriesUsed,
              originalLength: message.length,
              enhancedLength: enhancedMessage.length
            });
          } else {
            LoggerService.log('INFO', 'No relevant memories found, using original message', { userId });
          }
        } catch (error) {
          LoggerService.log('WARN', 'Failed to enhance message with memory, using original message', {
            error,
            userId,
            message: message.substring(0, 50)
          });
          // 🔧 修复：记忆增强失败时继续使用原始消息，不阻塞消息发送
        }
      }

      // 2. 调用原有的消息服务
      // 注意：这里需要根据实际的消息处理方式调整
      // 暂时使用简化的响应结构
      const response = {
        content: enhancedMessage, // 简化处理
        success: true
      };

      // 3. 异步提取和保存新记忆（不阻塞响应）
      let memoriesExtracted = 0;
      if (this.config.enabled && conversationHistory.length >= this.config.extraction.minConversationLength) {
        this.extractAndSaveMemories(
          conversationHistory,
          message,
          response.content || '',
          userId,
          { assistantId, topicId },
          _model
        ).then(count => {
          memoriesExtracted = count;
          LoggerService.log('INFO', 'Memories extracted and saved asynchronously', {
            userId,
            memoriesExtracted: count
          });
        }).catch(error => {
          LoggerService.log('ERROR', 'Async memory extraction failed', { error, userId });
        });
      }

      return {
        response,
        memoriesUsed,
        memoriesExtracted
      };
    } catch (error) {
      LoggerService.log('ERROR', 'Memory-enhanced message processing failed', { error, userId });
      
      // 降级到原有服务
      const response = {
        content: message,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      return {
        response,
        memoriesUsed: 0,
        memoriesExtracted: 0
      };
    }
  }

  /**
   * 清理对话历史，确保消息格式正确
   */
  private cleanConversationHistory(conversationHistory: any[]): any[] {
    return conversationHistory
      .filter((msg: any) => {
        // 过滤掉无效消息
        if (!msg || !msg.role) {
          console.warn('⚠️ 发现无效消息（缺少 role）:', msg);
          return false;
        }

        // 获取消息内容 - 改进版本
        let content = '';

        // 尝试多种方式获取内容
        if (typeof msg.content === 'string') {
          content = msg.content;
        } else if (Array.isArray(msg.content)) {
          // 处理新格式的消息内容
          content = msg.content
            .filter((part: any) => part && part.type === 'text' && part.text)
            .map((part: any) => part.text)
            .join(' ');
        } else if (msg.content && typeof msg.content === 'object') {
          // 处理对象格式的内容
          if (msg.content.text) {
            content = msg.content.text;
          } else if (msg.content.content) {
            content = msg.content.content;
          }
        }

        // 如果还是没有内容，尝试从其他字段获取
        if (!content && msg.text) {
          content = msg.text;
        }
        if (!content && msg.message) {
          content = msg.message;
        }

        // 尝试从 blocks 中获取内容（这是项目的主要存储方式）
        if (!content && msg.blocks && Array.isArray(msg.blocks) && msg.blocks.length > 0) {
          // 获取主文本块的内容
          // 通常第一个块是主文本块，包含消息的实际内容
          const mainBlockId = msg.blocks[0];
          if (mainBlockId) {
            // 这里我们需要从数据库或 Redux store 获取块的实际内容
            // 暂时先标记这个需要异步获取
            content = `[需要从块 ${mainBlockId} 获取内容]`;
          }
        }

        // 移除详细的消息内容提取日志

        // 过滤掉空内容的消息
        if (!content || !content.trim()) {
          console.warn('⚠️ 发现空内容消息:', msg);
          return false;
        }

        return true;
      })
      .map((msg: any) => {
        // 标准化消息格式 - 使用相同的内容提取逻辑
        let content = '';

        if (typeof msg.content === 'string') {
          content = msg.content;
        } else if (Array.isArray(msg.content)) {
          content = msg.content
            .filter((part: any) => part && part.type === 'text' && part.text)
            .map((part: any) => part.text)
            .join(' ');
        } else if (msg.content && typeof msg.content === 'object') {
          if (msg.content.text) {
            content = msg.content.text;
          } else if (msg.content.content) {
            content = msg.content.content;
          }
        }

        // 备用字段
        if (!content && msg.text) {
          content = msg.text;
        }
        if (!content && msg.message) {
          content = msg.message;
        }

        return {
          role: msg.role,
          content: content.trim()
        };
      })
      .filter((msg: any) => msg.content.length > 0); // 再次确保没有空内容
  }

  /**
   * 异步提取和保存记忆
   */
  private async extractAndSaveMemories(
    conversationHistory: any[],
    userMessage: string,
    assistantResponse: string,
    userId: string,
    metadata: {
      assistantId?: string;
      topicId?: string;
    } = {},
    model?: Model
  ): Promise<number> {
    try {
      if (!this.config.enabled) {
        return 0;
      }

      // 构建完整的对话消息，确保格式正确
      const cleanMessages = this.cleanConversationHistory(conversationHistory);

      const messages = [
        ...cleanMessages,
        // 添加当前对话，确保内容不为空
        ...(userMessage?.trim() ? [{ role: 'user', content: userMessage.trim() }] : []),
        ...(assistantResponse?.trim() ? [{ role: 'assistant', content: assistantResponse.trim() }] : [])
      ] as any[];

      // 移除构建消息数组的详细日志

      // 提取记忆（传递 userId 以获取已有记忆）
      const extractionResult = await this.memoryExtractor.extractMemories(messages, model, userId);
      
      if (extractionResult.facts.length === 0) {
        LoggerService.log('INFO', 'No memories extracted from conversation', { userId });
        return 0;
      }

      // 保存记忆
      const savedMemories = await this.memoryStorage.saveMemories(
        userId,
        extractionResult.facts,
        extractionResult.categories,
        extractionResult.importance,
        {
          ...metadata,
          extractedFrom: 'conversation',
          extractedAt: new Date().toISOString()
        }
      );

      LoggerService.log('INFO', 'Memories extracted and saved', {
        userId,
        factsExtracted: extractionResult.facts.length,
        memoriesSaved: savedMemories.length
      });

      return savedMemories.length;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to extract and save memories', { error, userId });
      return 0;
    }
  }

  /**
   * 手动添加记忆
   */
  public async addMemory(
    userId: string,
    content: string,
    category: MemoryCategory,
    importance: number = 5,
    metadata: Record<string, any> = {}
  ): Promise<boolean> {
    try {
      if (!this.config.enabled) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return false;
      }

      const memory = this.memoryService.createMemoryRecord(
        userId,
        content,
        category,
        importance,
        {
          ...metadata,
          addedManually: true,
          addedAt: new Date().toISOString()
        }
      );

      await this.memoryService.saveMemoryWithEmbedding(memory);

      LoggerService.log('INFO', 'Memory added manually', {
        userId,
        memoryId: memory.id,
        category,
        importance
      });

      return true;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to add memory manually', { error, userId });
      return false;
    }
  }

  /**
   * 获取用户记忆摘要
   */
  public async getUserMemorySummary(
    userId: string,
    options: {
      maxLength?: number;
      includeStats?: boolean;
    } = {}
  ): Promise<string> {
    try {
      return await this.contextBuilder.buildMemorySummary(userId, options);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get user memory summary', { error, userId });
      return '无法获取用户记忆摘要。';
    }
  }

  /**
   * 搜索用户记忆
   */
  public async searchUserMemories(
    userId: string,
    query: string,
    options: {
      limit?: number;
      category?: MemoryCategory;
    } = {}
  ): Promise<any[]> {
    try {
      if (!this.config.enabled) {
        return [];
      }

      const searchResults = await this.memoryStorage.searchMemories(userId, query, options);
      return searchResults;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to search user memories', { error, userId, query });
      return [];
    }
  }

  /**
   * 更新记忆配置
   */
  public updateConfig(newConfig: any): void {
    console.log('🔄 记忆服务配置更新:', {
      old: this.config,
      new: newConfig
    });

    this.config = { ...this.config, ...newConfig };

    console.log('✅ 记忆服务配置更新完成:', this.config);
  }

  /**
   * 检查记忆功能状态
   */
  public getMemoryStatus(): {
    enabled: boolean;
    config: any;
    stats?: any;
  } {
    return {
      enabled: this.config.enabled,
      config: {
        extractionModel: this.config.extraction.model,
        embeddingModel: this.config.search.embeddingModel,
        maxMemoriesPerUser: this.config.storage.maxMemoriesPerUser,
        retentionDays: this.config.storage.retentionDays
      }
    };
  }

  /**
   * 清除用户所有记忆
   */
  public async clearUserMemories(userId: string): Promise<boolean> {
    try {
      if (!this.config.enabled) {
        return false;
      }

      const memories = await this.memoryService.getUserMemories(userId);
      
      for (const memory of memories) {
        await this.memoryService.deleteMemory(memory.id);
      }

      LoggerService.log('INFO', 'All user memories cleared', {
        userId,
        memoriesDeleted: memories.length
      });

      return true;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to clear user memories', { error, userId });
      return false;
    }
  }
}
