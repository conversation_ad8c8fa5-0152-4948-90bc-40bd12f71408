import{j as e,B as s,n as l,a as t,A as n,T as a,I as i,b as r,m as o,P as x,D as d,y as c,J as u,Z as m,_ as h,L as b,w as j,t as v,i as p,F as g,e as f,S as y,M as k,h as C,k as S,l as w,K as z}from"./mui-vendor-DsBXMegs.js";import{u as W,f as I,r as T}from"./react-vendor-Be-rfjCm.js";import{o as O,A as N,a2 as A,a3 as E,e as D,a4 as P,f as K,a5 as J}from"./index-Dnlt-eWK.js";import{S as M}from"./Save-BEY1hRQb.js";import{A as R}from"./Add-CgwaVY5w.js";import{T as B}from"./TuneOutlined-DzMCixvP.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";function F(l){const{children:t,value:n,index:a,...i}=l;return e.jsx("div",{role:"tabpanel",hidden:n!==a,id:`model-settings-tabpanel-${a}`,"aria-labelledby":`model-settings-tab-${a}`,...i,children:n===a&&e.jsx(s,{sx:{p:3},children:t})})}const L=.7,q=()=>{var q;const G=W(),$=I(),X=O(),_=null==(q=$.state)?void 0:q.assistant,[U,V]=T.useState(L),[Y,Z]=T.useState(1),[H,Q]=T.useState(40),[ee,se]=T.useState(0),[le,te]=T.useState(0),[ne,ae]=T.useState(null),[ie,re]=T.useState([]),[oe,xe]=T.useState(4096),[de,ce]=T.useState({}),[ue,me]=T.useState("text"),[he,be]=T.useState("auto"),[je,ve]=T.useState(!0),[pe,ge]=T.useState(["TEXT"]),[fe,ye]=T.useState(1),[ke,Ce]=T.useState(!1),[Se,we]=T.useState("zh-CN"),[ze,We]=T.useState(!1),[Ie,Te]=T.useState(0),[Oe,Ne]=T.useState("medium"),[Ae,Ee]=T.useState(""),[De,Pe]=T.useState([]),[Ke,Je]=T.useState(!1),[Me,Re]=T.useState(!1),[Be,Fe]=T.useState(16e3),[Le,qe]=T.useState(5),[Ge,$e]=T.useState("medium"),[Xe,_e]=T.useState(0);T.useEffect((()=>{(async()=>{const e=A();Fe(e.contextLength||16e3),qe(e.contextCount||5),$e(e.defaultThinkingEffort||"medium"),e.maxOutputTokens&&e.maxOutputTokens!==oe&&xe(e.maxOutputTokens)})();const e=e=>{if("appSettings"===e.key&&e.newValue)try{const s=JSON.parse(e.newValue);s.maxOutputTokens&&s.maxOutputTokens!==oe&&xe(s.maxOutputTokens)}catch(s){console.error("解析localStorage变化失败:",s)}};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)}),[oe]),T.useEffect((()=>{_&&(null==_||_.temperature,null==_||_.topP,null==_||_.model,V(_.temperature??L),Z(_.topP??1),Q(_.topK??40),se(_.frequencyPenalty??0),te(_.presencePenalty??0),ae(_.seed??null),re(_.stopSequences??[]),xe(_.maxTokens??4096),ce(_.logitBias??{}),me(_.responseFormat??"text"),be(_.toolChoice??"auto"),ve(_.parallelToolCalls??!0),ge(_.responseModalities??["TEXT"]),ye(_.candidateCount??1),Ce(_.enableSpeech??!1),we(_.speechLanguage??"zh-CN"),We(_.enableThinking??!1),Te(_.thinkingBudget??0),Ne(_.mediaResolution??"medium"),Ee(_.model||""),Pe([]))}),[_]),T.useEffect((()=>{const e=U!==((null==_?void 0:_.temperature)??L)||Y!==((null==_?void 0:_.topP)??1)||H!==((null==_?void 0:_.topK)??40)||ee!==((null==_?void 0:_.frequencyPenalty)??0)||le!==((null==_?void 0:_.presencePenalty)??0)||ne!==((null==_?void 0:_.seed)??null)||JSON.stringify(ie)!==JSON.stringify((null==_?void 0:_.stopSequences)??[])||oe!==((null==_?void 0:_.maxTokens)??4096)||JSON.stringify(de)!==JSON.stringify((null==_?void 0:_.logitBias)??{})||ue!==((null==_?void 0:_.responseFormat)??"text")||he!==((null==_?void 0:_.toolChoice)??"auto")||je!==((null==_?void 0:_.parallelToolCalls)??!0)||JSON.stringify(pe)!==JSON.stringify((null==_?void 0:_.responseModalities)??["TEXT"])||fe!==((null==_?void 0:_.candidateCount)??1)||ke!==((null==_?void 0:_.enableSpeech)??!1)||Se!==((null==_?void 0:_.speechLanguage)??"zh-CN")||ze!==((null==_?void 0:_.enableThinking)??!1)||Ie!==((null==_?void 0:_.thinkingBudget)??0)||Oe!==((null==_?void 0:_.mediaResolution)??"medium")||Ae!==((null==_?void 0:_.model)||"")||De.length>0;Je(e)}),[U,Y,H,ee,le,ne,ie,oe,de,ue,he,je,pe,fe,ke,Se,ze,Ie,Oe,Ae,De,_]);const Ue=(e,s,l)=>{const t=[...De];if("type"===s){let s="";switch(l){case"number":s=0;break;case"boolean":s=!1;break;default:s=""}t[e]={...t[e],type:l,value:s}}else t[e]={...t[e],[s]:l};Pe(t)},Ve=(s,l)=>{switch(s.type){case"number":return e.jsx(v,{type:"number",value:s.value,onChange:e=>Ue(l,"value",parseFloat(e.target.value)||0),size:"small",fullWidth:!0,inputProps:{step:.01}});case"boolean":return e.jsx(C,{checked:s.value,onChange:e=>Ue(l,"value",e.target.checked)});case"json":return e.jsx(v,{multiline:!0,rows:3,value:"string"==typeof s.value?s.value:JSON.stringify(s.value,null,2),onChange:e=>{try{const s=JSON.parse(e.target.value);Ue(l,"value",s)}catch{Ue(l,"value",e.target.value)}},size:"small",fullWidth:!0,placeholder:'{"key": "value"}'});default:return e.jsx(v,{value:s.value,onChange:e=>Ue(l,"value",e.target.value),size:"small",fullWidth:!0,placeholder:"参数值"})}};return _?e.jsxs(s,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?t(e.palette.primary.main,.02):t(e.palette.background.default,.9)},children:[e.jsx(n,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(a,{children:[e.jsx(i,{edge:"start",color:"inherit",onClick:()=>{G(-1)},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(N,{})}),e.jsxs(r,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:[_.name," - 模型设置"]}),Ke&&e.jsx(o,{startIcon:e.jsx(M,{}),onClick:async()=>{if(_){Re(!0);try{const e={..._,temperature:U,topP:Y,...40!==H&&{topK:H},...0!==ee&&{frequencyPenalty:ee},...0!==le&&{presencePenalty:le},...null!==ne&&{seed:ne},...ie.length>0&&{stopSequences:ie},maxTokens:oe,...(()=>{const e=A();return E({...e,maxOutputTokens:oe}),{}})(),...Object.keys(de).length>0&&{logitBias:de},..."text"!==ue&&{responseFormat:ue},..."auto"!==he&&{toolChoice:he},...!0!==je&&{parallelToolCalls:je},...JSON.stringify(pe)!==JSON.stringify(["TEXT"])&&{responseModalities:pe},...1!==fe&&{candidateCount:fe},...ke&&{enableSpeech:ke,speechLanguage:Se},...ze&&{enableThinking:ze,thinkingBudget:Ie},..."medium"!==Oe&&{mediaResolution:Oe},model:Ae,updatedAt:(new Date).toISOString()};_.id,_.name,await K.saveAssistant(e),X(J(e)),Je(!1)}catch(e){console.error("保存助手设置失败:",e)}finally{Re(!1)}}},disabled:Me,variant:"contained",size:"small",sx:{background:"linear-gradient(90deg, #9333EA, #754AB4)",fontWeight:600,"&:hover":{background:"linear-gradient(90deg, #8324DB, #6D3CAF)"}},children:Me?"保存中...":"保存"})]})}),e.jsxs(s,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsxs(x,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(r,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"助手信息"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"当前正在配置的助手详细信息"})]}),e.jsx(d,{}),e.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(c,{sx:{width:{xs:40,sm:48},height:{xs:40,sm:48},bgcolor:t("#9333EA",.12),color:"#9333EA",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:_.emoji||_.name.charAt(0)}),e.jsxs(s,{sx:{flex:1},children:[e.jsx(r,{variant:"h6",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:_.name}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"配置模型参数和行为设置"}),_.isSystem&&e.jsx(u,{label:"系统助手",size:"small",sx:{mt:.5,bgcolor:t("#9333EA",.1),color:"#9333EA",fontWeight:500,fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24}}})]})]})})]}),e.jsx(x,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:e.jsxs(m,{value:Xe,onChange:(e,s)=>_e(s),variant:"fullWidth",sx:{"& .MuiTab-root":{fontWeight:600,fontSize:{xs:"0.8rem",sm:"0.875rem"},textTransform:"none","&.Mui-selected":{color:"#9333EA"}},"& .MuiTabs-indicator":{backgroundColor:"#9333EA",height:3}},children:[e.jsx(h,{label:"通用参数"}),e.jsx(h,{label:"OpenAI 专属"}),e.jsx(h,{label:"Gemini 专属"}),e.jsx(h,{label:"自定义参数"})]})}),e.jsx(F,{value:Xe,index:0,children:e.jsxs(x,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(r,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"通用参数设置"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"配置模型的基础参数，适用于大多数AI模型"})]}),e.jsx(d,{}),e.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:e.jsxs(b,{sx:{p:0},children:[e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsx(r,{variant:"subtitle2",sx:{mb:1,fontWeight:"medium"},children:"默认模型"}),e.jsx(v,{value:Ae,onChange:e=>Ee(e.target.value),placeholder:"输入模型ID",size:"small",fullWidth:!0})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"温度 (Temperature)"}),e.jsx(u,{label:U.toFixed(2),size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"控制回答的创造性和随机性。较低值更保守，较高值更有创意。"}),e.jsx(p,{value:U,onChange:(e,s)=>V(s),min:0,max:2,step:.01,marks:[{value:0,label:"0"},{value:.7,label:"0.7"},{value:2,label:"2"}]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"Top-P"}),e.jsx(u,{label:Y.toFixed(2),size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"控制词汇选择的多样性。较低值更聚焦，较高值更多样。"}),e.jsx(p,{value:Y,onChange:(e,s)=>Z(s),min:0,max:1,step:.01,marks:[{value:0,label:"0"},{value:1,label:"1"}]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"Top-K"}),e.jsx(u,{label:H,size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"限制每步采样时考虑的最可能的token数量。较低值更保守，较高值更多样。"}),e.jsx(p,{value:H,onChange:(e,s)=>Q(s),min:1,max:100,step:1,marks:[{value:1,label:"1"},{value:40,label:"40"},{value:100,label:"100"}]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"频率惩罚 (Frequency Penalty)"}),e.jsx(u,{label:ee.toFixed(2),size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"根据token在文本中出现的频率对其进行惩罚。正值减少重复，负值增加重复。"}),e.jsx(p,{value:ee,onChange:(e,s)=>se(s),min:-2,max:2,step:.01,marks:[{value:-2,label:"-2"},{value:0,label:"0"},{value:2,label:"2"}]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"存在惩罚 (Presence Penalty)"}),e.jsx(u,{label:le.toFixed(2),size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"对已出现的token进行惩罚，不考虑频率。正值鼓励新话题，负值保持话题。"}),e.jsx(p,{value:le,onChange:(e,s)=>te(s),min:-2,max:2,step:.01,marks:[{value:-2,label:"-2"},{value:0,label:"0"},{value:2,label:"2"}]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"随机种子 (Seed)"}),e.jsx(u,{label:ne??"随机",size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"设置随机种子以获得可重现的结果。留空使用随机种子。"}),e.jsx(v,{type:"number",value:ne??"",onChange:e=>ae(e.target.value?parseInt(e.target.value):null),placeholder:"留空使用随机种子",size:"small",fullWidth:!0})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"最大输出Token (Max Output Tokens)"}),e.jsx(u,{label:`${oe} tokens`,size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"限制模型生成的最大token数量。不同模型支持的最大输出长度不同，如果设置值超过模型限制，API会自动调整或返回错误。 较高值允许更长的回复，但会消耗更多资源和时间。"}),e.jsx(s,{sx:{mb:2,px:1},children:e.jsx(p,{value:Math.min(oe,65536),onChange:(e,s)=>{const l=s;xe(l);const t=A();E({...t,maxOutputTokens:l}),window.dispatchEvent(new CustomEvent("maxOutputTokensChanged",{detail:l}))},min:256,max:65536,step:256,marks:[{value:256,label:"256"},{value:2048,label:"2K"},{value:4096,label:"4K"},{value:8192,label:"8K"},{value:16384,label:"16K"},{value:32768,label:"32K"},{value:65536,label:"64K"}],sx:{"& .MuiSlider-markLabel":{fontSize:"0.7rem"}}})}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[e.jsx(r,{variant:"body2",color:"text.secondary",sx:{minWidth:"fit-content"},children:"精确值:"}),e.jsx(v,{type:"number",value:oe,onChange:e=>{const s=parseInt(e.target.value);if(!isNaN(s)&&s>=256&&s<=2e6){xe(s);const e=A();E({...e,maxOutputTokens:s}),window.dispatchEvent(new CustomEvent("maxOutputTokensChanged",{detail:s}))}},size:"small",sx:{width:120},slotProps:{htmlInput:{min:256,max:2e6}}}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"tokens (最大2M)"})]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(B,{sx:{color:"primary.main",fontSize:20}}),e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"上下文设置 (快速访问)"})]}),e.jsx(u,{label:`${64e3===Be?"不限":Be} 字符 | ${100===Le?"最大":Le} 条`,size:"small",color:"primary",variant:"outlined"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"快速调整上下文长度、消息数量和思维链设置。这些设置会影响所有助手的对话行为。"}),e.jsxs(s,{sx:{mb:2},children:[e.jsxs(r,{variant:"body2",sx:{mb:1,fontWeight:"medium"},children:["上下文长度: ",64e3===Be?"不限":Be," 字符"]}),e.jsx(p,{value:Be,onChange:(e,s)=>{const l=s;Fe(l);const t=A();E({...t,contextLength:l})},min:0,max:64e3,step:1e3,marks:[{value:0,label:"0"},{value:16e3,label:"16K"},{value:32e3,label:"32K"},{value:64e3,label:"64K"}]})]}),e.jsxs(s,{sx:{mb:2},children:[e.jsxs(r,{variant:"body2",sx:{mb:1,fontWeight:"medium"},children:["上下文消息数: ",100===Le?"最大":Le," 条"]}),e.jsx(p,{value:Le,onChange:(e,s)=>{const l=s;qe(l);const t=A();E({...t,contextCount:l})},min:0,max:100,step:1,marks:[{value:0,label:"0"},{value:25,label:"25"},{value:50,label:"50"},{value:100,label:"最大"}]})]}),e.jsxs(s,{children:[e.jsx(r,{variant:"body2",sx:{mb:1,fontWeight:"medium"},children:"默认思维链长度"}),e.jsxs(g,{size:"small",fullWidth:!0,children:[e.jsx(f,{children:"思维链长度"}),e.jsxs(y,{value:Ge,onChange:e=>{const s=e.target.value;$e(s);const l=A();E({...l,defaultThinkingEffort:s})},label:"思维链长度",children:[e.jsx(k,{value:"off",children:"关闭思考"}),e.jsx(k,{value:"low",children:"低强度思考"}),e.jsx(k,{value:"medium",children:"中强度思考（推荐）"}),e.jsx(k,{value:"high",children:"高强度思考"}),e.jsx(k,{value:"auto",children:"自动思考"})]})]})]})]}),e.jsx(d,{}),e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"停止序列 (Stop Sequences)"}),e.jsx(u,{label:ie.length,size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"遇到这些字符序列时停止生成。每行一个序列。"}),e.jsx(v,{multiline:!0,rows:3,value:ie.join("\n"),onChange:e=>re(e.target.value.split("\n").filter((e=>e.trim()))),placeholder:"每行输入一个停止序列",size:"small",fullWidth:!0})]})]})})]})}),e.jsx(F,{value:Xe,index:1,children:e.jsxs(x,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(r,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"},color:"#06b6d4"},children:"OpenAI 专属参数"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"专门针对OpenAI模型的高级参数配置"})]}),e.jsx(d,{}),e.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:e.jsx(b,{sx:{p:0},children:e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{mb:3},children:[e.jsx(r,{variant:"subtitle2",sx:{mb:1,fontWeight:"medium"},children:"响应格式 (Response Format)"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"指定模型响应的格式。JSON模式确保输出为有效JSON。"}),e.jsxs(g,{size:"small",fullWidth:!0,children:[e.jsx(f,{children:"响应格式"}),e.jsxs(y,{value:ue,onChange:e=>me(e.target.value),label:"响应格式",children:[e.jsx(k,{value:"text",children:"文本"}),e.jsx(k,{value:"json_object",children:"JSON对象"})]})]})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsx(r,{variant:"subtitle2",sx:{mb:1,fontWeight:"medium"},children:"工具选择 (Tool Choice)"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"控制模型是否以及如何调用函数工具。"}),e.jsxs(g,{size:"small",fullWidth:!0,children:[e.jsx(f,{children:"工具选择"}),e.jsxs(y,{value:he,onChange:e=>be(e.target.value),label:"工具选择",children:[e.jsx(k,{value:"none",children:"禁用"}),e.jsx(k,{value:"auto",children:"自动"}),e.jsx(k,{value:"required",children:"必须"})]})]})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"并行工具调用 (Parallel Tool Calls)"}),e.jsx(C,{checked:je,onChange:e=>ve(e.target.checked)})]}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"是否允许模型同时调用多个函数工具。"})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsx(r,{variant:"subtitle2",sx:{mb:1,fontWeight:"medium"},children:"Logit偏置 (Logit Bias)"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"修改指定token出现的可能性。格式：token_id:bias_value，每行一个。"}),e.jsx(v,{multiline:!0,rows:3,value:Object.entries(de).map((([e,s])=>`${e}:${s}`)).join("\n"),onChange:e=>{const s={};e.target.value.split("\n").forEach((e=>{const[l,t]=e.split(":");l&&t&&!isNaN(Number(t))&&(s[l.trim()]=Number(t))})),ce(s)},placeholder:"例如：50256:-100",size:"small",fullWidth:!0})]})]})})})]})}),e.jsx(F,{value:Xe,index:2,children:e.jsxs(x,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(r,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"},color:"#8b5cf6"},children:"Gemini 专属参数"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"专门针对Google Gemini模型的高级参数配置"})]}),e.jsx(d,{}),e.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:e.jsx(b,{sx:{p:0},children:e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{mb:3},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"候选数量 (Candidate Count)"}),e.jsx(u,{label:fe,size:"small"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"生成的候选响应数量。注意：仅部分模型支持多候选。"}),e.jsx(p,{value:fe,onChange:(e,s)=>ye(s),min:1,max:4,step:1,marks:[{value:1,label:"1"},{value:2,label:"2"},{value:3,label:"3"},{value:4,label:"4"}]})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsx(r,{variant:"subtitle2",sx:{mb:1,fontWeight:"medium"},children:"响应模态 (Response Modalities)"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"指定模型可以返回的内容类型。"}),e.jsxs(g,{size:"small",fullWidth:!0,children:[e.jsx(f,{children:"响应模态"}),e.jsxs(y,{multiple:!0,value:pe,onChange:e=>ge(e.target.value),label:"响应模态",children:[e.jsx(k,{value:"TEXT",children:"文本"}),e.jsx(k,{value:"IMAGE",children:"图像"}),e.jsx(k,{value:"AUDIO",children:"音频"})]})]})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"语音输出 (Speech Output)"}),e.jsx(C,{checked:ke,onChange:e=>Ce(e.target.checked)})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"启用语音输出功能。"}),ke&&e.jsxs(g,{size:"small",fullWidth:!0,sx:{mt:2},children:[e.jsx(f,{children:"语音语言"}),e.jsxs(y,{value:Se,onChange:e=>we(e.target.value),label:"语音语言",children:[e.jsx(k,{value:"zh-CN",children:"中文（简体）"}),e.jsx(k,{value:"en-US",children:"英语（美国）"}),e.jsx(k,{value:"ja-JP",children:"日语"}),e.jsx(k,{value:"ko-KR",children:"韩语"})]})]})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"思维链 (Thinking)"}),e.jsx(C,{checked:ze,onChange:e=>We(e.target.checked)})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"启用思维链功能，显示模型的推理过程。Gemini使用token数量控制(0-24576)，OpenAI使用强度等级控制。"}),ze&&e.jsxs(s,{sx:{mt:2},children:[e.jsxs(r,{variant:"body2",sx:{mb:1},children:["思维预算: ",Ie," tokens"]}),e.jsx(p,{value:Ie,onChange:(e,s)=>Te(s),min:0,max:24576,step:256,marks:[{value:0,label:"0"},{value:2048,label:"2K"},{value:8192,label:"8K"},{value:16384,label:"16K"},{value:24576,label:"24K"}]})]})]}),e.jsxs(s,{sx:{mb:3},children:[e.jsx(r,{variant:"subtitle2",sx:{mb:1,fontWeight:"medium"},children:"媒体分辨率 (Media Resolution)"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"处理图像和视频时的分辨率设置。"}),e.jsxs(g,{size:"small",fullWidth:!0,children:[e.jsx(f,{children:"媒体分辨率"}),e.jsxs(y,{value:Oe,onChange:e=>Ne(e.target.value),label:"媒体分辨率",children:[e.jsx(k,{value:"low",children:"低 (64 tokens)"}),e.jsx(k,{value:"medium",children:"中 (256 tokens)"}),e.jsx(k,{value:"high",children:"高 (256 tokens + 缩放)"})]})]})]})]})})})]})}),e.jsx(F,{value:Xe,index:3,children:e.jsxs(x,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(s,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(r,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"},color:"#10b981"},children:"自定义参数"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"为模型添加自定义参数，支持多种数据类型"})]}),e.jsx(d,{}),e.jsx(s,{sx:{p:{xs:1.5,sm:2}},children:e.jsxs(b,{sx:{p:0},children:[e.jsxs(j,{sx:{flexDirection:"column",alignItems:"stretch",py:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:"medium"},children:"自定义参数"}),e.jsx(o,{startIcon:e.jsx(R,{}),onClick:()=>{Pe([...De,{name:"",value:"",type:"string"}])},variant:"outlined",size:"small",children:"添加参数"})]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"为模型添加自定义参数，支持字符串、数字、布尔值和JSON格式。"}),0===De.length?e.jsx(s,{sx:{textAlign:"center",py:3,color:"text.secondary"},children:e.jsx(r,{variant:"body2",children:'暂无自定义参数，点击"添加参数"开始配置'})}):e.jsx(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:De.map(((l,t)=>e.jsxs(S,{variant:"outlined",sx:{p:0},children:[e.jsx(w,{sx:{pb:1},children:e.jsxs(s,{sx:{display:"flex",gap:2,alignItems:"center",flexWrap:"wrap"},children:[e.jsx(s,{sx:{flex:"1 1 200px",minWidth:"150px"},children:e.jsx(v,{label:"参数名",value:l.name,onChange:e=>Ue(t,"name",e.target.value),size:"small",fullWidth:!0,placeholder:"参数名称"})}),e.jsx(s,{sx:{flex:"0 0 120px"},children:e.jsxs(g,{size:"small",fullWidth:!0,children:[e.jsx(f,{children:"类型"}),e.jsxs(y,{value:l.type,onChange:e=>Ue(t,"type",e.target.value),label:"类型",children:[e.jsx(k,{value:"string",children:"字符串"}),e.jsx(k,{value:"number",children:"数字"}),e.jsx(k,{value:"boolean",children:"布尔值"}),e.jsx(k,{value:"json",children:"JSON"})]})]})}),e.jsx(s,{sx:{flex:"1 1 200px",minWidth:"150px"},children:Ve(l,t)})]})}),e.jsx(z,{sx:{pt:0,justifyContent:"flex-end"},children:e.jsx(i,{onClick:()=>(e=>{const s=De.filter(((s,l)=>l!==e));Pe(s)})(t),color:"error",size:"small",children:e.jsx(D,{})})})]},t)))})]}),e.jsx(j,{sx:{justifyContent:"center",py:3},children:e.jsx(o,{startIcon:e.jsx(P,{}),onClick:()=>{V(L),Z(1),Q(40),se(0),te(0),ae(null),re([]),xe(4096),ce({}),me("text"),be("auto"),ve(!0),ge(["TEXT"]),ye(1),Ce(!1),we("zh-CN"),We(!1),Te(0),Ne("medium"),Ee(""),Pe([])},variant:"outlined",color:"secondary",children:"重置为默认值"})})]})})]})})]})]}):e.jsx(s,{sx:{p:2},children:e.jsx(l,{severity:"error",children:"未找到助手信息，请返回重新选择"})})};export{q as default};
