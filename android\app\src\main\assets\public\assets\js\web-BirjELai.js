import{W as a}from"./index-BtK6VV6Z.js";import"./mui-vendor-hRDvsX89.js";import"./react-vendor-C9ilihHH.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";class e extends a{async canShare(){return"undefined"!=typeof navigator&&navigator.share?{value:!0}:{value:!1}}async share(a){if("undefined"==typeof navigator||!navigator.share)throw this.unavailable("Share API not available in this browser");return await navigator.share({title:a.title,text:a.text,url:a.url}),{}}}export{e as ShareWeb};
