import React, { useEffect, useState, lazy, Suspense } from 'react';
import { Navigate, Routes, Route } from 'react-router-dom';
import { getStorageItem } from '../shared/utils/storage';
// 使用懒加载导入组件
const ChatPage = lazy(() => import('../pages/ChatPage'));
const WelcomePage = lazy(() => import('../pages/WelcomePage'));
const SettingsPage = lazy(() => import('../pages/Settings'));
const AppearanceSettings = lazy(() => import('../pages/Settings/AppearanceSettings.tsx'));
const BehaviorSettings = lazy(() => import('../pages/Settings/BehaviorSettings'));
const ChatInterfaceSettings = lazy(() => import('../pages/Settings/ChatInterfaceSettings'));
const TopToolbarSettings = lazy(() => import('../pages/Settings/TopToolbarSettings'));
const TopToolbarDIYSettings = lazy(() => import('../pages/Settings/TopToolbarDIYSettings'));
const TopToolbarTestPage = lazy(() => import('../pages/Settings/TopToolbarTestPage'));
const DefaultModelSettings = lazy(() => import('../pages/Settings/DefaultModelSettings'));
// 导入知识库页面
const KnowledgeBasePage = lazy(() => import('../pages/KnowledgeBase'));
const KnowledgeSettings = lazy(() => import('../pages/Settings/KnowledgeSettings'));

const DefaultModelSettingsPage = lazy(() => import('../pages/Settings/DefaultModelSettings/index'));
const ModelProviderSettings = lazy(() => import('../pages/Settings/ModelProviderSettings'));
const AddProviderPage = lazy(() => import('../pages/Settings/AddProviderPage'));
const AboutPage = lazy(() => import('../pages/Settings/AboutPage'));
const VoiceSettings = lazy(() => import('../pages/Settings/VoiceSettings'));
const WebSearchSettings = lazy(() => import('../pages/Settings/WebSearchSettings'));
const AgentPromptsSettings = lazy(() => import('../pages/Settings/AgentPrompts'));
const MemorySettingsPage = lazy(() => import('../pages/Settings/MemorySettings.tsx'));
const DevToolsPage = lazy(() => import('../pages/DevToolsPage'));
const VueDemoPage = lazy(() => import('../pages/VueDemo'));
import DataSettingsPage from '../pages/Settings/DataSettings';
// 导入高级备份页面
const AdvancedBackupPage = lazy(() => import('../pages/Settings/DataSettings/AdvancedBackupPage'));
// 导入 MCP 相关页面
const MCPServerSettings = lazy(() => import('../pages/Settings/MCPServerSettings'));
const MCPServerDetail = lazy(() => import('../pages/Settings/MCPServerDetail'));
// 导入模型组合页面
const ModelComboSettings = lazy(() => import('../pages/Settings/ModelComboSettings'));
// 导入AI辩论设置页面
const AIDebateSettings = lazy(() => import('../pages/Settings/AIDebateSettings'));
import MessageBubbleSettings from "../pages/Settings/MessageBubbleSettings";
// 导入快捷短语设置页面
const QuickPhraseSettings = lazy(() => import('../components/QuickPhraseSettings'));
// 导入助手设置页面
const AssistantSettings = lazy(() => import('../components/TopicManagement/SettingsTab/AssistantSettings'));
const AssistantModelSettings = lazy(() => import('../components/TopicManagement/SettingsTab/AssistantModelSettings'));
// 导入记忆管理页面
// 移除旧的记忆管理页面

// 加载中组件
const LoadingFallback = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    background: '#f5f5f5'
  }}>
    <div>加载中...</div>
  </div>
);

// 路由提供者组件
const AppRouter: React.FC = () => {
  const [isFirstTimeUser, setIsFirstTimeUser] = useState<boolean | null>(null);

  useEffect(() => {
    async function checkFirstTimeUser() {
      try {
        const firstTimeUserValue = await getStorageItem<string>('first-time-user');
        setIsFirstTimeUser(firstTimeUserValue === null);
      } catch (error) {
        console.error('检查首次用户状态出错:', error);
        setIsFirstTimeUser(false); // 出错时默认为非首次用户
      }
    }

    checkFirstTimeUser();
  }, []);

  if (isFirstTimeUser === null) {
    // 显示加载状态
    return <LoadingFallback />;
  }

  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        <Route path="/" element={isFirstTimeUser ? <Navigate to="/welcome" replace /> : <Navigate to="/chat" replace />} />
        <Route path="/welcome" element={<WelcomePage />} />
        <Route path="/chat" element={<ChatPage />} />
        <Route path="/settings" element={<SettingsPage />} />
        <Route path="/settings/appearance" element={<AppearanceSettings />} />
        <Route path="/settings/appearance/chat-interface" element={<ChatInterfaceSettings />} />
        <Route path="/settings/appearance/message-bubble" element={<MessageBubbleSettings />} />
        <Route path="/settings/appearance/top-toolbar" element={<TopToolbarSettings />} />
        <Route path="/settings/appearance/top-toolbar-diy" element={<TopToolbarDIYSettings />} />
        <Route path="/settings/appearance/top-toolbar-test" element={<TopToolbarTestPage />} />
        <Route path="/settings/behavior" element={<BehaviorSettings />} />
        <Route path="/settings/default-model" element={<DefaultModelSettings />} />
        <Route path="/settings/default-model-settings" element={<DefaultModelSettingsPage />} />
        <Route path="/settings/agent-prompts" element={<AgentPromptsSettings />} />
        <Route path="/settings/ai-debate" element={<AIDebateSettings />} />
        <Route path="/settings/quick-phrases" element={<QuickPhraseSettings />} />
        <Route path="/settings/model-provider/:providerId" element={<ModelProviderSettings />} />
        <Route path="/settings/add-provider" element={<AddProviderPage />} />
        <Route path="/settings/about" element={<AboutPage />} />
        <Route path="/settings/voice" element={<VoiceSettings />} />
        <Route path="/settings/data" element={<DataSettingsPage />} />
        <Route path="/settings/data/advanced-backup" element={<AdvancedBackupPage />} />
        <Route path="/settings/web-search" element={<WebSearchSettings />} />
        <Route path="/settings/mcp-server" element={<MCPServerSettings />} />
        <Route path="/settings/mcp-server/:serverId" element={<MCPServerDetail />} />
        <Route path="/settings/model-combo" element={<ModelComboSettings />} />
        <Route path="/settings/knowledge" element={<KnowledgeSettings />} />
        <Route path="/settings/memory" element={<MemorySettingsPage />} />
        <Route path="/settings/assistant-settings" element={<AssistantSettings />} />
        <Route path="/settings/assistant-model-settings" element={<AssistantModelSettings />} />
        <Route path="/devtools" element={<DevToolsPage />} />
        <Route path="/vue-demo" element={<VueDemoPage />} />
        <Route path="/knowledge/*" element={<KnowledgeBasePage />} />
      </Routes>
    </Suspense>
  );
};

export default AppRouter;
