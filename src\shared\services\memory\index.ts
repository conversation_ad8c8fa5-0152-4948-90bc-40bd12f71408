/**
 * 记忆服务模块导出
 */

export { InternalMemoryService } from './InternalMemoryService';
export { MemoryExtractor } from './MemoryExtractor';
export { MemoryStorageService } from './MemoryStorageService';
export { MemoryEmbeddingService } from './EmbeddingService';
export { SemanticSearchService } from './SemanticSearchService';
export { ContextBuilder } from './ContextBuilder';
export { MemoryEnhancedMessageService } from './MemoryEnhancedMessageService';

// 导出类型
export type {
  MemoryRecord,
  MemoryOperation,
  MemorySearchResult,
  MemoryStats,
  MemoryCategory,
  MemoryOperationType,
  InternalMemoryConfig,
  MemoryContext
} from '../../types/internalMemory';

// 导出配置
export { 
  getMemoryConfig, 
  updateMemoryConfig,
  MEMORY_CATEGORY_LABELS,
  MEMORY_IMPORTANCE_LEVELS
} from '../../config/internalMemoryConfig';

// 导出数据库工具
export {
  dbRecordToMemoryRecord,
  memoryRecordToDBRecord,
  generateContentHash,
  generateUUID
} from '../../database/memorySchema';
