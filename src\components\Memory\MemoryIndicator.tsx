/**
 * 记忆状态指示器组件
 * 显示当前对话中使用的记忆信息
 */

import Delete from '@mui/icons-material/Delete';
import Close from '@mui/icons-material/Close';
import Psychology from '@mui/icons-material/Psychology';
import ViewAgenda from '@mui/icons-material/ViewAgenda';
import React, { useState } from 'react';
import {
  Box,
  Chip,
  Tooltip,
  IconButton,
  Popover,
  Typography,
  List,
  ListItem,
  ListItemText,
  Divider,
  Badge
} from '@mui/material';
import type { MemoryRecord } from '../../shared/types/internalMemory';
import { MEMORY_CATEGORY_LABELS } from '../../shared/config/internalMemoryConfig';
import { useDispatch } from 'react-redux';
import { deleteMemory } from '../../shared/store/slices/internalMemorySlice';

interface MemoryIndicatorProps {
  memories: MemoryRecord[];
  isMemoryEnabled: boolean;
  compact?: boolean;
}

const MemoryIndicator: React.FC<MemoryIndicatorProps> = ({
  memories,
  isMemoryEnabled,
  compact = false
}) => {
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteMemory = async (memoryId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 防止触发父元素的点击事件

    try {
      await dispatch(deleteMemory(memoryId) as any);
      console.log('✅ 记忆删除成功:', memoryId);
    } catch (error) {
      console.error('❌ 删除记忆失败:', error);
      alert('删除记忆失败，请重试');
    }
  };

  const open = Boolean(anchorEl);

  if (!isMemoryEnabled) {
    return null;
  }

  if (memories.length === 0) {
    return compact ? null : (
      <Tooltip title="当前对话未使用记忆信息">
        <Chip
          icon={<Psychology size={16} />}
          label="无记忆"
          size="small"
          variant="outlined"
          color="default"
        />
      </Tooltip>
    );
  }

  const memoryCount = memories.length;
  const categoryCounts = memories.reduce((acc, memory) => {
    acc[memory.category] = (acc[memory.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <>
      <Tooltip title={`使用了 ${memoryCount} 条记忆信息`}>
        <Badge badgeContent={memoryCount} color="primary" max={99}>
          <Chip
            icon={<Psychology size={16} />}
            label={compact ? '' : `${memoryCount} 条记忆`}
            size="small"
            variant="filled"
            color="primary"
            onClick={handleClick}
            clickable
          />
        </Badge>
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: { maxWidth: 400, maxHeight: 500 }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              使用的记忆信息
            </Typography>
            <IconButton size="small" onClick={handleClose}>
              <Close size={20} />
            </IconButton>
          </Box>

          {/* 分类统计 */}
          <Box mb={2}>
            <Typography variant="subtitle2" gutterBottom>
              分类统计
            </Typography>
            <Box display="flex" gap={1} flexWrap="wrap">
              {Object.entries(categoryCounts).map(([category, count]) => (
                <Chip
                  key={category}
                  label={`${MEMORY_CATEGORY_LABELS[category as keyof typeof MEMORY_CATEGORY_LABELS] || category}: ${count}`}
                  size="small"
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>

          <Divider />

          {/* 记忆列表 */}
          <ViewAgenda dense sx={{ maxHeight: 300, overflow: 'auto' }}>
            {memories.map((memory, index) => (
              <ListItem
                key={memory.id}
                divider={index < memories.length - 1}
                secondaryAction={
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={(e) => handleDeleteMemory(memory.id, e)}
                    size="small"
                    color="error"
                  >
                    <Delete size={16} />
                  </IconButton>
                }
              >
                <ListItemText
                  primary={memory.content}
                  secondary={
                    <span style={{ display: 'flex', gap: '8px', alignItems: 'center', marginTop: '4px' }}>
                      <Chip
                        label={MEMORY_CATEGORY_LABELS[memory.category as keyof typeof MEMORY_CATEGORY_LABELS] || memory.category}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                      <Chip
                        label={`重要性: ${memory.importance}/10`}
                        size="small"
                        variant="outlined"
                        color="secondary"
                      />
                    </span>
                  }
                  primaryTypographyProps={{
                    variant: 'body2',
                    sx: {
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      pr: 1 // 为删除按钮留出空间
                    }
                  }}
                />
              </ListItem>
            ))}
          </ViewAgenda>
        </Box>
      </Popover>
    </>
  );
};

export default MemoryIndicator;
