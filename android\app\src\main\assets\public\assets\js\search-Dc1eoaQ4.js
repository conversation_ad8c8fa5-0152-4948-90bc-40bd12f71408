const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/index-BtK6VV6Z.js","assets/js/mui-vendor-hRDvsX89.js","assets/js/react-vendor-C9ilihHH.js","assets/js/utils-vendor-BDm_82Hk.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/css/index-C_2Yhxqf.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,r,n)=>((t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n)(t,"symbol"!=typeof r?r+"":r,n);import{_ as r}from"./syntax-vendor-DfDNeb5M.js";import{aN as n,aF as i,b7 as o,aD as s,aE as a,aL as c,c as m}from"./index-BtK6VV6Z.js";class l{constructor(){t(this,"config",n()),t(this,"FACT_EXTRACTION_PROMPT",'你是一个个人信息整理专家，专门从对话中准确提取和整理事实、用户记忆和偏好。\n\n需要记住的信息类型：\n1. 个人偏好：喜好、厌恶、特定偏好\n2. 重要个人信息：姓名、关系、重要日期\n3. 计划和意图：即将到来的事件、目标、计划\n4. 活动偏好：餐饮、旅行、爱好偏好\n5. 健康信息：饮食限制、健身习惯\n6. 职业信息：工作、职业目标\n7. 其他信息：喜欢的书籍、电影、品牌等\n\n请从对话中提取值得记住的事实，每个事实应该：\n- 简洁明确（1-2句话）\n- 包含具体信息\n- 对未来对话有价值\n- 避免重复或过于琐碎的信息\n- **重要：仔细检查已有记忆，避免提取重复或相似的内容**\n- **如果新信息与已有记忆相似但有更新，则提取更新后的版本**\n- **如果没有新的有价值信息，返回空数组**\n\n返回JSON格式：\n{\n  "facts": ["事实1", "事实2"],\n  "categories": ["preference", "background"],\n  "importance": [7, 5]\n}\n\n其中：\n- facts: 提取的事实列表\n- categories: 对应的分类（preference/background/skill/habit/plan）\n- importance: 重要性评分（1-10，10最重要）')}async extractMemories(e,t,n){try{if(!this.config.enabled||e.length<this.config.extraction.minConversationLength)return{facts:[],categories:[],importance:[]};const s=this.formatConversation(e);let a="";if(n)try{const{InternalMemoryService:e}=await r((async()=>{const{InternalMemoryService:e}=await import("./index-BtK6VV6Z.js").then((e=>e.dh));return{InternalMemoryService:e}}),__vite__mapDeps([0,1,2,3,4,5])),t=e.getInstance(),i=await t.getUserMemories(n);i.length>0&&(a="\n\n已有记忆（避免重复提取）：\n",i.forEach(((e,t)=>{a+=`${t+1}. ${e.content}\n`})),a+="\n请避免提取与上述已有记忆重复或相似的内容。")}catch(o){console.warn("获取已有记忆失败:",o)}const c=[{role:"system",content:this.FACT_EXTRACTION_PROMPT+a},{role:"user",content:`对话内容：\n${s}`}],m=await this.callAIForExtraction(c,t),l=this.parseExtractionResponse(m);return i.log("INFO","Memory extraction completed",{factsCount:l.facts.length,existingMemoriesCount:a?a.split("\n").length-3:0,model:this.config.extraction.model}),l}catch(o){return i.log("ERROR","Memory extraction failed",{error:o}),{facts:[],categories:[],importance:[]}}}formatConversation(e){return e.slice(2*-this.config.extraction.maxMemoriesPerExtraction).map((e=>`${"user"===e.role?"用户":"AI助手"}: ${this.getMessageContent(e)}`)).join("\n\n")}getMessageContent(e){return"string"==typeof e.content?e.content:Array.isArray(e.content)?e.content.filter((e=>"text"===e.type)).map((e=>e.text)).join(" "):""}async callAIForExtraction(e,t){try{let r;r=t?{...t,temperature:.1,maxTokens:1e3}:{id:this.config.extraction.model,provider:"openai",name:this.config.extraction.model,apiKey:"",baseUrl:"",temperature:.1,maxTokens:1e3};const n=await o(e,r);return"string"==typeof n?n:n&&"object"==typeof n&&"content"in n?n.content:""}catch(r){throw i.log("ERROR","AI memory extraction failed",{error:r}),r}}parseExtractionResponse(e){try{const t=e.match(/\{[\s\S]*\}/);if(!t)throw new Error("No JSON found in response");const r=JSON.parse(t[0]);if(!r.facts||!Array.isArray(r.facts))throw new Error("Invalid facts format");const n=r.facts.slice(0,this.config.extraction.maxMemoriesPerExtraction),i=this.validateCategories(r.categories||[],n.length);return{facts:n,categories:i,importance:this.validateImportance(r.importance||[],n.length)}}catch(t){return i.log("ERROR","Failed to parse extraction response",{error:t,response:e}),this.fallbackExtraction(e)}}validateCategories(e,t){const r=[];for(let n=0;n<t;n++){const t=e[n];this.isValidCategory(t)?r.push(t):r.push("background")}return r}validateImportance(e,t){const r=[];for(let n=0;n<t;n++){const t=e[n];"number"==typeof t&&t>=1&&t<=10?r.push(Math.round(t)):r.push(5)}return r}isValidCategory(e){return["preference","background","skill","habit","plan"].includes(e)}fallbackExtraction(e){const t=e.split("\n").map((e=>e.trim())).filter((e=>e.length>10&&e.length<200)).slice(0,3),r=t.length>0?t:[],n=r.map((()=>"background")),i=r.map((()=>5));return{facts:r,categories:n,importance:i}}}const d=class e{constructor(){t(this,"memoryService"),t(this,"config",n()),this.memoryService=s.getInstance()}static getInstance(){return e.instance||(e.instance=new e),e.instance}async saveMemories(e,t,r,n,o={}){try{if(!this.memoryService.isEnabled())return i.log("WARN","Memory service is disabled"),[];const s=[];for(let a=0;a<t.length;a++){const c=t[a],m=r[a]||"background",l=n[a]||5,d=this.generateContentHash(c);if(await this.memoryService.isMemoryExists(e,d)){i.log("INFO","Memory already exists, skipping",{userId:e,contentHash:d});continue}const h=this.memoryService.createMemoryRecord(e,c,m,l,{...o,extractedAt:(new Date).toISOString()});await this.memoryService.saveMemory(h),s.push(h),i.log("INFO","Memory saved",{memoryId:h.id,category:h.category,importance:h.importance})}return await this.enforceStorageLimits(e),s}catch(s){return i.log("ERROR","Failed to save memories",{error:s,userId:e}),[]}}async getUserMemoriesWithPagination(e,t={}){try{if(!this.memoryService.isEnabled())return{memories:[],total:0,hasMore:!1};const{category:r,limit:n=20,offset:i=0,sortBy:o="createdAt",sortOrder:s="desc"}=t;let a;a=r?await this.memoryService.getMemoriesByCategory(e,r):await this.memoryService.getUserMemories(e),a.sort(((e,t)=>{let r,n;switch(o){case"createdAt":default:r=e.createdAt.getTime(),n=t.createdAt.getTime();break;case"updatedAt":r=e.updatedAt.getTime(),n=t.updatedAt.getTime();break;case"importance":r=e.importance,n=t.importance}return"asc"===s?r-n:n-r}));const c=a.length,m=a.slice(i,i+n);return{memories:m,total:c,hasMore:i+n<c}}catch(r){return i.log("ERROR","Failed to get user memories with pagination",{error:r,userId:e}),{memories:[],total:0,hasMore:!1}}}async searchMemories(e,t,r={}){try{if(!this.memoryService.isEnabled()||!t.trim())return[];const{category:n,limit:i=10}=r;let o;o=n?await this.memoryService.getMemoriesByCategory(e,n):await this.memoryService.getUserMemories(e);const s=t.toLowerCase();return o.filter((e=>e.content.toLowerCase().includes(s))).sort(((e,r)=>{const n=this.calculateTextRelevance(e.content,t),i=this.calculateTextRelevance(r.content,t);return n!==i?i-n:r.importance-e.importance})).slice(0,i)}catch(n){return i.log("ERROR","Failed to search memories",{error:n,userId:e,query:t}),[]}}async enforceStorageLimits(e){try{const t=this.config.storage.maxMemoriesPerUser;if(t<=0)return;const r=await this.memoryService.getUserMemories(e);if(r.length>t){const n=r.sort(((e,t)=>e.importance!==t.importance?e.importance-t.importance:e.createdAt.getTime()-t.createdAt.getTime())).slice(0,r.length-t);for(const e of n)await this.memoryService.deleteMemory(e.id);i.log("INFO","Enforced storage limits",{userId:e,deletedCount:n.length,remainingCount:t})}await this.memoryService.cleanupExpiredMemories(e)}catch(t){i.log("ERROR","Failed to enforce storage limits",{error:t,userId:e})}}calculateTextRelevance(e,t){const r=e.toLowerCase(),n=t.toLowerCase();if(r.includes(n))return 10;const i=n.split(/\s+/),o=r.split(/\s+/);let s=0;for(const a of i)o.some((e=>e.includes(a)))&&s++;return s/i.length*5}generateContentHash(e){let t=0;if(0===e.length)return t.toString();for(let r=0;r<e.length;r++){t=(t<<5)-t+e.charCodeAt(r),t&=t}return Math.abs(t).toString(36)}async getExtendedMemoryStats(e){try{const t=await this.memoryService.getMemoryStats(e),r=await this.memoryService.getUserMemories(e),n={};for(let e=1;e<=10;e++)n[e.toString()]=0;r.forEach((e=>{const t=e.importance.toString();n[t]=(n[t]||0)+1}));const i=new Date,o=new Date(i.getTime()-6048e5),s=new Date(i.getTime()-2592e6),a={thisWeek:r.filter((e=>e.createdAt>o)).length,thisMonth:r.filter((e=>e.createdAt>s&&e.createdAt<=o)).length,older:r.filter((e=>e.createdAt<=s)).length};return{basic:t,categoryDistribution:t.categoryCounts,importanceDistribution:n,timeDistribution:a}}catch(t){return i.log("ERROR","Failed to get extended memory stats",{error:t,userId:e}),{basic:{totalMemories:0,categoryCounts:{},recentGrowth:0,averageImportance:0},categoryDistribution:{},importanceDistribution:{},timeDistribution:{thisWeek:0,thisMonth:0,older:0}}}}};t(d,"instance");let h=d;const g=class e{constructor(){t(this,"memoryService"),t(this,"embeddingService"),this.memoryService=s.getInstance(),this.embeddingService=a.getInstance()}getConfig(){return n()}static getInstance(){return e.instance||(e.instance=new e),e.instance}async searchSimilarMemories(e,t,r={}){try{const o=this.getConfig();if(!o.enabled||!e.trim())return[];const{limit:s=o.search.maxResults,threshold:a=o.search.similarityThreshold,category:c}=r;let m,l;try{if(m=await this.embeddingService.generateMemoryEmbedding(e),!this.embeddingService.validateEmbedding(m))return i.log("WARN","Invalid query embedding generated, falling back to text search"),[]}catch(n){return i.log("WARN","Query embedding generation failed, falling back to text search",{error:n,query:e.substring(0,50)}),[]}l=c?await this.memoryService.getMemoriesByCategory(t,c):await this.memoryService.getUserMemories(t);const d=l.filter((e=>this.embeddingService.validateEmbedding(e.embedding)));if(0===d.length)return i.log("INFO","No memories with valid embeddings found",{userId:t}),[];const h=d.map((e=>({memory:e,similarity:this.embeddingService.calculateCosineSimilarity(m,e.embedding)}))).filter((e=>e.similarity>=a)).sort(((e,t)=>Math.abs(e.similarity-t.similarity)>.001?t.similarity-e.similarity:t.memory.importance-e.memory.importance)).slice(0,s);return i.log("INFO","Semantic search completed",{query:e.substring(0,50),userId:t,totalMemories:l.length,memoriesWithEmbeddings:d.length,resultsFound:h.length,threshold:a}),h}catch(o){return i.log("ERROR","Semantic search failed",{error:o,query:e.substring(0,50),userId:t}),[]}}async findRelevantMemories(e,t,r=5){try{return(await this.searchSimilarMemories(e,t,{limit:r})).map((e=>e.memory))}catch(n){return i.log("ERROR","Failed to find relevant memories",{error:n,query:e,userId:t}),[]}}async hybridSearch(e,t,r={}){try{const n=this.getConfig();if(!n.enabled||!e.trim())return[];const{limit:i=n.search.maxResults,semanticWeight:o=.7,textWeight:s=.3}=r,a=await this.searchSimilarMemories(e,t,{limit:2*i}),c=await this.textSearch(e,t,2*i),m=this.combineSearchResults(a,c,o,s);return this.deduplicateResults(m).sort(((e,t)=>t.similarity-e.similarity)).slice(0,i)}catch(n){return i.log("ERROR","Hybrid search failed",{error:n,query:e,userId:t}),[]}}async textSearch(e,t,r){try{const n=await this.memoryService.getUserMemories(t);return n.map((t=>({memory:t,similarity:this.calculateTextSimilarity(t.content,e)}))).filter((e=>e.similarity>0)).sort(((e,t)=>t.similarity-e.similarity)).slice(0,r)}catch(n){return i.log("ERROR","Text search failed",{error:n,query:e,userId:t}),[]}}calculateTextSimilarity(e,t){const r=e.toLowerCase(),n=t.toLowerCase();if(r.includes(n))return.9;const i=n.split(/\s+/).filter((e=>e.length>2)),o=r.split(/\s+/);if(0===i.length)return 0;let s=0;for(const a of i)o.some((e=>e.includes(a)||a.includes(e)))&&s++;return s/i.length*.7}combineSearchResults(e,t,r,n){const i=new Map;for(const o of e)i.set(o.memory.id,{memory:o.memory,similarity:o.similarity*r});for(const o of t){const e=i.get(o.memory.id);e?e.similarity+=o.similarity*n:i.set(o.memory.id,{memory:o.memory,similarity:o.similarity*n})}return Array.from(i.values())}deduplicateResults(e){const t=new Set;return e.filter((e=>!t.has(e.memory.id)&&(t.add(e.memory.id),!0)))}async getMemoryClusters(e,t={}){try{const{minClusterSize:r=2,maxClusters:n=10,similarityThreshold:o=.8}=t,s=(await this.memoryService.getUserMemories(e)).filter((e=>this.embeddingService.validateEmbedding(e.embedding)));if(s.length<r)return[];const a=[],c=new Set;for(const e of s){if(c.has(e.id))continue;const t=[e];c.add(e.id);for(const r of s){if(c.has(r.id))continue;this.embeddingService.calculateCosineSimilarity(e.embedding,r.embedding)>=o&&(t.push(r),c.add(r.id))}if(t.length>=r&&a.push(t),a.length>=n)break}return i.log("INFO","Memory clustering completed",{userId:e,totalMemories:s.length,clustersFound:a.length}),a}catch(r){return i.log("ERROR","Memory clustering failed",{error:r,userId:e}),[]}}};t(g,"instance");let u=g;const y=class e{constructor(){t(this,"semanticSearch"),this.semanticSearch=u.getInstance()}getConfig(){return n()}static getInstance(){return e.instance||(e.instance=new e),e.instance}async buildEnhancedContext(e,t,r={}){try{const o=this.getConfig();if(!o.enabled||!e.trim())return{relevantMemories:[],enhancedPrompt:e};const{assistantId:s,includeCategories:a,maxMemories:c=o.search.maxResults,contextStyle:m="detailed"}=r;let l=[];try{l=await this.semanticSearch.findRelevantMemories(e,t,c)}catch(n){i.log("WARN","Memory search failed, continuing without memory context",{error:n,userId:t,userMessage:e.substring(0,50)}),l=[]}const d=a?l.filter((e=>a.includes(e.category))):l;if(0===d.length)return i.log("INFO","No relevant memories found for context building",{userId:t,userMessage:e.substring(0,50)}),{relevantMemories:[],enhancedPrompt:e};const h=this.buildContextPrompt(e,d,m,s);return i.log("INFO","Enhanced context built successfully",{userId:t,memoriesUsed:d.length,contextStyle:m,originalLength:e.length,enhancedLength:h.length}),{relevantMemories:d,enhancedPrompt:h}}catch(o){return i.log("ERROR","Failed to build enhanced context",{error:o,userId:t,userMessage:e.substring(0,50)}),{relevantMemories:[],enhancedPrompt:e}}}buildContextPrompt(e,t,r,n){if(0===t.length)return e;const i=this.formatMemoryContext(t,r);switch(r){case"minimal":return`${i}\n\n${e}`;case"concise":return`用户背景：${i}\n\n用户请求：${e}`;default:return this.buildDetailedContext(e,i,n)}}buildDetailedContext(e,t,r){let n=`用户背景信息：\n${t}\n\n`;return n+=r?"请根据用户的背景信息和偏好，提供个性化的回答。\n\n":"请根据用户的背景信息和偏好，提供相关且有用的回答。\n\n",n+=`当前用户请求：${e}`,n}formatMemoryContext(e,t){if(0===e.length)return"";const r=this.categorizeMemories(e);switch(t){case"minimal":return this.formatMinimalContext(r);case"concise":return this.formatConciseContext(r);default:return this.formatDetailedContext(r)}}categorizeMemories(e){const t={};for(const r of e)t[r.category]||(t[r.category]=[]),t[r.category].push(r);for(const r in t)t[r].sort(((e,t)=>t.importance-e.importance));return t}formatMinimalContext(e){const t=[];for(const[,r]of Object.entries(e))for(const e of r.slice(0,2))t.push(e.content);return t.join("; ")}formatConciseContext(e){const t=[];for(const[r,n]of Object.entries(e)){const e=c[r]||r,i=n.slice(0,3).map((e=>`• ${e.content}`));i.length>0&&t.push(`${e}：\n${i.join("\n")}`)}return t.join("\n\n")}formatDetailedContext(e){const t=[],r={preference:1,background:2,skill:3,habit:4,plan:5},n=Object.entries(e).sort((([e],[t])=>(r[e]||99)-(r[t]||99)));for(const[i,o]of n){const e=c[i]||i,r=o.map((e=>{const t=e.importance>=8?"（重要）":"";return`• ${e.content}${t}`}));r.length>0&&t.push(`【${e}】\n${r.join("\n")}`)}return t.join("\n\n")}async buildMemorySummary(e,t={}){try{const{maxLength:r=500,includeStats:n=!0}=t,i=await this.semanticSearch.findRelevantMemories("",e,20);if(0===i.length)return"暂无用户记忆信息。";const o=this.categorizeMemories(i);let s="";for(const[e,t]of Object.entries(o)){const r=c[e]||e,n=t.slice(0,2);n.length>0&&(s+=`${r}：${n.map((e=>e.content)).join("；")}\n`)}return n&&(s+=`\n总计 ${i.length} 条记忆信息。`),s.length>r&&(s=s.substring(0,r-3)+"..."),s}catch(r){return i.log("ERROR","Failed to build memory summary",{error:r,userId:e}),"无法获取用户记忆摘要。"}}shouldEnhanceWithMemory(e,t){if(!this.getConfig().enabled)return!1;if(e.length<10)return!1;const r=e.toLowerCase();return["我的","我喜欢","我不喜欢","我想","我需要","我希望","帮我","为我","给我","推荐","建议","记住"].some((e=>r.includes(e)))}};t(y,"instance");let f=y;const p=class e{constructor(){t(this,"memoryExtractor"),t(this,"memoryStorage"),t(this,"contextBuilder"),t(this,"memoryService"),t(this,"config",n()),this.memoryExtractor=new l,this.memoryStorage=h.getInstance(),this.contextBuilder=f.getInstance(),this.memoryService=s.getInstance()}static getInstance(){return e.instance||(e.instance=new e),e.instance}async processMessageWithMemory(e,t,r,n={}){try{const{assistantId:s,topicId:a,conversationHistory:c=[]}=n;let m=e,l=0;if(this.config.enabled&&this.contextBuilder.shouldEnhanceWithMemory(e,t))try{const r=await this.contextBuilder.buildEnhancedContext(e,t,{assistantId:s,contextStyle:"detailed",maxMemories:this.config.search.maxResults});r.relevantMemories.length>0?(m=r.enhancedPrompt,l=r.relevantMemories.length,i.log("INFO","Message enhanced with memory context",{userId:t,memoriesUsed:l,originalLength:e.length,enhancedLength:m.length})):i.log("INFO","No relevant memories found, using original message",{userId:t})}catch(o){i.log("WARN","Failed to enhance message with memory, using original message",{error:o,userId:t,message:e.substring(0,50)})}const d={content:m,success:!0};let h=0;return this.config.enabled&&c.length>=this.config.extraction.minConversationLength&&this.extractAndSaveMemories(c,e,d.content||"",t,{assistantId:s,topicId:a},r).then((e=>{h=e,i.log("INFO","Memories extracted and saved asynchronously",{userId:t,memoriesExtracted:e})})).catch((e=>{i.log("ERROR","Async memory extraction failed",{error:e,userId:t})})),{response:d,memoriesUsed:l,memoriesExtracted:h}}catch(o){i.log("ERROR","Memory-enhanced message processing failed",{error:o,userId:t});return{response:{content:e,success:!1,error:o instanceof Error?o.message:"Unknown error"},memoriesUsed:0,memoriesExtracted:0}}}cleanConversationHistory(e){return e.filter((e=>{if(!e||!e.role)return console.warn("⚠️ 发现无效消息（缺少 role）:",e),!1;let t="";if("string"==typeof e.content?t=e.content:Array.isArray(e.content)?t=e.content.filter((e=>e&&"text"===e.type&&e.text)).map((e=>e.text)).join(" "):e.content&&"object"==typeof e.content&&(e.content.text?t=e.content.text:e.content.content&&(t=e.content.content)),!t&&e.text&&(t=e.text),!t&&e.message&&(t=e.message),!t&&e.blocks&&Array.isArray(e.blocks)&&e.blocks.length>0){const r=e.blocks[0];r&&(t=`[需要从块 ${r} 获取内容]`)}return!(!t||!t.trim())||(console.warn("⚠️ 发现空内容消息:",e),!1)})).map((e=>{let t="";return"string"==typeof e.content?t=e.content:Array.isArray(e.content)?t=e.content.filter((e=>e&&"text"===e.type&&e.text)).map((e=>e.text)).join(" "):e.content&&"object"==typeof e.content&&(e.content.text?t=e.content.text:e.content.content&&(t=e.content.content)),!t&&e.text&&(t=e.text),!t&&e.message&&(t=e.message),{role:e.role,content:t.trim()}})).filter((e=>e.content.length>0))}async extractAndSaveMemories(e,t,r,n,o={},s){try{if(!this.config.enabled)return 0;const a=[...this.cleanConversationHistory(e),...(null==t?void 0:t.trim())?[{role:"user",content:t.trim()}]:[],...(null==r?void 0:r.trim())?[{role:"assistant",content:r.trim()}]:[]],c=await this.memoryExtractor.extractMemories(a,s,n);if(0===c.facts.length)return i.log("INFO","No memories extracted from conversation",{userId:n}),0;const m=await this.memoryStorage.saveMemories(n,c.facts,c.categories,c.importance,{...o,extractedFrom:"conversation",extractedAt:(new Date).toISOString()});return i.log("INFO","Memories extracted and saved",{userId:n,factsExtracted:c.facts.length,memoriesSaved:m.length}),m.length}catch(a){return i.log("ERROR","Failed to extract and save memories",{error:a,userId:n}),0}}async addMemory(e,t,r,n=5,o={}){try{if(!this.config.enabled)return i.log("WARN","Memory service is disabled"),!1;const s=this.memoryService.createMemoryRecord(e,t,r,n,{...o,addedManually:!0,addedAt:(new Date).toISOString()});return await this.memoryService.saveMemoryWithEmbedding(s),i.log("INFO","Memory added manually",{userId:e,memoryId:s.id,category:r,importance:n}),!0}catch(s){return i.log("ERROR","Failed to add memory manually",{error:s,userId:e}),!1}}async getUserMemorySummary(e,t={}){try{return await this.contextBuilder.buildMemorySummary(e,t)}catch(r){return i.log("ERROR","Failed to get user memory summary",{error:r,userId:e}),"无法获取用户记忆摘要。"}}async searchUserMemories(e,t,r={}){try{if(!this.config.enabled)return[];return await this.memoryStorage.searchMemories(e,t,r)}catch(n){return i.log("ERROR","Failed to search user memories",{error:n,userId:e,query:t}),[]}}updateConfig(e){this.config,this.config={...this.config,...e},this.config}getMemoryStatus(){return{enabled:this.config.enabled,config:{extractionModel:this.config.extraction.model,embeddingModel:this.config.search.embeddingModel,maxMemoriesPerUser:this.config.storage.maxMemoriesPerUser,retentionDays:this.config.storage.retentionDays}}}async clearUserMemories(e){try{if(!this.config.enabled)return!1;const t=await this.memoryService.getUserMemories(e);for(const e of t)await this.memoryService.deleteMemory(e.id);return i.log("INFO","All user memories cleared",{userId:e,memoriesDeleted:t.length}),!0}catch(t){return i.log("ERROR","Failed to clear user memories",{error:t,userId:e}),!1}}};t(p,"instance");let x=p;const M=m("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),b=m("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]),v=m("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),R=m("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);export{M as B,f as C,v as D,h as M,u as S,x as a,R as b,b as c};
