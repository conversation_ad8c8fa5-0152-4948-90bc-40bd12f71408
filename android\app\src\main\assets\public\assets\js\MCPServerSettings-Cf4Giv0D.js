import{j as e,B as r,a as s,A as o,T as i,I as t,b as n,m as a,P as l,D as d,L as x,N as m,Q as c,y as p,x as h,J as b,h as g,w as u,o as f,p as v,q as j,t as y,F as S,e as w,S as C,M as k,g as W,R as z,r as P,U as M,n as H}from"./mui-vendor-hRDvsX89.js";import{u as A,r as R,a as I}from"./react-vendor-C9ilihHH.js";import{_ as U,A as B,$ as E,S as O}from"./index-BtK6VV6Z.js";import{A as T}from"./Add-B_Z45Y27.js";import{H as $}from"./Http-BWYxyoAC.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const D=()=>{const D=A(),[J,N]=R.useState([]),[F,L]=R.useState(!1),[G,q]=R.useState(!1),[Q,Y]=R.useState([]),[_,K]=R.useState(!1),[V,X]=R.useState(""),[Z,ee]=R.useState({open:!1,message:"",severity:"success"}),[re,se]=R.useState({name:"",type:"httpStream",description:"",baseUrl:"",isActive:!1,enableSSE:!1});R.useEffect((()=>{oe(),ie()}),[]);const oe=()=>{const e=U.getServers();N(e)},ie=()=>{try{const e=U.getBuiltinServers();Y(e)}catch(e){console.error("加载内置服务器失败:",e)}},te=e=>J.some((r=>r.name===e)),ne=r=>{switch(r){case"httpStream":return e.jsx($,{});case"inMemory":return e.jsx(O,{});default:return e.jsx(E,{})}},ae=e=>{switch(e){case"httpStream":return"HTTP Stream";case"inMemory":return"内存";default:return"未知"}},le=e=>{switch(e){case"httpStream":return"#9c27b0";case"inMemory":return"#ff9800";default:return"#9e9e9e"}};return e.jsxs(r,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?s(e.palette.primary.main,.02):s(e.palette.background.default,.9)},children:[e.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(i,{children:[e.jsx(t,{edge:"start",color:"inherit",onClick:()=>{D("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(B,{})}),e.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"MCP 服务器"}),e.jsx(a,{startIcon:e.jsx(T,{}),onClick:()=>L(!0),sx:{bgcolor:e=>s(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>s(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})}),e.jsxs(r,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[0===J.length?e.jsx(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:e.jsxs(r,{sx:{p:{xs:3,sm:4},textAlign:"center"},children:[e.jsx(E,{sx:{fontSize:{xs:40,sm:48},color:"primary.main",mb:{xs:1.5,sm:2}}}),e.jsx(n,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600,fontSize:{xs:"1.1rem",sm:"1.25rem"}},children:"还没有配置 MCP 服务器"}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:{xs:2.5,sm:3},fontSize:{xs:"0.875rem",sm:"0.875rem"}},children:"MCP 服务器可以为 AI 提供额外的工具和功能"}),e.jsxs(r,{sx:{display:"flex",gap:{xs:1.5,sm:2},flexWrap:"wrap",justifyContent:"center",flexDirection:{xs:"column",sm:"row"},alignItems:"center"},children:[e.jsx(a,{variant:"contained",startIcon:e.jsx(T,{}),onClick:()=>L(!0),fullWidth:window.innerWidth<600,sx:{bgcolor:"primary.main","&:hover":{bgcolor:"primary.dark"},minHeight:{xs:44,sm:36},fontSize:{xs:"0.9rem",sm:"0.875rem"}},children:"添加服务器"}),e.jsx(a,{variant:"outlined",onClick:()=>K(!0),fullWidth:window.innerWidth<600,sx:{borderColor:"primary.main",color:"primary.main","&:hover":{borderColor:"primary.dark",color:"primary.dark"},minHeight:{xs:44,sm:36},fontSize:{xs:"0.9rem",sm:"0.875rem"}},children:"导入配置"}),e.jsx(a,{variant:"outlined",onClick:()=>q(!0),fullWidth:window.innerWidth<600,sx:{borderColor:"primary.main",color:"primary.main","&:hover":{borderColor:"primary.dark",color:"primary.dark"},minHeight:{xs:44,sm:36},fontSize:{xs:"0.9rem",sm:"0.875rem"}},children:"内置服务器"})]})]})}):e.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(n,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"MCP 服务器列表"}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"管理您的 MCP 服务器配置和状态"})]}),e.jsx(d,{}),e.jsx(x,{disablePadding:!0,children:J.map(((o,i)=>e.jsxs(I.Fragment,{children:[e.jsxs(m,{onClick:()=>(e=>{D(`/settings/mcp-server/${e.id}`,{state:{server:e}})})(o),sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>s(e.palette.primary.main,.05)}},children:[e.jsx(c,{children:e.jsx(p,{sx:{bgcolor:s(le(o.type),.12),color:le(o.type),boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:ne(o.type)})}),e.jsx(h,{primary:e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:{xs:.5,sm:1},flexWrap:"wrap"},children:[e.jsx(n,{sx:{fontWeight:600,color:"text.primary",fontSize:{xs:"0.9rem",sm:"1rem"}},children:o.name}),e.jsx(b,{label:ae(o.type),size:"small",sx:{bgcolor:s(le(o.type),.1),color:le(o.type),fontWeight:500,fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24}}}),o.isActive&&e.jsx(b,{label:"运行中",size:"small",color:"success",variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24}}})]}),secondary:e.jsxs(r,{component:"div",sx:{mt:{xs:.5,sm:1}},children:[o.description&&e.jsx(n,{variant:"body2",color:"text.secondary",component:"div",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"},lineHeight:1.4,display:"-webkit-box",WebkitLineClamp:{xs:2,sm:3},WebkitBoxOrient:"vertical",overflow:"hidden"},children:o.description}),o.baseUrl&&e.jsx(n,{variant:"caption",color:"text.secondary",component:"div",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},mt:.5,wordBreak:"break-all"},children:o.baseUrl})]})}),e.jsx(g,{checked:o.isActive,onChange:e=>{e.stopPropagation(),(async(e,r)=>{try{await U.toggleServer(e,r),oe(),ee({open:!0,message:r?"服务器已启用":"服务器已停用",severity:"success"})}catch(s){ee({open:!0,message:"操作失败",severity:"error"})}})(o.id,e.target.checked)},color:"primary",onClick:e=>e.stopPropagation()})]}),i<J.length-1&&e.jsx(d,{variant:"inset",component:"li",sx:{ml:0}})]},o.id)))})]}),e.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(n,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"快捷操作"}),e.jsx(n,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"快速添加和管理 MCP 服务器"})]}),e.jsx(d,{}),e.jsxs(x,{disablePadding:!0,children:[e.jsx(u,{disablePadding:!0,children:e.jsxs(m,{onClick:()=>K(!0),sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>s(e.palette.primary.main,.05)}},children:[e.jsx(c,{children:e.jsx(p,{sx:{bgcolor:s("#06b6d4",.12),color:"#06b6d4",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:e.jsx(T,{})})}),e.jsx(h,{primary:e.jsx(n,{sx:{fontWeight:600,color:"text.primary",fontSize:{xs:"0.9rem",sm:"1rem"}},children:"导入配置"}),secondary:e.jsx(n,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"从 JSON 文件导入 MCP 服务器配置"})})]})}),e.jsx(d,{variant:"inset",component:"li",sx:{ml:0}}),e.jsx(u,{disablePadding:!0,children:e.jsxs(m,{onClick:()=>q(!0),sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>s(e.palette.primary.main,.05)}},children:[e.jsx(c,{children:e.jsx(p,{sx:{bgcolor:s("#8b5cf6",.12),color:"#8b5cf6",boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:e.jsx(E,{})})}),e.jsx(h,{primary:e.jsx(n,{sx:{fontWeight:600,color:"text.primary",fontSize:{xs:"0.9rem",sm:"1rem"}},children:"内置服务器"}),secondary:e.jsx(n,{sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"添加预配置的内置 MCP 服务器"})})]})})]})]})]}),e.jsxs(f,{open:F,onClose:()=>L(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(v,{children:"添加 MCP 服务器"}),e.jsxs(j,{children:[e.jsx(y,{autoFocus:!0,margin:"dense",label:"服务器名称",fullWidth:!0,variant:"outlined",value:re.name,onChange:e=>se({...re,name:e.target.value}),sx:{mb:2}}),e.jsxs(S,{fullWidth:!0,sx:{mb:2},children:[e.jsx(w,{children:"服务器类型"}),e.jsxs(C,{value:re.type,label:"服务器类型",onChange:e=>se({...re,type:e.target.value}),children:[e.jsx(k,{value:"httpStream",children:"HTTP Stream (支持SSE+HTTP)"}),e.jsx(k,{value:"inMemory",children:"内存服务器"})]})]}),"httpStream"===re.type&&e.jsx(y,{margin:"dense",label:"服务器 URL",fullWidth:!0,variant:"outlined",value:re.baseUrl,onChange:e=>se({...re,baseUrl:e.target.value}),placeholder:"https://example.com/mcp",sx:{mb:2}}),e.jsx(y,{margin:"dense",label:"描述（可选）",fullWidth:!0,variant:"outlined",multiline:!0,rows:2,value:re.description,onChange:e=>se({...re,description:e.target.value})}),"httpStream"===re.type&&e.jsx(W,{control:e.jsx(z,{checked:!0===re.enableSSE,onChange:e=>se({...re,enableSSE:e.target.checked})}),label:"启用SSE流（Server-Sent Events）",sx:{mt:1}})]}),e.jsxs(P,{children:[e.jsx(a,{onClick:()=>L(!1),children:"取消"}),e.jsx(a,{onClick:async()=>{if(re.name&&re.type)if("httpStream"!==re.type||re.baseUrl)try{const e={id:Date.now().toString(),name:re.name,type:re.type,description:re.description,baseUrl:re.baseUrl,isActive:!1,headers:{},env:{},args:[]};await U.addServer(e),oe(),L(!1),se({name:"",type:"httpStream",description:"",baseUrl:"",isActive:!1,enableSSE:!1}),ee({open:!0,message:"服务器添加成功",severity:"success"})}catch(e){ee({open:!0,message:"添加失败",severity:"error"})}else ee({open:!0,message:"HTTP Stream 服务器需要提供 URL",severity:"error"});else ee({open:!0,message:"请填写必要信息",severity:"error"})},variant:"contained",children:"添加"})]})]}),e.jsxs(f,{open:_,onClose:()=>K(!1),maxWidth:"md",fullWidth:!0,children:[e.jsx(v,{children:"导入 MCP 服务器配置"}),e.jsxs(j,{children:[e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"粘贴包含 MCP 服务器配置的 JSON 文件内容。支持的格式示例："}),e.jsx(r,{sx:{bgcolor:"grey.100",p:2,borderRadius:1,mb:2,fontFamily:"monospace",fontSize:"0.875rem"},children:'{\n  "mcpServers": {\n    "fetch": {\n      "type": "sse",\n      "url": "https://mcp.api-inference.modelscope.cn/sse/89261d74d6814a"\n    },\n    "memory": {\n      "type": "streamableHttp",\n      "url": "https://example.com/mcp/memory"\n    }\n  }\n}'}),e.jsx(y,{autoFocus:!0,margin:"dense",label:"JSON 配置",fullWidth:!0,multiline:!0,rows:10,variant:"outlined",value:V,onChange:e=>X(e.target.value),placeholder:"在此粘贴 JSON 配置..."})]}),e.jsxs(P,{children:[e.jsx(a,{onClick:()=>K(!1),children:"取消"}),e.jsx(a,{onClick:async()=>{try{const r=JSON.parse(V);if(!r.mcpServers||"object"!=typeof r.mcpServers)throw new Error("JSON 格式不正确：缺少 mcpServers 字段");let s=0;const o=[];for(const[i,t]of Object.entries(r.mcpServers))try{const e={id:Date.now().toString()+Math.random().toString(36).substring(2,11),name:i,type:t.type||"sse",baseUrl:t.url,description:`从 JSON 导入的服务器: ${i}`,isActive:!1,headers:{},env:{},args:[]};await U.addServer(e),s++}catch(e){o.push(`${i}: ${e instanceof Error?e.message:"未知错误"}`)}oe(),K(!1),X(""),ee(s>0?{open:!0,message:`成功导入 ${s} 个服务器${o.length>0?`，${o.length} 个失败`:""}`,severity:o.length>0?"error":"success"}:{open:!0,message:"导入失败："+o.join("; "),severity:"error"})}catch(e){ee({open:!0,message:`JSON 解析失败: ${e instanceof Error?e.message:"未知错误"}`,severity:"error"})}},variant:"contained",disabled:!V.trim(),children:"导入"})]})]}),e.jsxs(f,{open:G,onClose:()=>q(!1),maxWidth:"md",fullWidth:!0,fullScreen:window.innerWidth<600,sx:{"& .MuiDialog-paper":{maxHeight:{xs:"100vh",sm:"90vh"},margin:{xs:0,sm:2},borderRadius:{xs:0,sm:2}}},children:[e.jsx(v,{sx:{px:{xs:2,sm:3},py:{xs:2,sm:2.5},fontSize:{xs:"1.25rem",sm:"1.5rem"},fontWeight:600,borderBottom:"1px solid",borderColor:"divider"},children:"添加内置 MCP 服务器"}),e.jsxs(j,{sx:{px:{xs:2,sm:3},py:{xs:2,sm:3}},children:[e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:{xs:2,sm:3},fontSize:{xs:"0.875rem",sm:"0.875rem"},lineHeight:1.5},children:"选择要添加的内置 MCP 服务器。这些服务器由 Cherry Studio 提供，无需额外配置即可使用。"}),e.jsx(r,{sx:{display:"flex",flexDirection:"column",gap:{xs:1.5,sm:2},maxHeight:{xs:"calc(100vh - 200px)",sm:"70vh",md:"80vh"},overflow:"auto",pr:{xs:.5,sm:1},WebkitOverflowScrolling:"touch","&::-webkit-scrollbar":{width:{xs:"2px",sm:"4px"}},"&::-webkit-scrollbar-track":{background:"transparent"},"&::-webkit-scrollbar-thumb":{background:"rgba(0, 0, 0, 0.2)",borderRadius:"2px"}},children:Q.map((o=>{const i=te(o.name);return e.jsx(l,{elevation:0,sx:{border:"1px solid",borderColor:i?"#d1fae5":"#e5e7eb",borderRadius:{xs:2,sm:2},transition:"all 0.2s ease-in-out",backgroundColor:i?"#f0fdf4":"#ffffff",cursor:"pointer",touchAction:"manipulation","&:hover":{borderColor:i?"#a7f3d0":"#10b981",boxShadow:{xs:"0 2px 8px rgba(0,0,0,0.06)",sm:"0 4px 12px rgba(0,0,0,0.08)"}},"&:active":{transform:{xs:"scale(0.98)",sm:"none"}}},children:e.jsxs(r,{sx:{p:{xs:2,sm:2.5,md:3},display:"flex",flexDirection:{xs:"column",sm:"row"},alignItems:{xs:"stretch",sm:"flex-start"},gap:{xs:2,sm:2.5}},children:[e.jsxs(r,{sx:{flex:1,minWidth:0},children:[e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1,mb:{xs:.75,sm:1}},children:[e.jsx(n,{variant:"h6",fontWeight:600,sx:{fontSize:{xs:"1rem",sm:"1.1rem",md:"1.2rem"},color:"text.primary",lineHeight:1.3,wordBreak:"break-word"},children:o.name}),i&&e.jsx(b,{label:"已添加",size:"small",sx:{height:20,fontSize:"0.7rem",backgroundColor:"#dcfce7",color:"#166534",border:"1px solid #bbf7d0",fontWeight:500}})]}),e.jsx(n,{variant:"body2",sx:{color:"text.secondary",mb:{xs:1.5,sm:2},lineHeight:1.6,fontSize:{xs:"0.8rem",sm:"0.875rem",md:"0.9rem"},display:"-webkit-box",WebkitLineClamp:{xs:3,sm:2},WebkitBoxOrient:"vertical",overflow:"hidden"},children:o.description}),e.jsx(r,{sx:{display:"flex",alignItems:"center",gap:{xs:.75,sm:1},flexWrap:"wrap"},children:o.tags&&o.tags.map((r=>e.jsx(b,{label:r,size:"small",variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:22,sm:24},borderColor:"#e5e7eb",color:"#6b7280",backgroundColor:"#f9fafb",fontWeight:500,"& .MuiChip-label":{px:{xs:1,sm:1.5}},"&:hover":{borderColor:"#10b981",backgroundColor:s("#10b981",.05)}}},r)))})]}),e.jsx(r,{sx:{display:"flex",alignItems:{xs:"stretch",sm:"center"},flexShrink:0,mt:{xs:1,sm:0}},children:te(o.name)?e.jsx(a,{variant:"outlined",size:window.innerWidth<600?"medium":"small",fullWidth:window.innerWidth<600,disabled:!0,sx:{borderColor:"#d1d5db",color:"#6b7280",borderRadius:{xs:2,sm:1.5},px:{xs:3,sm:2},py:{xs:1,sm:.75},fontWeight:500,fontSize:{xs:"0.9rem",sm:"0.875rem"},textTransform:"none",minWidth:{xs:"auto",sm:"auto"},minHeight:{xs:44,sm:"auto"},cursor:"default"},children:"已添加"}):e.jsx(a,{onClick:e=>{e.stopPropagation(),(async e=>{if(te(e.name))ee({open:!0,message:`服务器 ${e.name} 已存在`,severity:"warning"});else try{await U.addBuiltinServer(e.name,{description:e.description,env:e.env,args:e.args,tags:e.tags,provider:e.provider}),oe(),ee({open:!0,message:`内置服务器 ${e.name} 添加成功`,severity:"success"})}catch(r){ee({open:!0,message:"添加内置服务器失败",severity:"error"})}})(o)},variant:"contained",size:window.innerWidth<600?"medium":"small",fullWidth:window.innerWidth<600,sx:{backgroundColor:"#10b981",color:"white",borderRadius:{xs:2,sm:1.5},px:{xs:3,sm:2},py:{xs:1,sm:.75},fontWeight:500,fontSize:{xs:"0.9rem",sm:"0.875rem"},textTransform:"none",minWidth:{xs:"auto",sm:"auto"},minHeight:{xs:44,sm:"auto"},"&:hover":{backgroundColor:"#059669"}},children:"添加"})})]})},o.id)}))})]}),e.jsx(P,{sx:{px:{xs:2,sm:3},py:{xs:2,sm:2.5},borderTop:"1px solid",borderColor:"divider",gap:{xs:1,sm:2}},children:e.jsx(a,{onClick:()=>q(!1),variant:"outlined",fullWidth:window.innerWidth<600,sx:{minHeight:{xs:44,sm:36},fontSize:{xs:"1rem",sm:"0.875rem"}},children:"关闭"})})]}),e.jsx(M,{open:Z.open,autoHideDuration:3e3,onClose:()=>ee({...Z,open:!1}),children:e.jsx(H,{severity:Z.severity,onClose:()=>ee({...Z,open:!1}),children:Z.message})})]})};export{D as default};
