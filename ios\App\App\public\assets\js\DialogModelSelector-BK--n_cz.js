import{c as e,j as r,z as a,a3 as o,m as i,B as s,o as l,p as t,I as d,D as n,Z as p,_ as m,q as c,L as u,w as v,a1 as x,y as h,x as b}from"./mui-vendor-hRDvsX89.js";import{a as g,r as j}from"./react-vendor-C9ilihHH.js";import{d as y,aj as f}from"./index-BtK6VV6Z.js";const M=e(r.jsx("path",{d:"M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"})),C=e(r.jsx("path",{d:"M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"})),k={button:e=>({textTransform:"none",color:e?"text.primary":"black",mr:1,fontWeight:"normal",fontSize:"0.9rem",border:"1px solid "+(e?"divider":"#eeeeee"),borderRadius:"16px",px:2,py:.5,"&:hover":{bgcolor:e?"rgba(255, 255, 255, 0.08)":"#f5f5f5",border:"1px solid "+(e?"rgba(255, 255, 255, 0.2)":"#e0e0e0")}}),dialogPaper:e=>({borderRadius:e?0:2,height:e?"100%":"auto",maxHeight:e?"100%":"80vh"}),dialogTitle:{display:"flex",justifyContent:"space-between",alignItems:"center",pb:1},tabsContainer:{borderBottom:1,borderColor:"divider"},dialogContent:{px:1,py:2},list:{pt:0}},S={listItem:(e,r)=>({borderRadius:1,mb:.5,cursor:"pointer",bgcolor:e?r?"rgba(144, 202, 249, 0.16)":"rgba(25, 118, 210, 0.08)":"transparent","&:hover":{bgcolor:e?r?"rgba(144, 202, 249, 0.24)":"rgba(25, 118, 210, 0.12)":r?"rgba(255, 255, 255, 0.08)":"rgba(0, 0, 0, 0.04)"}}),listItemIcon:{minWidth:40},avatar:(e,r,a)=>({width:28,height:28,bgcolor:(null==e?void 0:e.color)||(r?a:"grey.400"),color:"white"}),primaryText:e=>({variant:"body1",fontWeight:e?"medium":"normal"}),secondaryText:{variant:"caption",noWrap:!0}},T=[],I=({selectedModel:e,availableModels:v,handleModelSelect:x,handleMenuClick:h,handleMenuClose:b,menuOpen:M})=>{var S;const I=a(),W=o(I.breakpoints.down("sm")),[z,N]=g.useState("all"),B=y((e=>e.settings.providers||T)),$=j.useMemo((()=>"dark"===I.palette.mode),[I.palette.mode]),D=j.useMemo((()=>{const e=new Map;return B.forEach((r=>{e.set(r.id,r.name)})),e}),[B]),P=j.useCallback((e=>D.get(e)||e),[D]),L=j.useMemo((()=>{const e={},r={};v.forEach((a=>{const o=a.provider||a.providerType||"未知",i=P(o);r[o]||(r[o]={id:o,displayName:i}),e[o]||(e[o]=[]),e[o].push(a)}));const a=Object.values(r);return a.sort(((e,r)=>e.displayName.localeCompare(r.displayName))),{groups:e,providers:a}}),[v,P]),R=j.useCallback(((e,r)=>{N(r)}),[]),q=j.useCallback((e=>{x(e)}),[x]),E=j.useCallback((e=>{if(e.length<=16)return"0.9rem";const r=e.length/16,a=.9*Math.max(1/Math.sqrt(r),.65/.9);return`${Math.max(a,.65)}rem`}),[]);return r.jsxs(r.Fragment,{children:[r.jsx(i,{onClick:h,endIcon:r.jsx(C,{}),sx:{...k.button($),maxWidth:"200px","& .MuiButton-startIcon, & .MuiButton-endIcon":{flexShrink:0}},title:(null==e?void 0:e.name)||"选择模型",children:r.jsx(s,{sx:{fontSize:e?E(e.name):"0.9rem",fontWeight:"normal",transition:"font-size 0.2s ease",width:"100%",textAlign:"left",wordBreak:"keep-all",lineHeight:1.2},children:(null==e?void 0:e.name)||"选择模型"})}),r.jsxs(l,{open:M,onClose:b,fullScreen:W,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:k.dialogPaper(W)},children:[r.jsxs(t,{sx:k.dialogTitle,children:["选择模型",r.jsx(d,{edge:"end",onClick:b,"aria-label":"close",children:r.jsx(f,{})})]}),r.jsx(n,{}),r.jsx(s,{sx:k.tabsContainer,children:r.jsxs(p,{value:z,onChange:R,variant:"scrollable",scrollButtons:"auto","aria-label":"model provider tabs",children:[r.jsx(m,{label:"全部",value:"all"}),L.providers.map((e=>r.jsx(m,{label:e.displayName,value:e.id},e.id)))]})}),r.jsx(c,{sx:k.dialogContent,children:r.jsx(u,{sx:k.list,children:"all"===z?v.map((a=>r.jsx(w,{model:a,isSelected:(null==e?void 0:e.id)===a.id&&(null==e?void 0:e.provider)===a.provider,onSelect:()=>q(a),providerDisplayName:P(a.provider||a.providerType||"未知"),providers:B},`${a.id}-${a.provider}`))):null==(S=L.groups[z])?void 0:S.map((a=>r.jsx(w,{model:a,isSelected:(null==e?void 0:e.id)===a.id&&(null==e?void 0:e.provider)===a.provider,onSelect:()=>q(a),providerDisplayName:P(a.provider||a.providerType||"未知"),providers:B},`${a.id}-${a.provider}`)))})})]})]})},w=g.memo((({model:e,isSelected:o,onSelect:i,providerDisplayName:s,providers:l})=>{const t=a(),d=j.useMemo((()=>"dark"===t.palette.mode),[t.palette.mode]),n=j.useMemo((()=>null==l?void 0:l.find((r=>r.id===(e.provider||e.providerType)))),[l,e.provider,e.providerType]),p=j.useMemo((()=>S.listItem(o,d)),[o,d]),m=j.useMemo((()=>S.avatar(n,o,t.palette.primary.main)),[n,o,t.palette.primary.main]),c=j.useMemo((()=>S.primaryText(o)),[o]);return r.jsxs(v,{onClick:i,sx:{...p,cursor:"pointer","&:hover":{...p["&:hover"],backgroundColor:o?d?"rgba(144, 202, 249, 0.16)":"rgba(25, 118, 210, 0.08)":d?"rgba(255, 255, 255, 0.08)":"rgba(0, 0, 0, 0.04)"}},children:[r.jsx(x,{sx:S.listItemIcon,children:r.jsx(h,{sx:m,children:(null==n?void 0:n.avatar)||s[0]})}),r.jsx(b,{primary:e.name,secondary:e.description||`${s}模型`,slotProps:{primary:c,secondary:S.secondaryText}}),o&&r.jsx(M,{color:"primary",fontSize:"small"})]})}));export{M as C,I as D,C as K};
