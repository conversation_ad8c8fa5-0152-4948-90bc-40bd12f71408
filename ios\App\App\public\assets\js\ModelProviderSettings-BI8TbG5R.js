const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/index-BtK6VV6Z.js","assets/js/mui-vendor-hRDvsX89.js","assets/js/react-vendor-C9ilihHH.js","assets/js/utils-vendor-BDm_82Hk.js","assets/js/syntax-vendor-DfDNeb5M.js","assets/css/index-C_2Yhxqf.css"])))=>i.map(i=>d[i]);
import{c as e,j as r,o as s,p as t,C as i,B as o,t as n,D as a,q as l,b as d,W as c,X as p,J as x,a as h,I as m,Y as u,L as g,w as b,x as j,r as v,m as y,g as f,R as C,n as k,z as S,d as w,h as I,P as R,u as E,y as A,F as z,e as M,S as W,M as T,A as O,T as D,U as L}from"./mui-vendor-hRDvsX89.js";import{r as P,u as V,e as B}from"./react-vendor-C9ilihHH.js";import{b9 as F,ba as U,bb as _,bc as G,E as H,e as $,$ as K,f as q,u as Y,a as X,aC as J,A as N,bd as Q,be as Z,bf as ee,bg as re}from"./index-BtK6VV6Z.js";import{A as se}from"./Add-B_Z45Y27.js";import{E as te}from"./Edit-D9CSXufJ.js";import{S as ie}from"./Search-DceKHZcL.js";import{_ as oe}from"./syntax-vendor-DfDNeb5M.js";import{a as ne,M as ae}from"./ModelIcon-EPsB8sVh.js";import{A as le}from"./AvatarUploader-BqYh7pMX.js";import{I as de}from"./InfoOutlined-qyCTMRwB.js";import"./utils-vendor-BDm_82Hk.js";import"./CloudUpload-D6Ko6NJp.js";const ce=e([r.jsx("path",{d:"M12.03 6.3h-.06l-1.02 2.89h2.1zM3 17h2v5H3z"},"0"),r.jsx("path",{d:"M12 15c3.31 0 6-2.69 6-6s-2.69-6-6-6-6 2.69-6 6 2.69 6 6 6m-.63-10h1.25l2.63 7h-1.21l-.63-1.79h-2.83L9.96 12H8.74zM7 17h2v5H7zm4 0h2v5h-2zm4 0h6v5h-6z"},"1")]),pe=e(r.jsx("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"})),xe=e(r.jsx("path",{d:"M19 13H5v-2h14z"})),he=e(r.jsx("path",{d:"m23 12-2.44-2.79.34-3.69-3.61-.82-1.89-3.2L12 2.96 8.6 1.5 6.71 4.69 3.1 5.5l.34 3.7L1 12l2.44 2.79-.34 3.7 3.61.82L8.6 22.5l3.4-1.47 3.4 1.46 1.89-3.19 3.61-.82-.34-3.69zm-12.91 4.72-3.8-3.81 1.48-1.48 2.32 2.33 5.85-5.87 1.48 1.48z"}));const me=({open:e,onClose:f,provider:C,onAddModel:k,onAddModels:S,onRemoveModel:w,onRemoveModels:I,existingModels:R})=>{const[E,A]=P.useState(!1),[z,M]=P.useState([]),[W,T]=P.useState(""),[O,D]=P.useOptimistic(W,((e,r)=>r)),[L,V]=P.useState(new Set),[B,$]=P.useState(new Map),[K,q]=P.useTransition(),[Y,X]=P.useTransition(),J=P.useRef(null),N=P.useCallback((e=>R.some((r=>r.id===e))||!0===B.get(e)),[R,B]),Q=P.useMemo((()=>G.debounce((e=>{q((()=>{T(e)}))}),300)),[]);P.useEffect((()=>()=>{Q.cancel()}),[Q]);const Z=P.useMemo((()=>z.filter((e=>e.name.toLowerCase().includes(W.toLowerCase())||e.id.toLowerCase().includes(W.toLowerCase()))).reduce(((e,r)=>{const s=r.group||"其他模型";return e[s]||(e[s]=[]),e[s].push(r),e}),{})),[z,W]),ee=async()=>{try{A(!0);const e=J.current||C,r=await async function(e){try{F("获取模型列表","INFO",{provider:e.id});const{fetchModels:r}=await oe((async()=>{const{fetchModels:e}=await import("./index-BtK6VV6Z.js").then((e=>e.dk));return{fetchModels:e}}),__vite__mapDeps([0,1,2,3,4,5])),s=await r(e);return U("获取模型列表",200,{provider:e.id,modelsCount:s.length}),s}catch(r){return _(r,"APIService.fetchModels",{logLevel:"ERROR",additionalData:{provider:e.id}}),U("获取模型列表",500,{provider:e.id,error:r instanceof Error?r.message:"未知错误"}),[]}}(e),s=[...r];M(s);const t=new Set;s.forEach((e=>{e.group&&t.add(e.group)})),V(t)}catch(e){console.error("加载模型失败:",e)}finally{A(!1)}},re=P.useCallback((e=>{X((()=>{V((r=>{const s=new Set(r);return s.has(e)?s.delete(e):s.add(e),s}))}))}),[]),te=P.useCallback((e=>{N(e.id)||($((r=>{const s=new Map(r);return s.set(e.id,!0),s})),k(e))}),[N,k]),ae=P.useCallback((e=>{$((r=>{const s=new Map(r);return s.delete(e),s})),w(e)}),[w]),le=P.useCallback((e=>{var r;const s=(null==(r=Z[e])?void 0:r.filter((e=>!N(e.id))))||[];if(s.length>0)if($((e=>{const r=new Map(e);return s.forEach((e=>{r.set(e.id,!0)})),r})),S){const e=s.map((e=>({...e})));S(e)}else s.forEach((e=>{k({...e})}))}),[Z,N,S,k]),de=P.useCallback((e=>{var r,s;const t=(null==(s=null==(r=Z[e])?void 0:r.filter((e=>N(e.id))))?void 0:s.map((e=>e.id)))||[];t.length>0&&($((e=>{const r=new Map(e);return t.forEach((e=>{r.delete(e)})),r})),I?I(t):t.forEach((e=>{w(e)})))}),[Z,N,I,w]);return P.useEffect((()=>{e?(J.current||(J.current=C),ee(),$(new Map)):J.current=null}),[e]),r.jsxs(s,{open:e,onClose:f,fullWidth:!0,maxWidth:"md",PaperProps:{sx:{borderRadius:2,maxHeight:"90vh"}},children:[r.jsxs(t,{sx:{fontWeight:700,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent",display:"flex",alignItems:"center",justifyContent:"space-between",p:3},children:[C.name,"模型管理",E&&r.jsx(i,{size:24,sx:{ml:2}})]}),r.jsxs(o,{sx:{px:3,pb:2},children:[r.jsx(n,{fullWidth:!0,placeholder:"搜索模型...",size:"small",value:O,onChange:e=>{const r=e.target.value;D(r),Q(r)},InputProps:{startAdornment:r.jsx(ie,{sx:{mr:1,color:"text.secondary"}}),sx:{borderRadius:2}}}),K&&r.jsx(o,{sx:{display:"flex",justifyContent:"center",mt:1},children:r.jsx(i,{size:16})})]}),r.jsx(a,{}),r.jsx(l,{sx:{p:2,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:E||K||Y?r.jsx(o,{sx:{display:"flex",justifyContent:"center",my:4},children:r.jsx(i,{})}):r.jsx(r.Fragment,{children:0===Object.keys(Z).length?r.jsx(d,{variant:"body1",sx:{textAlign:"center",my:4,color:"text.secondary"},children:"找不到匹配的模型"}):Object.entries(Z).map((([e,s])=>{var t;const i=s.every((e=>N(e.id)));return r.jsxs(c,{expanded:L.has(e),onChange:()=>re(e),sx:{mb:2,border:"1px solid",borderColor:"divider",borderRadius:"8px !important","&:before":{display:"none"},boxShadow:"none"},children:[r.jsxs(o,{sx:{position:"relative"},children:[r.jsx(p,{expandIcon:r.jsx(H,{}),sx:{borderRadius:"8px",pr:6},children:r.jsxs(o,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(ne,{providerType:(null==C?void 0:C.providerType)||(null==C?void 0:C.type),modelId:null==(t=s[0])?void 0:t.id,size:24,style:{marginRight:8}}),r.jsx(d,{variant:"subtitle1",fontWeight:600,children:e}),r.jsx(x,{label:s.length,size:"small",sx:{ml:1,height:20,bgcolor:e=>h(e.palette.success.main,.1),color:"success.main",fontWeight:600,fontSize:"0.7rem"}})]})}),r.jsx(m,{size:"small",color:i?"error":"primary",onClick:r=>{r.stopPropagation(),i?de(e):le(e)},sx:{position:"absolute",right:48,top:"50%",transform:"translateY(-50%)",bgcolor:e=>h(i?e.palette.error.main:e.palette.primary.main,.1),"&:hover":{bgcolor:e=>h(i?e.palette.error.main:e.palette.primary.main,.2)}},children:i?r.jsx(xe,{}):r.jsx(se,{})})]}),r.jsx(u,{sx:{p:1},children:r.jsx(g,{disablePadding:!0,children:s.map((e=>r.jsx(b,{sx:{mb:1,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:N(e.id)?e=>h(e.palette.success.main,.05):"transparent",transition:"all 0.2s"},secondaryAction:N(e.id)?r.jsx(m,{edge:"end",color:"error",onClick:()=>ae(e.id),sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)}},children:r.jsx(xe,{})}):r.jsx(m,{edge:"end",color:"primary",onClick:()=>te(e),sx:{bgcolor:e=>h(e.palette.primary.main,.1),"&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)}},children:r.jsx(se,{})}),children:r.jsxs(o,{sx:{display:"flex",alignItems:"center",mr:2},children:[r.jsx(ne,{providerType:(null==C?void 0:C.providerType)||(null==C?void 0:C.type),modelId:e.id,size:32,style:{marginRight:12}}),r.jsx(j,{primary:r.jsx(d,{variant:"body1",fontWeight:600,children:e.name}),secondary:r.jsx(d,{variant:"body2",color:"text.secondary",fontSize:"0.8rem",children:e.id})})]})},e.id)))})})]},e)}))})}),r.jsx(a,{}),r.jsx(v,{sx:{p:2},children:r.jsx(y,{onClick:f,sx:{borderRadius:2,px:3,py:1,fontWeight:600},children:"关闭"})})]})},ue=[{pattern:"VL|vision|visual|image",types:[ae.Vision,ae.Chat]},{pattern:"gpt-4-vision|gpt-4v",types:[ae.Vision,ae.Chat],provider:"openai"},{pattern:"gpt-4o",types:[ae.Vision,ae.Chat,ae.FunctionCalling,ae.WebSearch],provider:"openai"},{pattern:"gemini-(pro|ultra)-vision",types:[ae.Vision,ae.Chat],provider:"google"},{pattern:"claude-3-(opus|sonnet|haiku)",types:[ae.Vision,ae.Chat,ae.FunctionCalling],provider:"anthropic"},{pattern:"qwen.*vl",types:[ae.Vision,ae.Chat],provider:"siliconflow"},{pattern:"FLUX|flux|black-forest|stable-diffusion|sd|dalle|midjourney",types:[ae.ImageGen]},{pattern:"image|img|picture|generate",types:[ae.ImageGen]},{pattern:"embedding|text-embedding|embeddings",types:[ae.Embedding]},{pattern:"text-embedding-.*|embedding-.*",types:[ae.Embedding],provider:"openai"},{pattern:".*-embedding-.*",types:[ae.Embedding],provider:"google"},{pattern:"tts|whisper|audio|speech",types:[ae.Audio,ae.Transcription]},{pattern:"tool|function|plugin",types:[ae.Tool,ae.Chat,ae.FunctionCalling]},{pattern:"function.*calling|function-calling",types:[ae.FunctionCalling,ae.Chat]},{pattern:"gpt-4-turbo|gpt-3.5-turbo",types:[ae.Chat,ae.FunctionCalling],provider:"openai"},{pattern:"web.*search|search|browse",types:[ae.WebSearch,ae.Chat]},{pattern:"gpt-4-turbo-search|gpt-4o-search",types:[ae.WebSearch,ae.Chat],provider:"openai"},{pattern:"rerank|rank",types:[ae.Rerank]},{pattern:"code|codex|coder|copilot|starcoder|codellama",types:[ae.CodeGen,ae.Chat]},{pattern:"translate|translation",types:[ae.Translation,ae.Chat]},{pattern:"reasoning|think|coder|math",types:[ae.Reasoning,ae.Chat]},{pattern:".*-thinking-.*",types:[ae.Reasoning,ae.Chat]},{pattern:"deepseek-reasoner|deepseek-r1",types:[ae.Reasoning,ae.Chat],provider:"deepseek"},{pattern:".*",types:[ae.Chat]}];function ge(e,r,s=ue){for(const i of s)if(!i.provider||i.provider===r)try{if(new RegExp(i.pattern,"i").test(e))return i.types}catch(t){if(e.toLowerCase().includes(i.pattern.toLowerCase()))return i.types}return[ae.Chat]}function be(e){return{[ae.Chat]:"聊天",[ae.Vision]:"视觉",[ae.Audio]:"语音",[ae.Embedding]:"嵌入向量",[ae.Tool]:"工具使用",[ae.Reasoning]:"推理",[ae.ImageGen]:"图像生成",[ae.FunctionCalling]:"函数调用",[ae.WebSearch]:"网络搜索",[ae.Rerank]:"重排序",[ae.CodeGen]:"代码生成",[ae.Translation]:"翻译",[ae.Transcription]:"转录"}[e]||e}const je=({open:e,onClose:i,rules:a,onSave:c,modelId:p,provider:h})=>{const[u,g]=P.useState([]),[b,j]=P.useState({pattern:"",types:[ae.Chat]}),[S,w]=P.useState([]),[I,R]=P.useState(!1);P.useEffect((()=>{e&&(g(a||[]),R(!1))}),[e,a]);const E=(e,r,s)=>{const t=[...u];t[e]="types"===r?{...t[e],types:s}:{...t[e],[r]:s},g(t)},A=(e,r)=>{j("types"===e?{...b,types:r}:{...b,[e]:r})},z=e=>{const r=[...u];r.splice(e,1),g(r)};return r.jsxs(s,{open:e,onClose:i,maxWidth:"md",fullWidth:!0,children:[r.jsx(t,{children:r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(d,{variant:"h6",children:"模型类型管理"}),r.jsx(y,{color:I?"primary":"secondary",onClick:()=>{R(!I)},children:I?"完成编辑":"编辑规则"})]})}),r.jsx(l,{children:I?r.jsxs(o,{children:[r.jsx(d,{variant:"subtitle1",gutterBottom:!0,children:"编辑模型类型匹配规则"}),r.jsx(d,{variant:"body2",color:"textSecondary",sx:{mb:2},children:"规则按顺序匹配，第一条匹配成功的规则将被应用。"}),r.jsx(o,{sx:{mb:3},children:u.map(((e,s)=>r.jsxs(o,{sx:{p:2,mb:2,border:"1px solid #e0e0e0",borderRadius:1,backgroundColor:"#f9f9f9"},children:[r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(o,{children:r.jsx(n,{fullWidth:!0,label:"匹配模式",value:e.pattern,onChange:e=>E(s,"pattern",e.target.value),helperText:"支持正则表达式或简单字符串",size:"small"})}),r.jsx(m,{color:"error",onClick:()=>z(s),children:r.jsx($,{})})]}),r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{mt:1},children:[r.jsx(o,{children:r.jsx(n,{fullWidth:!0,label:"提供商限制（可选）",value:e.provider||"",onChange:e=>E(s,"provider",e.target.value||void 0),placeholder:"例如: openai, google",size:"small"})}),r.jsx(o,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:Object.values(ae).map((t=>r.jsx(f,{control:r.jsx(C,{checked:e.types.includes(t),onChange:e=>((e,r,s)=>{let t=[...u[e].types];s?t.includes(r)||t.push(r):(t=t.filter((e=>e!==r)),0===t.length&&(t=[ae.Chat])),E(e,"types",t)})(s,t,e.target.checked),size:"small"}),label:be(t)},t)))})]})]},s)))}),r.jsxs(o,{sx:{p:2,mb:2,border:"1px dashed #a0a0a0",borderRadius:1},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,children:"添加新规则"}),r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsx(o,{children:r.jsx(n,{fullWidth:!0,label:"匹配模式",value:b.pattern,onChange:e=>A("pattern",e.target.value),helperText:"支持正则表达式或简单字符串",size:"small"})}),r.jsx(m,{color:"primary",onClick:()=>{""!==b.pattern.trim()&&(0!==b.types.length?(g([...u,b]),j({pattern:"",types:[ae.Chat]})):j({...b,types:[ae.Chat]}))},children:r.jsx(se,{})})]}),r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{mt:1},children:[r.jsx(o,{children:r.jsx(n,{fullWidth:!0,label:"提供商限制（可选）",value:b.provider||"",onChange:e=>A("provider",e.target.value||void 0),placeholder:"例如: openai, google",size:"small"})}),r.jsx(o,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:Object.values(ae).map((e=>r.jsx(f,{control:r.jsx(C,{checked:b.types.includes(e),onChange:r=>((e,r)=>{let s=[...b.types];r?s.includes(e)||s.push(e):(s=s.filter((r=>r!==e)),0===s.length&&(s=[ae.Chat])),A("types",s)})(e,r.target.checked),size:"small"}),label:be(e)},e)))})]})]})]}):r.jsxs(o,{children:[r.jsx(d,{variant:"subtitle1",gutterBottom:!0,children:"当前模型类型匹配规则"}),u.length>0?u.map(((e,s)=>r.jsxs(o,{sx:{p:2,mb:2,border:"1px solid #e0e0e0",borderRadius:1,backgroundColor:"#f9f9f9"},children:[r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[r.jsxs(o,{children:[r.jsxs(d,{variant:"body2",children:[r.jsx("strong",{children:"匹配模式:"})," ",e.pattern]}),e.provider&&r.jsxs(d,{variant:"body2",children:[r.jsx("strong",{children:"提供商:"})," ",e.provider]})]}),r.jsx(m,{color:"error",onClick:()=>z(s),children:r.jsx($,{})})]}),r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{mt:1},children:[r.jsx(o,{children:r.jsx(d,{variant:"body2",sx:{mb:1},children:r.jsx("strong",{children:"适用类型:"})})}),r.jsx(o,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:e.types.map((e=>r.jsx(x,{label:be(e),size:"small",sx:{mr:.5,mb:.5}},e)))})]})]},s))):r.jsx(k,{severity:"info",sx:{mt:2},children:"尚未配置任何类型匹配规则，将使用默认规则。"}),p&&h&&r.jsxs(o,{sx:{mt:4,p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,children:"测试模型类型匹配"}),r.jsxs(o,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,children:[r.jsxs(d,{variant:"body2",sx:{mr:1},children:[r.jsx("strong",{children:"模型ID:"})," ",p]}),r.jsxs(d,{variant:"body2",children:[r.jsx("strong",{children:"提供商:"})," ",h]})]}),r.jsx(y,{variant:"outlined",color:"primary",onClick:()=>{if(p&&h){const e=ge(p,h,u);w(e)}},size:"small",children:"测试匹配"}),S.length>0&&r.jsxs(o,{sx:{mt:2},children:[r.jsx(d,{variant:"body2",children:"匹配结果:"}),r.jsx(o,{display:"flex",flexWrap:"wrap",gap:.5,sx:{mt:1},children:S.map((e=>r.jsx(x,{label:be(e),color:"primary",size:"small",sx:{mr:.5,mb:.5}},e)))})]})]})]})}),r.jsxs(v,{children:[r.jsx(y,{onClick:i,children:"取消"}),r.jsx(y,{onClick:()=>{c(u),i()},variant:"contained",color:"primary",children:"保存"})]})]})},ve={basic:["chat"],input:["vision","audio"],output:["image_gen","transcription","translation"],advanced:["reasoning","function_calling","web_search","tool","code_gen"],data:["embedding","rerank"]},ye={chat:{description:"基础聊天功能，支持文本对话",color:"#4285F4"},vision:{description:"支持图像理解和分析",color:"#EA4335"},audio:{description:"支持音频处理和理解",color:"#FBBC05"},embedding:{description:"生成文本的向量表示，用于相似度搜索",color:"#34A853"},tool:{description:"支持使用外部工具和API",color:"#8E44AD"},reasoning:{description:"增强的推理能力，适合复杂问题解决",color:"#1ABC9C"},image_gen:{description:"生成图像的能力",color:"#E74C3C"},function_calling:{description:"支持函数调用，可与代码交互",color:"#3498DB"},web_search:{description:"支持网络搜索，获取实时信息",color:"#F39C12"},rerank:{description:"重新排序搜索结果或文档",color:"#16A085"},code_gen:{description:"代码生成和编程辅助",color:"#2C3E50"},translation:{description:"文本翻译功能",color:"#27AE60"},transcription:{description:"语音转文字功能",color:"#D35400"}},fe=({modelTypes:e,onChange:s,autoDetect:t,onAutoDetectChange:i,modelId:n,provider:a})=>{const l=S(),[c,p]=P.useState(!1),[u,g]=P.useState([]);P.useEffect((()=>{(async()=>{try{const e=await q.getSetting("modelTypeRules");g(e||ue)}catch(e){console.error("[EnhancedModelTypeSelector] 加载模型类型规则失败:",e),g(ue)}})()}),[]);const b=e=>({basic:"基础功能",input:"输入能力",output:"输出能力",advanced:"高级功能",data:"数据处理"}[e]||e);return r.jsxs(o,{sx:{mt:3},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(d,{variant:"subtitle2",children:"模型类型"}),r.jsx(w,{title:"模型类型决定了模型的能力和适用场景。您可以根据需要选择多种类型。",children:r.jsx(m,{size:"small",sx:{ml:.5},children:r.jsx(de,{fontSize:"small"})})})]}),r.jsxs(o,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(f,{control:r.jsx(I,{checked:t,onChange:e=>i(e.target.checked),size:"small"}),label:"自动检测"}),r.jsx(w,{title:"管理模型类型规则",children:r.jsx(m,{size:"small",onClick:()=>{p(!0)},sx:{ml:1},children:r.jsx(K,{fontSize:"small"})})})]})]}),r.jsx(R,{elevation:0,sx:{p:2,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"dark"===l.palette.mode?h(l.palette.background.paper,.6):h(l.palette.background.paper,.8)},children:Object.entries(ve).map((([i,n])=>r.jsxs(o,{sx:{mb:2},children:[r.jsx(d,{variant:"subtitle2",sx:{mb:1,color:"text.secondary",fontSize:"0.8rem",textTransform:"uppercase"},children:b(i)}),r.jsx(o,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:n.map((i=>{const o=i,n=ye[o]||{description:"",color:l.palette.primary.main};return r.jsx(w,{title:n.description,arrow:!0,placement:"top",children:r.jsx(x,{label:be(o),color:e.includes(o)?"primary":"default",onClick:()=>!t&&(r=>{if(!t){const t=[...e],i=t.indexOf(r);-1===i?t.push(r):t.splice(i,1),0===t.length&&t.push(ae.Chat),s(t)}})(o),sx:{mr:.5,mb:.5,bgcolor:e.includes(o)?h(n.color,.2):"default",color:e.includes(o)?n.color:"text.secondary",borderColor:e.includes(o)?n.color:"divider","&:hover":{bgcolor:e.includes(o)?h(n.color,.3):h(l.palette.action.hover,.1)}},disabled:t,variant:e.includes(o)?"filled":"outlined"})},o)}))})]},i)))}),r.jsx(E,{children:t?"根据模型ID和提供商自动检测模型类型":"点击类型标签来添加或移除"}),r.jsx(je,{open:c,onClose:()=>p(!1),rules:u,onSave:async e=>{g(e);try{await q.saveSetting("modelTypeRules",e)}catch(r){console.error("[EnhancedModelTypeSelector] 保存模型类型规则失败:",r)}},modelId:n,provider:a})]})},Ce=({open:e,onClose:i,onSave:a,editModel:c})=>{const[p,x]=P.useState({id:"",name:"",provider:"openai",enabled:!0,isDefault:!1}),[h,u]=P.useState({}),[g,b]=P.useState([ae.Chat]),[j,f]=P.useState(!0),[C,k]=P.useState(""),[S,I]=P.useState(!1);P.useEffect((()=>{if(c){if(x(c),c.modelTypes)b(c.modelTypes),f(!1);else{const e=ge(c.id,c.provider);b(e),f(!0)}(async()=>{try{const e=await q.getModel(c.id);(null==e?void 0:e.avatar)&&k(e.avatar)}catch(e){console.error("[SimpleModelDialog] 加载模型头像失败:",e)}})()}}),[c,e]);const R=(e,r)=>{if(x({...p,[e]:r}),("id"===e||"provider"===e)&&j){const s={...p,[e]:r},t=ge(s.id,s.provider);b(t)}h[e]&&u({...h,[e]:""})};return r.jsxs(s,{open:e,onClose:i,maxWidth:"sm",fullWidth:!0,children:[r.jsx(t,{children:c?"编辑模型":"添加模型"}),r.jsxs(l,{children:[r.jsxs(o,{sx:{mb:3,mt:1},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2,p:2,bgcolor:"rgba(25, 118, 210, 0.08)",borderRadius:1,border:"1px solid rgba(25, 118, 210, 0.2)"},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(A,{src:C,sx:{width:48,height:48,mr:2,bgcolor:"#1677ff"},children:!C&&p.name.charAt(0)}),r.jsxs(o,{children:[r.jsx(d,{variant:"body2",fontWeight:"medium",children:"模型头像"}),r.jsx(d,{variant:"caption",color:"text.secondary",children:"为此模型设置自定义头像"})]})]}),r.jsx(w,{title:"设置头像",children:r.jsx(m,{color:"primary",onClick:()=>{I(!0)},size:"small",sx:{bgcolor:"rgba(25, 118, 210, 0.12)","&:hover":{bgcolor:"rgba(25, 118, 210, 0.2)"}},children:r.jsx(pe,{})})})]}),r.jsx(n,{fullWidth:!0,label:"模型名称",value:p.name,onChange:e=>R("name",e.target.value),margin:"normal",error:!!h.name,helperText:h.name,required:!0}),r.jsxs(z,{fullWidth:!0,margin:"normal",children:[r.jsx(M,{id:"provider-label",children:"提供商"}),r.jsxs(W,{labelId:"provider-label",value:p.provider,onChange:e=>R("provider",e.target.value),label:"提供商",children:[r.jsx(T,{value:"openai",children:"OpenAI"}),r.jsx(T,{value:"anthropic",children:"Anthropic"}),r.jsx(T,{value:"google",children:"Google"}),r.jsx(T,{value:"deepseek",children:"DeepSeek"}),r.jsx(T,{value:"siliconflow",children:"SiliconFlow"}),r.jsx(T,{value:"volcengine",children:"火山引擎"}),r.jsx(T,{value:"custom",children:"自定义"})]}),r.jsx(E,{children:"选择API提供商，可以与模型ID自由组合"})]}),r.jsx(n,{fullWidth:!0,label:"模型ID",value:p.id,onChange:e=>R("id",e.target.value),margin:"normal",helperText:"模型的唯一标识符，例如：gpt-4、claude-3-opus"}),r.jsx(fe,{modelTypes:g,onChange:b,autoDetect:j,onAutoDetectChange:()=>{const e=!j;if(f(e),e){const e=ge(p.id,p.provider);b(e)}},modelId:p.id,provider:p.provider})]}),r.jsx(le,{open:S,onClose:()=>{I(!1)},onSave:async e=>{if(k(e),p.id)try{const r={...await q.getModel(p.id),id:p.id,avatar:e,updatedAt:(new Date).toISOString()};await q.saveModel(p.id,r),p.id}catch(r){console.error("[SimpleModelDialog] 保存模型头像到数据库失败:",r)}},currentAvatar:C,title:"设置模型头像"})]}),r.jsxs(v,{children:[r.jsx(y,{onClick:i,children:"取消"}),r.jsx(y,{onClick:async()=>{if((()=>{const e={};return p.name.trim()||(e.name="请输入模型名称"),u(e),0===Object.keys(e).length})()){const r={...p,modelTypes:j?void 0:g,capabilities:{...p.capabilities,multimodal:g.includes(ae.Vision)}};if(c&&c.id!==p.id&&C)try{await q.saveModel(p.id,{id:p.id,avatar:C,updatedAt:(new Date).toISOString()})}catch(e){console.error("[SimpleModelDialog] 保存模型头像到数据库失败:",e)}try{const e={...r,avatar:C,updatedAt:(new Date).toISOString()};await q.saveModel(r.id,e),r.id}catch(e){console.error("[SimpleModelDialog] 保存模型配置到数据库失败:",e)}a(r),i()}},variant:"contained",color:"primary",children:"保存"})]})]})};function ke(e){const r=e.providerType||e.provider;switch(r){case"openai":return"OpenAI (标准)";case"openai-aisdk":return"OpenAI (AI SDK)";case"anthropic":return"Anthropic";case"gemini":return"Gemini";case"grok":return"xAI (Grok)";case"deepseek":return"DeepSeek";case"zhipu":return"智谱AI";case"siliconflow":return"硅基流动";case"volcengine":return"火山引擎";default:return r||"Unknown"}}const Se=e=>e.trim()?(e=>e.endsWith("#")?e.replace("#",""):(e=>{if(e.endsWith("/")||e.endsWith("volces.com/api/v3"))return e;let r=e.trim();return r.endsWith("/")&&(r=r.slice(0,-1)),`${r}/v1/`})(e)+"chat/completions")(e):"",we=()=>{const e=V(),c=Y(),{providerId:p}=B(),x=X((e=>e.settings.providers.find((e=>e.id===p)))),[u,C]=P.useState(""),[S,w]=P.useState(""),[E,z]=P.useState(!0),[M,W]=P.useState(!1),[T,F]=P.useState(!1),[U,_]=P.useState(!1),[G,H]=P.useState(void 0),[q,ie]=P.useState(""),[oe,ae]=P.useState(""),[le,de]=P.useState(""),[pe,xe]=P.useState(!1),[ue,ge]=P.useState(!1),[be,je]=P.useState(null),[ve,ye]=P.useState(null),[fe,we]=P.useState(!1),[Ie,Re]=P.useState(!1),[Ee,Ae]=P.useState(""),[ze,Me]=P.useState({}),[We,Te]=P.useState(""),[Oe,De]=P.useState(""),[Le,Pe]=P.useState(!1);P.useEffect((()=>{x&&(C(x.apiKey||""),w(x.baseUrl||""),z(x.isEnabled),Me(x.extraHeaders||{}))}),[x]);const Ve=()=>{e("/settings/default-model",{replace:!0})},Be=()=>!!x&&(S&&!re(S)?(de("请输入有效的URL"),!1):(c(J({id:x.id,updates:{apiKey:u,baseUrl:S.trim(),isEnabled:E,extraHeaders:ze}})),!0)),Fe=(e,r,s)=>{Me((t=>{const i={...t};return e!==r&&delete i[e],i[r]=s,i}))},Ue=e=>{if(x){const r=x.models.filter((r=>r.id!==e));c(J({id:x.id,updates:{models:r}}))}},_e=P.useCallback((e=>{if(x){const r={...e,provider:x.id,providerType:x.providerType,enabled:!0};if(x.models.some((r=>r.id===e.id)))return;const s=[...x.models,r];c(J({id:x.id,updates:{models:s}}))}}),[x,c]),Ge=P.useCallback((e=>{if(x&&e.length>0){const r=e.filter((e=>!x.models.some((r=>r.id===e.id)))).map((e=>({...e,provider:x.id,providerType:x.providerType,enabled:!0})));if(0===r.length)return;const s=[...x.models,...r];c(J({id:x.id,updates:{models:s}}))}}),[x,c]),He=P.useCallback((e=>{if(x&&e.length>0){const r=x.models.filter((r=>!e.includes(r.id)));c(J({id:x.id,updates:{models:r}}))}}),[x,c]);return P.useEffect((()=>{be&&be.message&&be.message.length>80&&we(!0)}),[be]),x?r.jsxs(o,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?h(e.palette.primary.main,.02):h(e.palette.background.default,.9)},children:[r.jsx(O,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(D,{children:[r.jsx(m,{edge:"start",onClick:Ve,"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(N,{})}),r.jsx(d,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:x.name}),r.jsx(y,{onClick:()=>{Be()&&e("/settings/default-model",{replace:!0})},sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"保存"})]})}),r.jsxs(o,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[r.jsxs(R,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)",mb:3},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:3},children:[r.jsx(A,{sx:{width:56,height:56,bgcolor:x.color||"#9333EA",fontSize:"1.5rem",mr:2,boxShadow:"0 4px 8px rgba(0,0,0,0.1)"},children:x.avatar}),r.jsxs(o,{children:[r.jsx(d,{variant:"h6",sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:x.name}),r.jsx(d,{variant:"body2",color:"text.secondary",children:x.isSystem?"系统供应商":`${ke({providerType:x.providerType})} API`})]}),r.jsx(o,{sx:{ml:"auto",display:"flex",gap:1},children:!x.isSystem&&r.jsxs(r.Fragment,{children:[r.jsx(m,{onClick:()=>{x&&(Ae(x.name),Re(!0))},sx:{bgcolor:e=>h(e.palette.info.main,.1),"&:hover":{bgcolor:e=>h(e.palette.info.main,.2)}},children:r.jsx(te,{color:"info"})}),r.jsx(m,{color:"error",onClick:()=>F(!0),sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)}},children:r.jsx($,{})})]})})]}),x.isSystem?r.jsxs(o,{sx:{p:2,bgcolor:e=>h(e.palette.info.main,.1),borderRadius:2,border:"1px solid",borderColor:e=>h(e.palette.info.main,.3)},children:[r.jsx(d,{variant:"body2",color:"info.main",sx:{fontWeight:500},children:"🧠 系统供应商说明"}),r.jsx(d,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"模型组合供应商是系统内置的虚拟供应商，它使用您配置的模型组合来提供服务。 模型组合中的各个模型会使用它们各自配置的 API 密钥和基础 URL。"})]}):r.jsxs(r.Fragment,{children:[r.jsx(a,{sx:{my:3}}),r.jsx(d,{variant:"subtitle1",sx:{mb:2,fontWeight:600,color:"text.primary"},children:"API配置"}),r.jsxs(o,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"启用状态"}),r.jsx(f,{control:r.jsx(I,{checked:E,onChange:e=>z(e.target.checked),color:"primary"}),label:E?"已启用":"已禁用"})]}),r.jsxs(o,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"API密钥"}),r.jsx(n,{fullWidth:!0,placeholder:"输入API密钥",value:u,onChange:e=>C(e.target.value),variant:"outlined",type:"password",size:"small",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}},slotProps:{input:{"aria-invalid":!1,"aria-describedby":"provider-settings-api-key-helper-text"},formHelperText:{id:"provider-settings-api-key-helper-text"}}})]}),r.jsxs(o,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"基础URL (可选)"}),r.jsx(n,{fullWidth:!0,placeholder:"输入基础URL，例如: https://tow.bt6.top",value:S,onChange:e=>{w(e.target.value),de("")},error:!!le,helperText:r.jsxs("span",{children:[le&&r.jsx("span",{style:{display:"block",color:"error.main",marginBottom:"4px",fontSize:"0.75rem"},children:le}),r.jsx("span",{style:{display:"block",color:"text.secondary",marginBottom:"4px",fontSize:"0.75rem"},children:"在URL末尾添加#可强制使用自定义格式，末尾添加/也可保持原格式"}),S&&($e=null==x?void 0:x.providerType,!["anthropic","gemini"].includes($e||""))&&r.jsxs("span",{style:{display:"inline-block",color:S.endsWith("#")||S.endsWith("/")?"#ed6c02":"#666",fontFamily:"monospace",fontSize:"0.7rem",backgroundColor:"rgba(0, 0, 0, 0.04)",padding:"2px 6px",borderRadius:"4px",marginTop:"4px"},children:[S.endsWith("#")?"强制使用: ":S.endsWith("/")?"保持原格式: ":"完整地址: ",Se(S)]})]}),variant:"outlined",size:"small",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})]}),r.jsxs(o,{sx:{mb:3},children:[r.jsx(d,{variant:"subtitle2",gutterBottom:!0,color:"text.secondary",children:"自定义请求头 (可选)"}),r.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:2},children:[r.jsx(y,{variant:"outlined",startIcon:r.jsx(K,{}),onClick:()=>Pe(!0),sx:{borderRadius:2,borderColor:e=>h(e.palette.secondary.main,.5),color:"secondary.main","&:hover":{borderColor:"secondary.main",bgcolor:e=>h(e.palette.secondary.main,.1)}},children:"配置请求头"}),Object.keys(ze).length>0&&r.jsxs(d,{variant:"caption",color:"text.secondary",children:["已配置 ",Object.keys(ze).length," 个请求头"]})]})]}),r.jsx(o,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:r.jsx(y,{variant:"outlined",startIcon:ue?r.jsx(i,{size:16}):r.jsx(he,{}),onClick:async()=>{if(x){if(!Be()&&le)return void je({success:!1,message:"请输入有效的基础URL"});ge(!0),je(null);try{const e={id:x.models.length>0?x.models[0].id:"gpt-3.5-turbo",name:x.name,provider:x.id,providerType:x.providerType,apiKey:u,baseUrl:S,enabled:!0},r=await Q(e);je(r?{success:!0,message:"连接成功！API配置有效。"}:{success:!1,message:"连接失败，请检查API密钥和基础URL是否正确。"})}catch(e){console.error("测试API连接时出错:",e),je({success:!1,message:`连接错误: ${e instanceof Error?e.message:String(e)}`})}finally{ge(!1)}}},disabled:ue||!u,sx:{borderRadius:2,borderColor:e=>h(e.palette.info.main,.5),color:"info.main","&:hover":{borderColor:"info.main",bgcolor:e=>h(e.palette.info.main,.1)}},children:ue?"测试中...":"测试连接"})})]})]}),r.jsxs(R,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:3},children:[r.jsx(d,{variant:"subtitle1",sx:{fontWeight:600,flex:1,color:"text.primary"},children:x.isSystem?"模型组合":"可用模型"}),x.isSystem?r.jsx(y,{variant:"outlined",startIcon:r.jsx(K,{}),onClick:()=>window.location.href="/settings/model-combo",sx:{borderRadius:2,borderColor:e=>h(e.palette.primary.main,.5),color:"primary.main","&:hover":{borderColor:"primary.main",bgcolor:e=>h(e.palette.primary.main,.1)}},children:"管理组合"}):r.jsxs(r.Fragment,{children:[r.jsx(d,{variant:"caption",color:"text.secondary",sx:{mr:2,display:{xs:"none",sm:"block"}},children:"点击✓测试单个模型"}),r.jsx(y,{variant:"outlined",startIcon:r.jsx(ce,{}),onClick:()=>{Be()?xe(!0):le&&alert("请输入有效的基础URL")},sx:{mr:2,borderRadius:2,borderColor:e=>h(e.palette.info.main,.5),color:"info.main","&:hover":{borderColor:"info.main",bgcolor:e=>h(e.palette.info.main,.1)}},children:"自动获取"}),r.jsx(y,{startIcon:r.jsx(se,{}),onClick:()=>W(!0),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"手动添加"})]})]}),r.jsxs(g,{sx:{width:"100%"},children:[x.models.map((e=>r.jsx(R,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",transition:"all 0.2s","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.05)",borderColor:e=>h(e.palette.primary.main,.3)}},children:r.jsx(b,{secondaryAction:x.isSystem?r.jsx(o,{children:r.jsx(m,{"aria-label":"edit-combo",onClick:()=>window.location.href="/settings/model-combo",sx:{bgcolor:e=>h(e.palette.primary.main,.1),"&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)}},children:r.jsx(K,{color:"primary"})})}):r.jsxs(o,{children:[r.jsx(m,{"aria-label":"test",onClick:()=>(async e=>{var r;if(x){ye(e.id),je(null);try{const s={...e,apiKey:u,baseUrl:S,enabled:!0},t=await Z({messages:[{role:"user",content:"这是一条API测试消息，请简短回复以验证连接。"}],modelId:s.id});t.success?je({success:!0,message:`模型 ${e.name} 连接成功!\n\n响应内容: "${null==(r=t.content)?void 0:r.substring(0,100)}${t.content&&t.content.length>100?"...":""}"`}):je({success:!1,message:`模型 ${e.name} 连接失败：${t.error||"未知错误"}`})}catch(s){console.error("测试模型连接时出错:",s),je({success:!1,message:`连接错误: ${s instanceof Error?s.message:String(s)}`})}finally{ye(null)}}})(e),disabled:null!==ve,sx:{mr:1,bgcolor:e=>h(e.palette.success.main,.1),"&:hover":{bgcolor:e=>h(e.palette.success.main,.2)}},children:ve===e.id?r.jsx(i,{size:16,color:"success"}):r.jsx(he,{color:"success"})}),r.jsx(m,{"aria-label":"edit",onClick:()=>(e=>{H(e),ie(e.name),ae(e.id),_(!0)})(e),sx:{mr:1,bgcolor:e=>h(e.palette.info.main,.1),"&:hover":{bgcolor:e=>h(e.palette.info.main,.2)}},children:r.jsx(te,{color:"info"})}),r.jsx(m,{"aria-label":"delete",onClick:()=>Ue(e.id),sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)}},children:r.jsx($,{color:"error"})})]}),children:r.jsxs(o,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[r.jsx(ne,{providerType:x.providerType,modelId:e.id,size:32,style:{marginRight:12}}),r.jsx(j,{primary:r.jsxs(o,{sx:{display:"flex",alignItems:"center"},children:[r.jsx(d,{variant:"subtitle2",fontWeight:600,children:e.name}),e.isDefault&&r.jsx(o,{sx:{ml:1,px:1,py:.2,borderRadius:1,fontSize:"0.7rem",fontWeight:600,bgcolor:e=>h(e.palette.success.main,.1),color:"success.main"},children:"默认"})]}),secondary:r.jsxs(d,{variant:"body2",color:"text.secondary",fontSize:"0.8rem",children:["ID: ",e.id]})})]})})},e.id))),0===x.models.length&&r.jsxs(o,{sx:{textAlign:"center",py:3},children:[r.jsx(d,{color:"text.secondary",children:x.isSystem?"尚未创建任何模型组合":"尚未添加任何模型"}),x.isSystem&&r.jsx(y,{variant:"outlined",startIcon:r.jsx(se,{}),onClick:()=>window.location.href="/settings/model-combo",sx:{mt:2},children:"创建模型组合"})]})]})]}),r.jsx(L,{open:null!==be&&!fe,autoHideDuration:6e3,onClose:()=>je(null),anchorOrigin:{vertical:"bottom",horizontal:"center"},action:r.jsx(y,{color:"inherit",size:"small",onClick:()=>we(!0),children:"查看详情"}),children:r.jsx(k,{onClose:()=>je(null),severity:(null==be?void 0:be.success)?"success":"error",variant:"filled",sx:{width:"100%"},children:(null==be?void 0:be.success)?"连接测试成功!":"连接测试失败"})}),r.jsxs(s,{open:fe,onClose:()=>we(!1),maxWidth:"md",slotProps:{paper:{sx:{width:"100%",maxWidth:500,borderRadius:2}}},children:[r.jsxs(t,{sx:{fontWeight:600,color:(null==be?void 0:be.success)?"success.main":"error.main",display:"flex",alignItems:"center"},children:[(null==be?void 0:be.success)?r.jsx(he,{sx:{mr:1}}):null,"API测试结果"]}),r.jsx(l,{children:r.jsx(d,{sx:{whiteSpace:"pre-wrap"},children:(null==be?void 0:be.message)||""})}),r.jsx(v,{sx:{p:2},children:r.jsx(y,{onClick:()=>we(!1),variant:"contained",color:(null==be?void 0:be.success)?"success":"primary",sx:{borderRadius:2},children:"确定"})})]})]}),r.jsxs(s,{open:M,onClose:()=>W(!1),children:[r.jsx(t,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"添加模型"}),r.jsxs(l,{children:[r.jsx(n,{autoFocus:!0,margin:"dense",label:"模型名称",placeholder:"例如: GPT-4o",type:"text",fullWidth:!0,variant:"outlined",value:q,onChange:e=>ie(e.target.value),sx:{mb:2,mt:2}}),r.jsx(n,{margin:"dense",label:"模型ID",placeholder:"例如: gpt-4o",type:"text",fullWidth:!0,variant:"outlined",value:oe,onChange:e=>ae(e.target.value)})]}),r.jsxs(v,{sx:{p:2},children:[r.jsx(y,{onClick:()=>W(!1),children:"取消"}),r.jsx(y,{onClick:()=>{if(x&&q&&oe){const e={id:oe,name:q,provider:x.id,providerType:x.providerType,enabled:!0,isDefault:!1},r=[...x.models,e];c(J({id:x.id,updates:{models:r}})),ie(""),ae(""),W(!1)}},disabled:!q||!oe,sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})]}),r.jsx(Ce,{open:U,onClose:()=>_(!1),onSave:e=>{if(x&&e){const r=x.models.filter((e=>!G||e.id!==G.id));r.push(e),c(J({id:x.id,updates:{models:r}})),H(void 0),_(!1)}},editModel:G}),r.jsxs(s,{open:T,onClose:()=>F(!1),children:[r.jsx(t,{fontWeight:600,children:"删除提供商"}),r.jsx(l,{children:r.jsxs(d,{children:["确定要删除 ",r.jsx("b",{children:x.name})," 提供商吗？这将同时删除所有相关的模型配置。"]})}),r.jsxs(v,{sx:{p:2},children:[r.jsx(y,{onClick:()=>F(!1),children:"取消"}),r.jsx(y,{onClick:()=>{x&&c(ee(x.id)),F(!1),e("/settings/default-model",{replace:!0})},color:"error",sx:{bgcolor:e=>h(e.palette.error.main,.1),"&:hover":{bgcolor:e=>h(e.palette.error.main,.2)},borderRadius:2},children:"删除"})]})]}),r.jsxs(s,{open:Ie,onClose:()=>Re(!1),children:[r.jsx(t,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"编辑供应商名称"}),r.jsx(l,{children:r.jsx(n,{autoFocus:!0,margin:"dense",label:"供应商名称",placeholder:"例如: 我的智谱AI",type:"text",fullWidth:!0,variant:"outlined",value:Ee,onChange:e=>Ae(e.target.value),sx:{mb:2,mt:2}})}),r.jsxs(v,{sx:{p:2},children:[r.jsx(y,{onClick:()=>Re(!1),children:"取消"}),r.jsx(y,{onClick:()=>{x&&Ee.trim()&&(c(J({id:x.id,updates:{name:Ee.trim()}})),Re(!1),Ae(""))},disabled:!Ee.trim(),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"保存"})]})]}),r.jsxs(s,{open:Le,onClose:()=>Pe(!1),maxWidth:"md",fullWidth:!0,children:[r.jsx(t,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"配置自定义请求头"}),r.jsxs(l,{children:[r.jsx(d,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"用于解决 CORS 问题或添加特殊认证头"}),r.jsxs(o,{sx:{mb:3,p:2,bgcolor:"grey.50",borderRadius:2},children:[r.jsx(d,{variant:"subtitle2",sx:{mb:2,fontWeight:600},children:"快速操作"}),r.jsxs(o,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[r.jsx(y,{size:"small",variant:"outlined",onClick:()=>{Me((e=>({...e,"x-stainless-timeout":"REMOVE"})))},sx:{fontSize:"0.75rem"},children:"禁用 x-stainless-timeout"}),r.jsx(y,{size:"small",variant:"outlined",onClick:()=>{Me((e=>({...e,"x-stainless-retry-count":"REMOVE"})))},sx:{fontSize:"0.75rem"},children:"禁用 x-stainless-retry-count"}),r.jsx(y,{size:"small",variant:"outlined",color:"error",onClick:()=>{Me((e=>({...e,"x-stainless-timeout":"REMOVE","x-stainless-retry-count":"REMOVE","x-stainless-arch":"REMOVE","x-stainless-lang":"REMOVE","x-stainless-os":"REMOVE","x-stainless-package-version":"REMOVE","x-stainless-runtime":"REMOVE","x-stainless-runtime-version":"REMOVE"})))},sx:{fontSize:"0.75rem"},children:"禁用所有 stainless 头部"})]}),r.jsx(d,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:1},children:'设置值为 "REMOVE" 可以禁用默认的请求头'})]}),Object.entries(ze).map((([e,s])=>r.jsxs(o,{sx:{display:"flex",alignItems:"center",mb:2,gap:1},children:[r.jsx(n,{size:"small",label:"请求头名称",value:e,onChange:r=>Fe(e,r.target.value,s),sx:{flex:1}}),r.jsx(n,{size:"small",label:"请求头值",value:s,onChange:r=>Fe(e,e,r.target.value),sx:{flex:1,"& .MuiInputBase-input":{color:"REMOVE"===s?"error.main":"inherit"}},helperText:"REMOVE"===s?"此头部将被禁用":"",slotProps:{formHelperText:{sx:{color:"error.main",fontSize:"0.7rem"}}}}),r.jsx(m,{onClick:()=>(e=>{Me((r=>{const s={...r};return delete s[e],s}))})(e),sx:{color:"error.main","&:hover":{bgcolor:e=>h(e.palette.error.main,.1)}},children:r.jsx($,{})})]},e))),r.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:1,mt:3,p:2,bgcolor:"grey.50",borderRadius:2},children:[r.jsx(n,{size:"small",label:"新请求头名称",placeholder:"例如: x-stainless-timeout",value:We,onChange:e=>Te(e.target.value),sx:{flex:1}}),r.jsx(n,{size:"small",label:"新请求头值",placeholder:"例如: 30000",value:Oe,onChange:e=>De(e.target.value),sx:{flex:1}}),r.jsx(y,{startIcon:r.jsx(se,{}),onClick:()=>{We.trim()&&Oe.trim()&&(Me((e=>({...e,[We.trim()]:Oe.trim()}))),Te(""),De(""))},disabled:!We.trim()||!Oe.trim(),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})]}),r.jsxs(v,{sx:{p:2},children:[r.jsx(y,{onClick:()=>Pe(!1),children:"取消"}),r.jsx(y,{onClick:()=>Pe(!1),sx:{bgcolor:e=>h(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>h(e.palette.primary.main,.2)},borderRadius:2},children:"确定"})]})]}),x&&r.jsx(me,{open:pe,onClose:()=>xe(!1),provider:x,onAddModel:_e,onAddModels:Ge,onRemoveModel:Ue,onRemoveModels:He,existingModels:x.models||[]})]}):r.jsxs(o,{sx:{p:3},children:[r.jsx(d,{children:"未找到该提供商，请返回设置页面"}),r.jsx(y,{onClick:Ve,children:"返回"})]});var $e};export{we as default};
