import{j as e,B as s,C as t,A as r,T as a,I as n,y as i,a as l,b as o,m as c,P as x,g as d,h,J as p,t as j,F as m,e as u,S as y,M as v,R as g,W as f,X as b,Y as S,L as C,w as I,x as w,U as A,n as k}from"./mui-vendor-hRDvsX89.js";import{u as W,e as E,f as P,r as T}from"./react-vendor-C9ilihHH.js";import{_ as z,A as B,$ as J,a0 as N,E as O,S as U}from"./index-BtK6VV6Z.js";import{S as D}from"./Save-Cs0ESqii.js";import{T as H}from"./PlayArrow-BNverJfm.js";import{H as M}from"./Http-BWYxyoAC.js";import{D as F}from"./Description-BUq_2jbX.js";import{F as G}from"./Folder-BJFskRPQ.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const R=()=>{const R=W(),{serverId:L}=E(),Y=P(),[_,K]=T.useState(null),[X,$]=T.useState([]),[q,Q]=T.useState([]),[V,Z]=T.useState([]),[ee,se]=T.useState(!1),[te,re]=T.useState(!1),[ae,ne]=T.useState({open:!1,message:"",severity:"success"});T.useEffect((()=>{var e;if(null==(e=Y.state)?void 0:e.server)K(Y.state.server),ie(Y.state.server);else if(L){const e=z.getServerById(L);e&&(K(e),ie(e))}}),[L,Y.state]);const ie=async e=>{if(e.isActive){se(!0);try{const[s,t,r]=await Promise.all([z.listTools(e),z.listPrompts(e),z.listResources(e)]);$(s),Q(t),Z(r)}catch(s){console.error("加载服务器数据失败:",s)}finally{se(!1)}}},le=e=>{switch(e){case"httpStream":return"#9c27b0";case"inMemory":return"#ff9800";default:return"#9e9e9e"}};return _?e.jsxs(s,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:"background.default"},children:[e.jsx(r,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider"},children:e.jsxs(a,{children:[e.jsx(n,{edge:"start",onClick:()=>{R("/settings/mcp-server")},"aria-label":"back",sx:{color:"primary.main"},children:e.jsx(B,{})}),e.jsx(i,{sx:{bgcolor:l(le(_.type),.1),color:le(_.type),mr:2,width:32,height:32},children:(s=>{switch(s){case"httpStream":return e.jsx(M,{});case"inMemory":return e.jsx(U,{});default:return e.jsx(J,{})}})(_.type)}),e.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600},children:_.name}),e.jsx(c,{startIcon:te?e.jsx(t,{size:16}):e.jsx(H,{}),onClick:async()=>{if(_){re(!0);try{const e=await z.testConnection(_);ne({open:!0,message:e?"连接测试成功":"连接测试失败",severity:e?"success":"error"}),e&&_.isActive&&await ie(_)}catch(e){ne({open:!0,message:"连接测试失败",severity:"error"})}finally{re(!1)}}},disabled:te,size:"small",sx:{mr:1},children:"测试"}),e.jsx(c,{startIcon:e.jsx(D,{}),onClick:async()=>{if(_)try{await z.updateServer(_),ne({open:!0,message:"保存成功",severity:"success"})}catch(e){ne({open:!0,message:"保存失败",severity:"error"})}},variant:"contained",size:"small",children:"保存"})]})}),e.jsxs(s,{sx:{flexGrow:1,overflow:"auto",mt:8,px:2,py:2},children:[e.jsxs(x,{sx:{p:3,mb:2},children:[e.jsxs(o,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(J,{}),"基本信息"]}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(d,{control:e.jsx(h,{checked:_.isActive,onChange:e=>(async e=>{if(_)try{await z.toggleServer(_.id,e),K({..._,isActive:e}),e?await ie({..._,isActive:e}):($([]),Q([]),Z([])),ne({open:!0,message:e?"服务器已启用":"服务器已停用",severity:"success"})}catch(s){ne({open:!0,message:"操作失败",severity:"error"})}})(e.target.checked),color:"primary"}),label:"启用服务器"}),_.isActive&&e.jsx(p,{label:"运行中",size:"small",color:"success",variant:"outlined",sx:{ml:2}})]}),e.jsx(j,{fullWidth:!0,label:"服务器名称",value:_.name,onChange:e=>K({..._,name:e.target.value}),sx:{mb:2}}),e.jsxs(m,{fullWidth:!0,sx:{mb:2},children:[e.jsx(u,{children:"服务器类型"}),e.jsxs(y,{value:_.type,label:"服务器类型",onChange:e=>K({..._,type:e.target.value}),children:[e.jsx(v,{value:"httpStream",children:"HTTP Stream (支持SSE+HTTP)"}),e.jsx(v,{value:"inMemory",children:"内存服务器"})]})]}),"httpStream"===_.type&&e.jsx(j,{fullWidth:!0,label:"服务器 URL",value:_.baseUrl||"",onChange:e=>K({..._,baseUrl:e.target.value}),placeholder:"https://example.com/mcp",sx:{mb:2}}),e.jsx(j,{fullWidth:!0,label:"描述",value:_.description||"",onChange:e=>K({..._,description:e.target.value}),multiline:!0,rows:2,sx:{mb:2}}),e.jsx(j,{fullWidth:!0,label:"超时时间（秒）",type:"number",value:_.timeout||60,onChange:e=>K({..._,timeout:parseInt(e.target.value)||60}),inputProps:{min:1,max:300},sx:{mb:2}}),"httpStream"===_.type&&e.jsx(d,{control:e.jsx(g,{checked:!0===_.enableSSE,onChange:e=>K({..._,enableSSE:e.target.checked})}),label:"启用SSE流（Server-Sent Events）"})]}),e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(O,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(N,{}),"高级设置"]})}),e.jsxs(S,{children:[e.jsx(j,{fullWidth:!0,label:"请求头（JSON 格式）",value:JSON.stringify(_.headers||{},null,2),onChange:e=>{try{const s=JSON.parse(e.target.value);K({..._,headers:s})}catch(s){}},multiline:!0,rows:4,sx:{mb:2},placeholder:'{\\n  "Authorization": "Bearer token",\\n  "Content-Type": "application/json"\\n}'}),e.jsx(j,{fullWidth:!0,label:"环境变量（JSON 格式）",value:JSON.stringify(_.env||{},null,2),onChange:e=>{try{const s=JSON.parse(e.target.value);K({..._,env:s})}catch(s){}},multiline:!0,rows:4,sx:{mb:2},placeholder:'{\\n  "API_KEY": "your-api-key",\\n  "DEBUG": "true"\\n}'}),e.jsx(j,{fullWidth:!0,label:"参数（每行一个）",value:(_.args||[]).join("\n"),onChange:e=>{const s=e.target.value.split("\n").filter((e=>e.trim()));K({..._,args:s})},multiline:!0,rows:3,placeholder:"--verbose\\n--config=/path/to/config"})]})]}),_.isActive&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(O,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(N,{}),"可用工具 (",X.length,")"]})}),e.jsx(S,{children:ee?e.jsx(s,{sx:{display:"flex",justifyContent:"center",py:2},children:e.jsx(t,{})}):0===X.length?e.jsx(o,{color:"text.secondary",sx:{textAlign:"center",py:2},children:"暂无可用工具"}):e.jsx(C,{children:X.map(((s,t)=>e.jsx(I,{divider:!0,children:e.jsx(w,{primary:s.name,secondary:s.description||"无描述"})},t)))})})]}),_.isActive&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(O,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(F,{}),"可用提示词 (",q.length,")"]})}),e.jsx(S,{children:ee?e.jsx(s,{sx:{display:"flex",justifyContent:"center",py:2},children:e.jsx(t,{})}):0===q.length?e.jsx(o,{color:"text.secondary",sx:{textAlign:"center",py:2},children:"暂无可用提示词"}):e.jsx(C,{children:q.map(((s,t)=>e.jsx(I,{divider:!0,children:e.jsx(w,{primary:s.name,secondary:s.description||"无描述"})},t)))})})]}),_.isActive&&e.jsxs(f,{sx:{mb:2},children:[e.jsx(b,{expandIcon:e.jsx(O,{}),children:e.jsxs(o,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(G,{}),"可用资源 (",V.length,")"]})}),e.jsx(S,{children:ee?e.jsx(s,{sx:{display:"flex",justifyContent:"center",py:2},children:e.jsx(t,{})}):0===V.length?e.jsx(o,{color:"text.secondary",sx:{textAlign:"center",py:2},children:"暂无可用资源"}):e.jsx(C,{children:V.map(((t,r)=>e.jsx(I,{divider:!0,children:e.jsx(w,{primary:t.name,secondary:e.jsxs(s,{children:[e.jsx(o,{variant:"body2",color:"text.secondary",children:t.description||"无描述"}),e.jsxs(o,{variant:"caption",color:"text.secondary",children:["URI: ",t.uri]})]})})},r)))})})]})]}),e.jsx(A,{open:ae.open,autoHideDuration:3e3,onClose:()=>ne({...ae,open:!1}),children:e.jsx(k,{severity:ae.severity,onClose:()=>ne({...ae,open:!1}),children:ae.message})})]}):e.jsx(s,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:e.jsx(t,{})})};export{R as default};
