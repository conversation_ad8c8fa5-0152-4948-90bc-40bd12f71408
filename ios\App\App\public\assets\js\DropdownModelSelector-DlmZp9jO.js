import{z as e,j as r,F as t,S as o,V as a,y as n,M as i,B as l,b as s}from"./mui-vendor-hRDvsX89.js";import{a as d}from"./react-vendor-C9ilihHH.js";import{d as p}from"./index-BtK6VV6Z.js";const c=({selectedModel:c,availableModels:x,handleModelSelect:m})=>{const u=e(),g=p((e=>e.settings.providers||[])),b=d.useCallback((e=>{const r=g.find((r=>r.id===e));return r?r.name:e}),[g]),f=d.useCallback((e=>g.find((r=>r.id===e))),[g]),h=d.useMemo((()=>{const e={};x.forEach((r=>{const t=r.provider||r.providerType||"unknown";e[t]||(e[t]=[]),e[t].push(r)}));const r=Object.keys(e).sort(((e,r)=>{const t=g.findIndex((r=>r.id===e)),o=g.findIndex((e=>e.id===r));if(-1!==t&&-1!==o)return t-o;if(-1!==t)return-1;if(-1!==o)return 1;const a=b(e),n=b(r);return a.localeCompare(n)}));return{groups:e,sortedGroups:r}}),[x,b,g]),v=d.useCallback((e=>`${e.id}---${e.provider||""}`),[]),y=d.useCallback((()=>c?v(c):""),[c,v]);return r.jsx(t,{variant:"outlined",size:"small",sx:{minWidth:180,mr:1,"& .MuiOutlinedInput-root":{borderRadius:"16px",fontSize:"0.9rem",bgcolor:"dark"===u.palette.mode?"rgba(255, 255, 255, 0.03)":"#ffffff","& .MuiOutlinedInput-notchedOutline":{border:"1px solid "+("dark"===u.palette.mode?"rgba(255, 255, 255, 0.08)":"rgba(0, 0, 0, 0.08)")},"&:hover .MuiOutlinedInput-notchedOutline":{border:"1px solid "+("dark"===u.palette.mode?"rgba(255, 255, 255, 0.12)":"rgba(0, 0, 0, 0.12)")},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{border:"1px solid "+("dark"===u.palette.mode?"rgba(255, 255, 255, 0.15)":"rgba(0, 0, 0, 0.15)")},"&:hover":{bgcolor:"dark"===u.palette.mode?"rgba(255, 255, 255, 0.05)":"#ffffff"}}},children:r.jsx(o,{labelId:"model-select-label",id:"model-select",value:y(),onChange:e=>{const r=e.target.value;if(r)try{const[e,t]=r.split("---"),o=x.find((r=>r.id===e&&(r.provider||"")===t));o?setTimeout((()=>{m(o)}),0):console.error("未找到匹配的模型:",e,t)}catch(t){console.error("处理模型选择时出错:",t)}},displayEmpty:!0,renderValue:e=>{if(!e||!c)return r.jsx(s,{variant:"body2",sx:{color:u.palette.text.secondary,fontSize:"0.875rem"},children:"选择模型"});const t=(e=>{const r=.875;if(e.length<=18)return"0.875rem";const t=e.length/18,o=r*Math.max(1/Math.sqrt(t),.65/r);return`${Math.max(o,.65)}rem`})(c.name),o=b(c.provider);return r.jsxs(l,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",width:"100%",py:.5},children:[r.jsx(s,{variant:"body2",sx:{fontWeight:500,fontSize:t,color:u.palette.text.primary,maxWidth:"150px",transition:"font-size 0.2s ease",wordBreak:"keep-all",lineHeight:1.1},title:c.name,children:c.name}),r.jsx(s,{variant:"caption",sx:{fontSize:"0.7rem",color:u.palette.text.secondary,lineHeight:1,mt:.25,maxWidth:"150px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},title:o,children:o})]})},sx:{bgcolor:"transparent",border:"none","& .MuiSelect-select":{padding:"10px 32px 10px 12px",bgcolor:"transparent",border:"none","&:focus":{bgcolor:"transparent"}},"& .MuiSelect-icon":{color:u.palette.text.secondary},"&:before":{display:"none"},"&:after":{display:"none"},"&:focus":{bgcolor:"transparent"},"&:hover":{bgcolor:"transparent"}},MenuProps:{PaperProps:{sx:{maxHeight:"70vh",minHeight:300,mt:.5,boxShadow:"0 4px 20px rgba(0, 0, 0, 0.1)",bgcolor:"dark"===u.palette.mode?"#2A2A2A":"#FFFFFF","& .MuiList-root":{py:0,bgcolor:"transparent"}}},MenuListProps:{sx:{py:0,bgcolor:"transparent"}}},children:h.sortedGroups.map((e=>{const t=b(e),o=f(e),d=h.groups[e];return[r.jsxs(a,{sx:{bgcolor:"dark"===u.palette.mode?"#3A3A3A":"#F5F5F5",fontWeight:600,fontSize:"0.8rem",py:.75,px:2,minHeight:32,display:"flex",alignItems:"center",gap:.75,position:"sticky",top:0,zIndex:10,borderBottom:"1px solid "+("dark"===u.palette.mode?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"),"&:not(:first-of-type)":{borderTop:"1px solid "+("dark"===u.palette.mode?"rgba(255, 255, 255, 0.05)":"rgba(0, 0, 0, 0.05)")}},children:[r.jsx(n,{sx:{width:16,height:16,bgcolor:(null==o?void 0:o.color)||u.palette.primary.main,fontSize:"0.65rem"},children:(null==o?void 0:o.avatar)||t[0]}),t]},`header-${e}`),...d.map((e=>{const t=v(e);return r.jsx(i,{value:t,sx:{py:1,pl:3,pr:2,minHeight:40,bgcolor:"transparent","&:hover":{bgcolor:"dark"===u.palette.mode?"rgba(255, 255, 255, 0.08)":"rgba(0, 0, 0, 0.04)"},"&.Mui-selected":{bgcolor:"dark"===u.palette.mode?"rgba(33, 150, 243, 0.2)":"rgba(33, 150, 243, 0.1)","&:hover":{bgcolor:"dark"===u.palette.mode?"rgba(33, 150, 243, 0.3)":"rgba(33, 150, 243, 0.15)"}}},children:r.jsx(l,{sx:{display:"flex",alignItems:"center",width:"100%"},children:r.jsxs(l,{sx:{flex:1},children:[r.jsx(s,{variant:"body2",sx:{fontWeight:500,fontSize:"0.875rem",lineHeight:1.3},children:e.name}),e.description&&r.jsx(s,{variant:"caption",color:"text.secondary",sx:{display:"block",fontSize:"0.75rem",lineHeight:1.2,mt:.25},children:e.description})]})})},t)}))]})).flat()})})};export{c as D};
