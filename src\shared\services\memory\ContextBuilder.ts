/**
 * 上下文构建器
 * 基于记忆增强对话上下文
 */

import type { MemoryRecord, MemoryContext } from '../../types/internalMemory';
import { SemanticSearchService } from './SemanticSearchService';
import { getMemoryConfig, MEMORY_CATEGORY_LABELS } from '../../config/internalMemoryConfig';
import LoggerService from '../LoggerService';

/**
 * 上下文构建器类
 * 将相关记忆整合到对话上下文中
 */
export class ContextBuilder {
  private static instance: ContextBuilder;
  private semanticSearch: SemanticSearchService;

  private constructor() {
    this.semanticSearch = SemanticSearchService.getInstance();
  }

  /**
   * 获取当前配置（实时获取最新配置）
   */
  private getConfig() {
    return getMemoryConfig();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): ContextBuilder {
    if (!ContextBuilder.instance) {
      ContextBuilder.instance = new ContextBuilder();
    }
    return ContextBuilder.instance;
  }

  /**
   * 构建增强上下文
   */
  public async buildEnhancedContext(
    userMessage: string,
    userId: string,
    options: {
      assistantId?: string;
      includeCategories?: string[];
      maxMemories?: number;
      contextStyle?: 'detailed' | 'concise' | 'minimal';
    } = {}
  ): Promise<MemoryContext> {
    try {
      const config = this.getConfig();
      if (!config.enabled || !userMessage.trim()) {
        return {
          relevantMemories: [],
          enhancedPrompt: userMessage
        };
      }

      const {
        assistantId,
        includeCategories,
        maxMemories = config.search.maxResults,
        contextStyle = 'detailed'
      } = options;

      // 🔧 修复：为记忆搜索添加错误处理，防止阻塞消息发送
      let relevantMemories: any[] = [];
      try {
        // 搜索相关记忆
        relevantMemories = await this.semanticSearch.findRelevantMemories(
          userMessage,
          userId,
          maxMemories
        );
      } catch (searchError) {
        LoggerService.log('WARN', 'Memory search failed, continuing without memory context', {
          error: searchError,
          userId,
          userMessage: userMessage.substring(0, 50)
        });
        // 搜索失败时返回空记忆列表，不阻塞消息发送
        relevantMemories = [];
      }

      // 过滤分类（如果指定）
      const filteredMemories = includeCategories 
        ? relevantMemories.filter(memory => includeCategories.includes(memory.category))
        : relevantMemories;

      if (filteredMemories.length === 0) {
        LoggerService.log('INFO', 'No relevant memories found for context building', {
          userId,
          userMessage: userMessage.substring(0, 50)
        });
        return {
          relevantMemories: [],
          enhancedPrompt: userMessage
        };
      }

      // 构建增强提示词
      const enhancedPrompt = this.buildContextPrompt(
        userMessage,
        filteredMemories,
        contextStyle,
        assistantId
      );

      LoggerService.log('INFO', 'Enhanced context built successfully', {
        userId,
        memoriesUsed: filteredMemories.length,
        contextStyle,
        originalLength: userMessage.length,
        enhancedLength: enhancedPrompt.length
      });

      return {
        relevantMemories: filteredMemories,
        enhancedPrompt
      };
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to build enhanced context', { 
        error, 
        userId, 
        userMessage: userMessage.substring(0, 50) 
      });
      
      // 降级：返回原始消息
      return {
        relevantMemories: [],
        enhancedPrompt: userMessage
      };
    }
  }

  /**
   * 构建上下文提示词
   */
  private buildContextPrompt(
    userMessage: string,
    memories: MemoryRecord[],
    style: 'detailed' | 'concise' | 'minimal',
    assistantId?: string
  ): string {
    if (memories.length === 0) {
      return userMessage;
    }

    const memoryContext = this.formatMemoryContext(memories, style);
    
    switch (style) {
      case 'minimal':
        return `${memoryContext}\n\n${userMessage}`;
        
      case 'concise':
        return `用户背景：${memoryContext}\n\n用户请求：${userMessage}`;
        
      case 'detailed':
      default:
        return this.buildDetailedContext(userMessage, memoryContext, assistantId);
    }
  }

  /**
   * 构建详细上下文
   */
  private buildDetailedContext(
    userMessage: string,
    memoryContext: string,
    assistantId?: string
  ): string {
    let context = `用户背景信息：\n${memoryContext}\n\n`;
    
    if (assistantId) {
      context += `请根据用户的背景信息和偏好，提供个性化的回答。\n\n`;
    } else {
      context += `请根据用户的背景信息和偏好，提供相关且有用的回答。\n\n`;
    }
    
    context += `当前用户请求：${userMessage}`;
    
    return context;
  }

  /**
   * 格式化记忆上下文
   */
  private formatMemoryContext(memories: MemoryRecord[], style: 'detailed' | 'concise' | 'minimal'): string {
    if (memories.length === 0) {
      return '';
    }

    // 按分类组织记忆
    const categorizedMemories = this.categorizeMemories(memories);
    
    switch (style) {
      case 'minimal':
        return this.formatMinimalContext(categorizedMemories);
        
      case 'concise':
        return this.formatConciseContext(categorizedMemories);
        
      case 'detailed':
      default:
        return this.formatDetailedContext(categorizedMemories);
    }
  }

  /**
   * 按分类组织记忆
   */
  private categorizeMemories(memories: MemoryRecord[]): Record<string, MemoryRecord[]> {
    const categorized: Record<string, MemoryRecord[]> = {};
    
    for (const memory of memories) {
      if (!categorized[memory.category]) {
        categorized[memory.category] = [];
      }
      categorized[memory.category].push(memory);
    }
    
    // 按重要性排序每个分类中的记忆
    for (const category in categorized) {
      categorized[category].sort((a, b) => b.importance - a.importance);
    }
    
    return categorized;
  }

  /**
   * 格式化最简上下文
   */
  private formatMinimalContext(categorizedMemories: Record<string, MemoryRecord[]>): string {
    const items: string[] = [];
    
    for (const [, memories] of Object.entries(categorizedMemories)) {
      for (const memory of memories.slice(0, 2)) { // 每个分类最多2条
        items.push(memory.content);
      }
    }
    
    return items.join('; ');
  }

  /**
   * 格式化简洁上下文
   */
  private formatConciseContext(categorizedMemories: Record<string, MemoryRecord[]>): string {
    const sections: string[] = [];
    
    for (const [category, memories] of Object.entries(categorizedMemories)) {
      const categoryLabel = MEMORY_CATEGORY_LABELS[category as keyof typeof MEMORY_CATEGORY_LABELS] || category;
      const items = memories.slice(0, 3).map(memory => `• ${memory.content}`);
      
      if (items.length > 0) {
        sections.push(`${categoryLabel}：\n${items.join('\n')}`);
      }
    }
    
    return sections.join('\n\n');
  }

  /**
   * 格式化详细上下文
   */
  private formatDetailedContext(categorizedMemories: Record<string, MemoryRecord[]>): string {
    const sections: string[] = [];
    
    // 按优先级排序分类
    const categoryPriority: Record<string, number> = {
      'preference': 1,
      'background': 2,
      'skill': 3,
      'habit': 4,
      'plan': 5
    };

    const sortedCategories = Object.entries(categorizedMemories)
      .sort(([a], [b]) => (categoryPriority[a] || 99) - (categoryPriority[b] || 99));
    
    for (const [category, memories] of sortedCategories) {
      const categoryLabel = MEMORY_CATEGORY_LABELS[category as keyof typeof MEMORY_CATEGORY_LABELS] || category;
      const items = memories.map(memory => {
        const importance = memory.importance >= 8 ? '（重要）' : '';
        return `• ${memory.content}${importance}`;
      });
      
      if (items.length > 0) {
        sections.push(`【${categoryLabel}】\n${items.join('\n')}`);
      }
    }
    
    return sections.join('\n\n');
  }

  /**
   * 构建记忆摘要
   */
  public async buildMemorySummary(
    userId: string,
    options: {
      maxLength?: number;
      includeStats?: boolean;
    } = {}
  ): Promise<string> {
    try {
      const { maxLength = 500, includeStats = true } = options;
      
      const memories = await this.semanticSearch.findRelevantMemories('', userId, 20);
      
      if (memories.length === 0) {
        return '暂无用户记忆信息。';
      }
      
      const categorized = this.categorizeMemories(memories);
      let summary = '';
      
      // 构建分类摘要
      for (const [category, categoryMemories] of Object.entries(categorized)) {
        const categoryLabel = MEMORY_CATEGORY_LABELS[category as keyof typeof MEMORY_CATEGORY_LABELS] || category;
        const topMemories = categoryMemories.slice(0, 2);
        
        if (topMemories.length > 0) {
          summary += `${categoryLabel}：${topMemories.map(m => m.content).join('；')}\n`;
        }
      }
      
      // 添加统计信息
      if (includeStats) {
        summary += `\n总计 ${memories.length} 条记忆信息。`;
      }
      
      // 截断到指定长度
      if (summary.length > maxLength) {
        summary = summary.substring(0, maxLength - 3) + '...';
      }
      
      return summary;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to build memory summary', { error, userId });
      return '无法获取用户记忆摘要。';
    }
  }

  /**
   * 检查是否需要记忆增强
   */
  public shouldEnhanceWithMemory(
    userMessage: string,
    _userId: string
  ): boolean {
    if (!this.getConfig().enabled) {
      return false;
    }
    
    // 检查消息长度
    if (userMessage.length < 10) {
      return false;
    }
    
    // 检查是否包含个人化关键词
    const personalKeywords = [
      '我的', '我喜欢', '我不喜欢', '我想', '我需要', '我希望',
      '帮我', '为我', '给我', '推荐', '建议', '记住'
    ];
    
    const messageText = userMessage.toLowerCase();
    return personalKeywords.some(keyword => messageText.includes(keyword));
  }
}
