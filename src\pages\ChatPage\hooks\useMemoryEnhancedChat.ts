/**
 * 记忆增强聊天钩子
 * 集成记忆功能到聊天流程中
 */

import { useState, useCallback, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import type { Message, Model } from '../../../shared/types';
import type { MemoryRecord } from '../../../shared/types/internalMemory';
import {
  selectMemoryConfig,
  selectMemories,
  loadUserMemories,
  setCurrentUserId
} from '../../../shared/store/slices/internalMemorySlice';
import { MemoryEnhancedMessageService } from '../../../shared/services/memory/MemoryEnhancedMessageService';
import { ContextBuilder } from '../../../shared/services/memory/ContextBuilder';
import { updateMemoryConfig } from '../../../shared/config/internalMemoryConfig';
import LoggerService from '../../../shared/services/LoggerService';
import {
  selectMessagesForTopic
} from '../../../shared/store/selectors/messageSelectors';
import type { RootState } from '../../../shared/store';
import { dexieStorage } from '../../../shared/services/DexieStorageService';

interface UseMemoryEnhancedChatProps {
  currentTopic: any;
  selectedModel: Model | null;
  originalHandleSendMessage: (content: string, images?: any[], toolsEnabled?: boolean, files?: any[]) => Promise<any>;
}

interface MemoryEnhancedChatState {
  isMemoryEnabled: boolean;
  currentMemories: MemoryRecord[];
  memoryStats: {
    memoriesUsed: number;
    memoriesExtracted: number;
  };
  isProcessingMemory: boolean;
}

export const useMemoryEnhancedChat = ({
  currentTopic,
  selectedModel,
  originalHandleSendMessage
}: UseMemoryEnhancedChatProps) => {
  const dispatch = useDispatch();

  // Redux状态
  const memoryConfig = useSelector(selectMemoryConfig);
  const userMemories = useSelector(selectMemories);

  // 获取真实的消息数据
  const topicMessages = useSelector((state: RootState) =>
    currentTopic?.id ? selectMessagesForTopic(state, currentTopic.id) : []
  );
  
  // 本地状态
  const [memoryState, setMemoryState] = useState<MemoryEnhancedChatState>({
    isMemoryEnabled: memoryConfig.enabled,
    currentMemories: [],
    memoryStats: {
      memoriesUsed: 0,
      memoriesExtracted: 0
    },
    isProcessingMemory: false
  });

  // 服务实例
  const memoryService = MemoryEnhancedMessageService.getInstance();
  const contextBuilder = ContextBuilder.getInstance();

  // 模拟用户ID（在实际应用中应该从认证系统获取）
  const userId = 'demo-user';

  // 初始化记忆系统
  useEffect(() => {
    if (memoryConfig.enabled) {
      dispatch(setCurrentUserId(userId));
      dispatch(loadUserMemories(userId) as any);
    }
  }, [dispatch, userId, memoryConfig.enabled]);

  // 更新记忆服务配置
  useEffect(() => {
    console.log('🔧 更新记忆服务配置:', memoryConfig);

    // 更新全局配置（所有服务都会实时获取最新配置）
    updateMemoryConfig(memoryConfig);
  }, [memoryConfig]);

  // 更新记忆启用状态
  useEffect(() => {
    setMemoryState(prev => ({
      ...prev,
      isMemoryEnabled: memoryConfig.enabled
    }));
  }, [memoryConfig.enabled]);

  /**
   * 记忆增强的消息发送处理
   */
  /**
   * 获取对话历史 - 重构版本，从消息块中获取完整内容
   */
  const getConversationHistory = useCallback(async (): Promise<Message[]> => {
    try {
      if (!currentTopic?.id) {
        console.log('🚫 没有当前话题ID，返回空历史');
        return [];
      }

      // 使用 Redux 中的真实消息数据
      const messages = topicMessages || [];

      console.log('📋 开始获取消息的完整内容...');

      // 为每个消息获取完整的内容（从消息块中）
      const messagesWithContent = await Promise.all(
        messages.map(async (msg: any) => {
          let content = msg.content || '';

          // 如果消息没有直接的 content，从块中获取
          if (!content && msg.blocks && msg.blocks.length > 0) {
            try {
              // 获取主文本块（通常是第一个块）
              const mainBlockId = msg.blocks[0];
              const block = await dexieStorage.getMessageBlock(mainBlockId);

              if (block && (block as any).content) {
                content = (block as any).content;
              }
            } catch (error) {
              console.warn(`获取消息 ${msg.id} 的块内容失败:`, error);
            }
          }

          return {
            ...msg,
            content: content || `[消息 ${msg.id} 内容为空]`
          };
        })
      );

      console.log('📋 获取完整内容后的消息信息:', {
        topicId: currentTopic.id,
        topicTitle: currentTopic.title,
        totalMessages: messagesWithContent.length,
        messagesPreview: messagesWithContent.slice(0, 3).map((m: any) => ({
          id: m.id,
          role: m.role,
          content: m.content?.substring(0, 50) || '[无内容]',
          hasContent: !!m.content,
          hasBlocks: !!m.blocks,
          blocksLength: m.blocks?.length || 0
        }))
      });

      // 只返回最近的消息（避免处理过多历史）
      const recentMessages = messagesWithContent.slice(-10);

      console.log('📚 最终对话历史详情:', {
        totalMessages: messagesWithContent.length,
        recentMessages: recentMessages.length,
        messageRoles: recentMessages.map((m: any) => m.role),
        messageContents: recentMessages.map((m: any) => ({
          id: m.id,
          role: m.role,
          contentPreview: m.content?.substring(0, 50) || '[无内容]',
          contentLength: m.content?.length || 0
        })),
        isNewConversation: messagesWithContent.length <= 2
      });

      LoggerService.log('INFO', 'Retrieved conversation history with full content', {
        topicId: currentTopic.id,
        totalMessages: messagesWithContent.length,
        recentMessages: recentMessages.length
      });

      return recentMessages;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get conversation history', { error });
      return [];
    }
  }, [currentTopic?.id, topicMessages]);

  /**
   * 更新当前使用的记忆
   */
  const updateCurrentMemories = useCallback(async (query: string) => {
    try {
      if (!memoryConfig.enabled) {
        return;
      }

      const memoryContext = await contextBuilder.buildEnhancedContext(
        query,
        userId,
        {
          assistantId: currentTopic?.assistantId
        }
      );

      setMemoryState(prev => ({
        ...prev,
        currentMemories: memoryContext.relevantMemories
      }));
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to update current memories', { error });
    }
  }, [memoryConfig.enabled, userId, currentTopic?.assistantId, contextBuilder]);

  // 后台记忆处理函数 - 不阻塞消息发送
  const processMemoryInBackground = useCallback(async (content: string) => {
    // 记忆提取开始（移除详细日志）

    if (!memoryConfig.enabled || !currentTopic || !selectedModel) {
      console.log('❌ 记忆提取条件不满足，跳过处理');
      return;
    }

    // 使用 setTimeout 确保不阻塞主线程
    setTimeout(async () => {
      try {
        setMemoryState(prev => ({ ...prev, isProcessingMemory: true }));

        // 获取对话历史
        const conversationHistory = await getConversationHistory();
        console.log('📚 获取到对话历史:', conversationHistory.length, '条消息');

        // 检查是否达到最少对话轮数
        const minConversationLength = memoryConfig.extraction.minConversationLength || 3;
        if (conversationHistory.length < minConversationLength) {
          console.log(`⏳ 对话轮数不足 (${conversationHistory.length}/${minConversationLength})，跳过记忆提取`);
          return;
        }

        console.log('✅ 开始调用记忆增强服务...');

        // 使用记忆增强的消息服务
        const result = await memoryService.processMessageWithMemory(
          content,
          userId,
          selectedModel,
          {
            assistantId: currentTopic.assistantId,
            topicId: currentTopic.id,
            conversationHistory,
            onChunk: (chunk: string) => {
              // 这里可以处理流式响应
              console.log('Memory enhanced chunk:', chunk);
            }
          }
        );

        console.log('🎯 记忆处理结果:', result);

        // 更新记忆统计
        setMemoryState(prev => ({
          ...prev,
          memoryStats: {
            memoriesUsed: result.memoriesUsed,
            memoriesExtracted: result.memoriesExtracted
          }
        }));

        // 如果使用了记忆，更新当前记忆列表
        if (result.memoriesUsed > 0) {
          await updateCurrentMemories(content);
        }

        LoggerService.log('INFO', 'Memory enhanced message processed', {
          userId,
          topicId: currentTopic.id,
          memoriesUsed: result.memoriesUsed,
          memoriesExtracted: result.memoriesExtracted
        });
      } catch (error) {
        LoggerService.log('ERROR', 'Memory enhanced message processing failed', { error });
      } finally {
        setMemoryState(prev => ({ ...prev, isProcessingMemory: false }));
      }
    }, 100); // 100ms 延迟，确保消息先发送
  }, [
    memoryConfig.enabled,
    currentTopic?.id,
    currentTopic?.assistantId,
    userId,
    selectedModel,
    memoryService,
    getConversationHistory,
    updateCurrentMemories
  ]);

  // 构建记忆增强的系统提示词
  const buildMemoryEnhancedSystemPrompt = useCallback((originalSystemPrompt: string = '') => {
    if (!memoryConfig.enabled || !userMemories || userMemories.length === 0) {
      return originalSystemPrompt;
    }

    // 按类别整理记忆
    const memoriesByCategory = userMemories.reduce((acc: any, memory: any) => {
      const category = memory.category || 'general';
      if (!acc[category]) acc[category] = [];
      acc[category].push(memory.content);
      return acc;
    }, {});

    // 构建记忆部分
    let memorySection = '\n\n=== 用户记忆信息 ===\n';
    memorySection += '以下是关于用户的重要记忆信息，请在回答时自然地考虑这些信息，无需明确提及这是"记忆"：\n\n';

    // 分类别添加记忆
    const categoryLabels: any = {
      preference: '偏好设置',
      background: '背景信息',
      skill: '技能专长',
      habit: '使用习惯',
      plan: '计划目标',
      general: '其他信息'
    };

    Object.entries(memoriesByCategory).forEach(([category, memoryList]: [string, any]) => {
      const label = categoryLabels[category] || category;
      memorySection += `【${label}】\n`;
      (memoryList as string[]).forEach((memory: string) => {
        memorySection += `• ${memory}\n`;
      });
      memorySection += '\n';
    });

    memorySection += '请基于这些记忆信息提供个性化、连贯的回答，就像你一直了解用户一样。\n';
    memorySection += '=== 记忆信息结束 ===\n\n';

    console.log('🧠 记忆注入到系统提示词，记忆数量:', userMemories.length);

    return originalSystemPrompt + memorySection;
  }, [memoryConfig.enabled, userMemories]);

  // 记忆增强的消息发送处理 - 通过系统提示词注入记忆
  const handleMemoryEnhancedSendMessage = useCallback(async (
    content: string,
    images?: any[],
    toolsEnabled?: boolean,
    files?: any[]
  ) => {
    console.log('🧠 开始记忆增强消息发送（系统提示词模式）...');

    // 直接发送原始消息，记忆通过系统提示词注入
    const result = await originalHandleSendMessage(content, images, toolsEnabled, files);

    // 异步处理记忆提取（不阻塞消息发送）
    processMemoryInBackground(content);

    return result;
  }, [
    originalHandleSendMessage,
    processMemoryInBackground
  ]);

  /**
   * 手动添加记忆
   */
  const addMemory = useCallback(async (
    content: string,
    category: any,
    importance: number = 5
  ) => {
    try {
      const success = await memoryService.addMemory(
        userId,
        content,
        category,
        importance,
        {
          addedFrom: 'manual',
          topicId: currentTopic?.id
        }
      );

      if (success) {
        // 重新加载用户记忆
        dispatch(loadUserMemories(userId) as any);
        
        LoggerService.log('INFO', 'Memory added manually', {
          userId,
          content: content.substring(0, 50),
          category,
          importance
        });
      }

      return success;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to add memory manually', { error });
      return false;
    }
  }, [userId, currentTopic?.id, memoryService, dispatch]);

  /**
   * 搜索记忆
   */
  const searchMemories = useCallback(async (
    query: string,
    options: { limit?: number; category?: any } = {}
  ) => {
    try {
      return await memoryService.searchUserMemories(userId, query, options);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to search memories', { error });
      return [];
    }
  }, [userId, memoryService]);

  /**
   * 获取记忆摘要
   */
  const getMemorySummary = useCallback(async () => {
    try {
      return await memoryService.getUserMemorySummary(userId, {
        maxLength: 300,
        includeStats: true
      });
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get memory summary', { error });
      return '无法获取记忆摘要';
    }
  }, [userId, memoryService]);

  /**
   * 清除所有记忆
   */
  const clearAllMemories = useCallback(async () => {
    try {
      const success = await memoryService.clearUserMemories(userId);
      
      if (success) {
        // 重新加载用户记忆
        dispatch(loadUserMemories(userId) as any);
        
        // 清空当前记忆状态
        setMemoryState(prev => ({
          ...prev,
          currentMemories: [],
          memoryStats: {
            memoriesUsed: 0,
            memoriesExtracted: 0
          }
        }));
      }

      return success;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to clear all memories', { error });
      return false;
    }
  }, [userId, memoryService, dispatch]);

  return {
    // 状态
    isMemoryEnabled: memoryState.isMemoryEnabled,
    currentMemories: memoryState.currentMemories,
    memoryStats: memoryState.memoryStats,
    isProcessingMemory: memoryState.isProcessingMemory,
    userMemories,

    // 方法
    handleMemoryEnhancedSendMessage,
    buildMemoryEnhancedSystemPrompt,
    addMemory,
    searchMemories,
    getMemorySummary,
    clearAllMemories,
    updateCurrentMemories
  };
};
