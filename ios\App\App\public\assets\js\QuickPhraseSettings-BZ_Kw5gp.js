import{j as e,B as r,a as t,b as s,A as o,T as a,I as i,m as n,P as l,D as x,g as d,h as c,y as m,L as h,N as p,Q as b,x as g,J as u,o as j,p as f,q as v,t as y,n as k,r as w}from"./mui-vendor-hRDvsX89.js";import{u as z,r as S,a as C}from"./react-vendor-C9ilihHH.js";import{o as A,d as I,A as R,a1 as W,a2 as B}from"./index-BtK6VV6Z.js";import{Q as E,Z as P}from"./QuickPhraseService-DKEfB3mv.js";import{P as D}from"./plus-B-2qGkB5.js";import{T}from"./trash-2-DkmtltUW.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const G=()=>{const G=z(),Q=A(),M=I((e=>e.settings.showQuickPhraseButton??!0)),[O,F]=S.useState([]),[L,q]=S.useState(!0),[J,N]=S.useState(!1),[Y,Z]=S.useState(null),[$,H]=S.useState({title:"",content:""}),K=S.useCallback((async()=>{try{q(!0);const e=await E.getAll();F(e)}catch(e){console.error("加载快捷短语失败:",e)}finally{q(!1)}}),[]);S.useEffect((()=>{K()}),[K]);const U=()=>{Z(null),H({title:"",content:""}),N(!0)},V=()=>{N(!1),Z(null),H({title:"",content:""})};return L?e.jsx(r,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?t(e.palette.primary.main,.02):t(e.palette.background.default,.9),justifyContent:"center",alignItems:"center"},children:e.jsx(s,{children:"加载中..."})}):e.jsxs(r,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?t(e.palette.primary.main,.02):t(e.palette.background.default,.9)},children:[e.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(a,{children:[e.jsx(i,{edge:"start",color:"inherit",onClick:()=>{G("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(R,{})}),e.jsx(s,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"快捷短语设置"}),e.jsx(n,{startIcon:e.jsx(D,{size:16}),onClick:U,sx:{bgcolor:e=>t(e.palette.primary.main,.1),color:"primary.main","&:hover":{bgcolor:e=>t(e.palette.primary.main,.2)},borderRadius:2},children:"添加"})]})}),e.jsxs(r,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(s,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"显示设置"}),e.jsx(s,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"控制快捷短语按钮在聊天界面的显示"})]}),e.jsx(x,{}),e.jsxs(r,{sx:{p:{xs:1.5,sm:2}},children:[e.jsx(d,{control:e.jsx(c,{checked:M,onChange:e=>Q(W(e.target.checked))}),label:"在输入框显示快捷短语按钮"}),e.jsx(s,{variant:"caption",color:"text.secondary",sx:{display:"block",mt:.5,fontSize:{xs:"0.7rem",sm:"0.75rem"}},children:"控制是否在聊天输入框中显示快捷短语按钮"})]})]}),e.jsxs(l,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(s,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"快捷短语列表"}),e.jsx(s,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"管理您的快捷短语，在聊天时快速插入常用内容"})]}),e.jsx(x,{}),0===O.length?e.jsxs(r,{sx:{p:{xs:3,sm:4},textAlign:"center"},children:[e.jsx(m,{sx:{bgcolor:t("#9333EA",.12),color:"#9333EA",width:{xs:48,sm:64},height:{xs:48,sm:64},mx:"auto",mb:2},children:e.jsx(P,{size:24})}),e.jsx(s,{variant:"h6",color:"text.secondary",gutterBottom:!0,sx:{fontSize:{xs:"1rem",sm:"1.25rem"}},children:"还没有快捷短语"}),e.jsx(s,{variant:"body2",color:"text.secondary",sx:{mb:2,fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"创建您的第一个快捷短语，让聊天更高效"}),e.jsx(n,{variant:"contained",startIcon:e.jsx(D,{size:16}),onClick:U,sx:{borderRadius:2,textTransform:"none",fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"添加快捷短语"})]}):e.jsx(h,{disablePadding:!0,children:O.map(((o,a)=>e.jsxs(C.Fragment,{children:[e.jsxs(p,{sx:{transition:"all 0.2s","&:hover":{bgcolor:e=>t(e.palette.primary.main,.05)},py:{xs:1,sm:1.5},px:{xs:1.5,sm:2}},children:[e.jsx(b,{children:e.jsx(m,{sx:{bgcolor:t("#9333EA",.12),color:"#9333EA",boxShadow:"0 2px 6px rgba(0,0,0,0.05)",width:{xs:32,sm:40},height:{xs:32,sm:40}},children:e.jsx(P,{size:16})})}),e.jsx(g,{primary:e.jsxs(r,{sx:{display:"flex",alignItems:"center",gap:1,flexWrap:"wrap"},children:[e.jsx(s,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"0.9rem",sm:"1rem"}},children:o.title}),e.jsx(u,{size:"small",label:`${o.content.length} 字符`,variant:"outlined",sx:{fontSize:{xs:"0.7rem",sm:"0.75rem"},height:{xs:20,sm:24}}})]}),secondary:e.jsx(s,{variant:"body2",color:"text.secondary",sx:{mt:.5,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:o.content})}),e.jsxs(r,{sx:{display:"flex",gap:.5,ml:"auto"},children:[e.jsx(i,{onClick:()=>(e=>{Z(e),H({title:e.title,content:e.content}),N(!0)})(o),size:"small",sx:{color:"text.secondary","&:hover":{color:"primary.main",bgcolor:e=>t(e.palette.primary.main,.1)}},children:e.jsx(B,{size:16})}),e.jsx(i,{onClick:()=>(async e=>{try{await E.delete(e),await K()}catch(r){console.error("删除快捷短语失败:",r)}})(o.id),size:"small",sx:{color:"text.secondary","&:hover":{color:"error.main",bgcolor:e=>t(e.palette.error.main,.1)}},children:e.jsx(T,{size:16})})]})]}),a<O.length-1&&e.jsx(x,{variant:"inset",component:"li",sx:{ml:0}})]},o.id)))})]})]}),e.jsxs(j,{open:J,onClose:V,maxWidth:"sm",fullWidth:!0,slotProps:{paper:{sx:{borderRadius:2,boxShadow:"0 8px 32px rgba(0,0,0,0.12)"}}},children:[e.jsx(f,{sx:{fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent",pb:1},children:Y?"编辑快捷短语":"添加快捷短语"}),e.jsx(v,{sx:{pt:2},children:e.jsxs(r,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsx(y,{label:"标题",value:$.title,onChange:e=>H({...$,title:e.target.value}),fullWidth:!0,size:"small",placeholder:"为您的快捷短语起个名字",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}}),e.jsx(y,{label:"内容",value:$.content,onChange:e=>H({...$,content:e.target.value}),multiline:!0,rows:6,fullWidth:!0,size:"small",placeholder:"输入快捷短语的内容...",sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}}),$.content&&e.jsxs(k,{severity:"info",sx:{borderRadius:2,"& .MuiAlert-message":{fontSize:"0.875rem"}},children:["内容长度：",$.content.length," 字符"]})]})}),e.jsxs(w,{sx:{p:2,pt:1},children:[e.jsx(n,{onClick:V,sx:{borderRadius:2,textTransform:"none"},children:"取消"}),e.jsx(n,{onClick:async()=>{if($.title.trim()&&$.content.trim())try{Y?await E.update(Y.id,{title:$.title,content:$.content}):await E.add({title:$.title,content:$.content}),V(),await K()}catch(e){console.error("保存快捷短语失败:",e)}},variant:"contained",disabled:!$.title.trim()||!$.content.trim(),sx:{borderRadius:2,textTransform:"none",backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)","&:hover":{backgroundImage:"linear-gradient(90deg, #7c2d92, #6b4397)"}},children:Y?"更新":"添加"})]})]})]})};export{G as default};
