/**
 * 记忆嵌入向量服务
 * 复用现有的嵌入模型配置和服务
 */

import { MobileEmbeddingService } from '../MobileEmbeddingService';
import { getMemoryConfig } from '../../config/internalMemoryConfig';
import LoggerService from '../LoggerService';

/**
 * 记忆嵌入服务类
 * 为记忆系统提供向量嵌入功能
 */
export class MemoryEmbeddingService {
  private static instance: MemoryEmbeddingService;
  private embeddingService: MobileEmbeddingService;

  private constructor() {
    this.embeddingService = MobileEmbeddingService.getInstance();
  }

  /**
   * 获取当前配置（实时获取最新配置）
   */
  private getConfig() {
    return getMemoryConfig();
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): MemoryEmbeddingService {
    if (!MemoryEmbeddingService.instance) {
      MemoryEmbeddingService.instance = new MemoryEmbeddingService();
    }
    return MemoryEmbeddingService.instance;
  }

  /**
   * 为记忆内容生成嵌入向量
   */
  public async generateMemoryEmbedding(content: string): Promise<number[]> {
    try {
      const config = this.getConfig();
      if (!config.enabled) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return [];
      }

      console.log('🔧 使用嵌入模型:', config.search.embeddingModel);

      // 🔧 修复：为记忆嵌入向量生成添加错误处理，防止阻塞消息发送
      try {
        // 使用配置的嵌入模型
        const embedding = await this.embeddingService.getEmbedding(
          content,
          config.search.embeddingModel
        );

        LoggerService.log('INFO', 'Memory embedding generated', {
          contentLength: content.length,
          embeddingDimensions: embedding.length,
          model: config.search.embeddingModel
        });

        return embedding;
      } catch (embeddingError) {
        LoggerService.log('WARN', 'Memory embedding generation failed, returning empty vector', {
          error: embeddingError,
          content: content.substring(0, 50),
          model: config.search.embeddingModel
        });
        // 🔧 修复：嵌入模型失败时返回空向量，不阻塞记忆功能
        return [];
      }
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to generate memory embedding', {
        error,
        content: content.substring(0, 50)
      });
      // 🔧 修复：返回空向量而不是抛出异常
      return [];
    }
  }

  /**
   * 批量生成嵌入向量
   */
  public async generateBatchEmbeddings(contents: string[]): Promise<number[][]> {
    try {
      const config = this.getConfig();
      if (!config.enabled) {
        LoggerService.log('WARN', 'Memory service is disabled');
        return [];
      }

      const embeddings: number[][] = [];
      
      // 逐个生成嵌入向量（避免API限制）
      for (const content of contents) {
        try {
          const embedding = await this.generateMemoryEmbedding(content);
          embeddings.push(embedding);
          
          // 添加小延迟避免API限制
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          LoggerService.log('ERROR', 'Failed to generate embedding for content', {
            error,
            content: content.substring(0, 50)
          });
          // 使用空向量作为降级
          embeddings.push([]);
        }
      }

      LoggerService.log('INFO', 'Batch embeddings generated', {
        totalContents: contents.length,
        successfulEmbeddings: embeddings.filter(e => e.length > 0).length
      });

      return embeddings;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to generate batch embeddings', { error });
      return [];
    }
  }

  /**
   * 计算余弦相似度
   */
  public calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    try {
      if (vectorA.length === 0 || vectorB.length === 0) {
        return 0;
      }

      if (vectorA.length !== vectorB.length) {
        LoggerService.log('ERROR', 'Vector dimensions mismatch', {
          vectorALength: vectorA.length,
          vectorBLength: vectorB.length
        });
        return 0;
      }

      let dotProduct = 0;
      let magnitudeA = 0;
      let magnitudeB = 0;

      for (let i = 0; i < vectorA.length; i++) {
        dotProduct += vectorA[i] * vectorB[i];
        magnitudeA += vectorA[i] * vectorA[i];
        magnitudeB += vectorB[i] * vectorB[i];
      }

      magnitudeA = Math.sqrt(magnitudeA);
      magnitudeB = Math.sqrt(magnitudeB);

      if (magnitudeA === 0 || magnitudeB === 0) {
        return 0;
      }

      const similarity = dotProduct / (magnitudeA * magnitudeB);
      
      // 确保相似度在 [-1, 1] 范围内
      return Math.max(-1, Math.min(1, similarity));
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to calculate cosine similarity', { error });
      return 0;
    }
  }

  /**
   * 获取嵌入模型的维度
   */
  public async getEmbeddingDimensions(): Promise<number> {
    try {
      const config = this.getConfig();
      return await this.embeddingService.getEmbeddingDimensions(
        config.search.embeddingModel
      );
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to get embedding dimensions', { error });
      // 返回默认维度
      return 1536;
    }
  }

  /**
   * 验证嵌入向量
   */
  public validateEmbedding(embedding: number[]): boolean {
    if (!Array.isArray(embedding) || embedding.length === 0) {
      return false;
    }

    // 检查是否包含有效数值
    return embedding.every(value => 
      typeof value === 'number' && 
      !isNaN(value) && 
      isFinite(value)
    );
  }

  /**
   * 标准化嵌入向量
   */
  public normalizeEmbedding(embedding: number[]): number[] {
    try {
      if (!this.validateEmbedding(embedding)) {
        return embedding;
      }

      // 计算向量的模长
      const magnitude = Math.sqrt(
        embedding.reduce((sum, value) => sum + value * value, 0)
      );

      if (magnitude === 0) {
        return embedding;
      }

      // 标准化向量
      return embedding.map(value => value / magnitude);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to normalize embedding', { error });
      return embedding;
    }
  }

  /**
   * 计算向量间的欧几里得距离
   */
  public calculateEuclideanDistance(vectorA: number[], vectorB: number[]): number {
    try {
      if (vectorA.length !== vectorB.length || vectorA.length === 0) {
        return Infinity;
      }

      let sumSquaredDifferences = 0;
      for (let i = 0; i < vectorA.length; i++) {
        const difference = vectorA[i] - vectorB[i];
        sumSquaredDifferences += difference * difference;
      }

      return Math.sqrt(sumSquaredDifferences);
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to calculate Euclidean distance', { error });
      return Infinity;
    }
  }

  /**
   * 查找最相似的向量
   */
  public findMostSimilarVectors(
    queryVector: number[],
    candidateVectors: { id: string; vector: number[] }[],
    topK: number = 5
  ): { id: string; similarity: number }[] {
    try {
      if (!this.validateEmbedding(queryVector) || candidateVectors.length === 0) {
        return [];
      }

      const similarities = candidateVectors
        .map(candidate => ({
          id: candidate.id,
          similarity: this.calculateCosineSimilarity(queryVector, candidate.vector)
        }))
        .filter(result => result.similarity > this.getConfig().search.similarityThreshold)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, topK);

      return similarities;
    } catch (error) {
      LoggerService.log('ERROR', 'Failed to find most similar vectors', { error });
      return [];
    }
  }

  /**
   * 获取当前配置的嵌入模型
   */
  public getEmbeddingModel(): string {
    return this.getConfig().search.embeddingModel;
  }

  /**
   * 获取相似度阈值
   */
  public getSimilarityThreshold(): number {
    return this.getConfig().search.similarityThreshold;
  }
}
