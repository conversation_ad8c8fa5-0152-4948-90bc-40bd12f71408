// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-file-opener')
    implementation project(':capacitor-community-native-audio')
    implementation project(':capacitor-community-safe-area')
    implementation project(':capacitor-community-speech-recognition')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-share')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-toast')
    implementation project(':capawesome-capacitor-android-edge-to-edge-support')
    implementation project(':capawesome-capacitor-file-picker')
    implementation project(':capacitor-cors-bypass-enhanced')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
