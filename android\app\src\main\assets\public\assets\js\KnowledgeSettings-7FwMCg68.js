import{j as e,B as r,a as s,A as o,T as n,I as t,b as a,P as i,D as l,C as d,k as x,l as c,m as h,n as j,o as b,p as g,q as p,r as m}from"./mui-vendor-DsBXMegs.js";import{u,r as f}from"./react-vendor-Be-rfjCm.js";import{A as y,S as v,D as w,e as k,f as S,M as C}from"./index-Ck4sQVom.js";import{A as B}from"./Add-CgwaVY5w.js";import{L as z}from"./Launch-TVWoTAFV.js";import{C as A}from"./CreateKnowledgeDialog-DRqkZ7gl.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";import"./ExpandLess-B3I8kUh8.js";const W=()=>{const W=u(),[I,D]=f.useState(!1),[K,R]=f.useState({totalKnowledgeBases:0,totalDocuments:0,totalVectors:0,storageSize:"0 MB"}),[L,M]=f.useState(!1),[O,_]=f.useState(!1),[E,F]=f.useState(!1),G=async()=>{try{D(!0);const e=(await S.knowledge_bases.toArray()).length,r=(await S.knowledge_documents.toArray()).length,s=r,o=U(s*6144);R({totalKnowledgeBases:e,totalDocuments:r,totalVectors:s,storageSize:o})}catch(e){console.error("加载知识库统计信息失败:",e)}finally{D(!1)}},U=e=>{if(0===e)return"0 B";const r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["B","KB","MB","GB"][r]},V=()=>{F(!0)};return f.useEffect((()=>{G()}),[]),e.jsxs(r,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?s(e.palette.primary.main,.02):s(e.palette.background.default,.9)},children:[e.jsx(o,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(n,{children:[e.jsx(t,{edge:"start",color:"inherit",onClick:()=>{W("/settings")},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:e.jsx(y,{})}),e.jsx(a,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"知识库设置"})]})}),e.jsxs(r,{sx:{flexGrow:1,overflowY:"auto",p:{xs:1,sm:2},mt:8,"&::-webkit-scrollbar":{width:{xs:"4px",sm:"6px"}},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[e.jsxs(i,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsxs(a,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"},display:"flex",alignItems:"center",gap:1},children:[e.jsx(v,{sx:{color:"#059669"}}),"知识库统计"]}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"查看知识库的使用情况和存储统计"})]}),e.jsx(l,{}),e.jsx(r,{sx:{p:{xs:1.5,sm:2}},children:I?e.jsx(r,{display:"flex",justifyContent:"center",py:3,children:e.jsx(d,{})}):e.jsxs(r,{sx:{display:"flex",flexWrap:"wrap",margin:-1},children:[e.jsx(r,{sx:{width:{xs:"50%",sm:"25%"},p:1},children:e.jsx(x,{variant:"outlined",children:e.jsxs(c,{sx:{textAlign:"center",py:2},children:[e.jsx(a,{variant:"h4",color:"primary",fontWeight:"bold",children:K.totalKnowledgeBases}),e.jsx(a,{variant:"body2",color:"textSecondary",children:"知识库数量"})]})})}),e.jsx(r,{sx:{width:{xs:"50%",sm:"25%"},p:1},children:e.jsx(x,{variant:"outlined",children:e.jsxs(c,{sx:{textAlign:"center",py:2},children:[e.jsx(a,{variant:"h4",color:"success.main",fontWeight:"bold",children:K.totalDocuments}),e.jsx(a,{variant:"body2",color:"textSecondary",children:"文档数量"})]})})}),e.jsx(r,{sx:{width:{xs:"50%",sm:"25%"},p:1},children:e.jsx(x,{variant:"outlined",children:e.jsxs(c,{sx:{textAlign:"center",py:2},children:[e.jsx(a,{variant:"h4",color:"warning.main",fontWeight:"bold",children:K.totalVectors}),e.jsx(a,{variant:"body2",color:"textSecondary",children:"向量数量"})]})})}),e.jsx(r,{sx:{width:{xs:"50%",sm:"25%"},p:1},children:e.jsx(x,{variant:"outlined",children:e.jsxs(c,{sx:{textAlign:"center",py:2},children:[e.jsx(a,{variant:"h4",color:"info.main",fontWeight:"bold",children:K.storageSize}),e.jsx(a,{variant:"body2",color:"textSecondary",children:"存储大小"})]})})})]})})]}),e.jsxs(i,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"快速操作"}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"创建和管理知识库的快捷操作"})]}),e.jsx(l,{}),e.jsxs(r,{sx:{p:{xs:1.5,sm:2}},children:[e.jsxs(r,{sx:{display:"flex",flexWrap:"wrap",margin:-1},children:[e.jsx(r,{sx:{width:{xs:"100%",sm:"50%"},p:1},children:e.jsx(h,{fullWidth:!0,variant:"contained",startIcon:e.jsx(B,{}),onClick:V,disabled:I,sx:{py:1.5,background:"linear-gradient(45deg, #059669 30%, #10b981 90%)","&:hover":{background:"linear-gradient(45deg, #047857 30%, #059669 90%)"}},children:"创建知识库"})}),e.jsx(r,{sx:{width:{xs:"100%",sm:"50%"},p:1},children:e.jsx(h,{fullWidth:!0,variant:"outlined",startIcon:e.jsx(z,{}),onClick:()=>W("/knowledge"),sx:{py:1.5},children:"管理知识库"})})]}),0===K.totalKnowledgeBases&&e.jsx(j,{severity:"info",sx:{mt:2},children:'您还没有创建任何知识库。点击"创建知识库"开始使用知识库功能。'})]})]}),e.jsxs(i,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"知识库配置说明"}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"了解知识库的各项配置参数和使用说明"})]}),e.jsx(l,{}),e.jsxs(r,{sx:{p:{xs:1.5,sm:2}},children:[e.jsx(j,{severity:"info",sx:{mb:2},children:"知识库的嵌入模型、分块大小、相似度阈值等参数在创建知识库时设置，每个知识库可以有不同的配置。"}),e.jsxs(a,{variant:"body2",color:"text.secondary",children:["• ",e.jsx("strong",{children:"嵌入模型"}),"：用于将文本转换为向量，不同模型有不同的效果和维度",e.jsx("br",{}),"• ",e.jsx("strong",{children:"分块大小"}),"：文档分割的块大小，影响搜索精度和上下文长度",e.jsx("br",{}),"• ",e.jsx("strong",{children:"相似度阈值"}),"：搜索结果的最低相似度，越高结果越精确",e.jsx("br",{}),"• ",e.jsx("strong",{children:"文档数量"}),"：搜索时返回的文档段数量，影响回答的详细程度"]})]})]}),e.jsxs(i,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[e.jsxs(r,{sx:{p:{xs:1.5,sm:2},bgcolor:"rgba(0,0,0,0.01)"},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,fontSize:{xs:"1rem",sm:"1.1rem"}},children:"数据管理"}),e.jsx(a,{variant:"body2",color:"text.secondary",sx:{fontSize:{xs:"0.8rem",sm:"0.875rem"}},children:"导出备份和清理知识库数据"})]}),e.jsx(l,{}),e.jsxs(r,{sx:{p:{xs:1.5,sm:2}},children:[0===K.totalKnowledgeBases?e.jsx(j,{severity:"warning",sx:{mb:3},children:"您还没有创建任何知识库。请先创建知识库后再进行数据管理操作。"}):e.jsx(j,{severity:"info",sx:{mb:3},children:"数据管理操作会影响所有知识库，请谨慎操作。建议在操作前先导出备份。"}),e.jsx(r,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:0===K.totalKnowledgeBases?e.jsx(h,{variant:"contained",startIcon:e.jsx(B,{}),onClick:V,disabled:I,sx:{background:"linear-gradient(45deg, #059669 30%, #10b981 90%)","&:hover":{background:"linear-gradient(45deg, #047857 30%, #059669 90%)"}},children:"创建第一个知识库"}):e.jsxs(e.Fragment,{children:[e.jsx(h,{variant:"outlined",startIcon:e.jsx(w,{}),onClick:()=>_(!0),disabled:I,children:"导出数据"}),e.jsx(h,{variant:"outlined",color:"error",startIcon:e.jsx(k,{}),onClick:()=>M(!0),disabled:I,children:"清理所有数据"})]})})]})]})]}),e.jsxs(b,{open:L,onClose:()=>M(!1),children:[e.jsx(g,{children:"确认清理所有知识库数据"}),e.jsx(p,{children:e.jsx(a,{children:"此操作将删除所有知识库、文档和向量数据，且无法恢复。确定要继续吗？"})}),e.jsxs(m,{children:[e.jsx(h,{onClick:()=>M(!1),children:"取消"}),e.jsx(h,{onClick:async()=>{try{D(!0),await S.knowledge_bases.clear(),await S.knowledge_documents.clear(),await G(),M(!1),alert("知识库数据已清理完成")}catch(e){console.error("清理知识库数据失败:",e),alert("清理失败，请重试")}finally{D(!1)}},color:"error",disabled:I,children:I?e.jsx(d,{size:20}):"确认清理"})]})]}),e.jsxs(b,{open:O,onClose:()=>_(!1),children:[e.jsx(g,{children:"导出知识库数据"}),e.jsx(p,{children:e.jsx(a,{children:"将导出所有知识库和文档数据为JSON文件，可用于备份或迁移。"})}),e.jsxs(m,{children:[e.jsx(h,{onClick:()=>_(!1),children:"取消"}),e.jsx(h,{onClick:async()=>{try{D(!0);const e=await S.knowledge_bases.toArray(),r=await S.knowledge_documents.toArray(),s={version:"1.0",timestamp:(new Date).toISOString(),knowledgeBases:e,documents:r},o=new Blob([JSON.stringify(s,null,2)],{type:"application/json"}),n=URL.createObjectURL(o),t=document.createElement("a");t.href=n,t.download=`knowledge-backup-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(n),_(!1),alert("知识库数据导出成功")}catch(e){console.error("导出知识库数据失败:",e),alert("导出失败，请重试")}finally{D(!1)}},disabled:I,children:I?e.jsx(d,{size:20}):"确认导出"})]})]}),e.jsx(A,{open:E,onClose:()=>F(!1),onSave:async e=>{try{D(!0),await C.getInstance().createKnowledgeBase(e),F(!1),await G(),alert("知识库创建成功！")}catch(r){console.error("创建知识库失败:",r),alert("创建失败，请重试")}finally{D(!1)}},isEditing:!1})]})};export{W as default};
