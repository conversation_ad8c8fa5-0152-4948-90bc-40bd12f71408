import{c as t,j as e,B as i,a as o,A as s,T as r,I as c,b as a,P as l,N as n,y as d}from"./mui-vendor-DsBXMegs.js";import{u as p}from"./react-vendor-Be-rfjCm.js";import{A as h,aa as m,_ as x,S as b,I as g}from"./index-Ck4sQVom.js";import{L as v}from"./Language-D33lGtXR.js";import{C as j}from"./ChevronRight-DT2g11mS.js";import{T as f}from"./Tune-DzMCixvP.js";import{A as u}from"./AutoFixHigh-Bp3qoDJc.js";import{M as z,C as H}from"./MenuBook-CxpKomku.js";import{F as k}from"./Forum-CVhVMzbl.js";import"./syntax-vendor-DfDNeb5M.js";import"./utils-vendor-D1FP9aB2.js";const w=t(e.jsx("path",{d:"M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7s2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11"})),y=t(e.jsx("path",{d:"M16.56 8.94 7.62 0 6.21 1.41l2.38 2.38-5.15 5.15c-.59.59-.59 1.54 0 2.12l5.5 5.5c.29.29.68.44 1.06.44s.77-.15 1.06-.44l5.5-5.5c.59-.58.59-1.53 0-2.12M5.21 10 10 5.21 14.79 10zM19 11.5s-2 2.17-2 3.5c0 1.1.9 2 2 2s2-.9 2-2c0-1.33-2-3.5-2-3.5M2 20h20v4H2z"})),V=t(e.jsx("path",{d:"M20 5H4c-1.1 0-1.99.9-1.99 2L2 17c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2m-9 3h2v2h-2zm0 3h2v2h-2zM8 8h2v2H8zm0 3h2v2H8zm-1 2H5v-2h2zm0-3H5V8h2zm9 7H8v-2h8zm0-4h-2v-2h2zm0-3h-2V8h2zm3 3h-2v-2h2zm0-3h-2V8h2z"})),C=t(e.jsx("path",{d:"M15 9H9v6h6zm-2 4h-2v-2h2zm8-2V9h-2V7c0-1.1-.9-2-2-2h-2V3h-2v2h-2V3H9v2H7c-1.1 0-2 .9-2 2v2H3v2h2v2H3v2h2v2c0 1.1.9 2 2 2h2v2h2v-2h2v2h2v-2h2c1.1 0 2-.9 2-2v-2h2v-2h-2v-2zm-4 6H7V7h10z"})),M=t([e.jsx("circle",{cx:"9",cy:"9",r:"4"},"0"),e.jsx("path",{d:"M9 15c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4m7.76-9.64-1.68 1.69c.84 1.18.84 2.71 0 3.89l1.68 1.69c2.02-2.02 2.02-5.07 0-7.27M20.07 2l-1.63 1.63c2.77 3.02 2.77 7.56 0 10.74L20.07 16c3.9-3.89 3.91-9.95 0-14"},"1")]),A=t(e.jsx("path",{d:"M12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m7-7H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2m-1.75 9c0 .23-.02.46-.05.68l1.48 1.16c.13.11.17.3.08.45l-1.4 2.42c-.09.15-.27.21-.43.15l-1.74-.7c-.36.28-.76.51-1.18.69l-.26 1.85c-.03.17-.18.3-.35.3h-2.8c-.17 0-.32-.13-.35-.29l-.26-1.85c-.43-.18-.82-.41-1.18-.69l-1.74.7c-.16.06-.34 0-.43-.15l-1.4-2.42c-.09-.15-.05-.34.08-.45l1.48-1.16c-.03-.23-.05-.46-.05-.69s.02-.46.05-.68l-1.48-1.16c-.13-.11-.17-.3-.08-.45l1.4-2.42c.09-.15.27-.21.43-.15l1.74.7c.36-.28.76-.51 1.18-.69l.26-1.85c.03-.17.18-.3.35-.3h2.8c.17 0 .32.13.35.29l.26 1.85c.*********** 1.18.69l1.74-.7c.16-.06.34 0 .43.15l1.4 2.42c.***********-.08.45l-1.48 1.16c.***********.05.69"})),I=()=>{const t=p(),I=[{title:"基本设置",items:[{id:"appearance",title:"外观",description:"主题、字体大小和语言设置",icon:e.jsx(y,{}),path:"/settings/appearance",color:"#6366f1"},{id:"behavior",title:"行为",description:"消息发送和通知设置",icon:e.jsx(A,{}),path:"/settings/behavior",color:"#8b5cf6"}]},{title:"模型服务",items:[{id:"default-model",title:"配置模型",description:"管理AI模型和API密钥",icon:e.jsx(m,{}),path:"/settings/default-model",color:"#ec4899"},{id:"default-model-settings",title:"默认模型",description:"选择默认模型和自动化选项",icon:e.jsx(f,{}),path:"/settings/default-model-settings",color:"#4f46e5"},{id:"agent-prompts",title:"智能体提示词集合",description:"浏览和使用内置的丰富提示词模板",icon:e.jsx(u,{}),path:"/settings/agent-prompts",color:"#0ea5e9"},{id:"ai-debate",title:"AI辩论设置",description:"配置AI互相辩论讨论功能",icon:e.jsx(k,{}),path:"/settings/ai-debate",color:"#e11d48"},{id:"model-combo",title:"模型组合",description:"创建和管理多模型组合",icon:e.jsx(m,{}),path:"/settings/model-combo",color:"#f43f5e"},{id:"web-search",title:"网络搜索",description:"配置网络搜索和相关服务",icon:e.jsx(v,{}),path:"/settings/web-search",color:"#3b82f6"},{id:"mcp-server",title:"MCP 服务器",description:"高级服务器配置",icon:e.jsx(x,{}),path:"/settings/mcp-server",color:"#10b981"}]},{title:"快捷方式",items:[{id:"shortcuts",title:"快捷助手",description:"自定义键盘快捷键",icon:e.jsx(V,{}),path:"/settings/shortcuts",color:"#f59e0b"},{id:"quick-phrases",title:"快捷短语",description:"创建常用短语模板",icon:e.jsx(V,{}),path:"/settings/quick-phrases",color:"#f97316"}]},{title:"其他设置",items:[{id:"memory-management",title:"智能记忆",description:"管理AI记忆功能和个人偏好",icon:e.jsx(C,{}),path:"/settings/memory",color:"#7c3aed"},{id:"knowledge-settings",title:"知识库设置",description:"管理知识库配置和嵌入模型",icon:e.jsx(z,{}),path:"/settings/knowledge",color:"#059669"},{id:"data-settings",title:"数据设置",description:"管理数据存储和隐私选项",icon:e.jsx(b,{}),path:"/settings/data",color:"#0ea5e9"},{id:"voice-settings",title:"语音功能",description:"语音识别和文本转语音设置",icon:e.jsx(M,{}),path:"/settings/voice",color:"#8b5cf6"},{id:"features",title:"功能模块",description:"启用或禁用应用功能",icon:e.jsx(w,{}),path:"/settings/features",color:"#22c55e"},{id:"vue-demo",title:"Vue 组件演示",description:"Vue与Capacitor功能演示",icon:e.jsx(H,{}),path:"/vue-demo",color:"#42b983"},{id:"about",title:"关于我们",description:"应用信息和技术支持",icon:e.jsx(g,{}),path:"/settings/about",color:"#64748b"}]}];return e.jsxs(i,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:t=>"light"===t.palette.mode?o(t.palette.primary.main,.02):o(t.palette.background.default,.9)},children:[e.jsx(s,{position:"fixed",elevation:0,sx:{zIndex:t=>t.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:e.jsxs(r,{children:[e.jsx(c,{edge:"start",onClick:()=>{t("/chat")},"aria-label":"back",sx:{color:t=>t.palette.primary.main},children:e.jsx(h,{})}),e.jsx(a,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #9333EA, #754AB4)",backgroundClip:"text",color:"transparent"},children:"设置"})]})}),e.jsx(i,{sx:{flexGrow:1,overflow:"auto",px:2,py:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:I.map(((s,r)=>e.jsxs(i,{sx:{mb:3},children:[e.jsx(a,{variant:"subtitle1",sx:{px:1,mb:1.5,fontSize:"0.85rem",fontWeight:600,color:t=>"light"===t.palette.mode?"#475569":"#94A3B8",letterSpacing:"0.05em",textTransform:"uppercase"},children:s.title}),e.jsx(i,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"repeat(2, 1fr)"},gap:2},children:s.items.map((s=>e.jsx(l,{elevation:0,sx:{borderRadius:2,overflow:"hidden",border:"1px solid",borderColor:"divider",transition:"all 0.2s ease-in-out",bgcolor:"background.paper",boxShadow:"0 2px 6px rgba(0,0,0,0.02)","&:hover":{transform:"translateY(-2px)",boxShadow:"0 4px 12px rgba(0,0,0,0.05)",borderColor:t=>o(t.palette.primary.main,.3)}},children:e.jsx(n,{onClick:()=>{return e=s.path,void t(e);var e},sx:{p:0,height:"100%","&:hover":{bgcolor:"transparent"}},children:e.jsxs(i,{sx:{display:"flex",alignItems:"center",width:"100%",p:2},children:[e.jsx(d,{sx:{bgcolor:o(s.color,.12),color:s.color,mr:2,boxShadow:"0 2px 6px rgba(0,0,0,0.05)"},children:s.icon}),e.jsxs(i,{sx:{flex:1},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,mb:.5,color:"text.primary"},children:s.title}),e.jsx(a,{variant:"body2",sx:{color:"text.secondary",fontSize:"0.8rem",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden"},children:s.description})]}),e.jsx(j,{sx:{color:t=>o(t.palette.primary.main,.5),ml:1}})]})})},s.id)))})]},r)))})]})};export{I as default};
