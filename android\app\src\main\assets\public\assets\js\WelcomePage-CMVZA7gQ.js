import{c as e,j as i,z as a,B as r,a as s,E as t,k as o,y as n,b as l,C as p,a4 as d,a5 as c,a6 as m,ad as h,P as g,m as u,t as x}from"./mui-vendor-hRDvsX89.js";import{u as v,r as f}from"./react-vendor-C9ilihHH.js";import{u as b,aS as y,C as j,ay as k,aZ as w,a_ as A}from"./index-BtK6VV6Z.js";import{M as B,a as G}from"./ModelIcon-EPsB8sVh.js";import{K as I}from"./KeyboardArrowRight-BOZXxoVz.js";import"./utils-vendor-BDm_82Hk.js";import"./syntax-vendor-DfDNeb5M.js";const U=e(i.jsx("path",{d:"m18 7-1.41-1.41-6.34 6.34 1.41 1.41zm4.24-1.41L11.66 16.17 7.48 12l-1.41 1.41L11.66 19l12-12zM.41 13.41 6 19l1.41-1.41L1.83 12z"})),K=e(i.jsx("path",{d:"M12 3c-.46 0-.93.04-1.4.14-2.76.53-4.96 2.76-5.48 5.52-.48 2.61.48 5.01 2.22 6.56.43.38.66.91.66 1.47V19c0 1.1.9 2 2 2h.28c.35.6.98 1 1.72 1s1.38-.4 1.72-1H14c1.1 0 2-.9 2-2v-2.31c0-.55.22-1.09.64-1.46C18.09 13.95 19 12.08 19 10c0-3.87-3.13-7-7-7m2 16h-4v-1h4zm0-2h-4v-1h4zm-1.5-5.59V14h-1v-2.59L9.67 9.59l.71-.71L12 10.5l1.62-1.62.71.71z"})),q=e(i.jsx("path",{d:"M15.41 16.59 10.83 12l4.58-4.59L14 6l-6 6 6 6z"})),C=e(i.jsx("path",{d:"M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11z"})),S=e(i.jsx("path",{d:"m6 18 8.5-6L6 6zM16 6v12h2V6z"})),L=[{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",description:"快速、经济实惠的AI助手，适合大多数日常任务。",capabilities:["聊天对话","内容生成","简单问答","代码辅助"],requiresApiKey:!0,defaultBaseUrl:"https://api.openai.com/v1"},{id:"gpt-4",name:"GPT-4",provider:"openai",description:"强大的大型语言模型，具有更强的推理能力和更广泛的知识。",capabilities:["复杂推理","高级内容创作","代码生成","多步骤问题解决"],requiresApiKey:!0,defaultBaseUrl:"https://api.openai.com/v1"},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",description:"GPT-4的优化版本，提供更快的响应速度和更新的知识。",capabilities:["复杂推理","高级内容创作","代码生成","多步骤问题解决","更新的知识库"],requiresApiKey:!0,defaultBaseUrl:"https://api.openai.com/v1"},{id:"deepseek-ai/DeepSeek-V3",name:"DeepSeek V3",provider:"siliconflow",description:"由SiliconFlow提供的DeepSeek V3模型，拥有强大的中文理解和生成能力。",capabilities:["聊天对话","内容生成","中文优化","代码辅助","思考过程"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn/v1"},{id:"Qwen/Qwen2-VL-72B-Instruct",name:"Qwen2 VL 72B",provider:"siliconflow",description:"通义千问多模态模型，支持图像理解和视觉分析。",capabilities:["多模态理解","图像分析","内容创作","中文优化","视觉问答"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn/v1",multimodal:!0},{id:"Qwen/Qwen3-32B",name:"Qwen3 32B",provider:"siliconflow",description:"通义千问第三代旗舰大模型，具有卓越的中文理解和创作能力。",capabilities:["复杂推理","内容创作","代码生成","中文优化"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn/v1"},{id:"Qwen/Qwen2.5-Coder-32B-Instruct",name:"Qwen2.5 Coder",provider:"siliconflow",description:"通义千问专门优化的代码模型，擅长编程和技术文档生成。",capabilities:["代码生成","代码解释","技术文档","API设计"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn/v1"},{id:"Qwen/Qwen2.5-Math-72B-Instruct",name:"Qwen2.5 Math",provider:"siliconflow",description:"通义千问数学专精模型，擅长数学推理和解题。",capabilities:["数学推理","问题解决","公式推导","数据分析"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn/v1"},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"anthropic",description:"Anthropic最强大的模型，具有卓越的推理能力和创造力。",capabilities:["复杂推理","高级内容创作","代码生成","多步骤问题解决","更准确的回答"],requiresApiKey:!0,defaultBaseUrl:"https://api.anthropic.com/v1"},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"anthropic",description:"平衡性能和速度的中端模型，适合大多数任务。",capabilities:["聊天对话","内容生成","代码辅助","问题解决"],requiresApiKey:!0,defaultBaseUrl:"https://api.anthropic.com/v1"},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"anthropic",description:"快速、轻量级的模型，适合简单任务和实时应用。",capabilities:["快速回复","简单问答","基础内容生成"],requiresApiKey:!0,defaultBaseUrl:"https://api.anthropic.com/v1"},{id:"gemini-pro",name:"Gemini Pro",provider:"google",description:"Google的高性能大语言模型，具有强大的推理和生成能力。",capabilities:["复杂推理","内容生成","代码辅助","多语言支持"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1beta"},{id:"gemini-2.0-flash",name:"Gemini 2.0 Flash",provider:"google",description:"Google的快速大模型，具有良好的响应速度和质量的平衡。",capabilities:["快速响应","内容生成","代码辅助"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1beta"},{id:"gemini-2.5-flash-preview-04-17",name:"Gemini 2.5 Flash Preview 04-17",provider:"google",description:"Google最新的思考模型预览版，具有更强的推理能力和思考功能。",capabilities:["思考过程","复杂推理","内容生成","代码辅助","支持系统指令"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1beta"},{id:"gemini-ultra",name:"Gemini Ultra",provider:"google",description:"Google最先进的大语言模型，具有卓越的多模态能力。",capabilities:["复杂推理","高级内容创作","代码生成","多模态理解"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1"},{id:"grok-1",name:"Grok-1",provider:"grok",description:"xAI的Grok模型，擅长幽默风格回复和实时信息。",capabilities:["实时知识","网络搜索","幽默回复","代码生成"],requiresApiKey:!0,defaultBaseUrl:"https://api.x.ai/v1"},{id:"grok-2",name:"Grok-2",provider:"grok",description:"xAI的最新Grok模型，具有增强的推理能力和更新的知识库。",capabilities:["复杂推理","实时知识","代码生成","问题解决","多模态理解"],requiresApiKey:!0,defaultBaseUrl:"https://api.x.ai/v1"},{id:"DBV1.5-pro",name:"豆包 1.5 Pro",provider:"volcengine",description:"火山引擎旗舰级大模型，具有强大的中文理解和生成能力。",capabilities:["复杂推理","内容创作","代码生成","中文优化"],requiresApiKey:!0,defaultBaseUrl:"https://ark.cn-beijing.volces.com/api/v3/"},{id:"DBV1.5-lite",name:"豆包 1.5 Lite",provider:"volcengine",description:"火山引擎轻量级模型，快速响应，适合一般对话场景。",capabilities:["快速回复","内容生成","中文优化","基础问答"],requiresApiKey:!0,defaultBaseUrl:"https://ark.cn-beijing.volces.com/api/v3/"},{id:"DBV1.5-thinking-pro",name:"豆包 1.5 思考 Pro",provider:"volcengine",description:"火山引擎思考增强模型，展示详细的思考过程，提高推理能力。",capabilities:["思考过程","复杂推理","内容创作","代码生成"],requiresApiKey:!0,defaultBaseUrl:"https://ark.cn-beijing.volces.com/api/v3/"},{id:"deepseek-r1",name:"DeepSeek R1",provider:"volcengine",description:"火山引擎提供的DeepSeek R1模型，具有卓越的代码能力和综合表现。",capabilities:["代码生成","复杂推理","技术文档","API设计"],requiresApiKey:!0,defaultBaseUrl:"https://ark.cn-beijing.volces.com/api/v3/"},{id:"deepseek-chat",name:"DeepSeek-V3",provider:"deepseek",description:"DeepSeek最新的大型语言模型，具有优秀的中文和代码能力。",capabilities:["聊天对话","内容生成","中文优化","代码辅助","思考过程"],requiresApiKey:!0,defaultBaseUrl:"https://api.deepseek.com"},{id:"deepseek-reasoner",name:"DeepSeek-R1",provider:"deepseek",description:"DeepSeek的推理模型，擅长解决复杂推理问题。",capabilities:["复杂推理","思考过程","代码生成","多步骤问题解决"],requiresApiKey:!0,defaultBaseUrl:"https://api.deepseek.com"},{id:"siliconflow-llama3-8b-chat",name:"Llama3-8B Chat",provider:"siliconflow",description:"高效的Llama3-8B聊天模型",capabilities:["聊天对话","指令跟随"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",modelTypes:[B.Chat]},{id:"siliconflow-llama3-70b-chat",name:"Llama3-70B Chat",provider:"siliconflow",description:"强大的Llama3-70B聊天模型",capabilities:["聊天对话","文本生成","指令跟随"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",modelTypes:[B.Chat]},{id:"siliconflow-xcomposer2",name:"XComposer2",provider:"siliconflow",description:"专业的编写和创作模型",capabilities:["文本生成","编写创作","内容生成"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",modelTypes:[B.Chat]},{id:"siliconflow-deepseek-v2",name:"DeepSeek V2",provider:"siliconflow",description:"强大的中英双语大模型",capabilities:["中英双语","聊天对话","知识问答"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",modelTypes:[B.Chat]},{id:"Kwai-Kolors/Kolors",name:"Kolors",provider:"siliconflow",description:"快手开源的高质量图像生成模型",capabilities:["图像生成","文本到图像","创意绘画"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"stability-ai/sdxl",name:"SDXL",provider:"siliconflow",description:"Stable Diffusion XL图像生成模型",capabilities:["图像生成","文本到图像","高清图像"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"black-forest-labs/FLUX.1-schnell",name:"FLUX.1 Schnell",provider:"siliconflow",description:"Black Forest Labs的快速图像生成模型",capabilities:["图像生成","文本到图像","快速生成"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"black-forest-labs/FLUX.1-dev",name:"FLUX.1 Dev",provider:"siliconflow",description:"FLUX.1开发版，更高质量的图像生成",capabilities:["图像生成","文本到图像","高质量生成"],requiresApiKey:!0,defaultBaseUrl:"https://api.siliconflow.cn",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"grok-2-image-1212",name:"Grok-2 Image 1212",provider:"grok",description:"xAI的Grok-2图像生成模型，支持高质量图像创作",capabilities:["图像生成","文本到图像","创意绘画","高质量输出"],requiresApiKey:!0,defaultBaseUrl:"https://api.x.ai/v1",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"grok-2-image",name:"Grok-2 Image",provider:"grok",description:"xAI的Grok-2图像生成模型",capabilities:["图像生成","文本到图像","创意绘画"],requiresApiKey:!0,defaultBaseUrl:"https://api.x.ai/v1",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"grok-2-image-latest",name:"Grok-2 Image Latest",provider:"grok",description:"xAI的最新Grok-2图像生成模型",capabilities:["图像生成","文本到图像","创意绘画","最新功能"],requiresApiKey:!0,defaultBaseUrl:"https://api.x.ai/v1",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"gemini-2.0-flash-exp-image-generation",name:"Gemini 2.0 Flash Exp Image",provider:"google",description:"Google Gemini 2.0 Flash实验版图像生成模型",capabilities:["图像生成","文本到图像","多模态理解","实验功能"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1beta",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"gemini-2.0-flash-preview-image-generation",name:"Gemini 2.0 Flash Preview Image",provider:"google",description:"Google Gemini 2.0 Flash预览版图像生成模型",capabilities:["图像生成","文本到图像","多模态理解","预览功能"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1beta",imageGeneration:!0,modelTypes:[B.ImageGen]},{id:"gemini-2.0-flash-exp",name:"Gemini 2.0 Flash Exp",provider:"google",description:"Google Gemini 2.0 Flash实验版，支持图像生成功能",capabilities:["聊天对话","图像生成","文本到图像","多模态理解","实验功能"],requiresApiKey:!0,defaultBaseUrl:"https://generativelanguage.googleapis.com/v1beta",imageGeneration:!0,modelTypes:[B.Chat,B.ImageGen]}],T=()=>{const e=v(),B=b(),T=a(),[D,P]=f.useState(0),[z,F]=f.useState(!1),[V,Q]=f.useState("gpt-3.5-turbo"),[M,X]=f.useState(""),[R,W]=f.useState(""),[H,E]=f.useState(!1),$=async()=>{1!==D||M.trim()?2===D?await Y():P((e=>e+1)):W("请输入API密钥")},O=()=>{P((e=>e-1))},Y=async()=>{F(!0);try{const i=L.find((e=>e.id===V));if(i){const a={id:i.id,name:i.name,provider:i.provider,apiKey:M,baseUrl:i.defaultBaseUrl,maxTokens:4096,temperature:.7,enabled:!0,isDefault:!0,iconUrl:`/icons/${i.provider}.png`};B(w(a)),B(A(a.id)),await k("first-time-user","false"),await new Promise((e=>setTimeout(e,1500))),E(!0),setTimeout((()=>{e("/chat")}),2e3)}}catch(i){console.error("设置失败:",i)}finally{F(!1)}},Z=L.find((e=>e.id===V)),_=[i.jsx(K,{sx:{color:T.palette.primary.main}}),i.jsx(C,{sx:{color:T.palette.primary.main}}),i.jsx(U,{sx:{color:T.palette.primary.main}})];return i.jsx(r,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",bgcolor:e=>s(e.palette.primary.main,.05),py:4,backgroundImage:"radial-gradient(circle at 25px 25px, rgba(147, 51, 234, 0.1) 2%, transparent 0%), radial-gradient(circle at 75px 75px, rgba(147, 51, 234, 0.05) 2%, transparent 0%)",backgroundSize:"100px 100px"},children:i.jsxs(t,{maxWidth:"sm",children:[i.jsx(o,{elevation:6,sx:{p:{xs:3,md:4},borderRadius:3,overflow:"visible",position:"relative"},children:H?i.jsxs(r,{sx:{textAlign:"center",py:6,display:"flex",flexDirection:"column",alignItems:"center"},children:[i.jsx(n,{sx:{bgcolor:"success.main",width:80,height:80,mb:3,boxShadow:3,animation:"pulse 1.5s infinite"},children:i.jsx(y,{sx:{fontSize:50}})}),i.jsx(l,{variant:"h4",gutterBottom:!0,fontWeight:"bold",children:"设置完成"}),i.jsx(l,{variant:"body1",color:"text.secondary",paragraph:!0,children:"您的AetherLink已准备就绪，即将进入应用..."}),i.jsx(p,{size:24,sx:{mt:2}}),i.jsx("style",{children:"\n                @keyframes pulse {\n                  0% { transform: scale(1); }\n                  50% { transform: scale(1.05); }\n                  100% { transform: scale(1); }\n                }\n              "})]}):i.jsxs(i.Fragment,{children:[i.jsx(r,{sx:{position:"absolute",top:-30,left:"calc(50% - 30px)",width:60,height:60,borderRadius:"50%",backgroundColor:"primary.main",display:"flex",justifyContent:"center",alignItems:"center",boxShadow:3},children:i.jsx(j,{sx:{color:"#fff",fontSize:30}})}),i.jsxs(r,{sx:{textAlign:"center",mb:4,mt:3},children:[i.jsx(l,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"欢迎使用 AetherLink"}),i.jsx(l,{variant:"body1",color:"text.secondary",children:"让我们一起完成几个简单的设置步骤，开始您的AI之旅"})]}),i.jsxs(d,{activeStep:D,orientation:"vertical",children:[i.jsxs(c,{children:[i.jsx(m,{StepIconComponent:()=>i.jsx(n,{sx:{width:32,height:32,bgcolor:D>=0?"primary.main":"action.disabled"},children:_[0]}),children:i.jsx(l,{fontWeight:"medium",children:"选择AI模型"})}),i.jsxs(h,{children:[i.jsx(l,{variant:"body2",paragraph:!0,children:"选择您想要使用的AI模型。您可以随时在设置中更改或添加更多模型。"}),i.jsx(r,{sx:{mb:2,mt:1,display:"grid",gridTemplateColumns:"repeat(2, 1fr)",gap:2},children:L.slice(0,4).map((e=>i.jsx(g,{elevation:V===e.id?4:1,sx:{p:2,textAlign:"center",cursor:"pointer",borderRadius:2,border:V===e.id?`2px solid ${T.palette.primary.main}`:"none",bgcolor:V===e.id?s(T.palette.primary.main,.1):"background.paper","&:hover":{bgcolor:s(T.palette.primary.main,.05)}},onClick:()=>Q(e.id),children:i.jsxs(r,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:1},children:[i.jsx(G,{providerType:e.provider,modelId:e.id,size:32}),i.jsx(l,{variant:"subtitle2",children:e.name}),i.jsx(l,{variant:"caption",color:"text.secondary",children:e.provider})]})},e.id)))}),i.jsxs(r,{sx:{mb:2,mt:3,display:"flex",justifyContent:"space-between"},children:[i.jsx(u,{onClick:async()=>{try{await k("first-time-user","false"),e("/chat")}catch(i){console.error("保存用户状态失败:",i),e("/chat")}},startIcon:i.jsx(S,{}),children:"跳过设置"}),i.jsx(u,{variant:"contained",onClick:$,endIcon:i.jsx(I,{}),children:"继续"})]})]})]}),i.jsxs(c,{children:[i.jsx(m,{StepIconComponent:()=>i.jsx(n,{sx:{width:32,height:32,bgcolor:D>=1?"primary.main":"action.disabled"},children:_[1]}),children:i.jsx(l,{fontWeight:"medium",children:"设置API密钥"})}),i.jsxs(h,{children:[i.jsxs(r,{sx:{mb:3},children:[i.jsxs(l,{variant:"body2",paragraph:!0,children:["输入您的",(null==Z?void 0:Z.provider)||"","API密钥。这将安全地存储在您的设备上，不会发送到我们的服务器。"]}),i.jsx(x,{fullWidth:!0,label:"API密钥",value:M,onChange:e=>{X(e.target.value),W("")},margin:"normal",type:"password",error:!!R,helperText:R||`请输入您的${(null==Z?void 0:Z.provider)||""} API密钥`,required:!0,variant:"outlined",sx:{mt:2},slotProps:{input:{"aria-invalid":!!R,"aria-describedby":"welcome-api-key-helper-text"},formHelperText:{id:"welcome-api-key-helper-text"}}})]}),i.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[i.jsx(u,{onClick:O,startIcon:i.jsx(q,{}),children:"返回"}),i.jsx(u,{variant:"contained",onClick:$,endIcon:i.jsx(I,{}),children:"继续"})]})]})]}),i.jsxs(c,{children:[i.jsx(m,{StepIconComponent:()=>i.jsx(n,{sx:{width:32,height:32,bgcolor:D>=2?"primary.main":"action.disabled"},children:_[2]}),children:i.jsx(l,{fontWeight:"medium",children:"完成设置"})}),i.jsxs(h,{children:[i.jsx(r,{sx:{mb:3},children:i.jsxs(l,{variant:"body2",paragraph:!0,children:["您已经成功配置了 ",i.jsx("strong",{children:null==Z?void 0:Z.name})," (",null==Z?void 0:Z.provider,')。 点击"完成"按钮开始使用AetherLink。']})}),i.jsxs(r,{sx:{display:"flex",justifyContent:"space-between"},children:[i.jsx(u,{onClick:O,startIcon:i.jsx(q,{}),disabled:z,children:"返回"}),i.jsx(u,{variant:"contained",onClick:$,disabled:z,children:z?i.jsxs(i.Fragment,{children:[i.jsx(p,{size:24,sx:{mr:1},color:"inherit"}),"设置中..."]}):"完成"})]})]})]})]})]})}),i.jsxs(l,{variant:"caption",color:"text.secondary",sx:{display:"block",textAlign:"center",mt:3,opacity:.7},children:["AetherLink © ",(new Date).getFullYear()," 版本 1.0.0"]})]})})};export{T as default};
