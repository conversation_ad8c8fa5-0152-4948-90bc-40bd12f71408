/**
 * 记忆数据库表结构定义
 * 扩展现有的 Dexie 数据库
 */

import type { MemoryRecord, MemoryCategory } from '../types/internalMemory';

// 数据库中的记忆记录接口
export interface MemoryDBRecord {
  id: string;                    // UUID主键
  userId: string;                // 用户标识
  content: string;               // 记忆内容
  category: MemoryCategory;      // 记忆分类
  importance: number;            // 1-10 重要性评分
  embedding: number[];           // 向量表示
  hash: string;                  // 内容哈希，用于去重
  createdAt: number;             // 创建时间戳
  updatedAt: number;             // 更新时间戳
  metadata: string;              // JSON字符串形式的元数据
}

// 记忆表索引定义
export const MEMORY_TABLE_SCHEMA = {
  // 主键和索引定义
  primaryKey: 'id',
  indexes: [
    'userId',                    // 按用户查询
    'category',                  // 按分类查询
    'importance',                // 按重要性查询
    'hash',                      // 去重查询
    'createdAt',                 // 按创建时间查询
    'updatedAt',                 // 按更新时间查询
    '[userId+category]',         // 复合索引：用户+分类
    '[userId+createdAt]',        // 复合索引：用户+创建时间
    '[userId+importance]'        // 复合索引：用户+重要性
  ]
};

// 数据库记录转换为内存记录
export function dbRecordToMemoryRecord(dbRecord: MemoryDBRecord): MemoryRecord {
  return {
    id: dbRecord.id,
    userId: dbRecord.userId,
    content: dbRecord.content,
    category: dbRecord.category,
    importance: dbRecord.importance,
    embedding: dbRecord.embedding,
    hash: dbRecord.hash,
    createdAt: new Date(dbRecord.createdAt),
    updatedAt: new Date(dbRecord.updatedAt),
    metadata: dbRecord.metadata ? JSON.parse(dbRecord.metadata) : {}
  };
}

// 内存记录转换为数据库记录
export function memoryRecordToDBRecord(memoryRecord: MemoryRecord): MemoryDBRecord {
  return {
    id: memoryRecord.id,
    userId: memoryRecord.userId,
    content: memoryRecord.content,
    category: memoryRecord.category,
    importance: memoryRecord.importance,
    embedding: memoryRecord.embedding,
    hash: memoryRecord.hash,
    createdAt: memoryRecord.createdAt.getTime(),
    updatedAt: memoryRecord.updatedAt.getTime(),
    metadata: JSON.stringify(memoryRecord.metadata)
  };
}

// 生成内容哈希
export function generateContentHash(content: string): string {
  // 简单的哈希函数，用于去重
  let hash = 0;
  if (content.length === 0) return hash.toString();
  
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  return Math.abs(hash).toString(36);
}

// 生成UUID
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
