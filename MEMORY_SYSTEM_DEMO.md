# AetherLink 内置记忆系统演示指南

## 概述

AetherLink 现已集成完整的内置记忆系统，基于 Mem0 架构设计，提供智能的个人化对话体验。

## 功能特点

### 🧠 智能记忆提取
- **自动学习**: AI 自动从对话中提取用户偏好、背景信息和重要事实
- **分类管理**: 记忆按照偏好、背景、技能、习惯、计划等分类存储
- **重要性评分**: 每条记忆都有 1-10 的重要性评分

### 🔍 语义搜索
- **向量检索**: 基于 OpenAI Embeddings 的语义相似度搜索
- **混合搜索**: 结合文本匹配和语义理解
- **智能排序**: 按相似度和重要性综合排序

### 💬 上下文增强
- **个性化回复**: 根据用户记忆提供个性化的回答
- **背景感知**: AI 了解用户的背景和偏好
- **连续对话**: 跨对话保持上下文连贯性

### 🛡️ 隐私保护
- **本地存储**: 所有记忆数据存储在用户设备上
- **无外部依赖**: 不依赖外部记忆服务
- **用户控制**: 用户完全控制记忆的创建、编辑和删除

## 使用指南

### 1. 启用记忆功能

1. 进入 **设置** → **智能记忆**
2. 开启 **启用智能记忆功能** 开关
3. 配置记忆提取和搜索参数（可选）

### 2. 开始对话

记忆系统会在对话过程中自动工作：

```
用户: 我喜欢喝咖啡，特别是拿铁
AI: 好的，我记住了您喜欢咖啡，特别是拿铁。有什么关于咖啡的问题我可以帮您解答吗？

用户: 推荐一些咖啡店
AI: 根据您喜欢拿铁的偏好，我为您推荐一些专业的咖啡店...
```

### 3. 查看记忆状态

在聊天界面的顶部工具栏，您会看到：
- 🧠 记忆指示器：显示当前对话使用的记忆数量
- 点击可查看具体使用了哪些记忆信息

### 4. 管理记忆

在 **设置** → **智能记忆** 页面可以：
- 查看所有记忆列表
- 搜索特定记忆
- 查看记忆统计信息
- 运行系统测试

## 配置选项

### 记忆提取设置
- **提取模型**: 选择用于记忆提取的 LLM 模型
- **最少对话轮数**: 达到此轮数后开始提取记忆
- **每次最多提取数**: 限制单次提取的记忆数量

### 搜索设置
- **嵌入模型**: 选择用于生成向量的嵌入模型
- **相似度阈值**: 控制搜索结果的相关性要求
- **最大搜索结果数**: 限制返回的记忆数量

### 存储设置
- **每用户最大记忆数**: 限制存储的记忆总数
- **记忆保留天数**: 自动清理过期记忆

## 技术架构

```
内置记忆系统
├── 记忆提取器 (MemoryExtractor)
│   └── 使用 LLM API 从对话中提取事实
├── 语义搜索 (SemanticSearchService)
│   └── 基于向量相似度的记忆检索
├── 上下文构建器 (ContextBuilder)
│   └── 将相关记忆整合到对话上下文
├── 本地存储 (InternalMemoryService)
│   └── 基于 IndexedDB 的本地数据存储
└── 记忆管理 (MemoryStorageService)
    └── 高级记忆管理和维护功能
```

## 数据流程

1. **对话输入** → 检查是否需要记忆增强
2. **记忆搜索** → 查找相关的历史记忆
3. **上下文构建** → 将记忆整合到提示词中
4. **AI 回复** → 生成个性化回答
5. **记忆提取** → 从新对话中提取记忆（异步）
6. **记忆存储** → 保存新记忆到本地数据库

## 最佳实践

### 用户使用建议
1. **明确表达偏好**: 清楚地告诉 AI 您的喜好和需求
2. **提供背景信息**: 分享相关的个人背景和经历
3. **定期检查记忆**: 在设置中查看和管理记忆内容
4. **调整配置**: 根据使用体验调整记忆系统参数

### 开发者集成建议
1. **渐进式启用**: 可以为新用户默认关闭，让用户主动启用
2. **性能监控**: 监控记忆提取和搜索的性能影响
3. **用户反馈**: 收集用户对记忆准确性的反馈
4. **数据备份**: 提供记忆数据的导出和导入功能

## 故障排除

### 常见问题

**Q: 记忆功能不工作？**
A: 检查是否已启用记忆功能，确认 API 密钥配置正确。

**Q: 记忆提取不准确？**
A: 可以调整提取模型或增加最少对话轮数要求。

**Q: 搜索结果不相关？**
A: 降低相似度阈值或更换嵌入模型。

**Q: 记忆数据丢失？**
A: 记忆存储在本地 IndexedDB 中，清除浏览器数据会导致丢失。

### 系统测试

在记忆管理页面可以运行系统测试：
- 服务初始化测试
- 记忆创建和保存测试
- 记忆检索测试
- 文本搜索测试
- 数据清理测试

## 未来扩展

计划中的功能增强：
- 记忆聚类和关联分析
- 记忆重要性自动调整
- 跨设备记忆同步
- 记忆可视化界面
- 更多记忆分类支持

## 技术支持

如遇到问题，请：
1. 查看浏览器控制台错误信息
2. 运行系统测试检查功能状态
3. 检查网络连接和 API 配置
4. 联系技术支持团队

---

**注意**: 记忆系统需要有效的 OpenAI API 密钥来进行记忆提取和向量生成。请确保在模型配置中正确设置了 API 密钥。
