import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  List,
  ListItemText,

  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Paper,
  Divider,
  Chip,
  Alert,
  Switch,
  FormControlLabel,
  AppBar,
  Toolbar,
  alpha,
  ListItemButton,
  Avatar,
  ListItemAvatar
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Plus as AddIcon,
  Edit as EditIcon,
  Trash2 as DeleteIcon,
  Zap
} from 'lucide-react';
import QuickPhraseService from '../shared/services/QuickPhraseService';
import type { QuickPhrase } from '../shared/types';
import type { RootState } from '../shared/store';
import { setShowQuickPhraseButton } from '../shared/store/settingsSlice';

const QuickPhraseSettings: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // 从Redux获取快捷短语按钮显示设置
  const showQuickPhraseButton = useSelector((state: RootState) => state.settings.showQuickPhraseButton ?? true);

  const [phrases, setPhrases] = useState<QuickPhrase[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPhrase, setEditingPhrase] = useState<QuickPhrase | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    content: ''
  });

  // 返回按钮处理
  const handleBack = () => {
    navigate('/settings');
  };

  // 加载快捷短语
  const loadPhrases = useCallback(async () => {
    try {
      setLoading(true);
      const allPhrases = await QuickPhraseService.getAll();
      setPhrases(allPhrases);
    } catch (error) {
      console.error('加载快捷短语失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadPhrases();
  }, [loadPhrases]);

  // 打开添加对话框
  const handleAdd = () => {
    setEditingPhrase(null);
    setFormData({ title: '', content: '' });
    setDialogOpen(true);
  };

  // 打开编辑对话框
  const handleEdit = (phrase: QuickPhrase) => {
    setEditingPhrase(phrase);
    setFormData({
      title: phrase.title,
      content: phrase.content
    });
    setDialogOpen(true);
  };

  // 删除快捷短语
  const handleDelete = async (id: string) => {
    try {
      await QuickPhraseService.delete(id);
      await loadPhrases();
    } catch (error) {
      console.error('删除快捷短语失败:', error);
    }
  };

  // 关闭对话框
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingPhrase(null);
    setFormData({ title: '', content: '' });
  };

  // 保存快捷短语
  const handleSave = async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      return;
    }

    try {
      if (editingPhrase) {
        // 更新现有短语
        await QuickPhraseService.update(editingPhrase.id, {
          title: formData.title,
          content: formData.content
        });
      } else {
        // 添加新短语
        await QuickPhraseService.add({
          title: formData.title,
          content: formData.content
        });
      }

      handleCloseDialog();
      await loadPhrases();
    } catch (error) {
      console.error('保存快捷短语失败:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        bgcolor: (theme) => theme.palette.mode === 'light'
          ? alpha(theme.palette.primary.main, 0.02)
          : alpha(theme.palette.background.default, 0.9),
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <Typography>加载中...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      flexGrow: 1,
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      bgcolor: (theme) => theme.palette.mode === 'light'
        ? alpha(theme.palette.primary.main, 0.02)
        : alpha(theme.palette.background.default, 0.9),
    }}>
      {/* 顶部导航栏 - 采用模型配置风格 */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          bgcolor: 'background.paper',
          color: 'text.primary',
          borderBottom: 1,
          borderColor: 'divider',
          backdropFilter: 'blur(8px)',
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleBack}
            aria-label="back"
            sx={{
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              fontWeight: 600,
              backgroundImage: 'linear-gradient(90deg, #9333EA, #754AB4)',
              backgroundClip: 'text',
              color: 'transparent',
            }}
          >
            快捷短语设置
          </Typography>
          <Button
            startIcon={<AddIcon size={16} />}
            onClick={handleAdd}
            sx={{
              bgcolor: (theme) => alpha(theme.palette.primary.main, 0.1),
              color: 'primary.main',
              '&:hover': {
                bgcolor: (theme) => alpha(theme.palette.primary.main, 0.2),
              },
              borderRadius: 2,
            }}
          >
            添加
          </Button>
        </Toolbar>
      </AppBar>

      {/* 内容区域 */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          p: { xs: 1, sm: 2 },
          mt: 8,
          '&::-webkit-scrollbar': {
            width: { xs: '4px', sm: '6px' },
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.1)',
            borderRadius: '3px',
          },
        }}
      >
        {/* 显示设置 */}
        <Paper
          elevation={0}
          sx={{
            mb: 2,
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'divider',
            overflow: 'hidden',
            bgcolor: 'background.paper',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          }}
        >
          <Box sx={{ p: { xs: 1.5, sm: 2 }, bgcolor: 'rgba(0,0,0,0.01)' }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                fontSize: { xs: '1rem', sm: '1.1rem' }
              }}
            >
              显示设置
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
            >
              控制快捷短语按钮在聊天界面的显示
            </Typography>
          </Box>

          <Divider />

          <Box sx={{ p: { xs: 1.5, sm: 2 } }}>
            <FormControlLabel
              control={
                <Switch
                  checked={showQuickPhraseButton}
                  onChange={(e) => dispatch(setShowQuickPhraseButton(e.target.checked))}
                />
              }
              label="在输入框显示快捷短语按钮"
            />
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                display: 'block',
                mt: 0.5,
                fontSize: { xs: '0.7rem', sm: '0.75rem' }
              }}
            >
              控制是否在聊天输入框中显示快捷短语按钮
            </Typography>
          </Box>
        </Paper>

        {/* 快捷短语列表 */}
        <Paper
          elevation={0}
          sx={{
            mb: 2,
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'divider',
            overflow: 'hidden',
            bgcolor: 'background.paper',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          }}
        >
          <Box sx={{ p: { xs: 1.5, sm: 2 }, bgcolor: 'rgba(0,0,0,0.01)' }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                fontSize: { xs: '1rem', sm: '1.1rem' }
              }}
            >
              快捷短语列表
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
            >
              管理您的快捷短语，在聊天时快速插入常用内容
            </Typography>
          </Box>

          <Divider />

          {phrases.length === 0 ? (
            <Box sx={{ p: { xs: 3, sm: 4 }, textAlign: 'center' }}>
              <Avatar
                sx={{
                  bgcolor: alpha('#9333EA', 0.12),
                  color: '#9333EA',
                  width: { xs: 48, sm: 64 },
                  height: { xs: 48, sm: 64 },
                  mx: 'auto',
                  mb: 2,
                }}
              >
                <Zap size={24} />
              </Avatar>
              <Typography
                variant="h6"
                color="text.secondary"
                gutterBottom
                sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
              >
                还没有快捷短语
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mb: 2,
                  fontSize: { xs: '0.8rem', sm: '0.875rem' }
                }}
              >
                创建您的第一个快捷短语，让聊天更高效
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon size={16} />}
                onClick={handleAdd}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' }
                }}
              >
                添加快捷短语
              </Button>
            </Box>
          ) : (
            <List disablePadding>
              {phrases.map((phrase, index) => (
                <React.Fragment key={phrase.id}>
                  <ListItemButton
                    sx={{
                      transition: 'all 0.2s',
                      '&:hover': {
                        bgcolor: (theme) => alpha(theme.palette.primary.main, 0.05),
                      },
                      py: { xs: 1, sm: 1.5 },
                      px: { xs: 1.5, sm: 2 }
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: alpha('#9333EA', 0.12),
                          color: '#9333EA',
                          boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                          width: { xs: 32, sm: 40 },
                          height: { xs: 32, sm: 40 }
                        }}
                      >
                        <Zap size={16} />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              fontWeight: 600,
                              fontSize: { xs: '0.9rem', sm: '1rem' }
                            }}
                          >
                            {phrase.title}
                          </Typography>
                          <Chip
                            size="small"
                            label={`${phrase.content.length} 字符`}
                            variant="outlined"
                            sx={{
                              fontSize: { xs: '0.7rem', sm: '0.75rem' },
                              height: { xs: 20, sm: 24 }
                            }}
                          />
                        </Box>
                      }
                      secondary={
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            mt: 0.5,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            fontSize: { xs: '0.8rem', sm: '0.875rem' }
                          }}
                        >
                          {phrase.content}
                        </Typography>
                      }
                    />
                    <Box sx={{ display: 'flex', gap: 0.5, ml: 'auto' }}>
                      <IconButton
                        onClick={() => handleEdit(phrase)}
                        size="small"
                        sx={{
                          color: 'text.secondary',
                          '&:hover': {
                            color: 'primary.main',
                            bgcolor: (theme) => alpha(theme.palette.primary.main, 0.1),
                          },
                        }}
                      >
                        <EditIcon size={16} />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDelete(phrase.id)}
                        size="small"
                        sx={{
                          color: 'text.secondary',
                          '&:hover': {
                            color: 'error.main',
                            bgcolor: (theme) => alpha(theme.palette.error.main, 0.1),
                          },
                        }}
                      >
                        <DeleteIcon size={16} />
                      </IconButton>
                    </Box>
                  </ListItemButton>
                  {index < phrases.length - 1 && <Divider variant="inset" component="li" sx={{ ml: 0 }} />}
                </React.Fragment>
              ))}
            </List>
          )}
        </Paper>
      </Box>

      {/* 添加/编辑对话框 */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            }
          }
        }}
      >
        <DialogTitle
          sx={{
            fontWeight: 600,
            backgroundImage: 'linear-gradient(90deg, #9333EA, #754AB4)',
            backgroundClip: 'text',
            color: 'transparent',
            pb: 1
          }}
        >
          {editingPhrase ? '编辑快捷短语' : '添加快捷短语'}
        </DialogTitle>
        <DialogContent sx={{ pt: 2 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="标题"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              fullWidth
              size="small"
              placeholder="为您的快捷短语起个名字"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                }
              }}
            />

            <TextField
              label="内容"
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              multiline
              rows={6}
              fullWidth
              size="small"
              placeholder="输入快捷短语的内容..."
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                }
              }}
            />

            {formData.content && (
              <Alert
                severity="info"
                sx={{
                  borderRadius: 2,
                  '& .MuiAlert-message': {
                    fontSize: '0.875rem'
                  }
                }}
              >
                内容长度：{formData.content.length} 字符
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 1 }}>
          <Button
            onClick={handleCloseDialog}
            sx={{
              borderRadius: 2,
              textTransform: 'none'
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            disabled={!formData.title.trim() || !formData.content.trim()}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              backgroundImage: 'linear-gradient(90deg, #9333EA, #754AB4)',
              '&:hover': {
                backgroundImage: 'linear-gradient(90deg, #7c2d92, #6b4397)',
              }
            }}
          >
            {editingPhrase ? '更新' : '添加'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default QuickPhraseSettings;
