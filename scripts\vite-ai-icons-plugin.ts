import type { Plugin } from 'vite';
import { createHash } from 'crypto';
import fs from 'fs';
import path from 'path';

interface AiIconsPluginOptions {
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 缓存目录 */
  cacheDir?: string;
  /** 是否输出详细日志 */
  verbose?: boolean;
  /** 强制预构建的图标 */
  preloadIcons?: string[];
}

/**
 * AI图标库优化插件
 * 专门优化 @lobehub/icons 的构建缓存和分割策略
 */
export function aiIconsPlugin(options: AiIconsPluginOptions = {}): Plugin {
  const {
    enableCache = true,
    cacheDir = 'node_modules/.vite/ai-icons-cache',
    verbose = false,
    preloadIcons = [
      'OpenAI', 'Claude', 'Gemini', 'ChatGLM', 'Qwen', 'DeepSeek',
      'Groq', 'Mistral', 'Baidu', 'Tencent', 'ByteDance', 'Alibaba'
    ]
  } = options;

  let isProduction = false;
  let cacheHash = '';

  const log = (message: string) => {
    if (verbose) {
      console.log(`[AI Icons Plugin] ${message}`);
    }
  };

  return {
    name: 'vite-ai-icons-plugin',
    
    configResolved(config) {
      isProduction = config.command === 'build';
      
      // 生成缓存哈希
      const packageJsonPath = path.resolve('package.json');
      const packageJson = fs.existsSync(packageJsonPath) 
        ? fs.readFileSync(packageJsonPath, 'utf-8') 
        : '{}';
      
      const lobehubIconsVersion = JSON.parse(packageJson).dependencies?.['@lobehub/icons'] || 'unknown';
      cacheHash = createHash('md5')
        .update(lobehubIconsVersion + JSON.stringify(preloadIcons))
        .digest('hex')
        .substring(0, 8);
      
      log(`缓存哈希: ${cacheHash}, 生产模式: ${isProduction}`);
    },

    buildStart() {
      if (enableCache && isProduction) {
        // 确保缓存目录存在
        if (!fs.existsSync(cacheDir)) {
          fs.mkdirSync(cacheDir, { recursive: true });
          log(`创建缓存目录: ${cacheDir}`);
        }
      }
    },

    resolveId(id) {
      // 优化 @lobehub/icons 的解析
      if (id === '@lobehub/icons' || id.startsWith('@lobehub/icons/')) {
        log(`解析图标库: ${id}`);
        return null; // 让 Vite 正常处理
      }
    },

    load(id) {
      // 对于预加载的图标，确保它们被正确标记
      if (id.includes('@lobehub/icons') && isProduction) {
        const iconName = this.getModuleInfo(id)?.meta?.iconName;
        if (iconName && preloadIcons.includes(iconName)) {
          log(`预加载图标: ${iconName}`);
        }
      }
      return null;
    },

    generateBundle(options, bundle) {
      if (!isProduction) return;

      // 分析图标库的使用情况
      const iconUsage = new Map<string, number>();
      
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'chunk') {
          // 分析代码中的图标使用
          const iconMatches = chunk.code.match(/@lobehub\/icons/g);
          if (iconMatches) {
            iconUsage.set(fileName, iconMatches.length);
            log(`文件 ${fileName} 使用了 ${iconMatches.length} 个图标`);
          }
        }
      }

      // 输出图标使用统计
      if (verbose && iconUsage.size > 0) {
        console.log('\n[AI Icons Plugin] 图标使用统计:');
        for (const [file, count] of iconUsage.entries()) {
          console.log(`  ${file}: ${count} 个图标`);
        }
      }
    },

    configureServer(server) {
      // 开发模式下的优化
      server.middlewares.use('/ai-icons-cache', (req, res, next) => {
        // 为图标资源添加长期缓存头
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        next();
      });
    }
  };
}

/**
 * 获取图标库的缓存键
 */
function getIconsCacheKey(version: string, preloadIcons: string[]): string {
  return createHash('md5')
    .update(version + JSON.stringify(preloadIcons.sort()))
    .digest('hex');
}

/**
 * 检查缓存是否有效
 */
function isCacheValid(cacheDir: string, cacheKey: string): boolean {
  const cacheFile = path.join(cacheDir, `${cacheKey}.json`);
  return fs.existsSync(cacheFile);
}

/**
 * 保存缓存信息
 */
function saveCache(cacheDir: string, cacheKey: string, data: any): void {
  const cacheFile = path.join(cacheDir, `${cacheKey}.json`);
  fs.writeFileSync(cacheFile, JSON.stringify(data, null, 2));
}

/**
 * 读取缓存信息
 */
function loadCache(cacheDir: string, cacheKey: string): any {
  const cacheFile = path.join(cacheDir, `${cacheKey}.json`);
  if (fs.existsSync(cacheFile)) {
    return JSON.parse(fs.readFileSync(cacheFile, 'utf-8'));
  }
  return null;
}
