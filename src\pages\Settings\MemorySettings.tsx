import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  AppBar,
  Toolbar,
  IconButton,
  Paper,
  List,
  ListItem,
  ListItemText,
  Switch,
  Divider,
  alpha,
  Button,
  Slider,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Tabs,
  Tab,
  Alert,
  TextField,
  InputAdornment,
  CircularProgress,
  Card,
  CardContent
} from '@mui/material';
import { ArrowLeft, Brain, Bot, Search, Database, List as ListIcon, BarChart, Bug, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState } from '../../shared/store';
import { updateConfig } from '../../shared/store/slices/internalMemorySlice';
import { getMemoryConfig } from '../../shared/config/internalMemoryConfig';
import { getAvailableEmbeddingModels } from '../../shared/services/MobileEmbeddingService';
import { dexieStorage } from '../../shared/services/DexieStorageService';
import DialogModelSelector from '../../pages/ChatPage/components/DialogModelSelector';
import DropdownModelSelector from '../../pages/ChatPage/components/DropdownModelSelector';
import { loadUserMemories, loadMemoryStats, selectMemories, selectMemoryStats, selectMemoryLoading, saveMemory, deleteMemory } from '../../shared/store/slices/internalMemorySlice';
import { MemorySystemTest } from '../../shared/services/memory/MemorySystemTest';
import { MEMORY_CATEGORY_LABELS } from '../../shared/config/internalMemoryConfig';

// 标签页面板组件
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`memory-tabpanel-${index}`}
      aria-labelledby={`memory-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const MemorySettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // 获取当前设置
  const memoryConfig = useSelector((state: RootState) => state.internalMemory.config);
  const providers = useSelector((state: RootState) => state.settings.providers || []);
  const modelSelectorStyle = useSelector((state: RootState) => state.settings.modelSelectorStyle || 'dialog');

  // 本地状态
  const [localConfig, setLocalConfig] = useState(memoryConfig);
  const [isLoading, setIsLoading] = useState(true);
  const [showSaveAlert, setShowSaveAlert] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // 模型选择器状态
  const [memoryModelSelectorOpen, setMemoryModelSelectorOpen] = useState(false);

  // 获取可用模型
  const [availableModels, setAvailableModels] = useState<any[]>([]);
  const [availableEmbeddingModels, setAvailableEmbeddingModels] = useState<any[]>([]);

  // 记忆管理状态
  const memories = useSelector(selectMemories);
  const memoryStats = useSelector(selectMemoryStats);
  const memoryLoading = useSelector(selectMemoryLoading);
  const [searchQuery, setSearchQuery] = useState('');

  // 系统测试状态
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTest, setIsRunningTest] = useState(false);

  // 模拟用户ID（在实际应用中应该从认证系统获取）
  const userId = 'demo-user';

  // 计算可用模型
  useEffect(() => {
    const models = providers
      .filter(provider => provider.isEnabled)
      .flatMap(provider =>
        provider.models
          .filter(model => model.enabled)
          .map(model => ({
            ...model,
            providerName: provider.name,
            providerColor: provider.color,
            providerAvatar: provider.avatar
          }))
      );
    setAvailableModels(models);
  }, [providers]);

  // 初始化嵌入模型
  useEffect(() => {
    const embeddingModels = getAvailableEmbeddingModels();
    setAvailableEmbeddingModels(embeddingModels);
  }, []);

  // 加载记忆数据
  useEffect(() => {
    console.log('🔍 记忆数据加载检查:', {
      enabled: localConfig.enabled,
      userId,
      memoryLoading
    });

    if (localConfig.enabled) {
      console.log('📥 开始加载记忆数据...');
      dispatch(loadUserMemories(userId) as any);
      dispatch(loadMemoryStats(userId) as any);
    } else {
      console.log('⚠️ 记忆功能未启用，跳过数据加载');
    }
  }, [dispatch, userId, localConfig.enabled]);

  // 从数据库加载配置
  useEffect(() => {
    const loadConfigFromDatabase = async () => {
      try {
        setIsLoading(true);
        const memoryConfigKey = 'memory_config_global';
        const savedConfig = await dexieStorage.getSetting(memoryConfigKey);
        
        if (savedConfig) {
          setLocalConfig(savedConfig);
          dispatch(updateConfig(savedConfig));
        } else {
          const defaultConfig = getMemoryConfig();
          setLocalConfig(defaultConfig);
          await dexieStorage.saveSetting(memoryConfigKey, defaultConfig);
        }
      } catch (error) {
        console.error('加载配置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadConfigFromDatabase();
  }, [dispatch]);

  // 处理返回按钮点击
  const handleBack = () => {
    navigate('/settings');
  };

  // 保存配置
  const handleSave = async () => {
    try {
      console.log('💾 保存记忆配置:', localConfig);

      // 更新 Redux store
      dispatch(updateConfig(localConfig));

      // 保存到数据库
      const memoryConfigKey = 'memory_config_global';
      await dexieStorage.saveSetting(memoryConfigKey, localConfig);

      // 强制更新全局配置
      const { updateMemoryConfig } = await import('../../shared/config/internalMemoryConfig');
      updateMemoryConfig(localConfig);

      console.log('✅ 记忆配置保存成功，当前嵌入模型:', localConfig.search.embeddingModel);

      setShowSaveAlert(true);
      setTimeout(() => setShowSaveAlert(false), 3000);
    } catch (error) {
      console.error('保存设置时出错:', error);
      alert('保存设置失败，请重试');
    }
  };

  // 处理配置更新
  const updateLocalConfig = (updates: any) => {
    setLocalConfig(prev => ({ ...prev, ...updates }));
  };

  // 处理标签页切换
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // 处理记忆模型选择
  const handleMemoryModelSelect = (model: any) => {
    updateLocalConfig({
      extraction: {
        ...localConfig.extraction,
        model: model.id
      }
    });
    setMemoryModelSelectorOpen(false);
  };

  // 获取当前选中的模型
  const selectedMemoryModel = availableModels.find(m => m.id === localConfig.extraction.model) || null;

  // 过滤记忆
  const filteredMemories = memories.filter(memory =>
    memory.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 重新生成所有记忆的嵌入向量
  const regenerateEmbeddings = async () => {
    setIsRunningTest(true);
    setTestResults(null);

    try {
      console.log('🔄 开始重新生成记忆嵌入向量...');

      // 获取所有记忆
      const allMemories = memories || [];
      console.log('📋 找到记忆数量:', allMemories.length);

      if (allMemories.length === 0) {
        setTestResults({
          success: true,
          results: { '记忆数量': 0 },
          errors: ['没有需要重新生成嵌入向量的记忆']
        });
        return;
      }

      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      // 导入记忆服务
      const { MemoryEmbeddingService } = await import('../../shared/services/memory/EmbeddingService');
      const embeddingService = MemoryEmbeddingService.getInstance();

      // 逐个重新生成嵌入向量
      for (const memory of allMemories) {
        try {
          console.log(`🔄 重新生成记忆 ${memory.id} 的嵌入向量...`);

          // 使用嵌入服务重新生成嵌入向量
          const newEmbedding = await embeddingService.generateMemoryEmbedding(memory.content);

          // 更新记忆的嵌入向量
          const updatedMemory = {
            ...memory,
            embedding: newEmbedding,
            updatedAt: new Date()
          };

          await dispatch(saveMemory(updatedMemory) as any);
          successCount++;
          console.log(`✅ 记忆 ${memory.id} 嵌入向量重新生成成功，维度:`, newEmbedding.length);
        } catch (error) {
          errorCount++;
          const errorMsg = `记忆 ${memory.id} 重新生成失败: ${error instanceof Error ? error.message : '未知错误'}`;
          errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
        }
      }

      // 重新加载记忆数据
      await dispatch(loadUserMemories('demo-user') as any);
      await dispatch(loadMemoryStats('demo-user') as any);

      setTestResults({
        success: errorCount === 0,
        results: {
          '总记忆数': allMemories.length,
          '成功重新生成': successCount,
          '失败数量': errorCount,
          '嵌入向量更新': errorCount === 0
        },
        errors
      });

      console.log('🎯 嵌入向量重新生成完成:', { successCount, errorCount });
    } catch (error) {
      console.error('❌ 重新生成嵌入向量失败:', error);
      setTestResults({
        success: false,
        results: {},
        errors: [error instanceof Error ? error.message : '未知错误']
      });
    } finally {
      setIsRunningTest(false);
    }
  };

  // 删除单个记忆
  const handleDeleteMemory = async (memoryId: string) => {
    try {
      await dispatch(deleteMemory(memoryId) as any);
      console.log('✅ 记忆删除成功:', memoryId);
    } catch (error) {
      console.error('❌ 删除记忆失败:', error);
      alert('删除记忆失败，请重试');
    }
  };

  // 运行系统测试
  const runSystemTest = async () => {
    setIsRunningTest(true);
    try {
      const tester = new MemorySystemTest();
      const results = await tester.runBasicTests();
      setTestResults(results);
    } catch (error) {
      console.error('Test failed:', error);
      setTestResults({
        success: false,
        results: {},
        errors: [error instanceof Error ? error.message : String(error)]
      });
    } finally {
      setIsRunningTest(false);
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Typography>正在加载记忆设置...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: (theme) => theme.palette.mode === 'light'
        ? alpha(theme.palette.primary.main, 0.02)
        : alpha(theme.palette.background.default, 0.9),
    }}>
      {/* 顶部导航栏 - 采用模型配置风格 */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          bgcolor: 'background.paper',
          color: 'text.primary',
          borderBottom: 1,
          borderColor: 'divider',
          backdropFilter: 'blur(8px)',
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleBack}
            aria-label="back"
            sx={{
              color: (theme) => theme.palette.primary.main,
            }}
          >
            <ArrowLeft size={20} />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              fontWeight: 600,
              backgroundImage: 'linear-gradient(90deg, #4f46e5, #8b5cf6)',
              backgroundClip: 'text',
              color: 'transparent',
            }}
          >
            记忆功能设置
          </Typography>
          <Button
            variant="contained"
            onClick={handleSave}
            sx={{
              bgcolor: 'primary.main',
              '&:hover': { bgcolor: 'primary.dark' },
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600
            }}
          >
            保存设置
          </Button>
        </Toolbar>
      </AppBar>

      {/* 成功提示 */}
      {showSaveAlert && (
        <Alert severity="success" sx={{ m: 0, borderRadius: 0, mt: 8 }}>
          记忆设置已保存到数据库
        </Alert>
      )}

      {/* 标签页导航 */}
      <Box sx={{
        borderBottom: 1,
        borderColor: 'divider',
        bgcolor: 'background.paper',
        mt: showSaveAlert ? 0 : 8
      }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{ px: 2 }}
        >
          <Tab
            icon={<Brain size={20} />}
            label="基础设置"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
          <Tab
            icon={<Bot size={20} />}
            label="记忆提取"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
          <Tab
            icon={<Search size={20} />}
            label="记忆搜索"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
          <Tab
            icon={<Database size={20} />}
            label="存储设置"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
          <Tab
            icon={<ListIcon size={20} />}
            label="我的记忆"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
          <Tab
            icon={<BarChart size={20} />}
            label="统计分析"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
          <Tab
            icon={<Bug size={20} />}
            label="系统测试"
            sx={{ textTransform: 'none', fontWeight: 600 }}
          />
        </Tabs>
      </Box>

      {/* 主要内容区域 - 采用模型配置风格 */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          p: 2,
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.1)',
            borderRadius: '3px',
          },
        }}
      >
        {/* 基础设置标签页 */}
        <TabPanel value={tabValue} index={0}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                记忆功能开关
              </Typography>
              <Typography variant="body2" color="text.secondary">
                启用后，AI将能够记住对话中的重要信息，提供个性化体验
              </Typography>
            </Box>

            <Divider />

            <List disablePadding>
              <ListItem>
                <ListItemText primary="启用记忆功能" />
                <Switch
                  edge="end"
                  checked={localConfig.enabled}
                  onChange={(e) => updateLocalConfig({ enabled: e.target.checked })}
                />
              </ListItem>
            </List>
          </Paper>
        </TabPanel>

        {/* 记忆提取设置标签页 */}
        <TabPanel value={tabValue} index={1}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                记忆提取模型
              </Typography>
              <Typography variant="body2" color="text.secondary">
                选择用于从对话中提取记忆信息的AI模型
              </Typography>
            </Box>

            <Divider />

            <Box sx={{ p: 2 }}>
              {modelSelectorStyle === 'dropdown' ? (
                <DropdownModelSelector
                  selectedModel={selectedMemoryModel}
                  availableModels={availableModels}
                  handleModelSelect={handleMemoryModelSelect}
                />
              ) : (
                <DialogModelSelector
                  selectedModel={selectedMemoryModel}
                  availableModels={availableModels}
                  handleModelSelect={handleMemoryModelSelect}
                  handleMenuClick={() => setMemoryModelSelectorOpen(true)}
                  handleMenuClose={() => setMemoryModelSelectorOpen(false)}
                  menuOpen={memoryModelSelectorOpen}
                />
              )}
            </Box>
          </Paper>

          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                提取参数设置
              </Typography>
              <Typography variant="body2" color="text.secondary">
                配置记忆提取的触发条件和数量限制
              </Typography>
            </Box>

            <Divider />

            <List disablePadding>
              <ListItem>
                <ListItemText
                  primary="最少对话轮数"
                  secondary={`当前设置：${localConfig.extraction.minConversationLength} 轮 - 达到此轮数后开始提取记忆`}
                />
              </ListItem>
              <ListItem>
                <Box sx={{ width: '100%', px: 2 }}>
                  <Slider
                    value={localConfig.extraction.minConversationLength}
                    onChange={(_, value) => updateLocalConfig({
                      extraction: {
                        ...localConfig.extraction,
                        minConversationLength: value as number
                      }
                    })}
                    min={1}
                    max={10}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    color="primary"
                  />
                </Box>
              </ListItem>

              <Divider />

              <ListItem>
                <ListItemText
                  primary="每次最大提取记忆数"
                  secondary={`当前设置：${localConfig.extraction.maxMemoriesPerExtraction} 条 - 限制单次提取的记忆数量`}
                />
              </ListItem>
              <ListItem>
                <Box sx={{ width: '100%', px: 2 }}>
                  <Slider
                    value={localConfig.extraction.maxMemoriesPerExtraction}
                    onChange={(_, value) => updateLocalConfig({
                      extraction: {
                        ...localConfig.extraction,
                        maxMemoriesPerExtraction: value as number
                      }
                    })}
                    min={1}
                    max={20}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    color="primary"
                  />
                </Box>
              </ListItem>
            </List>
          </Paper>
        </TabPanel>

        {/* 记忆搜索设置标签页 */}
        <TabPanel value={tabValue} index={2}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                嵌入模型设置
              </Typography>
              <Typography variant="body2" color="text.secondary">
                选择用于生成记忆向量的嵌入模型
              </Typography>
            </Box>

            <Divider />

            <Box sx={{ p: 2 }}>
              <FormControl fullWidth>
                <Select
                  value={localConfig.search.embeddingModel}
                  onChange={(e) => updateLocalConfig({
                    search: {
                      ...localConfig.search,
                      embeddingModel: e.target.value
                    }
                  })}
                  displayEmpty
                  sx={{ borderRadius: 1 }}
                >
                  {availableEmbeddingModels.map((model) => (
                    <MenuItem key={model.id} value={model.id}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={model.provider}
                          size="small"
                          sx={{ fontSize: '0.75rem' }}
                        />
                        {model.name}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Paper>

          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                搜索参数设置
              </Typography>
              <Typography variant="body2" color="text.secondary">
                配置记忆搜索的相似度阈值和结果数量
              </Typography>
            </Box>

            <Divider />

            <List disablePadding>
              <ListItem>
                <ListItemText
                  primary="相似度阈值"
                  secondary={`当前设置：${localConfig.search.similarityThreshold} - 控制搜索结果的相关性要求`}
                />
              </ListItem>
              <ListItem>
                <Box sx={{ width: '100%', px: 2 }}>
                  <Slider
                    value={localConfig.search.similarityThreshold}
                    onChange={(_, value) => updateLocalConfig({
                      search: {
                        ...localConfig.search,
                        similarityThreshold: value as number
                      }
                    })}
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    marks
                    valueLabelDisplay="auto"
                    color="primary"
                  />
                </Box>
              </ListItem>

              <Divider />

              <ListItem>
                <ListItemText
                  primary="最大搜索结果数"
                  secondary={`当前设置：${localConfig.search.maxResults} 条 - 限制返回的记忆数量`}
                />
              </ListItem>
              <ListItem>
                <Box sx={{ width: '100%', px: 2 }}>
                  <Slider
                    value={localConfig.search.maxResults}
                    onChange={(_, value) => updateLocalConfig({
                      search: {
                        ...localConfig.search,
                        maxResults: value as number
                      }
                    })}
                    min={1}
                    max={20}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    color="primary"
                  />
                </Box>
              </ListItem>
            </List>
          </Paper>
        </TabPanel>

        {/* 存储设置标签页 */}
        <TabPanel value={tabValue} index={3}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                存储容量设置
              </Typography>
              <Typography variant="body2" color="text.secondary">
                配置记忆存储的容量限制和清理策略
              </Typography>
            </Box>

            <Divider />

            <List disablePadding>
              <ListItem>
                <ListItemText
                  primary="每用户最大记忆数"
                  secondary={`当前设置：${localConfig.storage.maxMemoriesPerUser} 条 - 超出限制时自动清理旧记忆`}
                />
              </ListItem>
              <ListItem>
                <Box sx={{ width: '100%', px: 2 }}>
                  <Slider
                    value={localConfig.storage.maxMemoriesPerUser}
                    onChange={(_, value) => updateLocalConfig({
                      storage: {
                        ...localConfig.storage,
                        maxMemoriesPerUser: value as number
                      }
                    })}
                    min={100}
                    max={5000}
                    step={100}
                    marks
                    valueLabelDisplay="auto"
                    color="primary"
                  />
                </Box>
              </ListItem>

              <Divider />

              <ListItem>
                <ListItemText
                  primary="记忆保留天数"
                  secondary={`当前设置：${localConfig.storage.retentionDays} 天 - 超过此天数的记忆将被自动清理`}
                />
              </ListItem>
              <ListItem>
                <Box sx={{ width: '100%', px: 2 }}>
                  <Slider
                    value={localConfig.storage.retentionDays}
                    onChange={(_, value) => updateLocalConfig({
                      storage: {
                        ...localConfig.storage,
                        retentionDays: value as number
                      }
                    })}
                    min={7}
                    max={365}
                    step={7}
                    marks={[
                      { value: 7, label: '7天' },
                      { value: 30, label: '30天' },
                      { value: 90, label: '90天' },
                      { value: 180, label: '180天' },
                      { value: 365, label: '365天' }
                    ]}
                    valueLabelDisplay="auto"
                    color="primary"
                  />
                </Box>
              </ListItem>
            </List>
          </Paper>
        </TabPanel>

        {/* 我的记忆标签页 */}
        <TabPanel value={tabValue} index={4}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                记忆搜索
              </Typography>
              <Typography variant="body2" color="text.secondary">
                搜索和查看已保存的记忆信息
              </Typography>
            </Box>

            <Divider />

            <Box sx={{ p: 2 }}>
              <TextField
                fullWidth
                placeholder="搜索记忆内容..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search size={20} />
                      </InputAdornment>
                    ),
                  },
                }}
                sx={{ mb: 2 }}
              />

              {memoryLoading.memories ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                    正在加载记忆数据...
                  </Typography>
                </Box>
              ) : (
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    共找到 {filteredMemories.length} 条记忆
                  </Typography>

                  {filteredMemories.length === 0 ? (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {searchQuery ? '没有找到匹配的记忆' : '暂无记忆数据'}
                      </Typography>
                      {!localConfig.enabled && (
                        <Typography variant="body2" color="warning.main">
                          记忆功能未启用，请在"基础设置"中开启
                        </Typography>
                      )}
                      {localConfig.enabled && memories.length === 0 && (
                        <Typography variant="body2" color="info.main">
                          开始对话后，AI将自动提取重要信息作为记忆
                        </Typography>
                      )}
                    </Box>
                  ) : (
                    <List>
                      {filteredMemories.slice(0, 10).map((memory, index) => (
                        <ListItem
                          key={memory.id || index}
                          divider
                          secondaryAction={
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() => handleDeleteMemory(memory.id)}
                              size="small"
                              color="error"
                            >
                              <Trash2 size={16} />
                            </IconButton>
                          }
                        >
                          <ListItemText
                            primary={memory.content}
                            secondary={
                              <span>
                                <span style={{ display: 'block', fontSize: '0.75rem', color: 'rgba(0, 0, 0, 0.6)' }}>
                                  类别: {(MEMORY_CATEGORY_LABELS as any)[memory.category] || memory.category}
                                </span>
                                <span style={{ display: 'block', fontSize: '0.75rem', color: 'rgba(0, 0, 0, 0.6)' }}>
                                  创建时间: {new Date(memory.createdAt).toLocaleString()}
                                </span>
                              </span>
                            }
                          />
                        </ListItem>
                      ))}
                      {filteredMemories.length > 10 && (
                        <ListItem>
                          <ListItemText
                            primary={
                              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                                还有 {filteredMemories.length - 10} 条记忆...
                              </Typography>
                            }
                          />
                        </ListItem>
                      )}
                    </List>
                  )}
                </Box>
              )}
            </Box>
          </Paper>
        </TabPanel>

        {/* 统计分析标签页 */}
        <TabPanel value={tabValue} index={5}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                记忆统计信息
              </Typography>
              <Typography variant="body2" color="text.secondary">
                查看记忆系统的使用统计和分析数据
              </Typography>
            </Box>

            <Divider />

            <Box sx={{ p: 2 }}>
              {memoryLoading.stats ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                    正在加载统计数据...
                  </Typography>
                </Box>
              ) : memoryStats ? (
                <Box>
                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h4" color="primary">
                          {memoryStats.totalMemories || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          总记忆数
                        </Typography>
                      </CardContent>
                    </Card>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h4" color="secondary">
                          {Object.keys(memoryStats.categoryCounts || {}).length}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          记忆类别数
                        </Typography>
                      </CardContent>
                    </Card>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h4" color="success.main">
                          {(memoryStats as any).recentMemories || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          近7天新增
                        </Typography>
                      </CardContent>
                    </Card>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h4" color="warning.main">
                          {(memoryStats as any).averageImportance || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          平均重要性
                        </Typography>
                      </CardContent>
                    </Card>
                  </Box>

                  {/* 分类统计 */}
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                      记忆分类分布
                    </Typography>
                    {Object.entries(memoryStats.categoryCounts || {}).map(([category, count]) => (
                      <Box key={category} sx={{ mb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2">
                            {(MEMORY_CATEGORY_LABELS as any)[category] || category}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {count} 条
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    暂无统计数据
                  </Typography>
                  {!localConfig.enabled && (
                    <Typography variant="body2" color="warning.main">
                      记忆功能未启用，请在"基础设置"中开启
                    </Typography>
                  )}
                  {localConfig.enabled && memories.length === 0 && (
                    <Typography variant="body2" color="info.main">
                      开始对话后，系统将自动生成统计数据
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          </Paper>
        </TabPanel>

        {/* 系统测试标签页 */}
        <TabPanel value={tabValue} index={6}>
          <Paper
            elevation={0}
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              overflow: 'hidden',
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.01)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                记忆系统测试
              </Typography>
              <Typography variant="body2" color="text.secondary">
                运行系统测试以验证记忆功能的正常工作
              </Typography>
            </Box>

            <Divider />

            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Button
                  variant="contained"
                  onClick={runSystemTest}
                  disabled={isRunningTest}
                  startIcon={isRunningTest ? <CircularProgress size={20} /> : <Bug size={20} />}
                >
                  {isRunningTest ? '正在测试...' : '运行系统测试'}
                </Button>

                <Button
                  variant="outlined"
                  onClick={regenerateEmbeddings}
                  disabled={isRunningTest}
                  startIcon={isRunningTest ? <CircularProgress size={20} /> : <Search size={20} />}
                  color="secondary"
                >
                  {isRunningTest ? '正在重新生成...' : '重新生成嵌入向量'}
                </Button>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                如果更换了嵌入模型，需要重新生成所有记忆的嵌入向量以确保记忆搜索正常工作
              </Typography>

              {testResults && (
                <Box sx={{ mt: 2 }}>
                  <Alert severity={testResults.success ? 'success' : 'error'} sx={{ mb: 2 }}>
                    {testResults.success ? '所有测试通过！' : '测试失败，请检查系统配置'}
                  </Alert>

                  {testResults.results && Object.keys(testResults.results).length > 0 && (
                    <Box>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                        测试结果详情:
                      </Typography>
                      {Object.entries(testResults.results).map(([testName, result]) => (
                        <Box key={testName} sx={{ mb: 1 }}>
                          <Typography variant="body2">
                            {testName}: {result ? '✅ 通过' : '❌ 失败'}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  )}

                  {testResults.errors && testResults.errors.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                        错误信息:
                      </Typography>
                      {testResults.errors.map((error: string, index: number) => (
                        <Typography key={index} variant="body2" color="error" sx={{ mb: 0.5 }}>
                          • {error}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          </Paper>
        </TabPanel>
      </Box>
    </Box>
  );
};

export default MemorySettingsPage;
