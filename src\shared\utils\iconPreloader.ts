/**
 * AI模型图标预加载器
 * 确保图标库被正确缓存，避免每次构建时重新分割
 */

// 预加载核心图标，确保它们被包含在主bundle中
import {
  // 最常用的图标 - 确保这些被预加载
  OpenAI,
  <PERSON>,
  Gemini,
  ChatGLM,
  Qwen,
  DeepSeek,
  Groq,
  Mistral,
  
  // 国内主流厂商
  Baidu,
  Tencent,
  ByteDance,
  Alibaba,
  
  // 专业服务
  HuggingFace,
  Replicate,
  Together,
  
} from '@lobehub/icons';

// 核心图标映射 - 这些图标会被预加载到主bundle中
export const CORE_ICONS = {
  // OpenAI 生态
  openai: OpenAI,
  gpt: OpenAI,
  chatgpt: OpenAI,
  
  // Anthropic
  anthropic: <PERSON>,
  claude: <PERSON>,
  
  // Google
  google: Gemini,
  gemini: Gemini,
  bard: Gemini,
  
  // 智谱AI
  chatglm: ChatGLM,
  glm: ChatGLM,
  zhipu: ChatGLM,
  
  // 阿里巴巴
  qwen: <PERSON>wen,
  tongyi: <PERSON>wen,
  alibaba: Alibaba,
  
  // DeepSeek
  deepseek: DeepSeek,
  
  // 百度
  baidu: Baidu,
  wenxin: Bai<PERSON>,
  ernie: Bai<PERSON>,
  
  // 腾讯
  tencent: Tencent,
  hunyuan: Tencent,
  
  // 字节跳动
  bytedance: ByteDance,
  doubao: ByteDance,
  
  // 专业服务
  groq: Groq,
  mistral: Mistral,
  huggingface: HuggingFace,
  hf: HuggingFace,
  replicate: Replicate,
  together: Together,
  togetherai: Together,
} as const;

// 图标预加载函数
export const preloadCoreIcons = () => {
  // 这个函数确保核心图标被包含在主bundle中
  // Vite会识别这些静态导入并将它们包含在预构建中
  return CORE_ICONS;
};

// 获取预加载的图标
export const getCoreIcon = (key: string) => {
  const normalizedKey = key.toLowerCase().replace(/[-_\s]/g, '');
  return CORE_ICONS[normalizedKey as keyof typeof CORE_ICONS] || OpenAI;
};

// 检查是否为核心图标
export const isCoreIcon = (key: string): boolean => {
  const normalizedKey = key.toLowerCase().replace(/[-_\s]/g, '');
  return normalizedKey in CORE_ICONS;
};

// 导出类型
export type CoreIconKey = keyof typeof CORE_ICONS;
