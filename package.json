{"name": "aetherlink", "private": true, "version": "0.4.5", "type": "module", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "scripts": {"postinstall": "patch-package", "dev": "vite", "build": "vite build", "build:turbo": "node scripts/turbo-build.js", "build:full": "node scripts/fast-full-build.js", "build:full-safe": "npm run type-check:incremental && npm run build:fast", "build:check": "npm run type-check && vite build", "build:parallel": "concurrently \"npm run type-check\" \"vite build\" --kill-others-on-fail", "build:fast": "vite build", "build:ultra": "node scripts/ultra-build.js", "build:ultra-fast": "echo '⚡ 启动超快终极构建...' && npm run type-check:fast && npm run lint:fast && npm run build:fast && echo '✅ 超快终极构建完成'", "type-check": "node scripts/type-check.js", "type-check:fast": "tsc --noEmit --skipLib<PERSON><PERSON><PERSON> --incremental", "type-check:incremental": "tsc --noEmit --incremental", "type-check:tsc": "tsc --noEmit", "type-check:strict": "echo '🔍 执行严格类型检查...' && tsc --noEmit --strict --exactOptionalPropertyTypes && echo '✅ 严格类型检查通过'", "type-check:watch": "tsc --noEmit --watch", "lint": "eslint .", "lint:fast": "eslint . --cache --cache-location=node_modules/.tmp/eslint-cache", "check:full": "node scripts/fast-full-check.js", "preview": "vite preview", "build:android": "npm run build && npx cap sync android", "open:android": "npx cap open android", "build:apk": "npm run build:android && npm run open:android", "dev:android": "npm run dev -- --host && npx cap run android", "build:ios": "npm run build && npx cap sync ios", "buildandios": "npm run build && npx cap sync android && npx cap sync ios", "open:ios": "npx cap open ios", "dev:ios": "npm run dev -- --host && npx cap run ios", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "npm run clean && npm run build"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@anthropic-ai/sdk": "^0.50.4", "@capacitor-community/file-opener": "^7.0.1", "@capacitor-community/native-audio": "^7.0.0", "@capacitor-community/safe-area": "^7.0.0-alpha.1", "@capacitor-community/speech-recognition": "^7.0.0", "@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/clipboard": "^7.0.1", "@capacitor/core": "^7.2.0", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.2.0", "@capacitor/keyboard": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capacitor/toast": "^7.0.1", "@capawesome/capacitor-android-edge-to-edge-support": "^7.2.2", "@capawesome/capacitor-file-picker": "^7.0.1", "@codemirror/basic-setup": "^0.20.0", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google/genai": "^1.2.0", "@google/generative-ai": "^0.24.1", "@hello-pangea/dnd": "^18.0.1", "@mendable/firecrawl-js": "^1.25.1", "@modelcontextprotocol/sdk": "^1.12.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.1", "@reduxjs/toolkit": "^2.8.1", "@shikijs/markdown-it": "^3.4.2", "@types/howler": "^2.2.12", "@types/lodash": "^4.17.16", "@types/qrcode": "^1.5.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.2.4", "ai": "^4.3.16", "axios": "^1.9.0", "better-react-mathjax": "^2.3.0", "buffer": "6.0.3", "capacitor-cors-bypass-enhanced": "^1.0.4", "chart.js": "^4.4.9", "cors": "^2.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dexie": "^4.0.11", "dompurify": "^3.2.6", "emittery": "^1.1.0", "event-source-polyfill": "^1.0.31", "eventsource": "^4.0.0", "express": "^5.1.0", "framer-motion": "^12.12.1", "howler": "^2.2.4", "http-proxy-middleware": "^3.0.5", "idb": "^8.0.3", "jsqr": "^1.4.0", "katex": "^0.16.22", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "marked": "^15.0.12", "mathjax": "^3.2.2", "mermaid": "^11.6.0", "microsoft-cognitiveservices-speech-sdk": "^1.43.1", "node-fetch": "^2.7.0", "notistack": "^3.0.2", "ofetch": "^1.4.1", "openai": "^4.98.0", "primeicons": "^7.0.0", "primevue": "^4.3.4", "qrcode": "^1.5.4", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-is": "^19.1.0", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-speech-recognition": "^4.0.1", "react-syntax-highlighter": "^15.6.1", "redux-persist": "^6.0.0", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.1.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-cjk-friendly": "^1.2.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "shiki": "^3.4.2", "tiny-pinyin": "^1.3.2", "tokenx": "^0.4.1", "tree-kill": "^1.2.2", "uuid": "^11.1.0", "vue": "^3.5.14", "zod": "^3.25.20", "zod-to-json-schema": "^3.24.5", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@swc/core": "^1.11.29", "@types/dom-speech-recognition": "^0.0.6", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/marked": "^5.0.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-katex": "^3.0.4", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.10.0", "concurrently": "^9.1.2", "esbuild": "^0.25.4", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "local-cors-proxy": "^1.1.0", "patch-package": "^8.0.0", "rimraf": "^6.0.1", "terser": "^5.39.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-checker": "^0.9.3"}}