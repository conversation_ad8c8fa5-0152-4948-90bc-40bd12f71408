import{c as e,j as r,P as a,B as o,a as s,b as i,J as t,I as l,v as n,D as d,n as c,g as x,h as b,t as h,A as m,T as p,aa as j,m as g}from"./mui-vendor-DsBXMegs.js";import{r as u,u as v}from"./react-vendor-Be-rfjCm.js";import{au as f,u as y,a as S,E as w,I as z,A as k,ac as C}from"./index-Ck4sQVom.js";import{S as V}from"./Search-D7hYSVTA.js";import{E as L}from"./ExpandLess-B3I8kUh8.js";import{A as T,g as W,a as I,b as R,c as A,s as D}from"./agentPrompts-C1ibQK6R.js";import{e as M}from"./utils-vendor-D1FP9aB2.js";import"./syntax-vendor-DfDNeb5M.js";const O=e(r.jsx("path",{d:"M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2zM4 6h16v10H4z"})),P=e(r.jsx("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5"})),B="settings",E=M({name:"settings",initialState:{theme:"system",thinkingDisplayStyle:"compact",thoughtAutoCollapse:!0,defaultThinkingEffort:"medium",multiModelDisplayStyle:"horizontal",showToolDetails:!0,showCitationDetails:!0,fontSize:16,language:"zh-CN",sendWithEnter:!0,enableNotifications:!0,models:[],providers:[],autoNameTopic:!0,modelSelectorStyle:"dialog",toolbarDisplayStyle:"both",showSystemPromptBubble:!0,showAIDebateButton:!0,showQuickPhraseButton:!0,systemPromptVariables:{enableTimeVariable:!1,enableLocationVariable:!1,customLocation:""},isLoading:!1},reducers:{setTheme:(e,r)=>{e.theme=r.payload,f(B,e)},updateSettings:(e,r)=>{Object.assign(e,r.payload),f(B,e)},initSettings:(e,r)=>r.payload}}),{setTheme:G,updateSettings:H,initSettings:N}=E.actions,Y=()=>{const e=y(),m=S((e=>e.settings)),[p,j]=u.useState(!1),g=m.systemPromptVariables||{enableTimeVariable:!1,enableLocationVariable:!1,customLocation:"",enableOSVariable:!1},v=r=>{e(H({systemPromptVariables:{...g,...r}}))};return r.jsxs(a,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",cursor:"pointer",p:2,bgcolor:e=>s(e.palette.primary.main,.02),"&:hover":{bgcolor:e=>s(e.palette.primary.main,.04)}},onClick:()=>j(!p),children:[r.jsxs(o,{sx:{flexGrow:1},children:[r.jsx(i,{variant:"subtitle1",sx:{fontWeight:600,mb:.5},children:"🔧 系统提示词变量注入"}),r.jsx(i,{variant:"body2",color:"text.secondary",children:"为系统提示词自动注入时间、位置等动态变量"})]}),r.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:1,mr:2},children:[g.enableTimeVariable&&r.jsx(t,{icon:r.jsx(T,{sx:{fontSize:"0.8rem"}}),label:"时间",size:"small",color:"primary",variant:"outlined",sx:{fontSize:"0.7rem",height:"20px"}}),g.enableLocationVariable&&r.jsx(t,{icon:r.jsx(P,{sx:{fontSize:"0.8rem"}}),label:"位置",size:"small",color:"primary",variant:"outlined",sx:{fontSize:"0.7rem",height:"20px"}}),g.enableOSVariable&&r.jsx(t,{icon:r.jsx(O,{sx:{fontSize:"0.8rem"}}),label:"系统",size:"small",color:"primary",variant:"outlined",sx:{fontSize:"0.7rem",height:"20px"}}),!g.enableTimeVariable&&!g.enableLocationVariable&&!g.enableOSVariable&&r.jsx(i,{variant:"caption",color:"text.secondary",children:"未启用"})]}),r.jsx(l,{size:"small",children:p?r.jsx(L,{}):r.jsx(w,{})})]}),r.jsxs(n,{in:p,children:[r.jsx(d,{}),r.jsxs(o,{sx:{p:2},children:[r.jsx(c,{severity:"info",icon:r.jsx(z,{}),sx:{mb:2,fontSize:"0.85rem"},children:"启用后，系统会在发送消息时自动在系统提示词末尾追加相应的变量信息。"}),r.jsxs(o,{sx:{mb:3},children:[r.jsx(x,{control:r.jsx(b,{checked:g.enableTimeVariable,onChange:e=>{return r=e.target.checked,void v({enableTimeVariable:r});var r},color:"primary"}),label:r.jsxs(o,{children:[r.jsxs(i,{variant:"subtitle2",sx:{fontWeight:600},children:[r.jsx(T,{sx:{fontSize:"1rem",mr:.5,verticalAlign:"middle"}}),"时间变量"]}),r.jsxs(i,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.8rem"},children:["自动注入当前时间：",W()]})]})}),g.enableTimeVariable&&r.jsx(o,{sx:{mt:1,ml:4,p:1,bgcolor:"rgba(0,0,0,0.02)",borderRadius:1},children:r.jsx(i,{variant:"caption",color:"text.secondary",children:"将在系统提示词末尾自动追加时间信息"})})]}),r.jsxs(o,{sx:{mb:3},children:[r.jsx(x,{control:r.jsx(b,{checked:g.enableLocationVariable,onChange:e=>{return r=e.target.checked,void v({enableLocationVariable:r});var r},color:"primary"}),label:r.jsxs(o,{children:[r.jsxs(i,{variant:"subtitle2",sx:{fontWeight:600},children:[r.jsx(P,{sx:{fontSize:"1rem",mr:.5,verticalAlign:"middle"}}),"位置变量"]}),r.jsxs(i,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.8rem"},children:["注入位置信息：",I(g.customLocation)]})]})}),g.enableLocationVariable&&r.jsxs(o,{sx:{mt:1,ml:4},children:[r.jsx(h,{fullWidth:!0,size:"small",placeholder:"输入自定义位置（如：北京市朝阳区）",value:g.customLocation,onChange:e=>{return r=e.target.value,void v({customLocation:r});var r},sx:{mb:1}}),r.jsx(o,{sx:{p:1,bgcolor:"rgba(0,0,0,0.02)",borderRadius:1},children:r.jsxs(i,{variant:"caption",color:"text.secondary",children:["将在系统提示词末尾自动追加位置信息",r.jsx("br",{}),"留空将使用系统检测的位置信息"]})})]})]}),r.jsxs(o,{children:[r.jsx(x,{control:r.jsx(b,{checked:g.enableOSVariable,onChange:e=>{return r=e.target.checked,void v({enableOSVariable:r});var r},color:"primary"}),label:r.jsxs(o,{children:[r.jsxs(i,{variant:"subtitle2",sx:{fontWeight:600},children:[r.jsx(O,{sx:{fontSize:"1rem",mr:.5,verticalAlign:"middle"}}),"操作系统变量"]}),r.jsxs(i,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.8rem"},children:["注入操作系统信息：",R()]})]})}),g.enableOSVariable&&r.jsx(o,{sx:{mt:1,ml:4,p:1,bgcolor:"rgba(0,0,0,0.02)",borderRadius:1},children:r.jsx(i,{variant:"caption",color:"text.secondary",children:"将在系统提示词末尾自动追加操作系统信息"})})]})]})]})]})},F=()=>{const e=v(),[c,x]=u.useState(""),[b,f]=u.useState(new Set(["general"])),[y,S]=u.useState(null),z=u.useMemo((()=>A()),[]),T=u.useMemo((()=>c.trim()?D(c):[]),[c]),W=e=>r.jsx(a,{elevation:0,sx:{borderRadius:1,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-1px)",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"}},children:r.jsxs(o,{sx:{p:1.2},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:.4},children:[r.jsxs(i,{variant:"subtitle2",sx:{fontWeight:600,fontSize:"0.85rem"},children:[e.emoji," ",e.name]}),r.jsx(g,{size:"small",startIcon:r.jsx(C,{sx:{fontSize:"0.7rem"}}),onClick:()=>(async e=>{try{await navigator.clipboard.writeText(e.content),S(e.id),setTimeout((()=>S(null)),2e3)}catch(r){console.error("复制失败:",r)}})(e),color:y===e.id?"success":"primary",variant:"outlined",sx:{minWidth:"auto",px:.8,py:.3,fontSize:"0.7rem",height:"24px"},children:y===e.id?"已复制":"复制"})]}),r.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:.8,fontSize:"0.75rem",lineHeight:1.2},children:e.description}),r.jsx(o,{sx:{display:"flex",flexWrap:"wrap",gap:.25},children:e.tags.map((e=>r.jsx(t,{label:e,size:"small",variant:"outlined",sx:{fontSize:"0.6rem",height:"16px","& .MuiChip-label":{px:.4}}},e)))})]})},e.id);return r.jsxs(o,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"100vh",bgcolor:e=>"light"===e.palette.mode?s(e.palette.primary.main,.02):s(e.palette.background.default,.9)},children:[r.jsx(m,{position:"fixed",elevation:0,sx:{zIndex:e=>e.zIndex.drawer+1,bgcolor:"background.paper",color:"text.primary",borderBottom:1,borderColor:"divider",backdropFilter:"blur(8px)"},children:r.jsxs(p,{children:[r.jsx(l,{edge:"start",color:"inherit",onClick:()=>{e(-1)},"aria-label":"back",sx:{color:e=>e.palette.primary.main},children:r.jsx(k,{})}),r.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:600,backgroundImage:"linear-gradient(90deg, #4f46e5, #8b5cf6)",backgroundClip:"text",color:"transparent"},children:"智能体提示词集合"})]})}),r.jsxs(o,{sx:{flexGrow:1,overflowY:"auto",p:2,mt:8,"&::-webkit-scrollbar":{width:"6px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.1)",borderRadius:"3px"}},children:[r.jsx(Y,{}),r.jsxs(a,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(o,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[r.jsx(i,{variant:"subtitle1",sx:{fontWeight:600},children:"搜索提示词"}),r.jsx(i,{variant:"body2",color:"text.secondary",children:"在丰富的提示词库中快速找到您需要的模板"})]}),r.jsx(d,{}),r.jsx(o,{sx:{p:2},children:r.jsx(h,{fullWidth:!0,placeholder:"输入关键词搜索提示词...",value:c,onChange:e=>x(e.target.value),slotProps:{input:{startAdornment:r.jsx(j,{position:"start",children:r.jsx(V,{})})}},variant:"outlined",sx:{"& .MuiOutlinedInput-root":{borderRadius:1}}})})]}),c.trim()&&T.length>0&&r.jsxs(a,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(o,{sx:{p:2,bgcolor:"rgba(0,0,0,0.01)"},children:[r.jsxs(i,{variant:"subtitle1",sx:{fontWeight:600},children:["搜索结果 (",T.length,")"]}),r.jsxs(i,{variant:"body2",color:"text.secondary",children:["找到 ",T.length," 个匹配的提示词"]})]}),r.jsx(d,{}),r.jsx(o,{sx:{p:2},children:r.jsx(o,{sx:{display:"flex",flexDirection:"column",gap:1},children:T.map(W)})})]}),c.trim()&&0===T.length&&r.jsx(a,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:r.jsx(o,{sx:{p:3,textAlign:"center"},children:r.jsx(i,{color:"text.secondary",children:"没有找到匹配的提示词"})})}),!c.trim()&&z.map((e=>{const s=b.has(e.id);return r.jsxs(a,{elevation:0,sx:{mb:2,borderRadius:2,border:"1px solid",borderColor:"divider",overflow:"hidden",bgcolor:"background.paper",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[r.jsxs(o,{sx:{display:"flex",alignItems:"center",cursor:"pointer",p:2,bgcolor:"rgba(0,0,0,0.01)","&:hover":{bgcolor:"action.hover"}},onClick:()=>(e=>{const r=new Set(b);r.has(e)?r.delete(e):r.add(e),f(r)})(e.id),children:[r.jsxs(i,{variant:"subtitle1",sx:{flexGrow:1,fontWeight:600},children:[e.emoji," ",e.name]}),r.jsxs(i,{variant:"body2",color:"text.secondary",sx:{mr:2},children:[e.prompts.length," 个提示词"]}),s?r.jsx(L,{}):r.jsx(w,{})]}),r.jsxs(n,{in:s,children:[r.jsx(d,{}),r.jsxs(o,{sx:{p:2},children:[r.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:e.description}),r.jsx(o,{sx:{display:"flex",flexDirection:"column",gap:1},children:e.prompts.map(W)})]})]})]},e.id)}))]})]})};export{F as default};
